"""
路径规划数据结构
定义请求和响应的数据格式，与前端JavaScript保持兼容
"""

import uuid
import time
import math
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
import numpy as np


@dataclass
class Point3D:
    """3D点坐标"""
    lng: float = 0.0  # 经度
    lat: float = 0.0  # 纬度
    alt: float = 0.0  # 高度
    x: float = 0.0    # 笛卡尔坐标X
    y: float = 0.0    # 笛卡尔坐标Y
    z: float = 0.0    # 笛卡尔坐标Z
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典格式"""
        return {
            'lng': self.lng,
            'lat': self.lat,
            'alt': self.alt,
            'x': self.x,
            'y': self.y,
            'z': self.z
        }

    def get(self, key: str, default=None):
        """获取属性值，类似字典的get方法"""
        if hasattr(self, key):
            return getattr(self, key)
        return default
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Point3D':
        """从字典创建Point3D对象"""
        return cls(
            lng=data.get('lng', 0.0),
            lat=data.get('lat', 0.0),
            alt=data.get('alt', 0.0),
            x=data.get('x', 0.0),
            y=data.get('y', 0.0),
            z=data.get('z', 0.0)
        )


@dataclass
class PathPoint:
    """路径点"""
    lng: float
    lat: float
    alt: float
    timestamp: int = 0
    speed: float = 0.0
    heading: float = 0.0
    action: str = 'fly'  # fly, hover, land, takeoff

    @property
    def x(self) -> float:
        """兼容性属性：返回lng作为x坐标"""
        return self.lng

    @property
    def y(self) -> float:
        """兼容性属性：返回lat作为y坐标"""
        return self.lat

    @property
    def z(self) -> float:
        """兼容性属性：返回alt作为z坐标"""
        return self.alt
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'lng': self.lng,
            'lat': self.lat,
            'alt': self.alt,
            'timestamp': self.timestamp,
            'speed': self.speed,
            'heading': self.heading,
            'action': self.action
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PathPoint':
        """从字典创建PathPoint对象"""
        return cls(
            lng=data['lng'],
            lat=data['lat'],
            alt=data['alt'],
            timestamp=data.get('timestamp', 0),
            speed=data.get('speed', 0.0),
            heading=data.get('heading', 0.0),
            action=data.get('action', 'fly')
        )


@dataclass
class DroneSpecs:
    """无人机性能参数"""
    max_flight_time: int = 1800  # 最大飞行时间(秒)
    battery_capacity: int = 5000  # 电池容量(mAh)
    payload: float = 0.0  # 载重(kg)
    wind_resistance: float = 10.0  # 抗风能力(m/s)
    max_speed: float = 15.0  # 最大速度(m/s)
    max_altitude: float = 500.0  # 最大飞行高度(m)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'maxFlightTime': self.max_flight_time,
            'batteryCapacity': self.battery_capacity,
            'payload': self.payload,
            'windResistance': self.wind_resistance,
            'maxSpeed': self.max_speed,
            'maxAltitude': self.max_altitude
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DroneSpecs':
        """从字典创建DroneSpecs对象"""
        return cls(
            max_flight_time=data.get('maxFlightTime', 1800),
            battery_capacity=data.get('batteryCapacity', 5000),
            payload=data.get('payload', 0.0),
            wind_resistance=data.get('windResistance', 10.0),
            max_speed=data.get('maxSpeed', 15.0),
            max_altitude=data.get('maxAltitude', 500.0)
        )


@dataclass
class WeatherCondition:
    """天气条件"""
    wind_speed: float = 0.0  # 风速(m/s)
    wind_direction: float = 0.0  # 风向(度)
    visibility: float = 10000.0  # 能见度(米)
    precipitation: float = 0.0  # 降水量(mm/h)
    temperature: float = 20.0  # 温度(°C)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'windSpeed': self.wind_speed,
            'windDirection': self.wind_direction,
            'visibility': self.visibility,
            'precipitation': self.precipitation,
            'temperature': self.temperature
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WeatherCondition':
        """从字典创建WeatherCondition对象"""
        return cls(
            wind_speed=data.get('windSpeed', 0.0),
            wind_direction=data.get('windDirection', 0.0),
            visibility=data.get('visibility', 10000.0),
            precipitation=data.get('precipitation', 0.0),
            temperature=data.get('temperature', 20.0)
        )


class PathPlanningRequest:
    """路径规划请求"""
    
    def __init__(self, data: Optional[Dict[str, Any]] = None):
        if data is None:
            data = {}
        
        # 基本路径参数
        self.start_point = Point3D.from_dict(data.get('startPoint', {}))
        self.end_point = Point3D.from_dict(data.get('endPoint', {}))
        
        # 飞行参数
        self.flight_height = data.get('flightHeight', 100.0)
        self.safety_distance = data.get('safetyDistance', 20.0)
        self.max_speed = data.get('maxSpeed', 15.0)
        
        # 无人机性能参数
        self.drone_specs = DroneSpecs.from_dict(data.get('droneSpecs', {}))
        
        # 环境数据
        self.buildings = data.get('buildings', [])
        self.obstacles = data.get('obstacles', [])
        self.no_fly_zones = data.get('noFlyZones', [])
        
        # 天气条件
        self.weather = WeatherCondition.from_dict(data.get('weather', {}))
        
        # 算法配置
        self.algorithm = data.get('algorithm', 'StraightLine')
        self.parameters = data.get('parameters', {})
        self.optimization = data.get('optimization', 'distance')
        self.precision = data.get('precision', 'medium')
        
        # 约束条件
        constraints_data = data.get('constraints', {})
        self.constraints = {
            'max_compute_time': constraints_data.get('maxComputeTime', 30000),
            'max_memory_usage': constraints_data.get('maxMemoryUsage', 512),
            'min_path_points': constraints_data.get('minPathPoints', 10),
            'max_path_points': constraints_data.get('maxPathPoints', 1000),
            'smoothness_weight': constraints_data.get('smoothnessWeight', 0.3),
            'safety_weight': constraints_data.get('safetyWeight', 0.4),
            'efficiency_weight': constraints_data.get('efficiencyWeight', 0.3)
        }
        
        # 请求元数据
        self.request_id = data.get('requestId', str(uuid.uuid4()))
        self.timestamp = data.get('timestamp', int(time.time() * 1000))
        self.user_id = data.get('userId', 'anonymous')
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'requestId': self.request_id,
            'timestamp': self.timestamp,
            'userId': self.user_id,
            'startPoint': self.start_point.to_dict(),
            'endPoint': self.end_point.to_dict(),
            'flightHeight': self.flight_height,
            'safetyDistance': self.safety_distance,
            'maxSpeed': self.max_speed,
            'droneSpecs': self.drone_specs.to_dict(),
            'buildings': self.buildings,
            'obstacles': self.obstacles,
            'noFlyZones': self.no_fly_zones,
            'weather': self.weather.to_dict(),
            'algorithm': self.algorithm,
            'parameters': self.parameters,
            'optimization': self.optimization,
            'precision': self.precision,
            'constraints': self.constraints
        }
    
    def estimate_complexity(self) -> str:
        """估算问题复杂度"""
        # 计算距离
        distance = self._calculate_distance()
        
        # 计算障碍物数量
        obstacle_count = len(self.buildings) + len(self.obstacles) + len(self.no_fly_zones)
        
        # 评估复杂度
        if distance < 1000 and obstacle_count < 10:
            return 'low'
        elif distance < 5000 and obstacle_count < 50:
            return 'medium'
        else:
            return 'high'
    
    def _calculate_distance(self) -> float:
        """计算起点到终点的直线距离"""
        if self.start_point.lng != 0 and self.start_point.lat != 0:
            # 使用地理坐标计算
            return self._haversine_distance(
                self.start_point.lat, self.start_point.lng,
                self.end_point.lat, self.end_point.lng
            )
        else:
            # 使用笛卡尔坐标计算
            return np.sqrt(
                (self.end_point.x - self.start_point.x)**2 +
                (self.end_point.y - self.start_point.y)**2 +
                (self.end_point.z - self.start_point.z)**2
            )
    
    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """使用Haversine公式计算地理距离"""
        R = 6371000  # 地球半径(米)
        
        lat1_rad = np.radians(lat1)
        lat2_rad = np.radians(lat2)
        delta_lat = np.radians(lat2 - lat1)
        delta_lon = np.radians(lon2 - lon1)
        
        a = (np.sin(delta_lat / 2)**2 +
             np.cos(lat1_rad) * np.cos(lat2_rad) *
             np.sin(delta_lon / 2)**2)
        c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))
        
        return R * c


class PathPlanningResponse:
    """路径规划响应 - 包含前端需要的所有字段"""

    def __init__(self):
        # 基本响应信息（前端必需）
        self.success = False
        self.error = ""
        self.execution_time = 0.0

        # 路径数据（前端必需）
        self.path: List[PathPoint] = []
        self.path_length = 0.0  # 路径总长度(米)

        # 详细指标（前端算法对比需要）
        self.turning_cost = 0.0      # 转向成本
        self.risk_value = 0.0        # 风险值
        self.collision_cost = 0.0    # 碰撞代价（兼容性保留）
        self.estimated_collision_cost = 0.0  # 预估碰撞代价（基于保护区）
        self.actual_collision_cost = 0.0  # 实际碰撞代价（基于真实检测）
        self.final_cost = 0.0        # 最终代价

        # 质量评估（前端可选使用）
        self.quality = {
            'risk_score': 0,  # 风险评分(0-100)
            'smoothness': 0,  # 平滑度(0-100)
        }

        # 元数据（前端可选使用）
        self.metadata = {}

        # 交通数据（前端可选使用）
        self.traffic_data = []

        # 保护区信息（前端展示用）
        self.protection_zones_info = {
            'involved_zones': [],  # 参与计算的保护区列表
            'total_zones': 0,      # 总保护区数量
            'collision_cost_breakdown': {},  # 各保护区的碰撞代价贡献
            'reference_values': {}  # 参考值信息
        }

    def calculate_statistics(self):
        """计算路径统计信息和指标"""
        print(f"🔍 calculate_statistics: 开始计算，路径点数: {len(self.path) if self.path else 0}")

        if not self.path or len(self.path) < 2:
            print(f"🔍 calculate_statistics: 路径无效，跳过计算")
            return

        # 计算路径长度
        self.path_length = self._calculate_path_length()
        print(f"🔍 calculate_statistics: 路径长度 = {self.path_length}")

        # 计算转向成本
        self.turning_cost = self._calculate_turning_cost()
        print(f"🔍 calculate_statistics: 转向成本 = {self.turning_cost}")

        # 计算风险值（基于路径复杂度和长度）
        base_risk = self.path_length * 0.01  # 基础风险：每公里1%
        turning_risk = self.turning_cost * 0.1  # 转向风险
        length_risk = max(5.0, self.path_length / 100)  # 长度风险：每100米0.01
        self.risk_value = base_risk + turning_risk + length_risk
        print(f"🔍 calculate_statistics: 风险值 = {self.risk_value} (基础:{base_risk:.2f} + 转向:{turning_risk:.2f} + 长度:{length_risk:.2f})")

        # 计算碰撞代价（检查是否有保护区管理器的数据）
        print(f"🔍 calculate_statistics: 开始计算碰撞代价...")
        print(f"🔍 calculate_statistics: 路径点数: {len(self.path)}")

        # 🔧 修复：优先使用统一检测管理器的碰撞代价
        # 检查多个可能的碰撞代价数据源
        collision_cost_found = False

        # 1. 检查统一检测管理器的碰撞代价
        if hasattr(self, 'unified_collision_cost') and self.unified_collision_cost is not None:
            print(f"🔍 calculate_statistics: 发现统一检测管理器碰撞代价: {self.unified_collision_cost}")
            self.collision_cost = self.unified_collision_cost
            collision_cost_found = True

        # 2. 检查保护区碰撞代价数据
        elif hasattr(self, 'estimated_collision_cost') and self.estimated_collision_cost is not None:
            print(f"🔍 calculate_statistics: 发现保护区碰撞代价数据: {self.estimated_collision_cost}")
            self.collision_cost = self.estimated_collision_cost
            collision_cost_found = True

        # 3. 检查路径航点的碰撞代价总和
        elif self.path and hasattr(self.path[0], 'estimated_collision_cost'):
            waypoint_collision_sum = sum(
                getattr(wp, 'estimated_collision_cost', 0) for wp in self.path
            )
            if waypoint_collision_sum > 0:
                print(f"🔍 calculate_statistics: 从航点累加碰撞代价: {waypoint_collision_sum}")
                self.collision_cost = waypoint_collision_sum
                collision_cost_found = True

        # 4. 回退到估算方法（仅在没有其他数据时使用）
        if not collision_cost_found:
            print(f"🔍 calculate_statistics: 未发现任何碰撞代价数据，使用估算方法")
            # 基于路径长度和航点数量的估算碰撞代价
            num_waypoints = len(self.path)
            base_collision = num_waypoints * 0.5  # 每个航点的基础碰撞代价
            path_complexity_collision = self.turning_cost * 0.01  # 路径复杂度增加碰撞风险
            length_collision = self.path_length * 0.001  # 路径长度增加碰撞风险

            self.collision_cost = base_collision + path_complexity_collision + length_collision
            print(f"🔍 calculate_statistics: 估算碰撞代价 = {self.collision_cost} (基础:{base_collision:.2f} + 复杂度:{path_complexity_collision:.2f} + 长度:{length_collision:.2f})")
        else:
            print(f"🔍 calculate_statistics: 使用真实碰撞代价: {self.collision_cost}")

        print(f"🔍 calculate_statistics: 最终碰撞代价 = {self.collision_cost}")

        # 计算最终代价 - 使用论文标准公式14和15
        self.final_cost = self._calculate_final_cost_with_dynamic_weights()
        print(f"🔍 calculate_statistics: 最终代价 = {self.final_cost}")
        print(f"🔍 calculate_statistics: 计算完成")

    def _calculate_final_cost_with_dynamic_weights(self) -> float:
        """
        使用论文标准公式14和15计算最终代价

        公式14: PathFinalCost = α·(RiskSum/RiskSum_ref) + β·(RoadCrashCost/RoadCrashCost_ref)
                               + γ·(Length/Length_manhattan) + δ·(OrientAdjustCost/OrientAdjustCost_ref)

        公式15动态权重：
        α = 0.6 * (1 - e^(-k * RiskDensity))
        β = 0.3 * (1 - e^(-k * RiskDensity))
        γ = 0.7 * e^(-k * RiskDensity) + 0.1
        δ = 0.2 * e^(-k * RiskDensity)
        """
        import math

        # 计算风险密度 (公式8: RiskDensity = RiskSum / Length)
        risk_density = self.risk_value / max(self.path_length, 1.0)  # 避免除零

        # 使用固定权重（按照您的要求）
        alpha = 0.5   # 风险权重
        beta = 0.4    # 碰撞权重
        gamma = 0.05  # 长度权重
        delta = 0.05  # 转向权重

        # 不再输出详细的固定权重日志，避免冗余

        # 🔧 修复：使用论文标准公式计算参考值

        # 1. 计算曼哈顿距离 - 公式2
        # Length_manhattan = 3 * (|x_end - x_start| + |y_end - y_start| + |z_end - z_start|)
        if len(self.path) >= 2:
            start_point = self.path[0]
            end_point = self.path[-1]

            # 将经纬度转换为米（简化处理）
            # 1度经度约等于111320米 * cos(纬度)，1度纬度约等于111320米
            lat_to_meters = 111320.0
            lng_to_meters = 111320.0 * math.cos(math.radians((start_point.lat + end_point.lat) / 2))

            dx = abs(end_point.lng - start_point.lng) * lng_to_meters
            dy = abs(end_point.lat - start_point.lat) * lat_to_meters
            dz = abs(getattr(end_point, 'alt', 120) - getattr(start_point, 'alt', 120))

            length_manhattan = 3.0 * (dx + dy + dz)
            # 不再输出详细的曼哈顿距离计算日志，避免冗余
        else:
            length_manhattan = self.path_length * 1.5  # 备用计算
            # 不再输出详细的备用曼哈顿距离计算日志，避免冗余

        # 2. 转向成本参考值 - 公式4
        # OrientAdjustCost_reference = 0.3 * (n - 2) * Δθ_max
        n_waypoints = len(self.path)
        delta_theta_max = math.radians(45.0)  # 45度转换为弧度（按照您的要求）
        turning_reference = 0.3 * max(0, n_waypoints - 2) * delta_theta_max if n_waypoints >= 3 else 30.0
        # 不再输出详细的转向成本参考值日志，避免冗余

        # 3. 碰撞代价参考值 - 公式13（更新版）
        # RoadCrashCost_reference = (∑AverageCrashCost / ProtectzoneNum) * 300
        # 🔧 更新：去掉航点数量n的影响
        # 如果有全局参考值，使用它；否则按公式计算
        collision_reference = getattr(self, 'global_collision_reference', None)
        if collision_reference is None:
            # 尝试从保护区管理器获取实际数据
            try:
                from protection_zones import ProtectionZoneManager
                protection_manager = ProtectionZoneManager()
                zones = protection_manager.zones

                if zones:
                    # 🔧 更新公式：(∑AverageCrashCost / ProtectzoneNum) * 300 (去掉n)
                    total_crash_cost = sum(zone.average_crash_cost for zone in zones)
                    protectzone_num = len(zones)
                    average_crash_cost = total_crash_cost / protectzone_num
                    collision_reference = average_crash_cost * 300
                    print(f"🔍 碰撞代价参考值计算（实际保护区数据，更新公式）: 总代价={total_crash_cost:.4f}/m², 保护区数={protectzone_num}, 平均代价={average_crash_cost:.4f}/m², 结果={collision_reference:.4f} (已去掉航点数量影响)")
                else:
                    # 使用默认值 - 🔧 更新：去掉航点数量
                    collision_reference = 8.0 * 300 / 9
                    print(f"🔍 碰撞代价参考值计算（默认值，更新公式）: 结果={collision_reference:.4f} (已去掉航点数量影响)")
            except ImportError:
                # 如果无法导入保护区管理器，使用默认计算 - 🔧 更新：去掉航点数量
                collision_reference = 8.0 * 300 / 9
                print(f"🔍 碰撞代价参考值计算（备用默认值，更新公式）: 结果={collision_reference:.4f} (已去掉航点数量影响)")

        # 4. 风险参考值 - 按照公式计算（所有路径风险的平均值）
        # 如果有全局参考值，使用它；否则使用当前路径的风险值作为参考
        risk_reference = getattr(self, 'global_risk_reference', self.risk_value if self.risk_value > 0 else 1.0)

        # 严格按照公式14计算最终代价
        # 第一项：风险代价归一化
        risk_term = alpha * (self.risk_value / max(risk_reference, 1.0))

        # 第二项：碰撞代价归一化
        collision_term = beta * (self.collision_cost / max(collision_reference, 1.0))

        # 第三项：长度代价归一化
        length_term = gamma * (self.path_length / max(length_manhattan, 1.0))

        # 第四项：转向代价归一化
        orient_term = delta * (self.turning_cost / max(turning_reference, 1.0))

        # 最终代价（公式14）
        final_cost = risk_term + collision_term + length_term + orient_term

        # 简化日志输出，只在需要时显示详细信息
        path_id = getattr(self, 'path_id', 'Unknown')
        if hasattr(self, '_debug_enabled') and self._debug_enabled:
            print(f"🔍 {path_id}: 动态权重 α={alpha:.3f}, β={beta:.3f}, γ={gamma:.3f}, δ={delta:.3f}")
            print(f"🔍 {path_id}: 风险密度={risk_density:.6f}, 最终代价={final_cost:.6f}")

        return final_cost

    def _calculate_path_length(self) -> float:
        """
        计算路径总长度 - 🔧 修复：使用欧几里得距离（与论文公式1一致）
        严格按照论文公式1: Length = Σ√[(x_{i+1} - x_i)² + (y_{i+1} - y_i)² + (z_{i+1} - z_i)²]
        """
        if len(self.path) < 2:
            print(f"🔍 _calculate_path_length: 路径点不足，返回0")
            return 0.0

        total_length = 0.0
        print(f"🔍 _calculate_path_length: 开始计算（论文公式1），路径点数: {len(self.path)}")

        try:
            for i in range(1, len(self.path)):
                prev_point = self.path[i - 1]
                curr_point = self.path[i]

                # 🔧 修复：优先使用经纬度坐标（更精确）
                if (hasattr(prev_point, 'lng') and hasattr(prev_point, 'lat') and
                    hasattr(curr_point, 'lng') and hasattr(curr_point, 'lat') and
                    prev_point.lng != 0 and prev_point.lat != 0 and
                    curr_point.lng != 0 and curr_point.lat != 0):

                    # 将经纬度转换为米（使用标准地理计算）
                    avg_lat = (prev_point.lat + curr_point.lat) / 2
                    lng_to_meters = 111320.0 * math.cos(math.radians(avg_lat))
                    lat_to_meters = 110540.0

                    dx = (curr_point.lng - prev_point.lng) * lng_to_meters
                    dy = (curr_point.lat - prev_point.lat) * lat_to_meters
                    dz = getattr(curr_point, 'alt', 100) - getattr(prev_point, 'alt', 100)

                    # 论文公式1：3D欧几里得距离
                    distance = math.sqrt(dx*dx + dy*dy + dz*dz)

                    if i <= 3:  # 只打印前3段的详细信息
                        print(f"🔍 段{i}: ({prev_point.lng:.6f},{prev_point.lat:.6f}) -> ({curr_point.lng:.6f},{curr_point.lat:.6f})")
                        print(f"    dx={dx:.2f}m, dy={dy:.2f}m, dz={dz:.2f}m, 距离={distance:.2f}m")

                elif (hasattr(prev_point, 'x') and hasattr(prev_point, 'y') and
                      hasattr(curr_point, 'x') and hasattr(curr_point, 'y')):
                    # 备用：使用笛卡尔坐标
                    dx = curr_point.x - prev_point.x
                    dy = curr_point.y - prev_point.y
                    dz = getattr(curr_point, 'z', 0) - getattr(prev_point, 'z', 0)
                    distance = math.sqrt(dx*dx + dy*dy + dz*dz)

                    if i <= 3:
                        print(f"🔍 段{i}(笛卡尔): dx={dx:.2f}, dy={dy:.2f}, dz={dz:.2f}, 距离={distance:.2f}m")
                else:
                    print(f"❌ 段{i}: 坐标数据无效")
                    continue

                total_length += distance

        except Exception as e:
            print(f"❌ _calculate_path_length: 计算异常: {e}")
            import traceback
            traceback.print_exc()
            return 0.0

        print(f"🔍 _calculate_path_length: 总长度: {total_length:.2f}米")
        return total_length

    def _calculate_turning_cost(self) -> float:
        """计算转向成本"""
        if len(self.path) < 3:
            return 0.0

        turning_cost = 0.0
        for i in range(1, len(self.path) - 1):
            prev_point = self.path[i - 1]
            curr_point = self.path[i]
            next_point = self.path[i + 1]

            # 计算转向角度
            angle = self._calculate_turning_angle(prev_point, curr_point, next_point)
            turning_cost += abs(angle)  # 统一转向权重为1，与改进算法保持一致

        return turning_cost

    def _calculate_turning_angle(self, prev_point, curr_point, next_point) -> float:
        """
        计算转向角度（偏离角）- 与改进算法保持一致

        计算公式：偏离角 = π - 夹角
        其中夹角是两个方向向量的夹角
        偏离角表示路径偏离直线的程度，直线路径偏离角为0，90度转向偏离角为π/2
        """
        import math

        # 计算两个向量
        vec1_x = curr_point.lng - prev_point.lng
        vec1_y = curr_point.lat - prev_point.lat
        vec2_x = next_point.lng - curr_point.lng
        vec2_y = next_point.lat - curr_point.lat

        # 计算向量的模长
        mag1 = math.sqrt(vec1_x**2 + vec1_y**2)
        mag2 = math.sqrt(vec2_x**2 + vec2_y**2)

        if mag1 == 0 or mag2 == 0:
            return 0.0

        # 计算夹角
        dot_product = vec1_x * vec2_x + vec1_y * vec2_y
        cos_angle = dot_product / (mag1 * mag2)
        cos_angle = max(-1.0, min(1.0, cos_angle))  # 限制在[-1, 1]范围内

        # 计算夹角
        angle = math.acos(cos_angle)

        # 返回偏离角（π - 夹角），与改进算法保持一致
        deviation_angle = math.pi - angle

        return deviation_angle

    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """使用Haversine公式计算地理距离"""
        R = 6371000  # 地球半径(米)

        lat1_rad = np.radians(lat1)
        lat2_rad = np.radians(lat2)
        delta_lat = np.radians(lat2 - lat1)
        delta_lon = np.radians(lon2 - lon1)

        a = (np.sin(delta_lat / 2)**2 +
             np.cos(lat1_rad) * np.cos(lat2_rad) *
             np.sin(delta_lon / 2)**2)
        c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))

        return R * c

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式 - 包含所有前端需要的字段"""
        return {
            'success': self.success,
            'error': self.error,
            'executionTime': self.execution_time,
            'execution_time': self.execution_time,  # 兼容字段
            'path': [point.to_dict() for point in self.path],
            'pathLength': self.path_length,
            'path_length': self.path_length,  # 兼容字段
            'turningCost': self.turning_cost,
            'turning_cost': self.turning_cost,  # 兼容字段
            'riskValue': self.risk_value,
            'risk_value': self.risk_value,  # 兼容字段
            'collisionCost': self.collision_cost,
            'collision_cost': self.collision_cost,  # 兼容字段
            'estimatedCollisionCost': self.estimated_collision_cost,
            'estimated_collision_cost': self.estimated_collision_cost,  # 兼容字段
            'actualCollisionCost': self.actual_collision_cost,
            'protectionZonesInfo': self.protection_zones_info,
            'actual_collision_cost': self.actual_collision_cost,  # 兼容字段
            'finalCost': self.final_cost,
            'final_cost': self.final_cost,  # 兼容字段
            'quality': self.quality,
            'metadata': self.metadata,
            'traffic_data': self.traffic_data  # 交通数据
        }
