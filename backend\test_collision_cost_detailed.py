#!/usr/bin/env python3
"""
详细分析碰撞代价计算问题
检查为什么碰撞代价只有10-20而不是预期的几万到几十万
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from algorithms.data_structures import Point3D, PathPlanningRequest
from algorithms.unified_detection_manager import UnifiedDetectionManager
from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathPlanning

def test_collision_cost_detailed():
    """详细分析碰撞代价计算过程"""
    
    print("🔍 详细分析碰撞代价计算问题")
    print("=" * 60)
    
    # 创建一个简单的2点路径
    waypoints = [
        Point3D(lng=139.767300, lat=35.681200, alt=100.0, x=139.767300, y=35.681200, z=100),
        Point3D(lng=139.773400, lat=35.715300, alt=100.0, x=139.773400, y=35.715300, z=100)
    ]
    
    print(f"📍 测试路径：{len(waypoints)}个航点，长度约3.8公里")
    
    # 1. 使用统一检测管理器获取保护区
    print(f"\n🛡️ 1. 获取保护区数据")
    
    unified_manager = UnifiedDetectionManager()
    result = unified_manager.detect_for_path(waypoints, 100.0)
    
    print(f"   保护区数量: {len(result.protection_zones)}")
    print(f"   统一检测报告的碰撞代价: {result.collision_cost:.4f}")
    
    # 2. 手动计算每个保护区的碰撞代价
    print(f"\n🔧 2. 手动计算每个保护区的碰撞代价")
    
    total_manual_cost = 0
    for i, zone in enumerate(result.protection_zones[:5]):  # 只分析前5个
        print(f"\n   保护区{i}: {zone.zone_id}")
        print(f"      类型: {zone.zone_type}")
        print(f"      密度: {zone.collision_cost_density:.2f}/m²")
        print(f"      面积: {zone.area:.2f}m²")
        
        # 计算每个航点与该保护区的交集
        zone_total_cost = 0
        for j, waypoint in enumerate(waypoints):
            intersection_area = zone.get_intersection_area(waypoint, 30.0)
            waypoint_cost = zone.collision_cost_density * intersection_area
            zone_total_cost += waypoint_cost
            
            if intersection_area > 0:
                print(f"      航点{j}: 交集面积={intersection_area:.2f}m², 代价={waypoint_cost:.4f}")
            else:
                print(f"      航点{j}: 无交集")
        
        print(f"      保护区总代价: {zone_total_cost:.4f}")
        total_manual_cost += zone_total_cost
    
    print(f"\n   前5个保护区手动计算总代价: {total_manual_cost:.4f}")
    
    # 3. 分析算法内部的碰撞代价计算
    print(f"\n🔧 3. 分析算法内部碰撞代价计算")
    
    # 创建算法实例
    algorithm = ImprovedClusterBasedPathPlanning()
    
    # 设置建筑物数据
    algorithm.all_buildings = result.buildings if result.buildings else []
    
    # 创建成本计算器并设置数据
    if hasattr(algorithm, 'cost_calculator') and algorithm.cost_calculator:
        cost_calc = algorithm.cost_calculator
        
        # 设置建筑物数据
        cost_calc.set_buildings_data(result.buildings if result.buildings else [])
        
        # 手动计算碰撞代价
        print(f"   成本计算器建筑物数量: {len(cost_calc.all_buildings) if hasattr(cost_calc, 'all_buildings') else 0}")
        
        # 调用碰撞代价计算方法
        if hasattr(cost_calc, 'calculate_collision_cost'):
            manual_collision_cost = cost_calc.calculate_collision_cost(waypoints, result.protection_zones)
            print(f"   成本计算器计算的碰撞代价: {manual_collision_cost:.4f}")
        
        # 检查保护区数据是否正确传递
        if hasattr(cost_calc, 'protection_zones'):
            print(f"   成本计算器保护区数量: {len(cost_calc.protection_zones) if cost_calc.protection_zones else 0}")
    
    # 4. 检查交集面积计算方法
    print(f"\n🔧 4. 检查交集面积计算方法")
    
    if result.protection_zones:
        test_zone = result.protection_zones[0]
        test_waypoint = waypoints[0]
        
        print(f"   测试保护区: {test_zone.zone_id}")
        print(f"   测试航点: ({test_waypoint.lng:.6f}, {test_waypoint.lat:.6f})")
        
        # 测试不同半径的交集面积
        for radius in [10, 20, 30, 50]:
            intersection = test_zone.get_intersection_area(test_waypoint, radius)
            print(f"   半径{radius}m: 交集面积={intersection:.2f}m²")
        
        # 检查保护区的多边形点
        if hasattr(test_zone, 'polygon_points') and test_zone.polygon_points:
            print(f"   保护区多边形点数量: {len(test_zone.polygon_points)}")
            for k, point in enumerate(test_zone.polygon_points[:3]):  # 只显示前3个点
                x = getattr(point, 'x', getattr(point, 'lng', 0))
                y = getattr(point, 'y', getattr(point, 'lat', 0))
                print(f"   点{k}: ({x:.6f}, {y:.6f})")
    
    # 5. 理论计算vs实际计算
    print(f"\n📊 5. 理论计算vs实际计算对比")
    
    # 理论最大碰撞代价
    circle_area = 3.14159 * 30 * 30  # 30米圆形面积
    theoretical_max_per_zone = 70 * circle_area  # 最高密度70/m²
    theoretical_total_max = theoretical_max_per_zone * len(result.protection_zones) * len(waypoints)
    
    print(f"   30米圆形面积: {circle_area:.2f}m²")
    print(f"   单个保护区理论最大代价: {theoretical_max_per_zone:.2f}")
    print(f"   全路径理论最大代价: {theoretical_total_max:.2f}")
    print(f"   实际碰撞代价: {result.collision_cost:.4f}")
    print(f"   差异倍数: {theoretical_total_max / max(result.collision_cost, 0.0001):.0f}倍")
    
    # 6. 问题诊断
    print(f"\n❗ 6. 问题诊断")
    
    problems = []
    
    if result.collision_cost < 1000:
        problems.append("碰撞代价过低，可能是交集面积计算错误")
    
    if total_manual_cost != result.collision_cost:
        diff = abs(total_manual_cost - result.collision_cost)
        if diff > 0.1:
            problems.append(f"手动计算与统一检测结果不一致，差异: {diff:.4f}")
    
    if len(result.protection_zones) > 0:
        avg_area = sum(zone.area for zone in result.protection_zones) / len(result.protection_zones)
        if avg_area < 100:
            problems.append("保护区平均面积过小，可能影响碰撞代价计算")
    
    if problems:
        for i, problem in enumerate(problems, 1):
            print(f"   {i}. {problem}")
    else:
        print(f"   未发现明显问题")
    
    # 7. 修复建议
    print(f"\n💡 7. 修复建议")
    print(f"   1. 检查交集面积计算算法，确保正确计算圆形与多边形的交集")
    print(f"   2. 验证保护区坐标系统，确保与航点坐标系统一致")
    print(f"   3. 检查碰撞代价累加逻辑，确保所有保护区代价都被正确累加")
    print(f"   4. 验证保护区密度设置，确保符合实际预期")
    
    return {
        'protection_zones_count': len(result.protection_zones),
        'reported_collision_cost': result.collision_cost,
        'manual_collision_cost': total_manual_cost,
        'theoretical_max': theoretical_total_max,
        'problems_found': len(problems)
    }

if __name__ == "__main__":
    try:
        result = test_collision_cost_detailed()
        print(f"\n✅ 详细分析完成")
        print(f"   保护区数量: {result['protection_zones_count']}")
        print(f"   报告碰撞代价: {result['reported_collision_cost']:.4f}")
        print(f"   手动计算代价: {result['manual_collision_cost']:.4f}")
        print(f"   理论最大代价: {result['theoretical_max']:.2f}")
        print(f"   发现问题数量: {result['problems_found']}")
    except Exception as e:
        print(f"\n❌ 详细分析失败: {e}")
        import traceback
        traceback.print_exc()
