#!/usr/bin/env python3
"""
测试选中路径标记修复
验证导出表格中的"是否选中"字段是否正确标记最佳路径
"""

import json
import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

def test_selected_path_logic():
    """测试选中路径逻辑"""
    
    print("=" * 60)
    print("选中路径标记修复测试")
    print("=" * 60)
    
    # 模拟路径数据
    mock_paths_data = []
    
    # 创建81条模拟路径，其中路径42有最低的最终代价
    for i in range(81):
        path_id = i + 1
        
        # 模拟不同的最终代价，让路径42成为最优路径
        if path_id == 42:
            final_cost = 1.2345  # 最低代价
        else:
            final_cost = 1.5 + (i * 0.01)  # 其他路径有更高代价
        
        path_data = {
            'path_id': path_id,
            'final_cost': final_cost,
            'path_length': 1000 + (i * 10),
            'turning_cost': 0.1 + (i * 0.001),
            'risk_value': 0.3 + (i * 0.002),
            'collision_cost': 0.2 + (i * 0.001)
        }
        mock_paths_data.append(path_data)
    
    print(f"📊 创建了 {len(mock_paths_data)} 条模拟路径")
    print(f"🎯 路径42应该是最优路径（最终代价: {mock_paths_data[41]['final_cost']:.6f}）")
    
    # 测试选中路径识别逻辑
    print("\n1. 测试选中路径识别逻辑")
    print("-" * 40)
    
    selected_path_id = None
    min_final_cost = float('inf')
    
    # 找到最终代价最低的路径作为选中路径
    for i, path_data in enumerate(mock_paths_data):
        final_cost = path_data.get('final_cost', float('inf'))
        if final_cost < min_final_cost:
            min_final_cost = final_cost
            selected_path_id = i + 1  # 路径ID从1开始（按生成顺序）
    
    print(f"✅ 识别出的选中路径ID: {selected_path_id}")
    print(f"✅ 最终代价: {min_final_cost:.6f}")
    
    # 验证结果
    if selected_path_id == 42:
        print("✅ 选中路径识别正确！")
    else:
        print(f"❌ 选中路径识别错误！应该是42，但识别为{selected_path_id}")
    
    # 测试is_selected字段设置
    print("\n2. 测试is_selected字段设置")
    print("-" * 40)
    
    selected_count = 0
    for i, path_data in enumerate(mock_paths_data):
        path_id = i + 1
        is_selected = 1 if path_id == selected_path_id else 0
        path_data['is_selected'] = is_selected
        
        if is_selected:
            selected_count += 1
            print(f"✅ 路径{path_id}被标记为选中（最终代价: {path_data['final_cost']:.6f}）")
    
    print(f"📊 总共有 {selected_count} 条路径被标记为选中")
    
    if selected_count == 1:
        print("✅ is_selected字段设置正确！")
    else:
        print(f"❌ is_selected字段设置错误！应该只有1条路径被选中，但有{selected_count}条")
    
    # 生成CSV预览
    print("\n3. 生成CSV预览（前10行）")
    print("-" * 40)
    
    print("路径ID | 最终代价   | 是否选中 | 排名")
    print("-" * 35)
    
    # 按最终代价排序以显示排名
    sorted_paths = sorted(mock_paths_data, key=lambda p: p['final_cost'])
    
    for i, path_data in enumerate(sorted_paths[:10]):
        path_id = path_data['path_id']
        final_cost = path_data['final_cost']
        is_selected = "✓" if path_data['is_selected'] else ""
        rank = i + 1
        
        print(f"{path_id:6} | {final_cost:10.6f} | {is_selected:8} | {rank:4}")
    
    # 验证最优路径是否在第一位
    best_path = sorted_paths[0]
    if best_path['is_selected'] == 1:
        print(f"\n✅ 最优路径（路径{best_path['path_id']}）正确标记为选中！")
    else:
        print(f"\n❌ 最优路径（路径{best_path['path_id']}）没有被标记为选中！")
    
    # 保存测试数据
    print("\n4. 保存测试数据")
    print("-" * 40)
    
    test_data = {
        'test_info': {
            'total_paths': len(mock_paths_data),
            'selected_path_id': selected_path_id,
            'min_final_cost': min_final_cost,
            'selected_count': selected_count
        },
        'paths_data': mock_paths_data
    }
    
    output_file = backend_dir / 'test_selected_path_data.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 测试数据已保存到: {output_file}")
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"✅ 选中路径识别: {'正确' if selected_path_id == 42 else '错误'}")
    print(f"✅ 选中标记数量: {'正确' if selected_count == 1 else '错误'}")
    print(f"✅ 最优路径标记: {'正确' if best_path['is_selected'] == 1 else '错误'}")
    
    if selected_path_id == 42 and selected_count == 1 and best_path['is_selected'] == 1:
        print("🎉 所有测试通过！选中路径标记修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

def test_csv_export_format():
    """测试CSV导出格式"""
    
    print("\n" + "=" * 60)
    print("CSV导出格式测试")
    print("=" * 60)
    
    # 模拟CSV表头
    headers = [
        'path_id', 'path_index', 'flight_direction', 'height_layer',
        'cluster_id', 'cluster_type', 'waypoints_count', 'path_length',
        'turning_cost', 'risk_value', 'collision_cost', 'final_cost',
        'weight_alpha', 'weight_beta', 'weight_gamma', 'weight_delta',
        'risk_normalized', 'collision_normalized', 'length_normalized', 'turning_normalized',
        'risk_term', 'collision_term', 'length_term', 'orient_term',
        'risk_percent', 'collision_percent', 'length_percent', 'turning_percent',
        'is_selected'
    ]
    
    print(f"📋 CSV表头包含 {len(headers)} 个字段")
    print(f"🎯 'is_selected' 字段位于第 {headers.index('is_selected') + 1} 列")
    
    # 验证is_selected字段存在
    if 'is_selected' in headers:
        print("✅ 'is_selected' 字段存在于CSV表头中")
    else:
        print("❌ 'is_selected' 字段不存在于CSV表头中")
    
    return 'is_selected' in headers

if __name__ == "__main__":
    print("🚀 开始测试选中路径标记修复...")
    
    # 测试选中路径逻辑
    logic_test_passed = test_selected_path_logic()
    
    # 测试CSV导出格式
    format_test_passed = test_csv_export_format()
    
    print("\n" + "=" * 60)
    print("最终测试结果:")
    print(f"📊 选中路径逻辑: {'✅ 通过' if logic_test_passed else '❌ 失败'}")
    print(f"📋 CSV导出格式: {'✅ 通过' if format_test_passed else '❌ 失败'}")
    
    if logic_test_passed and format_test_passed:
        print("🎉 所有测试通过！修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
