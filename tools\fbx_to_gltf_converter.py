#!/usr/bin/env python3
"""
FBX到glTF批量转换工具
使用Blender Python API将FBX文件转换为Web友好的glTF格式
"""

import os
import sys
import glob
import json
import time
from pathlib import Path

def create_blender_script():
    """创建Blender转换脚本"""
    script_content = '''
import bpy
import os
import sys
import json
from mathutils import Vector

def clear_scene():
    """清空场景"""
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # 清空集合
    for collection in bpy.data.collections:
        bpy.data.collections.remove(collection)

def convert_fbx_to_gltf(fbx_path, gltf_path):
    """转换单个FBX文件到glTF"""
    try:
        print(f"正在转换: {fbx_path}")
        
        # 清空场景
        clear_scene()
        
        # 导入FBX
        bpy.ops.import_scene.fbx(filepath=fbx_path)
        
        # 获取导入的对象
        imported_objects = [obj for obj in bpy.context.scene.objects if obj.select_get()]
        
        if not imported_objects:
            imported_objects = list(bpy.context.scene.objects)
        
        print(f"导入了 {len(imported_objects)} 个对象")
        
        # 优化几何体
        for obj in imported_objects:
            if obj.type == 'MESH':
                bpy.context.view_layer.objects.active = obj
                obj.select_set(True)
                
                # 进入编辑模式进行优化
                bpy.ops.object.mode_set(mode='EDIT')
                
                # 移除重复顶点
                bpy.ops.mesh.remove_doubles(threshold=0.001)
                
                # 重新计算法线
                bpy.ops.mesh.normals_make_consistent(inside=False)
                
                # 退出编辑模式
                bpy.ops.object.mode_set(mode='OBJECT')
        
        # 导出为glTF
        bpy.ops.export_scene.gltf(
            filepath=gltf_path,
            export_format='GLB',  # 使用二进制格式
            export_selected=False,
            export_apply=True,
            export_yup=True,
            export_materials='EXPORT',
            export_colors=True,
            export_cameras=False,
            export_lights=False,
            export_animations=False,
            export_force_sampling=False,
            export_optimize_animation_size=False
        )
        
        print(f"转换完成: {gltf_path}")
        return True
        
    except Exception as e:
        print(f"转换失败 {fbx_path}: {str(e)}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: blender --background --python script.py -- <input_dir> <output_dir> [file_list]")
        sys.exit(1)
    
    # 获取参数
    args = sys.argv[sys.argv.index("--") + 1:]
    input_dir = args[0]
    output_dir = args[1]
    file_list_path = args[2] if len(args) > 2 else None
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取要转换的文件列表
    if file_list_path and os.path.exists(file_list_path):
        with open(file_list_path, 'r', encoding='utf-8') as f:
            files_to_convert = [line.strip() for line in f if line.strip()]
    else:
        # 获取所有FBX文件
        fbx_pattern = os.path.join(input_dir, "*.fbx")
        files_to_convert = glob.glob(fbx_pattern)
    
    print(f"找到 {len(files_to_convert)} 个FBX文件待转换")
    
    # 转换统计
    success_count = 0
    fail_count = 0
    conversion_log = []
    
    for fbx_file in files_to_convert:
        if not os.path.isabs(fbx_file):
            fbx_file = os.path.join(input_dir, fbx_file)
            
        if not os.path.exists(fbx_file):
            print(f"文件不存在: {fbx_file}")
            continue
            
        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(fbx_file))[0]
        gltf_file = os.path.join(output_dir, f"{base_name}.glb")
        
        # 记录文件信息
        file_size = os.path.getsize(fbx_file)
        start_time = time.time()
        
        # 执行转换
        success = convert_fbx_to_gltf(fbx_file, gltf_file)
        
        end_time = time.time()
        conversion_time = end_time - start_time
        
        # 记录结果
        result = {
            'input_file': fbx_file,
            'output_file': gltf_file,
            'input_size': file_size,
            'output_size': os.path.getsize(gltf_file) if success and os.path.exists(gltf_file) else 0,
            'conversion_time': conversion_time,
            'success': success
        }
        
        conversion_log.append(result)
        
        if success:
            success_count += 1
            print(f"✓ 成功 ({success_count}/{len(files_to_convert)}): {base_name}")
        else:
            fail_count += 1
            print(f"✗ 失败 ({fail_count}/{len(files_to_convert)}): {base_name}")
    
    # 保存转换日志
    log_file = os.path.join(output_dir, "conversion_log.json")
    with open(log_file, 'w', encoding='utf-8') as f:
        json.dump({
            'summary': {
                'total_files': len(files_to_convert),
                'success_count': success_count,
                'fail_count': fail_count,
                'success_rate': success_count / len(files_to_convert) * 100 if files_to_convert else 0
            },
            'conversions': conversion_log
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\\n转换完成!")
    print(f"成功: {success_count}")
    print(f"失败: {fail_count}")
    print(f"成功率: {success_count / len(files_to_convert) * 100:.1f}%")
    print(f"转换日志已保存到: {log_file}")

if __name__ == "__main__":
    main()
'''
    
    return script_content

def select_test_files(input_dir, percentage=10, max_files=50):
    """选择测试文件（10%或最多50个）"""
    fbx_pattern = os.path.join(input_dir, "*.fbx")
    all_files = glob.glob(fbx_pattern)
    
    if not all_files:
        print(f"在 {input_dir} 中没有找到FBX文件")
        return []
    
    # 按文件大小排序，选择不同大小的文件进行测试
    all_files.sort(key=lambda x: os.path.getsize(x))
    
    # 计算要选择的文件数量
    total_files = len(all_files)
    target_count = min(max_files, max(1, int(total_files * percentage / 100)))
    
    # 均匀选择文件（小、中、大文件都包含）
    selected_files = []
    step = total_files // target_count
    
    for i in range(0, total_files, step):
        if len(selected_files) >= target_count:
            break
        selected_files.append(all_files[i])
    
    print(f"从 {total_files} 个文件中选择了 {len(selected_files)} 个进行测试转换")
    
    # 显示选择的文件信息
    for i, file_path in enumerate(selected_files[:10]):  # 只显示前10个
        size_mb = os.path.getsize(file_path) / (1024 * 1024)
        print(f"  {i+1}. {os.path.basename(file_path)} ({size_mb:.2f} MB)")
    
    if len(selected_files) > 10:
        print(f"  ... 还有 {len(selected_files) - 10} 个文件")
    
    return selected_files

def run_conversion(input_dir, output_dir, test_files=None):
    """运行转换过程"""
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建Blender脚本
    script_content = create_blender_script()
    script_path = os.path.join(output_dir, "convert_script.py")
    
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"Blender脚本已创建: {script_path}")
    
    # 如果指定了测试文件，创建文件列表
    file_list_path = None
    if test_files:
        file_list_path = os.path.join(output_dir, "test_files.txt")
        with open(file_list_path, 'w', encoding='utf-8') as f:
            for file_path in test_files:
                f.write(f"{os.path.basename(file_path)}\\n")
        print(f"测试文件列表已创建: {file_list_path}")
    
    # 构建Blender命令
    blender_cmd = [
        "blender",
        "--background",
        "--python", script_path,
        "--",
        input_dir,
        output_dir
    ]
    
    if file_list_path:
        blender_cmd.append(file_list_path)
    
    print("\\n要运行转换，请执行以下命令:")
    print(" ".join(blender_cmd))
    print("\\n注意: 请确保已安装Blender并且blender命令在PATH中")
    
    return script_path, file_list_path

def main():
    """主函数"""
    # 设置路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    
    input_dir = os.path.join(project_root, "data", "tokyo23", "bldg", "lod1")
    output_dir = os.path.join(project_root, "data", "converted_gltf")
    
    print("FBX到glTF批量转换工具")
    print("=" * 50)
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    
    # 检查输入目录
    if not os.path.exists(input_dir):
        print(f"错误: 输入目录不存在: {input_dir}")
        return
    
    # 选择测试文件（10%）
    test_files = select_test_files(input_dir, percentage=10, max_files=50)
    
    if not test_files:
        print("没有找到可转换的文件")
        return
    
    # 运行转换
    script_path, file_list_path = run_conversion(input_dir, output_dir, test_files)
    
    print("\\n转换脚本已准备就绪!")
    print("\\n下一步:")
    print("1. 确保安装了Blender (https://www.blender.org/download/)")
    print("2. 运行上面显示的blender命令")
    print("3. 转换完成后，我们将修改Three.js代码使用glTF文件")

if __name__ == "__main__":
    main()
