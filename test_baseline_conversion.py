#!/usr/bin/env python3
"""
测试基准路径ID转换
"""

import json
import glob
import os

def test_baseline_conversion():
    # 查找最新的JSON文件
    json_files = glob.glob('all_81_paths_data_*.json')
    if not json_files:
        print("❌ 没有找到JSON文件")
        return
    
    latest_json = max(json_files, key=os.path.getctime)
    print(f"📂 使用JSON文件: {latest_json}")
    
    # 读取JSON数据
    with open(latest_json, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 查看第一条路径的path_id
    first_path = data['all_paths'][0]
    path_id = first_path.get('path_id')
    
    print(f"第一条路径的path_id: \"{path_id}\"")
    print(f"path_id类型: {type(path_id)}")
    print(f"path_id长度: {len(path_id)}")
    print(f"path_id字节表示: {path_id.encode('utf-8')}")
    
    # 测试条件判断
    if path_id == 'BASELINE_A*':
        print("✅ 条件匹配成功")
        display_id = '🎯基准A*'
    else:
        print("❌ 条件匹配失败")
        display_id = str(path_id)
    
    print(f"转换后的display_id: \"{display_id}\"")
    
    # 查看所有路径的path_id
    print("\n📋 所有路径的path_id:")
    for i, path in enumerate(data['all_paths'][:5]):
        pid = path.get('path_id')
        print(f"  路径{i}: \"{pid}\" (类型: {type(pid)})")

if __name__ == "__main__":
    test_baseline_conversion()
