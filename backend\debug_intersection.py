#!/usr/bin/env python3
"""
调试交集计算
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_intersection():
    """调试交集计算"""
    from algorithms.improved_cluster_pathfinding import (
        CostCalculator, ImprovedPathPoint, ProtectionZone
    )
    from algorithms.data_structures import Point3D
    
    config = {'maxTurnAngle': 90, 'riskEdgeDistance': 50, 'kValue': 5}
    calculator = CostCalculator(config)
    
    # 创建测试航点
    waypoint = ImprovedPathPoint(x=100, y=100, z=100, waypoint_index=1)
    
    # 创建测试保护区
    protection_zones = [
        ProtectionZone(
            zone_id="vehicle_zone_1",
            zone_type="vehicle",
            polygon_points=[
                Point3D(lng=90, lat=90, alt=0, x=90, y=90, z=0),
                Point3D(lng=110, lat=90, alt=0, x=110, y=90, z=0),
                Point3D(lng=110, lat=110, alt=0, x=110, y=110, z=0),
                Point3D(lng=90, lat=110, alt=0, x=90, y=110, z=0)
            ],
            collision_cost_density=15.0
        )
    ]
    
    zone = protection_zones[0]
    
    print("=== 调试交集计算 ===")
    print(f"保护区ID: {zone.zone_id}")
    print(f"保护区面积: {zone.area}")
    print(f"碰撞代价密度: {zone.collision_cost_density}")
    
    # 计算保护区中心
    zone_center_x = sum(getattr(p, 'x', p.lng) for p in zone.polygon_points) / len(zone.polygon_points)
    zone_center_y = sum(getattr(p, 'y', p.lat) for p in zone.polygon_points) / len(zone.polygon_points)
    print(f"保护区中心: ({zone_center_x}, {zone_center_y})")
    
    print(f"航点位置: ({waypoint.x}, {waypoint.y})")
    
    # 计算距离
    import math
    distance = math.sqrt((zone_center_x - waypoint.x)**2 + (zone_center_y - waypoint.y)**2)
    print(f"距离: {distance}")
    print(f"搜索半径: 30.0")
    print(f"距离 <= 半径: {distance <= 30.0}")
    
    # 计算交集面积
    waypoint_pos = Point3D(lng=waypoint.x, lat=waypoint.y, alt=waypoint.z, x=waypoint.x, y=waypoint.y, z=waypoint.z)
    print(f"航点Point3D: lng={waypoint_pos.lng}, lat={waypoint_pos.lat}, x={waypoint_pos.x}, y={waypoint_pos.y}")
    intersection_area = zone.get_intersection_area(waypoint_pos, 30.0)
    print(f"交集面积: {intersection_area}")

    # 手动验证交集计算
    center_x = getattr(waypoint_pos, 'x', waypoint_pos.lng)
    center_y = getattr(waypoint_pos, 'y', waypoint_pos.lat)
    print(f"用于计算的航点坐标: ({center_x}, {center_y})")

    manual_distance = math.sqrt((zone_center_x - center_x)**2 + (zone_center_y - center_y)**2)
    print(f"手动计算距离: {manual_distance}")

    if manual_distance <= 30.0:
        circle_area = math.pi * 30.0 * 30.0
        expected_intersection = min(zone.area, circle_area)
        print(f"预期交集面积: {expected_intersection}")
    else:
        print("预期交集面积: 0.0")
    
    # 计算碰撞代价
    zone_cost = zone.collision_cost_density * intersection_area
    print(f"保护区碰撞代价: {zone_cost}")
    
    # 使用计算器计算
    estimated_cost = calculator._calculate_estimated_collision_cost(waypoint, protection_zones)
    print(f"估计碰撞代价: {estimated_cost}")
    
    return estimated_cost > 0

if __name__ == '__main__':
    success = debug_intersection()
    print(f'调试结果: {"成功" if success else "失败"}')
