#!/usr/bin/env python3
"""
测试修复后的导出功能
"""

import sys
import os
sys.path.append('.')

def test_fixed_export():
    """测试修复后的导出功能"""
    print("🧪 测试修复后的导出功能...")
    
    try:
        # 导入Flask应用
        from app import app
        
        # 创建测试客户端
        with app.test_client() as client:
            print("📡 发送导出请求...")
            
            response = client.post('/api/export_calculated_paths', 
                                 json={
                                     "timestamp": "2025-07-31T12:30:00",
                                     "request_type": "complete_csv_export"
                                 })
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.get_json()
                print(f"✅ 导出成功!")
                print(f"   文件名: {result.get('filename', '未知')}")
                print(f"   路径数量: {result.get('total_paths', '未知')}")
                print(f"   源JSON: {result.get('source_json', '未知')}")
                print(f"   导出时间: {result.get('export_time', '未知')}")
                
                # 检查生成的CSV文件
                filepath = result.get('filepath')
                if filepath and os.path.exists(filepath):
                    print(f"\n📁 检查生成的CSV文件: {filepath}")
                    
                    with open(filepath, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    print(f"   文件行数: {len(lines)}")
                    print(f"   表头: {lines[0].strip()}")
                    
                    # 检查保护区信息
                    protection_zones_found = 0
                    for i, line in enumerate(lines[1:], 1):
                        fields = line.strip().split(',')
                        if len(fields) > 11:  # 保护区数量字段位置
                            zone_count = fields[11]
                            if zone_count and zone_count != '0':
                                protection_zones_found += 1
                                if protection_zones_found <= 3:  # 只显示前3个
                                    print(f"   路径{i}: 保护区数量={zone_count}")
                    
                    print(f"   有保护区的路径数: {protection_zones_found}")
                
                return True
            else:
                print(f"❌ 导出失败: {response.status_code}")
                try:
                    error_data = response.get_json()
                    print(f"   错误信息: {error_data.get('error', '未知错误')}")
                except:
                    print(f"   响应内容: {response.get_data(as_text=True)}")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_export()
    exit(0 if success else 1)
