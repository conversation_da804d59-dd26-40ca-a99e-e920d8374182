"""
保护区管理系统
用于计算预估碰撞代价的预设区域管理
"""

from typing import List, Dict, Tuple, Any
import math
import os
import tempfile
from dataclasses import dataclass
from enum import Enum

class ProtectionZoneType(Enum):
    """保护区类型"""
    ROAD_SIDE = "road_side"  # 沿路保护区
    PARK = "park"  # 公园
    COMMERCIAL = "commercial"  # 商业区
    RESIDENTIAL = "residential"  # 住宅区
    SCHOOL = "school"  # 学校
    HOSPITAL = "hospital"  # 医院
    TRANSPORT_HUB = "transport_hub"  # 交通枢纽
    # 新增类型：道路交通和人流密集区
    ROAD_TRAFFIC = "road_traffic"        # 道路交通区 🚗
    PEDESTRIAN_AREA = "pedestrian_area"  # 人流密集区 👥
    INTERSECTION = "intersection"        # 路口 🚦
    BUS_STOP = "bus_stop"               # 公交站 🚌
    SUBWAY_STATION = "subway_station"    # 地铁站 🚇

@dataclass
class ProtectionZone:
    """
    保护区定义 - 🔧 重新设计：基于人数和车辆数计算碰撞代价
    """
    id: str
    name: str
    zone_type: ProtectionZoneType
    center: Tuple[float, float]  # (lng, lat)
    radius: float  # 半径（米）
    people_count: int  # 人数
    vehicle_count: int  # 车辆数
    description: str = ""

    # 碰撞代价常数（根据项目需求文档）
    PERSON_CRASH_COST = 8.0    # 每人碰撞代价
    VEHICLE_CRASH_COST = 15.0  # 每车碰撞代价

    @property
    def total_crash_cost(self) -> float:
        """
        总碰撞代价 = 人数 × 人员代价 + 车辆数 × 车辆代价
        """
        return (self.people_count * self.PERSON_CRASH_COST +
                self.vehicle_count * self.VEHICLE_CRASH_COST)

    @property
    def average_crash_cost(self) -> float:
        """
        平均碰撞代价（每平方米）
        将总代价分摊到保护区面积上
        """
        area = math.pi * (self.radius ** 2)  # 保护区面积
        return self.total_crash_cost / area if area > 0 else 0.0

    @property
    def collision_cost_factor(self) -> float:
        """
        向后兼容的碰撞代价系数
        """
        return self.total_crash_cost

    def get_icon_info(self) -> dict:
        """
        获取保护区图标信息
        返回包含图标、颜色等可视化信息的字典
        """
        icon_mapping = {
            ProtectionZoneType.ROAD_TRAFFIC: {
                'icon': '🚗',
                'symbol': 'car',
                'color': '#ff6b35',
                'description': '道路交通'
            },
            ProtectionZoneType.PEDESTRIAN_AREA: {
                'icon': '👥',
                'symbol': 'people',
                'color': '#4ecdc4',
                'description': '人流密集区'
            },
            ProtectionZoneType.INTERSECTION: {
                'icon': '🚦',
                'symbol': 'traffic-light',
                'color': '#ffe66d',
                'description': '交通路口'
            },
            ProtectionZoneType.BUS_STOP: {
                'icon': '🚌',
                'symbol': 'bus',
                'color': '#a8e6cf',
                'description': '公交站'
            },
            ProtectionZoneType.SUBWAY_STATION: {
                'icon': '🚇',
                'symbol': 'subway',
                'color': '#ff8b94',
                'description': '地铁站'
            },
            ProtectionZoneType.PARK: {
                'icon': '🌳',
                'symbol': 'park',
                'color': '#88d8b0',
                'description': '公园'
            },
            ProtectionZoneType.COMMERCIAL: {
                'icon': '🏢',
                'symbol': 'building',
                'color': '#ffd93d',
                'description': '商业区'
            },
            ProtectionZoneType.TRANSPORT_HUB: {
                'icon': '🚉',
                'symbol': 'train',
                'color': '#6bcf7f',
                'description': '交通枢纽'
            },
            ProtectionZoneType.SCHOOL: {
                'icon': '🏫',
                'symbol': 'school',
                'color': '#74b9ff',
                'description': '学校'
            },
            ProtectionZoneType.HOSPITAL: {
                'icon': '🏥',
                'symbol': 'hospital',
                'color': '#fd79a8',
                'description': '医院'
            },
            ProtectionZoneType.RESIDENTIAL: {
                'icon': '🏠',
                'symbol': 'home',
                'color': '#fdcb6e',
                'description': '住宅区'
            },
            ProtectionZoneType.ROAD_SIDE: {
                'icon': '🛣️',
                'symbol': 'road',
                'color': '#636e72',
                'description': '沿路区域'
            }
        }

        return icon_mapping.get(self.zone_type, {
            'icon': '📍',
            'symbol': 'marker',
            'color': '#ddd',
            'description': '未知类型'
        })
    
    def contains_point(self, lng: float, lat: float) -> bool:
        """检查点是否在保护区内"""
        distance = self._calculate_distance(lng, lat, self.center[0], self.center[1])
        return distance <= self.radius
    
    def get_distance_to_point(self, lng: float, lat: float) -> float:
        """计算点到保护区中心的距离"""
        return self._calculate_distance(lng, lat, self.center[0], self.center[1])
    
    def get_collision_cost(self, lng: float, lat: float) -> float:
        """
        计算指定点的碰撞代价
        🔧 修复：直接返回总碰撞代价，不需要面积计算
        碰撞代价 = 人数 × 人员代价 + 车辆数 × 车辆代价
        """
        distance = self.get_distance_to_point(lng, lat)
        if distance > self.radius:
            return 0.0

        # 🔧 修复：直接返回总碰撞代价
        # 无人机进入保护区就承担全部碰撞风险
        return self.total_crash_cost
    
    @staticmethod
    def _calculate_distance(lng1: float, lat1: float, lng2: float, lat2: float) -> float:
        """计算两点间距离（米）"""
        R = 6371000  # 地球半径（米）
        dLat = math.radians(lat2 - lat1)
        dLng = math.radians(lng2 - lng1)
        a = (math.sin(dLat/2) * math.sin(dLat/2) +
             math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) *
             math.sin(dLng/2) * math.sin(dLng/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        return R * c

class ProtectionZoneManager:
    """保护区管理器 - 使用智能单例模式避免重复初始化"""

    _instance = None
    _initialized = False
    _init_lock_file = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ProtectionZoneManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # 使用环境变量和文件锁确保真正的单次初始化
        if not ProtectionZoneManager._initialized:
            # 检查是否是Flask reloader子进程
            is_reloader = os.environ.get('WERKZEUG_RUN_MAIN') == 'true'

            # 创建临时锁文件路径
            if ProtectionZoneManager._init_lock_file is None:
                temp_dir = tempfile.gettempdir()
                ProtectionZoneManager._init_lock_file = os.path.join(
                    temp_dir, 'protection_zone_manager_init.lock'
                )

            # 检查是否已经初始化过
            should_init = True
            if is_reloader and os.path.exists(ProtectionZoneManager._init_lock_file):
                should_init = False
                print("🛡️ 保护区管理器：检测到Flask重启，跳过重复初始化")

            if should_init:
                self.zones: List[ProtectionZone] = []
                self._initialize_default_zones()
                ProtectionZoneManager._initialized = True

                # 创建锁文件
                try:
                    with open(ProtectionZoneManager._init_lock_file, 'w') as f:
                        f.write(str(os.getpid()))
                except:
                    pass  # 忽略文件创建失败

                print("🛡️ 保护区管理器：智能单例初始化完成")
            else:
                # 如果跳过初始化，需要设置默认值
                if not hasattr(self, 'zones'):
                    self.zones: List[ProtectionZone] = []
                ProtectionZoneManager._initialized = True
                print("🛡️ 保护区管理器：使用缓存实例")
    
    def _initialize_default_zones(self):
        """
        初始化默认保护区 - 🔧 重新设计：基于人数和车辆数

        设计原则：
        - 每个保护区明确规定人数和车辆数
        - 人员碰撞代价：8.0/人
        - 车辆碰撞代价：15.0/车
        - 总代价 = 人数 × 8.0 + 车辆数 × 15.0
        """
        # 🔧 全新设计的保护区，基于人车数量
        default_zones = [
            # 交通枢纽 - 人流和车流都很大
            ProtectionZone(
                id="tokyo_station",
                name="东京站",
                zone_type=ProtectionZoneType.TRANSPORT_HUB,
                center=(139.7673, 35.6812),
                radius=400,
                people_count=500,  # 500人
                vehicle_count=80,  # 80辆车
                description="东京最大的交通枢纽，人流密集"
            ),
            ProtectionZone(
                id="shinjuku_station",
                name="新宿站",
                zone_type=ProtectionZoneType.TRANSPORT_HUB,
                center=(139.7006, 35.6896),
                radius=350,
                people_count=600,  # 600人
                vehicle_count=70,  # 70辆车
                description="世界最繁忙的车站之一"
            ),

            # 商业区 - 人流密集，车辆较多
            ProtectionZone(
                id="shibuya_crossing",
                name="涩谷十字路口",
                zone_type=ProtectionZoneType.COMMERCIAL,
                center=(139.7016, 35.6598),
                radius=300,
                people_count=800,  # 800人
                vehicle_count=50,  # 50辆车
                description="世界著名的繁忙十字路口"
            ),
            ProtectionZone(
                id="ginza_district",
                name="银座商业区",
                zone_type=ProtectionZoneType.COMMERCIAL,
                center=(139.7671, 35.6735),
                radius=600,
                people_count=400,  # 400人
                vehicle_count=60,  # 60辆车
                description="高端购物和商业区"
            ),

            # 公园 - 人流中等，车辆很少
            ProtectionZone(
                id="ueno_park",
                name="上野公园",
                zone_type=ProtectionZoneType.PARK,
                center=(139.7744, 35.7151),
                radius=800,
                people_count=200,  # 200人
                vehicle_count=5,   # 5辆车（管理车辆）
                description="著名的樱花观赏地和文化区"
            ),
            ProtectionZone(
                id="imperial_palace",
                name="皇居东御苑",
                zone_type=ProtectionZoneType.PARK,
                center=(139.7538, 35.6852),
                radius=500,
                people_count=150,  # 150人
                vehicle_count=3,   # 3辆车
                description="皇室庭园，游客较多"
            ),

            # 学校 - 学生密集，车辆较少
            ProtectionZone(
                id="tokyo_university",
                name="东京大学",
                zone_type=ProtectionZoneType.SCHOOL,
                center=(139.7617, 35.7136),
                radius=600,
                people_count=800,  # 800人（学生+教职工）
                vehicle_count=20,  # 20辆车
                description="日本顶尖大学，学生密集"
            ),
            ProtectionZone(
                id="waseda_university",
                name="早稻田大学",
                zone_type=ProtectionZoneType.SCHOOL,
                center=(139.7197, 35.7089),
                radius=400,
                people_count=600,  # 600人
                vehicle_count=15,  # 15辆车
                description="著名私立大学"
            ),

            # 医院 - 人流中等，救护车等车辆
            ProtectionZone(
                id="tokyo_hospital",
                name="东京大学医学部附属医院",
                zone_type=ProtectionZoneType.HOSPITAL,
                center=(139.7625, 35.7089),
                radius=300,
                people_count=300,  # 300人（患者+医护）
                vehicle_count=25,  # 25辆车（救护车+私家车）
                description="大型综合医院"
            ),

            # 住宅区 - 人流较少，私家车较多
            ProtectionZone(
                id="roppongi_residential",
                name="六本木住宅区",
                zone_type=ProtectionZoneType.RESIDENTIAL,
                center=(139.7319, 35.6627),
                radius=400,
                people_count=200,  # 200人
                vehicle_count=40,  # 40辆车
                description="高档住宅区"
            ),

            # 道路交通区 - 车辆为主
            ProtectionZone(
                id="akasaka_traffic",
                name="赤坂交通区",
                zone_type=ProtectionZoneType.ROAD_TRAFFIC,
                center=(139.7370, 35.6735),
                radius=250,
                people_count=100,  # 100人（行人）
                vehicle_count=120, # 120辆车
                description="车流密集的商务区道路"
            ),
            ProtectionZone(
                id="yamanote_line_side",
                name="山手线沿线",
                zone_type=ProtectionZoneType.ROAD_TRAFFIC,
                center=(139.7300, 35.6800),
                radius=200,
                people_count=80,   # 80人
                vehicle_count=100, # 100辆车
                description="环状铁路沿线区域"
            ),

            # 人流密集区 - 人员为主
            ProtectionZone(
                id="ameya_yokocho",
                name="阿美横丁市场",
                zone_type=ProtectionZoneType.PEDESTRIAN_AREA,
                center=(139.7744, 35.7120),
                radius=150,
                people_count=400,  # 400人
                vehicle_count=10,  # 10辆车（送货车）
                description="传统市场，人流密集"
            ),
            ProtectionZone(
                id="shibuya_scramble",
                name="涩谷全向十字路口",
                zone_type=ProtectionZoneType.PEDESTRIAN_AREA,
                center=(139.7016, 35.6595),
                radius=100,
                people_count=1000, # 1000人（高峰期）
                vehicle_count=5,   # 5辆车
                description="世界最繁忙的行人过街点"
            ),

            # 路口 - 人车混合
            ProtectionZone(
                id="major_intersection",
                name="主要路口",
                zone_type=ProtectionZoneType.INTERSECTION,
                center=(139.7450, 35.6850),
                radius=100,
                people_count=200,  # 200人
                vehicle_count=80,  # 80辆车
                description="主要交通路口"
            ),

            # 公交和地铁站 - 人流为主，少量车辆
            ProtectionZone(
                id="shinjuku_bus_terminal",
                name="新宿巴士总站",
                zone_type=ProtectionZoneType.BUS_STOP,
                center=(139.7020, 35.6910),
                radius=180,
                people_count=300,  # 300人
                vehicle_count=20,  # 20辆车（巴士）
                description="大型巴士换乘中心"
            ),
            ProtectionZone(
                id="ginza_subway",
                name="银座地铁站",
                zone_type=ProtectionZoneType.SUBWAY_STATION,
                center=(139.7640, 35.6717),
                radius=200,
                people_count=400,  # 400人
                vehicle_count=8,   # 8辆车
                description="多线换乘地铁站"
            ),
            ProtectionZone(
                id="metro_exit",
                name="地铁出口",
                zone_type=ProtectionZoneType.SUBWAY_STATION,
                center=(139.7680, 35.6820),
                radius=120,
                people_count=200,  # 200人
                vehicle_count=5,   # 5辆车
                description="地铁出入口区域"
            )
        ]
        
        self.zones.extend(default_zones)
        print(f"🛡️ 初始化了 {len(default_zones)} 个默认保护区")

        # 打印保护区统计信息
        zone_types = {}
        total_average_crash_cost = 0.0
        for zone in default_zones:
            zone_type = zone.zone_type.value
            zone_types[zone_type] = zone_types.get(zone_type, 0) + 1
            total_average_crash_cost += zone.average_crash_cost

        print(f"🛡️ 保护区类型统计: {zone_types}")
        print(f"🛡️ 总平均碰撞代价: {total_average_crash_cost:.4f}/m²")
        print(f"🛡️ 平均碰撞代价: {total_average_crash_cost/len(default_zones):.4f}/m²")

        # 显示预期的30米范围内碰撞代价
        detection_area = math.pi * 30 * 30  # 30米圆的面积
        print(f"🛡️ 30米检测范围面积: {detection_area:.0f}m²")
        print(f"🛡️ 预期碰撞代价范围: {(min(zone.average_crash_cost for zone in default_zones) * detection_area):.1f} - {(max(zone.average_crash_cost for zone in default_zones) * detection_area):.1f}")
        print(f"🛡️ 实际物体碰撞代价: 8.0-15.0 (单个), 多个物体可达几十到上百")
    
    def add_zone(self, zone: ProtectionZone):
        """添加保护区"""
        self.zones.append(zone)
        print(f"🛡️ 添加保护区: {zone.name} ({zone.zone_type.value})")
    
    def get_zones_for_path(self, path_points: List[Tuple[float, float]],
                          buffer_distance: float = 500) -> List[ProtectionZone]:
        """
        获取路径相关的保护区
        使用500米缓冲区确保能检测到路径经过的保护区（500米缓冲区 + 保护区半径）
        """
        relevant_zones = []

        for zone in self.zones:
            zone_is_relevant = False
            for lng, lat in path_points:
                distance = zone.get_distance_to_point(lng, lat)
                # 只有当路径点在保护区半径 + 小缓冲区范围内时才认为相关
                if distance <= zone.radius + buffer_distance:
                    zone_is_relevant = True
                    break

            if zone_is_relevant:
                relevant_zones.append(zone)

        print(f"🛡️ 路径相关保护区: {len(relevant_zones)} 个 (缓冲区: {buffer_distance}m)")
        for zone in relevant_zones:
            print(f"   - {zone.name} (半径: {zone.radius}m)")
        return relevant_zones
    
    def calculate_path_collision_cost(self, path_points: List[Tuple[float, float]]) -> float:
        """计算路径的预估碰撞代价"""
        total_cost = 0.0
        relevant_zones = self.get_zones_for_path(path_points)

        print(f"🛡️ 开始计算路径碰撞代价，路径点数: {len(path_points)}, 相关保护区: {len(relevant_zones)}")

        for i, (lng, lat) in enumerate(path_points):
            point_cost = 0.0
            for zone in relevant_zones:
                zone_cost = zone.get_collision_cost(lng, lat)
                if zone_cost > 0:
                    point_cost += zone_cost
                    if i < 3:  # 只显示前3个点的详细信息
                        print(f"🛡️ 点{i} 在保护区 {zone.name} 的碰撞代价: {zone_cost:.4f}")

            total_cost += point_cost
            if i < 3:  # 只显示前3个点的详细信息
                print(f"🛡️ 点{i} 总碰撞代价: {point_cost:.4f}")

        # 🔧 重要修复：移除平均化处理，使用累积总和
        # 这样碰撞代价会随着路径长度和经过的保护区数量增加

        print(f"🛡️ 路径总碰撞代价: {total_cost:.4f} (未平均化)")
        return total_cost
    
    def get_zone_statistics(self) -> Dict[str, Any]:
        """获取保护区统计信息"""
        stats = {
            'total_zones': len(self.zones),
            'by_type': {},
            'high_risk_zones': [],
            'coverage_area': 0.0
        }
        
        for zone in self.zones:
            zone_type = zone.zone_type.value
            if zone_type not in stats['by_type']:
                stats['by_type'][zone_type] = 0
            stats['by_type'][zone_type] += 1
            
            # 🔧 新设计：基于总碰撞代价判断高风险区域
            if zone.total_crash_cost >= 5000:  # 总代价超过5000的为高风险区域
                stats['high_risk_zones'].append({
                    'name': zone.name,
                    'people_count': zone.people_count,
                    'vehicle_count': zone.vehicle_count,
                    'total_crash_cost': zone.total_crash_cost,
                    'average_crash_cost': zone.average_crash_cost
                })
            
            # 计算覆盖面积（平方公里）
            area = math.pi * (zone.radius / 1000) ** 2
            stats['coverage_area'] += area
        
        return stats
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式 - 🔧 新设计：包含人车数量信息"""
        return {
            'zones': [
                {
                    'id': zone.id,
                    'name': zone.name,
                    'type': zone.zone_type.value,
                    'center': zone.center,
                    'radius': zone.radius,
                    'people_count': zone.people_count,
                    'vehicle_count': zone.vehicle_count,
                    'total_crash_cost': zone.total_crash_cost,
                    'average_crash_cost': zone.average_crash_cost,
                    'collision_cost_factor': zone.collision_cost_factor,
                    'description': zone.description
                }
                for zone in self.zones
            ],
            'statistics': self.get_zone_statistics()
        }
