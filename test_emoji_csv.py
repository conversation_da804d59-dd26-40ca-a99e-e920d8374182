#!/usr/bin/env python3
"""
测试emoji字符在CSV中的保存
"""

import csv
import os
from datetime import datetime

def test_emoji_csv():
    # 创建测试数据
    test_data = [
        {'path_id': '🎯基准A*', 'direction': 'OPTIMAL', 'value': 123.45},
        {'path_id': '1', 'direction': '9', 'value': 234.56},
        {'path_id': '2', 'direction': '8', 'value': 345.67}
    ]
    
    # 创建CSV文件
    timestamp = datetime.now().strftime('%Y-%m-%dT%H-%M-%S')
    filename = f'emoji_test_{timestamp}.csv'
    filepath = os.path.join('csv', filename)
    
    # 确保目录存在
    os.makedirs('csv', exist_ok=True)
    
    # 写入CSV文件，使用UTF-8编码
    with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = ['path_id', 'direction', 'value']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        # 写入表头
        writer.writerow({
            'path_id': '路径ID',
            'direction': '方向',
            'value': '数值'
        })
        
        # 写入数据
        writer.writerows(test_data)
    
    print(f"✅ 测试文件创建成功: {filepath}")
    
    # 读取并验证
    with open(filepath, 'r', encoding='utf-8-sig') as f:
        content = f.read()
        print(f"\n📋 文件内容:")
        print(content)
        
        if '🎯' in content:
            print("✅ emoji字符保存成功")
        else:
            print("❌ emoji字符丢失")
    
    return filepath

if __name__ == "__main__":
    test_emoji_csv()
