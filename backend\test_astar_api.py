#!/usr/bin/env python3
"""
测试A*算法API
验证修复后的A*算法是否正常工作
"""

import requests
import json
import time

def test_astar_api():
    """测试A*算法API"""
    
    print("🧪 ===== A*算法API测试 =====")
    
    # API端点
    url = "http://localhost:5000/api/calculate_path"
    
    # 测试数据
    test_data = {
        "algorithm": "AStar",
        "startPoint": {
            "lng": 139.7673,
            "lat": 35.6812,
            "alt": 100.0,
            "x": 0,
            "y": 0,
            "z": 100
        },
        "endPoint": {
            "lng": 139.7734,
            "lat": 35.7153,
            "alt": 100.0,
            "x": 1000,
            "y": 1000,
            "z": 100
        },
        "flightHeight": 100.0,
        "safetyDistance": 30.0,
        "buildings": [],  # 简化测试，不添加建筑物
        "parameters": {
            "gridSize": 10,
            "heuristicWeight": 1.0,
            "maxIterations": 10000,
            "allowDiagonal": True,
            "smoothPath": True
        }
    }
    
    print(f"📍 测试场景:")
    print(f"   起点: 东京站 ({test_data['startPoint']['lng']:.6f}, {test_data['startPoint']['lat']:.6f})")
    print(f"   终点: 上野公园 ({test_data['endPoint']['lng']:.6f}, {test_data['endPoint']['lat']:.6f})")
    print(f"   飞行高度: {test_data['flightHeight']}米")
    print(f"   安全距离: {test_data['safetyDistance']}米")
    print(f"   网格大小: {test_data['parameters']['gridSize']}米")
    print()
    
    try:
        print("🚀 发送API请求...")
        start_time = time.time()
        
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        request_time = time.time() - start_time
        print(f"⏱️ 请求耗时: {request_time:.3f}秒")
        
        print(f"📡 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API请求成功!")
            
            # 检查响应结构
            print(f"📊 响应分析:")
            print(f"   success: {result.get('success', 'N/A')}")
            print(f"   error: {result.get('error', 'N/A')}")
            
            if result.get('success'):
                path = result.get('path', [])
                print(f"   路径点数量: {len(path)}")
                
                if 'path_length' in result:
                    print(f"   路径长度: {result['path_length']:.2f}米")
                
                if 'execution_time' in result:
                    print(f"   执行时间: {result['execution_time']:.3f}秒")
                
                if 'collision_cost' in result:
                    print(f"   碰撞代价: {result['collision_cost']}")
                
                # 显示路径样本
                if path:
                    print(f"📍 路径样本点:")
                    sample_count = min(3, len(path))
                    for i in range(sample_count):
                        point = path[i]
                        print(f"   点{i+1}: ({point['lng']:.6f}, {point['lat']:.6f}, {point['alt']:.1f}m)")
                    
                    if len(path) > 6:
                        print(f"   ... (中间省略)")
                        for i in range(max(sample_count, len(path)-2), len(path)):
                            point = path[i]
                            print(f"   点{i+1}: ({point['lng']:.6f}, {point['lat']:.6f}, {point['alt']:.1f}m)")
                
                # 常识性检查
                print(f"🧠 常识性检查:")
                
                # 检查路径点数量
                if 5 <= len(path) <= 50:
                    print(f"   ✅ 路径点数量合理: {len(path)}个")
                else:
                    print(f"   ❌ 路径点数量异常: {len(path)}个")
                
                # 检查起终点
                if path:
                    start_path = path[0]
                    end_path = path[-1]
                    
                    start_diff = abs(start_path['lng'] - test_data['startPoint']['lng']) + abs(start_path['lat'] - test_data['startPoint']['lat'])
                    end_diff = abs(end_path['lng'] - test_data['endPoint']['lng']) + abs(end_path['lat'] - test_data['endPoint']['lat'])
                    
                    if start_diff < 0.001 and end_diff < 0.001:
                        print(f"   ✅ 起终点正确")
                    else:
                        print(f"   ❌ 起终点偏差过大: 起点偏差{start_diff:.6f}, 终点偏差{end_diff:.6f}")
                
                print(f"   ✅ A*算法API测试成功!")
                
            else:
                print(f"❌ 算法执行失败: {result.get('error', '未知错误')}")
                return False
                
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接失败，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    
    print(f"\n🧪 ===== 测试完成 =====")
    return True

def test_health_api():
    """测试健康检查API"""
    print("🔍 测试健康检查API...")
    
    try:
        response = requests.get("http://localhost:5000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查API正常")
            return True
        else:
            print(f"❌ 健康检查API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查API错误: {e}")
        return False

def main():
    """主函数"""
    print("🔍 A*算法API完整测试")
    print("=" * 50)
    
    # 先测试健康检查
    if not test_health_api():
        print("❌ 后端服务不可用，请先启动后端服务")
        return
    
    print()
    
    # 测试A*算法API
    success = test_astar_api()
    
    if success:
        print("\n🎉 所有测试通过！A*算法API工作正常")
    else:
        print("\n❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
