2025-07-27 07:38:23 |     INFO | drone_backend | 步骤001 | 算法开始 | 开始执行算法: TestAlgorithm
2025-07-27 07:38:23 |     INFO | drone_backend | 步骤002 | 路径生成 | 生成路径 81 条 | 耗时: 150.50ms
2025-07-27 07:38:23 |     INFO | drone_backend | 步骤003 | 分簇处理 | 完成分簇: 13 个簇，81 条路径 | 耗时: 75.20ms
2025-07-27 07:38:23 |     INFO | drone_backend | 步骤004 | 代价计算 | 路径最终代价计算完成: 0.234567 | 耗时: 25.80ms
2025-07-27 07:38:23 |     INFO | drone_backend | 步骤005 | 路径切换 | 路径切换: Path_1_1 -> Path_2_3 | 耗时: 45.30ms
2025-07-27 07:38:23 |     INFO | drone_backend | 步骤006 | API请求 | POST /api/pathplanning/calculate
2025-07-27 07:38:24 |     INFO | drone_backend | 步骤007 | API响应 | /api/pathplanning/calculate 响应: 200 | 耗时: 320.50ms
2025-07-27 07:38:24 |    ERROR | drone_backend | 步骤008 | 错误处理 | 发生错误: ValueError: 这是一个测试错误
2025-07-27 07:38:24 |     INFO | drone_backend | 步骤009 | 算法结束 | 算法执行成功: TestAlgorithm
