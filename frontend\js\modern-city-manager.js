/**
 * 现代化3D城市管理器
 * 基于Mapbox + OpenStreetMap + Three.js的专业级3D城市可视化系统
 */

class ModernCityManager {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = null;
        
        // 配置选项
        this.options = {
            mapboxToken: options.mapboxToken || 'your-mapbox-token',
            initialLocation: options.initialLocation || [139.7670, 35.6814], // 东京
            initialZoom: options.initialZoom || 15,
            style: options.style || 'mapbox://styles/mapbox/dark-v10',
            enable3D: options.enable3D !== false,
            enableBuildings: options.enableBuildings !== false,
            enableOSMData: options.enableOSMData !== false,
            ...options
        };
        
        // 核心组件
        this.map = null;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        
        // 数据管理
        this.buildingData = new Map();
        this.osmCache = new Map();
        this.loadedTiles = new Set();

        // 无人机路径规划
        this.drone = null;
        this.startPoint = null;
        this.endPoint = null;
        this.currentPath = [];
        this.pathLine = null;
        this.pathMarkers = [];

        // 飞行状态
        this.isFlying = false;
        this.flightProgress = 0;
        this.flightSpeed = 0.5; // 降低到0.5米/秒，便于观察

        // 保护区状态
        this.protectionZonesInitialized = false;

        // 轨迹绘制
        this.flightTrail = [];
        this.maxTrailLength = 50; // 最大轨迹点数

        // 算法设置 - 默认使用改进分簇算法
        this.algorithm = 'ImprovedClusterBased';
        this.flightHeight = 70;  // 修改默认飞行高度为70米（第5个高度层级，适合城市环境）
        this.safetyDistance = 20;

        // 交互状态
        this.isSettingStart = false;
        this.isSettingEnd = false;

        // 建筑交互相关属性
        this.currentPopup = null;
        this.hoveredBuildingId = null;

        // 算法集成器
        this.algorithmIntegrator = null;

        // 新增：算法对比管理器
        this.comparisonManager = null;

        // 新增：物体检测可视化器
        this.objectDetectionVisualizer = null;

        // 状态管理
        this.isInitialized = false;
        this.isLoading = false;
        this.currentBounds = null;

        // 事件系统
        this.eventListeners = new Map();

        // 路径规划器（将在init方法中初始化）
        this.pathPlanner = null;

        // 交通密度管理器（将在init方法中初始化）
        this.trafficDensityManager = null;

        // 构造函数完成（log将在init方法中调用）
    }
    
    /**
     * 初始化系统
     */
    async init() {
        try {
            this.log('开始初始化现代化3D城市系统...', 'info');
            
            // 检查依赖
            await this.checkDependencies();

            // 初始化路径规划器
            try {
                this.pathPlanner = new PathPlanner();
                this.log('✅ 路径规划器初始化成功', 'success');
            } catch (error) {
                this.log(`❌ 路径规划器初始化失败: ${error.message}`, 'error');
                throw error;
            }

            // 初始化容器
            this.initContainer();
            
            // 初始化Mapbox地图（3D建筑将在地图加载完成后自动初始化）
            await this.initMapbox();
            
            // 初始化OSM数据加载
            if (this.options.enableOSMData) {
                this.initOSMDataLoader();
            }

            // 创建无人机模型
            this.createDrone();

            // 初始化Python算法客户端
            await this.initPythonAlgorithmClient();

            // 初始化算法对比管理器
            this.initComparisonManager();

            // 初始化物体检测可视化器
            this.initObjectDetectionVisualizer();

            // 设置事件监听
            this.setupEventListeners();

            // 启动3D建筑监控
            this.start3DBuildingMonitor();

            // 检测并优化东京区域设置
            this.optimizeForTokyo();

            this.isInitialized = true;
            this.log('🏙️ 现代化3D城市系统初始化完成！', 'success');

        // 等待地图完全加载后再初始化保护区
        this.map.on('idle', () => {
            // 只执行一次
            if (!this.protectionZonesInitialized) {
                this.protectionZonesInitialized = true;
                this.log('🔍 地图空闲状态，开始初始化保护区...', 'info');
                this.initializeProtectionZones();

                // 延迟加载数据
                setTimeout(() => {
                    this.loadProtectionZones();
                }, 1000);
            }
        });
            
            // 触发初始化完成事件
            this.emit('initialized');
            
        } catch (error) {
            this.log(`初始化失败: ${error.message}`, 'error');
            throw error;
        }
    }
    
    /**
     * 检查依赖项
     */
    async checkDependencies() {
        const dependencies = [
            { name: 'Mapbox GL JS', check: () => typeof mapboxgl !== 'undefined' },
            { name: 'Three.js', check: () => typeof THREE !== 'undefined' }
        ];
        
        for (const dep of dependencies) {
            if (!dep.check()) {
                throw new Error(`缺少依赖: ${dep.name}`);
            }
        }
        
        this.log('依赖检查通过', 'info');
    }
    
    /**
     * 初始化容器
     */
    initContainer() {
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            throw new Error(`找不到容器: ${this.containerId}`);
        }
        
        // 设置容器样式
        this.container.style.position = 'relative';
        this.container.style.width = '100%';
        this.container.style.height = '100%';
        
        this.log('容器初始化完成', 'info');
    }
    
    /**
     * 初始化Mapbox地图
     */
    async initMapbox() {
        return new Promise((resolve, reject) => {
            try {
                // 设置访问令牌
                mapboxgl.accessToken = this.options.mapboxToken;
                
                // 创建地图
                this.map = new mapboxgl.Map({
                    container: this.containerId,
                    style: this.options.style,
                    center: this.options.initialLocation,
                    zoom: this.options.initialZoom,
                    pitch: 70, // 更大的3D倾斜角度，确保能看到高度差
                    bearing: -17.6,
                    antialias: true
                });
                
                // 地图加载完成
                this.map.on('load', () => {
                    this.log('Mapbox地图加载完成', 'success');

                    // 地图加载完成后立即初始化3D建筑
                    if (this.options.enableBuildings) {
                        setTimeout(() => {
                            this.init3DBuildings();
                        }, 100);
                    }

                    resolve();
                });
                
                // 地图移动事件
                this.map.on('moveend', () => {
                    this.onMapMove();
                });
                
                // 错误处理
                this.map.on('error', (e) => {
                    this.log(`Mapbox错误: ${e.error.message}`, 'error');
                    reject(e.error);
                });
                
            } catch (error) {
                reject(error);
            }
        });
    }
    
    /**
     * 初始化3D建筑
     */
    init3DBuildings() {
        try {
            this.log('🏗️ 开始初始化3D建筑图层...', 'info');

            // 检查地图是否已加载
            if (!this.map.isStyleLoaded()) {
                this.log('⏳ 等待地图样式加载完成...', 'info');
                this.map.on('styledata', () => {
                    if (this.map.isStyleLoaded()) {
                        this.init3DBuildings();
                    }
                });
                return;
            }

            // 检查是否已存在3D建筑图层
            if (this.map.getLayer('3d-buildings')) {
                this.map.removeLayer('3d-buildings');
                this.log('🔄 移除现有3D建筑图层', 'info');
            }

            // 添加3D建筑图层
            this.map.addLayer({
                'id': '3d-buildings',
                'source': 'composite',
                'source-layer': 'building',
                'filter': ['==', 'extrude', 'true'],
                'type': 'fill-extrusion',
                'minzoom': 12, // 进一步降低最小缩放级别
                'paint': {
                    'fill-extrusion-color': [
                        'case',
                        ['boolean', ['feature-state', 'hover'], false],
                        '#ff6b6b', // 悬停颜色
                        [
                            'interpolate',
                            ['linear'],
                            [
                                'case',
                                ['has', 'height'], ['get', 'height'],
                                ['has', 'render_height'], ['get', 'render_height'],
                                20 // 默认高度用于颜色计算
                            ],
                            0, '#74b9ff',    // 低层建筑 - 浅蓝色
                            30, '#0984e3',   // 中低层 - 蓝色
                            60, '#6c5ce7',   // 中高层 - 紫色
                            100, '#fd79a8',  // 高层 - 粉色
                            150, '#ff6b6b'   // 超高层 - 红色
                        ]
                    ],
                    'fill-extrusion-height': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        13, 0,
                        13.05, [
                            'case',
                            ['has', 'height'], ['get', 'height'],
                            ['has', 'render_height'], ['get', 'render_height'],
                            // 根据建筑类型设置默认高度
                            ['case',
                                ['==', ['get', 'type'], 'house'], 8,
                                ['==', ['get', 'type'], 'apartments'], 25,
                                ['==', ['get', 'type'], 'commercial'], 15,
                                ['==', ['get', 'type'], 'office'], 40,
                                ['==', ['get', 'type'], 'industrial'], 12,
                                // 根据建筑标签设置高度
                                ['case',
                                    ['==', ['get', 'building'], 'house'], 8,
                                    ['==', ['get', 'building'], 'apartments'], 25,
                                    ['==', ['get', 'building'], 'commercial'], 15,
                                    ['==', ['get', 'building'], 'office'], 40,
                                    ['==', ['get', 'building'], 'industrial'], 12,
                                    // 最终默认高度
                                    20
                                ]
                            ]
                        ]
                    ],
                    'fill-extrusion-base': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        13, 0,
                        13.05, [
                            'case',
                            ['has', 'min_height'], ['get', 'min_height'],
                            0
                        ]
                    ],
                    'fill-extrusion-opacity': 0.85
                }
            });

            this.log('✅ 3D建筑图层添加成功', 'success');

            // 建筑交互
            this.setupBuildingInteraction();

            // 设置最佳视角显示3D建筑
            setTimeout(() => {
                this.map.easeTo({
                    pitch: 60,
                    bearing: -17.6,
                    zoom: 16, // 适中的缩放级别
                    duration: 1500
                });

                this.log('🎯 已设置3D建筑最佳视角', 'success');
            }, 500);

            // 延迟调试信息
            setTimeout(() => {
                this.debugBuildingData();
            }, 1000);

        } catch (error) {
            this.log(`❌ 3D建筑图层初始化失败: ${error.message}`, 'error');
            console.error('3D建筑初始化错误:', error);

            // 尝试备用方法
            setTimeout(() => {
                this.tryAlternative3DBuildings();
            }, 1000);
        }
    }

    /**
     * 调试建筑数据
     */
    debugBuildingData() {
        // 等待地图加载完成后检查建筑数据
        setTimeout(() => {
            try {
                const features = this.map.queryRenderedFeatures({
                    layers: ['3d-buildings']
                });

                console.log('=== 3D建筑调试信息 ===');
                console.log(`找到 ${features.length} 个建筑要素`);

                if (features.length > 0) {
                    const sampleBuilding = features[0];
                    console.log('示例建筑属性:', sampleBuilding.properties);
                    console.log('建筑高度:', sampleBuilding.properties.height);
                    console.log('建筑类型:', sampleBuilding.properties.type);
                    console.log('是否可挤出:', sampleBuilding.properties.extrude);

                    // 统计建筑高度分布
                    const heights = features
                        .map(f => f.properties.height)
                        .filter(h => h && h > 0)
                        .sort((a, b) => a - b);

                    if (heights.length > 0) {
                        console.log('建筑高度统计:');
                        console.log('- 最低:', heights[0], 'm');
                        console.log('- 最高:', heights[heights.length - 1], 'm');
                        console.log('- 平均:', (heights.reduce((a, b) => a + b, 0) / heights.length).toFixed(1), 'm');
                        console.log('- 有高度数据的建筑:', heights.length, '个');
                    } else {
                        console.log('⚠️ 警告：没有找到有效的建筑高度数据');
                        this.tryAlternative3DMethod();
                    }
                } else {
                    console.log('⚠️ 警告：没有找到3D建筑要素');
                    this.tryAlternative3DMethod();
                }

            } catch (error) {
                console.error('调试建筑数据时出错:', error);
                this.tryAlternative3DMethod();
            }
        }, 2000);
    }

    /**
     * 尝试替代的3D建筑方法
     */
    tryAlternative3DMethod() {
        console.log('尝试替代的3D建筑显示方法...');

        try {
            // 移除现有图层
            if (this.map.getLayer('3d-buildings')) {
                this.map.removeLayer('3d-buildings');
            }

            // 使用更简单但更可靠的3D建筑配置
            this.map.addLayer({
                'id': '3d-buildings',
                'source': 'composite',
                'source-layer': 'building',
                'filter': ['==', 'extrude', 'true'],
                'type': 'fill-extrusion',
                'minzoom': 15,
                'paint': {
                    'fill-extrusion-color': '#aaa',
                    // 简化的高度配置
                    'fill-extrusion-height': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        15, 0,
                        15.05, [
                            'case',
                            ['has', 'height'], ['get', 'height'],
                            ['has', 'render_height'], ['get', 'render_height'],
                            // 根据建筑面积估算高度
                            ['*', ['sqrt', ['get', 'area']], 0.1]
                        ]
                    ],
                    'fill-extrusion-base': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        15, 0,
                        15.05, [
                            'case',
                            ['has', 'min_height'], ['get', 'min_height'],
                            0
                        ]
                    ],
                    'fill-extrusion-opacity': 0.6
                }
            });

            this.log('🔄 已尝试替代3D建筑方法', 'info');

        } catch (error) {
            console.error('替代3D建筑方法失败:', error);
            this.log('❌ 替代3D建筑方法失败', 'error');
        }
    }

    /**
     * 强制显示3D建筑
     */
    force3DBuildings() {
        this.log('🔧 强制显示3D建筑...', 'info');

        try {
            // 移除现有图层
            if (this.map.getLayer('3d-buildings')) {
                this.map.removeLayer('3d-buildings');
                this.log('🗑️ 移除现有3D建筑图层', 'info');
            }

            // 确保地图缩放到合适级别
            const currentZoom = this.map.getZoom();
            if (currentZoom < 14) {
                this.log('📏 调整地图缩放级别到17', 'info');
                this.map.setZoom(17);
            }

            // 使用最简单但可靠的3D建筑配置
            this.map.addLayer({
                'id': '3d-buildings',
                'source': 'composite',
                'source-layer': 'building',
                'filter': ['==', 'extrude', 'true'],
                'type': 'fill-extrusion',
                'minzoom': 10, // 更低的最小缩放级别
                'paint': {
                    'fill-extrusion-color': [
                        'interpolate',
                        ['linear'],
                        ['get', 'height'],
                        0, '#74b9ff',
                        50, '#0984e3',
                        100, '#6c5ce7',
                        200, '#fd79a8'
                    ],
                    // 强制显示高度 - 使用多种数据源
                    'fill-extrusion-height': [
                        'case',
                        // 优先使用height属性
                        ['>', ['to-number', ['get', 'height'], 0], 0],
                        ['get', 'height'],
                        // 其次使用render_height
                        ['>', ['to-number', ['get', 'render_height'], 0], 0],
                        ['get', 'render_height'],
                        // 使用levels估算高度
                        ['>', ['to-number', ['get', 'levels'], 0], 0],
                        ['*', ['get', 'levels'], 3],
                        // 使用building:levels估算高度
                        ['>', ['to-number', ['get', 'building:levels'], 0], 0],
                        ['*', ['get', 'building:levels'], 3],
                        // 根据建筑类型设置默认高度
                        ['case',
                            ['==', ['get', 'type'], 'house'], 8,
                            ['==', ['get', 'type'], 'apartments'], 25,
                            ['==', ['get', 'type'], 'commercial'], 15,
                            ['==', ['get', 'type'], 'office'], 40,
                            ['==', ['get', 'type'], 'industrial'], 12,
                            ['==', ['get', 'building'], 'house'], 8,
                            ['==', ['get', 'building'], 'apartments'], 25,
                            ['==', ['get', 'building'], 'commercial'], 15,
                            ['==', ['get', 'building'], 'office'], 40,
                            ['==', ['get', 'building'], 'industrial'], 12,
                            // 最后的默认高度
                            30
                        ]
                    ],
                    'fill-extrusion-base': 0,
                    'fill-extrusion-opacity': 0.8
                }
            });

            // 强制设置最佳视角
            this.map.easeTo({
                pitch: 60,
                bearing: -17.6,
                zoom: 17,
                duration: 1000
            });

            this.log('🔧 强制3D建筑显示已应用', 'success');

            // 设置建筑交互
            setTimeout(() => {
                this.setupBuildingInteraction();
                this.debugBuildingData();
            }, 1000);

            // 验证图层是否成功添加
            setTimeout(() => {
                if (this.map.getLayer('3d-buildings')) {
                    const features = this.map.queryRenderedFeatures({
                        layers: ['3d-buildings']
                    });
                    this.log(`✅ 3D建筑图层验证成功，找到 ${features.length} 个建筑`, 'success');
                } else {
                    this.log('❌ 3D建筑图层验证失败', 'error');
                }
            }, 2000);

        } catch (error) {
            console.error('强制显示3D建筑失败:', error);
            this.log(`❌ 强制显示3D建筑失败: ${error.message}`, 'error');
        }
    }
    
    /**
     * 设置建筑交互
     */
    setupBuildingInteraction() {
        let hoveredBuildingId = null;
        let hoverTimeout = null;

        // 清理之前的事件监听器（防止重复绑定）
        this.map.off('mousemove', '3d-buildings');
        this.map.off('mouseleave', '3d-buildings');
        this.map.off('click', '3d-buildings');
        this.map.off('mouseenter', '3d-buildings');

        // 鼠标悬停
        this.map.on('mousemove', '3d-buildings', (e) => {
            // 清理之前的延时
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            if (e.features.length > 0) {
                const currentBuildingId = e.features[0].id;

                // 如果是同一个建筑，不需要重新处理
                if (hoveredBuildingId === currentBuildingId) {
                    return;
                }

                // 清理之前的悬停状态
                if (hoveredBuildingId !== null) {
                    this.map.setFeatureState(
                        { source: 'composite', sourceLayer: 'building', id: hoveredBuildingId },
                        { hover: false }
                    );
                }

                // 设置新的悬停状态
                hoveredBuildingId = currentBuildingId;
                this.map.setFeatureState(
                    { source: 'composite', sourceLayer: 'building', id: hoveredBuildingId },
                    { hover: true }
                );

                // 显示建筑信息
                this.showBuildingInfo(e.features[0], e.point);
            }
        });

        // 鼠标离开建筑图层
        this.map.on('mouseleave', '3d-buildings', () => {
            this.clearBuildingHover();
        });

        // 地图移动时也清理悬停状态
        this.map.on('move', () => {
            // 延迟清理，避免地图移动时频繁清理
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
            }
            hoverTimeout = setTimeout(() => {
                this.clearBuildingHover();
            }, 100);
        });

        // 点击事件
        this.map.on('click', '3d-buildings', (e) => {
            this.onBuildingClick(e.features[0], e.point);
        });

        // 改变鼠标样式
        this.map.on('mouseenter', '3d-buildings', () => {
            this.map.getCanvas().style.cursor = 'pointer';
        });

        this.map.on('mouseleave', '3d-buildings', () => {
            this.map.getCanvas().style.cursor = '';
        });

        // 存储当前悬停状态，供其他方法使用
        this.hoveredBuildingId = null;
    }

    /**
     * 清理建筑悬停状态
     */
    clearBuildingHover() {
        if (this.hoveredBuildingId !== null) {
            this.map.setFeatureState(
                { source: 'composite', sourceLayer: 'building', id: this.hoveredBuildingId },
                { hover: false }
            );
        }
        this.hoveredBuildingId = null;
        this.hideBuildingInfo();
    }
    
    /**
     * 初始化OSM数据加载器
     */
    initOSMDataLoader() {
        this.osmLoader = new OSMDataLoader({
            onDataLoaded: (data, bounds) => {
                this.onOSMDataLoaded(data, bounds);
            },
            onError: (error) => {
                this.log(`OSM数据加载失败: ${error.message}`, 'error');
            }
        });

        this.log('OSM数据加载器初始化完成', 'info');
    }

    /**
     * 初始化Python算法客户端
     */
    async initPythonAlgorithmClient() {
        try {
            // 检查Python算法客户端是否可用
            if (typeof window.pythonAlgorithmClient === 'undefined' || !window.pythonAlgorithmClient) {
                this.log('⚠️ Python算法客户端未加载', 'warning');
                return;
            }

            // 初始化客户端
            const success = await window.pythonAlgorithmClient.initialize();

            if (success) {
                this.pythonClient = window.pythonAlgorithmClient;
                this.log('🐍 Python算法客户端初始化完成', 'success');
            } else {
                this.log('❌ Python后端服务不可用，将使用简化模式', 'warning');
            }

        } catch (error) {
            this.log(`❌ Python算法客户端初始化失败: ${error.message}`, 'error');
            console.error('Python算法客户端初始化错误:', error);
        }
    }

    /**
     * 初始化算法对比管理器
     */
    initComparisonManager() {
        try {
            if (typeof AlgorithmComparisonManager !== 'undefined') {
                this.comparisonManager = new AlgorithmComparisonManager(this);
                // 🔧 将算法对比管理器设置为全局可访问，供步骤演示使用
                window.algorithmComparisonManager = this.comparisonManager;
                this.log('🔄 算法对比管理器初始化完成', 'success');
            } else {
                this.log('⚠️ 算法对比管理器类未加载', 'warning');
            }
        } catch (error) {
            this.log(`❌ 算法对比管理器初始化失败: ${error.message}`, 'error');
            console.error('算法对比管理器初始化错误:', error);
        }
    }

    /**
     * 初始化物体检测可视化器
     */
    initObjectDetectionVisualizer() {
        try {
            if (typeof ObjectDetectionVisualizer !== 'undefined') {
                this.objectDetectionVisualizer = new ObjectDetectionVisualizer(this);
                this.log('👁️ 物体检测可视化器初始化完成', 'success');
            } else {
                this.log('⚠️ 物体检测可视化器类未加载', 'warning');
            }
        } catch (error) {
            this.log(`❌ 物体检测可视化器初始化失败: ${error.message}`, 'error');
            console.error('物体检测可视化器初始化错误:', error);
        }
    }
    
    /**
     * 地图移动事件处理
     */
    onMapMove() {
        const bounds = this.map.getBounds();
        const zoom = this.map.getZoom();
        
        // 只在高缩放级别加载详细数据
        if (zoom >= 16 && this.options.enableOSMData) {
            this.loadOSMDataForBounds(bounds);
        }
        
        this.currentBounds = bounds;
        this.emit('mapMove', { bounds, zoom });
    }
    
    /**
     * 加载指定区域的OSM数据
     */
    async loadOSMDataForBounds(bounds) {
        const tileKey = this.getBoundsTileKey(bounds);
        
        // 避免重复加载
        if (this.loadedTiles.has(tileKey) || this.isLoading) {
            return;
        }
        
        this.isLoading = true;
        this.loadedTiles.add(tileKey);
        
        try {
            await this.osmLoader.loadData(bounds);
        } catch (error) {
            this.log(`加载OSM数据失败: ${error.message}`, 'error');
            this.loadedTiles.delete(tileKey);
        } finally {
            this.isLoading = false;
        }
    }
    
    /**
     * OSM数据加载完成处理
     */
    onOSMDataLoaded(data, bounds) {
        this.log(`OSM数据加载完成: ${data.features.length} 个要素`, 'info');
        
        // 处理建筑数据
        const buildings = data.features.filter(f => f.properties.building);
        this.processBuildingData(buildings);
        
        // 处理道路数据
        const roads = data.features.filter(f => f.properties.highway);
        this.processRoadData(roads);
        
        this.emit('osmDataLoaded', { data, bounds });
    }
    
    /**
     * 处理建筑数据
     */
    processBuildingData(buildings) {
        buildings.forEach(building => {
            const id = building.id || building.properties.id;
            this.buildingData.set(id, building);
        });
        
        this.log(`处理了 ${buildings.length} 个建筑`, 'info');
    }
    
    /**
     * 处理道路数据
     */
    processRoadData(roads) {
        // 这里可以添加道路可视化逻辑
        this.log(`处理了 ${roads.length} 条道路`, 'info');
    }
    
    /**
     * 显示建筑信息
     */
    showBuildingInfo(feature, point) {
        // 先清理之前的弹窗
        this.hideBuildingInfo();

        const properties = feature.properties;
        const height = properties.height || '未知';
        const type = properties.type || '建筑';

        // 创建信息弹窗
        const popup = new mapboxgl.Popup({
            closeButton: false,
            closeOnClick: false,
            className: 'building-info-popup'
        })
        .setLngLat(this.map.unproject(point))
        .setHTML(`
            <div class="building-info">
                <h4>${type}</h4>
                <p>高度: ${height}m</p>
                <p>ID: ${feature.id}</p>
            </div>
        `)
        .addTo(this.map);

        this.currentPopup = popup;
    }
    
    /**
     * 隐藏建筑信息
     */
    hideBuildingInfo() {
        // 清理当前弹窗
        if (this.currentPopup) {
            this.currentPopup.remove();
            this.currentPopup = null;
        }

        // 强制清理所有建筑信息弹窗（防止内存泄漏）
        const existingPopups = document.querySelectorAll('.building-info-popup');
        existingPopups.forEach(popup => {
            if (popup.parentNode) {
                popup.parentNode.removeChild(popup);
            }
        });

        // 清理所有mapbox弹窗
        const mapboxPopups = document.querySelectorAll('.mapboxgl-popup');
        mapboxPopups.forEach(popup => {
            // 只清理建筑信息弹窗，保留其他弹窗
            const buildingInfo = popup.querySelector('.building-info');
            if (buildingInfo && popup.parentNode) {
                popup.parentNode.removeChild(popup);
            }
        });
    }
    
    /**
     * 建筑点击事件
     */
    onBuildingClick(feature, point) {
        this.log(`点击建筑: ${feature.id}`, 'info');
        this.emit('buildingClick', { feature, point });
    }
    
    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', () => {
            if (this.map) {
                this.map.resize();
            }
        });

        // 算法选择变化
        const algorithmSelect = document.getElementById('algorithm-select');
        if (algorithmSelect) {
            algorithmSelect.addEventListener('change', (e) => {
                this.algorithm = e.target.value;
                this.log(`🔄 算法已切换为: ${this.algorithm}`, 'info');

                // 如果有Python客户端，验证算法可用性
                if (this.pythonClient && !this.pythonClient.isAlgorithmAvailable(this.algorithm)) {
                    this.log(`⚠️ 算法 ${this.algorithm} 在Python后端不可用`, 'warning');
                }
            });

            // 设置初始算法 - 确保与HTML选择器同步
            this.algorithm = algorithmSelect.value || 'ImprovedClusterBased';
            this.log(`🧠 初始算法: ${this.algorithm}`, 'info');

            // 确保选择器显示正确的值
            if (algorithmSelect.value !== this.algorithm) {
                algorithmSelect.value = this.algorithm;
                this.log(`🔄 算法选择器已同步为: ${this.algorithm}`, 'info');
            }
        }
    }

    /**
     * 启动3D建筑监控
     */
    start3DBuildingMonitor() {
        // 每5秒检查一次3D建筑图层
        this.buildingMonitorInterval = setInterval(() => {
            this.check3DBuildingStatus();
        }, 5000);

        // 地图缩放时也检查
        this.map.on('zoomend', () => {
            setTimeout(() => {
                this.check3DBuildingStatus();
            }, 500);
        });

        // 地图移动结束后也检查
        this.map.on('moveend', () => {
            setTimeout(() => {
                this.check3DBuildingStatus();
            }, 1000);
        });

        // 样式加载完成后恢复3D建筑
        this.map.on('styledata', () => {
            if (this.map.isStyleLoaded()) {
                setTimeout(() => {
                    this.check3DBuildingStatus();
                }, 1000);
            }
        });

        this.log('🔍 3D建筑监控已启动', 'info');
    }

    /**
     * 检查3D建筑状态
     */
    check3DBuildingStatus() {
        if (!this.map || !this.map.isStyleLoaded()) {
            return;
        }

        const zoom = this.map.getZoom();
        const has3DLayer = this.map.getLayer('3d-buildings');

        // 如果缩放级别足够但没有3D建筑图层，重新添加
        if (zoom >= 14 && !has3DLayer) {
            this.log('🔄 检测到3D建筑图层丢失，正在恢复...', 'warning');
            this.init3DBuildings();
        }

        // 如果有图层但不可见，尝试修复
        if (has3DLayer && zoom >= 14) {
            const features = this.map.queryRenderedFeatures({
                layers: ['3d-buildings']
            });

            if (features.length === 0) {
                // 可能是图层配置问题，重新初始化
                this.log('🔧 3D建筑图层存在但无内容，重新初始化...', 'info');
                setTimeout(() => {
                    this.init3DBuildings();
                }, 1000);
            }
        }
    }
    
    /**
     * 获取边界的瓦片键
     */
    getBoundsTileKey(bounds) {
        const ne = bounds.getNorthEast();
        const sw = bounds.getSouthWest();
        return `${sw.lng.toFixed(4)},${sw.lat.toFixed(4)},${ne.lng.toFixed(4)},${ne.lat.toFixed(4)}`;
    }
    
    /**
     * 事件系统
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }
    
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    // 避免在log事件处理中调用log方法导致无限递归
                    if (event !== 'log') {
                        this.log(`事件处理错误 ${event}: ${error.message}`, 'error');
                    } else {
                        console.error(`Log事件处理错误: ${error.message}`);
                    }
                }
            });
        }
    }
    
    /**
     * 日志系统
     */
    log(message, level = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[${timestamp}] ${message}`;
        
        switch (level) {
            case 'error':
                console.error(logMessage);
                break;
            case 'warning':
                console.warn(logMessage);
                break;
            case 'success':
                console.log(`✅ ${logMessage}`);
                break;
            default:
                console.log(logMessage);
        }
        
        // 发送到UI日志（避免循环调用）
        try {
            this.emit('log', { message, level, timestamp });
        } catch (error) {
            // 避免在emit过程中的错误导致无限递归
            console.error('Log emit error:', error);
        }
    }
    
    // ==================== 东京23区优化功能 ====================

    /**
     * 检测并优化东京区域设置
     */
    optimizeForTokyo() {
        const currentCity = this.getCurrentCity();
        const tokyoDistricts = ['tokyo', 'shinjuku', 'shibuya', 'minato', 'chiyoda', 'chuo', 'taito'];

        if (tokyoDistricts.includes(currentCity)) {
            this.log(`🗾 检测到东京区域: ${currentCity}，启用优化设置`, 'info');

            // 启用高精度3D建筑
            this.enableHighDetailBuildings();

            // 优化飞行参数
            this.optimizeFlightParameters();

            // 加载东京特色地标
            this.loadTokyoLandmarks();

            // 设置东京专用样式
            this.applyTokyoStyle();
        }
    }

    /**
     * 获取当前城市
     */
    getCurrentCity() {
        const citySelect = document.getElementById('city-select');
        return citySelect ? citySelect.value : 'tokyo';
    }

    /**
     * 启用高精度3D建筑
     */
    enableHighDetailBuildings() {
        // 提高建筑细节层级
        if (this.map.getLayer('3d-buildings')) {
            this.map.setPaintProperty('3d-buildings', 'fill-extrusion-opacity', 0.9);
            this.map.setPaintProperty('3d-buildings', 'fill-extrusion-color', [
                'case',
                ['>', ['get', 'height'], 100],
                '#ff6b6b', // 高层建筑 - 红色
                ['>', ['get', 'height'], 50],
                '#4ecdc4', // 中层建筑 - 青色
                '#45b7d1'  // 低层建筑 - 蓝色
            ]);
        }

        this.log('✨ 已启用高精度3D建筑渲染', 'success');
    }

    /**
     * 优化飞行参数
     */
    optimizeFlightParameters() {
        // 针对东京密集建筑环境优化
        this.flightHeight = 70; // 修改默认巡航飞行高度为70米（适合城市环境，避开低层建筑）
        this.safetyDistance = 30; // 增加安全距离
        this.flightSpeed = 0.3;   // 进一步降低飞行速度以便观察轨迹

        // 更新UI显示
        const heightSlider = document.getElementById('flight-height');
        const safetySlider = document.getElementById('safety-distance');
        const heightValue = document.getElementById('flight-height-value');
        const safetyValue = document.getElementById('safety-distance-value');

        if (heightSlider) {
            heightSlider.value = this.flightHeight;
            heightValue.textContent = this.flightHeight;
        }

        if (safetySlider) {
            safetySlider.value = this.safetyDistance;
            safetyValue.textContent = this.safetyDistance;
        }

        this.log('🚁 已优化东京区域飞行参数', 'success');
    }

    /**
     * 加载东京特色地标
     */
    loadTokyoLandmarks() {
        const landmarks = this.getTokyoLandmarks();

        // 添加地标数据源
        this.map.addSource('tokyo-landmarks', {
            type: 'geojson',
            data: {
                type: 'FeatureCollection',
                features: landmarks
            }
        });

        // 添加地标图层
        this.map.addLayer({
            id: 'tokyo-landmarks-layer',
            type: 'symbol',
            source: 'tokyo-landmarks',
            layout: {
                'icon-image': 'custom-marker',
                'icon-size': 1.2,
                'text-field': ['get', 'name'],
                'text-font': ['Open Sans Bold', 'Arial Unicode MS Bold'],
                'text-size': 12,
                'text-offset': [0, 2],
                'text-anchor': 'top'
            },
            paint: {
                'text-color': '#ffffff',
                'text-halo-color': '#000000',
                'text-halo-width': 2
            }
        });

        this.log(`🏯 已加载 ${landmarks.length} 个东京地标`, 'success');
    }

    /**
     * 获取东京地标数据
     */
    getTokyoLandmarks() {
        return [
            {
                type: 'Feature',
                properties: { name: '东京塔', type: 'tower' },
                geometry: { type: 'Point', coordinates: [139.7454, 35.6586] }
            },
            {
                type: 'Feature',
                properties: { name: '东京晴空塔', type: 'tower' },
                geometry: { type: 'Point', coordinates: [139.8107, 35.7101] }
            },
            {
                type: 'Feature',
                properties: { name: '皇居', type: 'palace' },
                geometry: { type: 'Point', coordinates: [139.7528, 35.6852] }
            },
            {
                type: 'Feature',
                properties: { name: '浅草寺', type: 'temple' },
                geometry: { type: 'Point', coordinates: [139.7967, 35.7148] }
            },
            {
                type: 'Feature',
                properties: { name: '明治神宫', type: 'shrine' },
                geometry: { type: 'Point', coordinates: [139.6993, 35.6762] }
            },
            {
                type: 'Feature',
                properties: { name: '新宿都政府', type: 'government' },
                geometry: { type: 'Point', coordinates: [139.6917, 35.6896] }
            },
            {
                type: 'Feature',
                properties: { name: '银座', type: 'shopping' },
                geometry: { type: 'Point', coordinates: [139.7671, 35.6719] }
            },
            {
                type: 'Feature',
                properties: { name: '涩谷十字路口', type: 'crossing' },
                geometry: { type: 'Point', coordinates: [139.7016, 35.6598] }
            }
        ];
    }

    /**
     * 应用东京专用样式
     */
    applyTokyoStyle() {
        // 使用适合东京夜景的深色主题
        this.map.setStyle('mapbox://styles/mapbox/dark-v10');

        // 添加东京特色的颜色方案
        this.map.on('style.load', () => {
            // 重新初始化所有图层（样式切换后需要重新添加）
            setTimeout(() => {
                this.init3DBuildings();
                this.enableHighDetailBuildings();

                // 重新创建无人机模型
                this.createDrone();

                // 重新加载东京地标
                this.loadTokyoLandmarks();

                // 如果有路径数据，重新可视化
                if (this.currentPath.length > 0) {
                    this.visualizePath();
                }

                // 如果有起点终点，重新显示
                if (this.startPoint) {
                    this.recreateStartPoint();
                }
                if (this.endPoint) {
                    this.recreateEndPoint();
                }

                this.log('🔄 样式切换后图层重建完成', 'success');
            }, 1000);
        });

        this.log('🌃 已应用东京夜景主题', 'success');
    }

    /**
     * 重新创建起点标记
     */
    recreateStartPoint() {
        if (!this.startPoint) return;

        // 移除旧的起点标记（如果存在）
        if (this.map.getSource('start-point')) {
            this.map.removeLayer('start-point-layer');
            this.map.removeSource('start-point');
        }

        // 重新创建起点
        this.setStartPoint(this.startPoint);
    }

    /**
     * 重新创建终点标记
     */
    recreateEndPoint() {
        if (!this.endPoint) return;

        // 移除旧的终点标记（如果存在）
        if (this.map.getSource('end-point')) {
            this.map.removeLayer('end-point-layer');
            this.map.removeSource('end-point');
        }

        // 重新创建终点
        this.setEndPoint(this.endPoint);
    }

    // ==================== 无人机路径规划功能 ====================

    /**
     * 清理无人机相关的所有资源
     */
    cleanupDroneResources() {
        try {
            // 定义所有需要清理的图层
            const layersToRemove = [
                'drone-model',
                'drone-shadow',
                'protection-zones-fill',
                'protection-zones-border',
                'protection-zones-labels'
            ];

            // 定义所有需要清理的源
            const sourcesToRemove = [
                'drone',
                'protection-zones'
            ];

            // 移除图层
            layersToRemove.forEach(layerId => {
                if (this.map.getLayer(layerId)) {
                    this.map.removeLayer(layerId);
                    this.log(`🗑️ 移除图层: ${layerId}`, 'debug');
                }
            });

            // 移除源
            sourcesToRemove.forEach(sourceId => {
                if (this.map.getSource(sourceId)) {
                    this.map.removeSource(sourceId);
                    this.log(`🗑️ 移除源: ${sourceId}`, 'debug');
                }
            });

            this.log('🧹 无人机资源清理完成', 'info');

        } catch (error) {
            this.log(`⚠️ 清理无人机资源时出错: ${error.message}`, 'warning');
            console.warn('清理无人机资源时出错:', error);
        }
    }

    /**
     * 创建无人机模型
     */
    createDrone() {
        try {
            // 🔧 完全清理所有相关的图层和源
            this.cleanupDroneResources();

            this.log('🔄 开始创建无人机模型...', 'info');

            // 无人机几何体 - 使用Mapbox的3D对象
            const droneData = {
                type: 'FeatureCollection',
                features: [{
                    type: 'Feature',
                    properties: {
                        height: 8
                    },
                    geometry: {
                        type: 'Point',
                        coordinates: [139.7670, 35.6814] // 默认位置：东京皇居
                    }
                }]
            };

            // 添加无人机数据源
            this.map.addSource('drone', {
                type: 'geojson',
                data: droneData
            });

            // 添加保护区数据源（如果不存在）
            if (!this.map.getSource('protection-zones')) {
                this.map.addSource('protection-zones', {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: []
                    }
                });
            }

            // 添加无人机阴影图层（模拟地面投影）
            this.map.addLayer({
                id: 'drone-shadow',
                type: 'circle',
                source: 'drone',
                paint: {
                    'circle-radius': 8,
                    'circle-color': '#000000',
                    'circle-opacity': 0.3,
                    'circle-translate': [2, 2] // 偏移模拟阴影
                }
            });

            // 添加无人机主体图层
            this.map.addLayer({
                id: 'drone-model',
                type: 'circle',
                source: 'drone',
                paint: {
                    'circle-radius': 12,
                    'circle-color': '#ff4444',
                    'circle-stroke-color': '#ffffff',
                    'circle-stroke-width': 3,
                    'circle-opacity': 0.9
                }
            });

            // 添加保护区填充图层
            this.map.addLayer({
                id: 'protection-zones-fill',
                type: 'fill',
                source: 'protection-zones',
                paint: {
                    'fill-color': [
                        'case',
                        ['==', ['get', 'type'], 'park'], '#28a745',
                        ['==', ['get', 'type'], 'commercial'], '#ffc107',
                        ['==', ['get', 'type'], 'transport_hub'], '#dc3545',
                        ['==', ['get', 'type'], 'school'], '#17a2b8',
                        ['==', ['get', 'type'], 'hospital'], '#6f42c1',
                        ['==', ['get', 'type'], 'road_side'], '#6c757d',
                        '#007bff'
                    ],
                    'fill-opacity': [
                        'case',
                        ['get', 'active'], 0.4,  // 参与运算的保护区更明显
                        0.2  // 未参与运算的保护区较淡
                    ]
                }
            });

            // 添加保护区边框图层
            this.map.addLayer({
                id: 'protection-zones-border',
                type: 'line',
                source: 'protection-zones',
                paint: {
                    'line-color': [
                        'case',
                        ['==', ['get', 'type'], 'park'], '#28a745',
                        ['==', ['get', 'type'], 'commercial'], '#ffc107',
                        ['==', ['get', 'type'], 'transport_hub'], '#dc3545',
                        ['==', ['get', 'type'], 'school'], '#17a2b8',
                        ['==', ['get', 'type'], 'hospital'], '#6f42c1',
                        ['==', ['get', 'type'], 'road_side'], '#6c757d',
                        '#007bff'
                    ],
                    'line-width': [
                        'case',
                        ['get', 'active'], 3,  // 参与运算的保护区边框更粗
                        1  // 未参与运算的保护区边框较细
                    ],
                    'line-opacity': 0.8
                }
            });

            // 添加保护区标签图层
            this.map.addLayer({
                id: 'protection-zones-labels',
                type: 'symbol',
                source: 'protection-zones',
                layout: {
                    'text-field': [
                        'concat',
                        ['get', 'name'],
                        '\n',
                        ['case', ['get', 'active'], '✓ 参与运算', '○ 未参与']
                    ],
                    'text-font': ['Open Sans Bold', 'Arial Unicode MS Bold'],
                    'text-size': 12,
                    'text-anchor': 'center',
                    'text-offset': [0, 0]
                },
                paint: {
                    'text-color': '#ffffff',
                    'text-halo-color': '#000000',
                    'text-halo-width': 2
                }
            });

            // 隐藏无人机（初始状态）
            this.map.setLayoutProperty('drone-model', 'visibility', 'none');

            this.log('✅ 无人机模型创建完成', 'success');

        } catch (error) {
            this.log(`❌ 无人机模型创建失败: ${error.message}`, 'error');
            console.error('创建无人机模型时出错:', error);
        }
    }

    /**
     * 初始化保护区数据源和图层
     */
    initializeProtectionZones() {
        try {
            this.log('🛡️ 正在初始化保护区数据源和图层...', 'info');

            // 创建保护区数据源
            if (!this.map.getSource('protection-zones')) {
                this.map.addSource('protection-zones', {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: []
                    }
                });
                this.log('✅ 保护区数据源创建成功', 'success');
            }

            // 添加保护区填充图层
            if (!this.map.getLayer('protection-zones-fill')) {
                this.map.addLayer({
                    id: 'protection-zones-fill',
                    type: 'fill',
                    source: 'protection-zones',
                    paint: {
                        'fill-color': [
                            'case',
                            ['==', ['get', 'type'], 'park'], '#28a745',
                            ['==', ['get', 'type'], 'commercial'], '#ffc107',
                            ['==', ['get', 'type'], 'transport_hub'], '#dc3545',
                            ['==', ['get', 'type'], 'school'], '#17a2b8',
                            ['==', ['get', 'type'], 'hospital'], '#6f42c1',
                            ['==', ['get', 'type'], 'road_side'], '#6c757d',
                            '#007bff'
                        ],
                        'fill-opacity': [
                            'case',
                            ['get', 'active'], 0.4,
                            0.2
                        ]
                    }
                });
                this.log('✅ 保护区填充图层创建成功', 'success');
            }

            // 添加保护区边框图层
            if (!this.map.getLayer('protection-zones-border')) {
                this.map.addLayer({
                    id: 'protection-zones-border',
                    type: 'line',
                    source: 'protection-zones',
                    paint: {
                        'line-color': [
                            'case',
                            ['==', ['get', 'type'], 'park'], '#28a745',
                            ['==', ['get', 'type'], 'commercial'], '#ffc107',
                            ['==', ['get', 'type'], 'transport_hub'], '#dc3545',
                            ['==', ['get', 'type'], 'school'], '#17a2b8',
                            ['==', ['get', 'type'], 'hospital'], '#6f42c1',
                            ['==', ['get', 'type'], 'road_side'], '#6c757d',
                            '#007bff'
                        ],
                        'line-width': [
                            'case',
                            ['get', 'active'], 3,
                            1
                        ],
                        'line-opacity': 0.8
                    }
                });
                this.log('✅ 保护区边框图层创建成功', 'success');
            }

            // 添加保护区标签图层
            if (!this.map.getLayer('protection-zones-labels')) {
                this.map.addLayer({
                    id: 'protection-zones-labels',
                    type: 'symbol',
                    source: 'protection-zones',
                    layout: {
                        'text-field': [
                            'concat',
                            ['get', 'name'],
                            '\n',
                            ['case', ['get', 'active'], '✓ 参与运算', '○ 未参与']
                        ],
                        'text-font': ['Open Sans Bold', 'Arial Unicode MS Bold'],
                        'text-size': 12,
                        'text-anchor': 'center',
                        'text-offset': [0, 0]
                    },
                    paint: {
                        'text-color': '#ffffff',
                        'text-halo-color': '#000000',
                        'text-halo-width': 2
                    }
                });
                this.log('✅ 保护区标签图层创建成功', 'success');
            }

            this.log('🛡️ 保护区系统初始化完成', 'success');

        } catch (error) {
            this.log(`❌ 保护区初始化失败: ${error.message}`, 'error');
        }
    }

    /**
     * 加载保护区数据并在地图上显示
     */
    async loadProtectionZones() {
        try {
            this.log('🛡️ 开始加载保护区数据...', 'info');

            // 检查地图是否已初始化
            if (!this.map || !this.map.isStyleLoaded()) {
                this.log('⚠️ 地图尚未完全加载，延迟加载保护区', 'warning');
                setTimeout(() => {
                    this.loadProtectionZones();
                }, 2000);
                return;
            }

            // 检查保护区数据源是否存在
            if (!this.map.getSource('protection-zones')) {
                this.log('⚠️ 保护区数据源不存在，延迟加载', 'warning');
                setTimeout(() => {
                    this.loadProtectionZones();
                }, 1000);
                return;
            }

            this.log('🛡️ 正在尝试从API加载保护区数据...', 'info');

            // 先尝试API，如果失败则使用模拟数据
            try {
                const response = await fetch('/api/protection-zones/info');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.success) {
                    this.displayProtectionZones(data.zones);
                    this.log(`🛡️ 成功从API加载 ${data.zones.length} 个保护区`, 'success');
                    return;
                } else {
                    throw new Error(data.error);
                }
            } catch (apiError) {
                this.log(`⚠️ API加载失败: ${apiError.message}，使用模拟数据`, 'warning');
                this.loadMockProtectionZones();
            }

        } catch (error) {
            this.log(`❌ 保护区加载错误: ${error.message}，使用模拟数据`, 'error');
            this.loadMockProtectionZones();
        }
    }

    /**
     * 加载模拟保护区数据（当API不可用时）
     */
    loadMockProtectionZones() {
        const mockZones = [
            {
                id: "ueno_park",
                name: "上野公园",
                type: "park",
                center: [139.7734, 35.7153],
                radius: 500,
                average_crash_cost: 0.010,
                collision_cost_factor: 28.27,
                description: "人流密集的大型公园"
            },
            {
                id: "shibuya_crossing",
                name: "涩谷十字路口",
                type: "commercial",
                center: [139.7016, 35.6598],
                radius: 300,
                average_crash_cost: 0.062,
                collision_cost_factor: 175.27,
                description: "世界最繁忙的十字路口"
            },
            {
                id: "tokyo_station",
                name: "东京站",
                type: "transport_hub",
                center: [139.7673, 35.6812],
                radius: 400,
                average_crash_cost: 0.026,
                collision_cost_factor: 73.50,
                description: "主要交通枢纽"
            },
            {
                id: "tokyo_university",
                name: "东京大学",
                type: "school",
                center: [139.7625, 35.7126],
                radius: 800,
                average_crash_cost: 0.013,
                collision_cost_factor: 36.75,
                description: "主要大学校园"
            },
            // 新增：道路交通保护区
            {
                id: "main_road_1",
                name: "主干道1",
                type: "road_traffic",
                center: [139.7300, 35.6850],
                radius: 150,
                average_crash_cost: 0.035,
                collision_cost_factor: 98.95,
                description: "主要道路交通区域"
            },
            {
                id: "highway_entrance",
                name: "高速入口",
                type: "road_traffic",
                center: [139.7800, 35.6900],
                radius: 200,
                average_crash_cost: 0.045,
                collision_cost_factor: 127.22,
                description: "高速公路入口区域"
            },
            // 新增：人流密集区保护区
            {
                id: "shopping_street",
                name: "购物街",
                type: "pedestrian_area",
                center: [139.7650, 35.6750],
                radius: 180,
                average_crash_cost: 0.028,
                collision_cost_factor: 79.16,
                description: "步行购物街区"
            },
            {
                id: "festival_area",
                name: "节庆广场",
                type: "pedestrian_area",
                center: [139.7550, 35.6950],
                radius: 250,
                average_crash_cost: 0.040,
                collision_cost_factor: 113.08,
                description: "节庆活动广场"
            },
            // 新增：路口保护区
            {
                id: "major_intersection",
                name: "主要路口",
                type: "intersection",
                center: [139.7450, 35.6850],
                radius: 100,
                average_crash_cost: 0.055,
                collision_cost_factor: 155.49,
                description: "主要交通路口"
            },
            // 新增：公交站保护区
            {
                id: "central_bus_stop",
                name: "中央公交站",
                type: "bus_stop",
                center: [139.7600, 35.6880],
                radius: 80,
                average_crash_cost: 0.025,
                collision_cost_factor: 70.68,
                description: "中央公交站台"
            },
            // 新增：地铁站保护区
            {
                id: "metro_exit",
                name: "地铁出口",
                type: "subway_station",
                center: [139.7700, 35.6820],
                radius: 120,
                average_crash_cost: 0.030,
                collision_cost_factor: 84.81,
                description: "地铁站出入口"
            }
        ];

        this.displayProtectionZones(mockZones);
        this.log(`🛡️ 成功加载 ${mockZones.length} 个模拟保护区`, 'success');
    }

    /**
     * 在地图上显示保护区
     */
    displayProtectionZones(zones) {
        const features = zones.map(zone => {
            // 创建圆形保护区的多边形
            const center = zone.center;
            const radius = zone.radius;
            const points = 32; // 圆形的点数
            const coordinates = [];

            for (let i = 0; i <= points; i++) {
                const angle = (i * 2 * Math.PI) / points;
                const lng = center[0] + (radius / 111320) * Math.cos(angle); // 大约转换为度
                const lat = center[1] + (radius / 110540) * Math.sin(angle);
                coordinates.push([lng, lat]);
            }

            return {
                type: 'Feature',
                properties: {
                    id: zone.id,
                    name: zone.name,
                    type: zone.type,
                    average_crash_cost: zone.average_crash_cost,
                    collision_cost_factor: zone.collision_cost_factor,
                    radius: zone.radius,
                    description: zone.description,
                    active: false // 初始状态为未参与运算
                },
                geometry: {
                    type: 'Polygon',
                    coordinates: [coordinates]
                }
            };
        });

        // 更新保护区数据源
        const protectionSource = this.map.getSource('protection-zones');
        if (protectionSource) {
            protectionSource.setData({
                type: 'FeatureCollection',
                features: features
            });
            this.log('✅ 保护区数据已更新到地图', 'success');
        } else {
            this.log('❌ 保护区数据源不存在，无法显示保护区', 'error');
            return;
        }

        this.log(`🛡️ 保护区可视化完成`, 'info');

        // 更新保护区信息面板
        this.updateProtectionZonesPanel(zones);
    }

    /**
     * 更新保护区的参与状态
     */
    updateProtectionZoneStatus(activeZoneIds) {
        console.log('🛡️ updateProtectionZoneStatus 被调用');
        console.log('🛡️ 接收到的参数:', activeZoneIds);
        console.log('🛡️ 参数类型:', typeof activeZoneIds);
        console.log('🛡️ 参数长度:', activeZoneIds ? activeZoneIds.length : 'undefined');

        const source = this.map.getSource('protection-zones');
        if (!source) {
            console.error('❌ 保护区数据源不可用，无法更新状态');
            this.log('⚠️ 保护区数据源不可用，无法更新状态', 'warning');
            return;
        }

        console.log('🛡️ 更新保护区状态，接收到的活跃ID:', activeZoneIds);

        const data = source._data;
        if (data && data.features) {
            // 🔧 修复：处理不同的ID格式
            const normalizedActiveIds = activeZoneIds.map(id => {
                // 移除可能的前缀（如 "frontend_"）
                return id.replace(/^frontend_/, '');
            });

            console.log('🛡️ 标准化后的活跃ID:', normalizedActiveIds);

            data.features.forEach(feature => {
                const featureId = feature.properties.id;
                const isActive = normalizedActiveIds.includes(featureId) || activeZoneIds.includes(featureId);
                feature.properties.active = isActive;

                if (isActive) {
                    console.log(`🛡️ 保护区 ${feature.properties.name} (${featureId}) 标记为活跃`);
                }
            });

            source.setData(data);
            this.log(`🛡️ 更新了 ${normalizedActiveIds.length} 个活跃保护区的状态`, 'info');

            // 同时更新保护区面板显示
            this.updateProtectionZonesPanelStatus(normalizedActiveIds);
        }
    }

    /**
     * 更新保护区信息面板
     */
    updateProtectionZonesPanel(zones) {
        const content = document.getElementById('protection-zones-content');
        if (!content) {
            console.log('❌ 保护区内容元素未找到');
            return;
        }

        // 显示保护区面板
        const panel = document.getElementById('protection-zones-panel');
        if (panel) {
            panel.style.display = 'block';
            console.log('✅ 保护区面板已显示');
        }

        const html = `
            <div style="margin-bottom: 15px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span>总保护区数:</span>
                    <span style="color: #00d4ff;">${zones.length}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span>活跃区域:</span>
                    <span id="active-zones-count" style="color: #28a745;">0</span>
                </div>
            </div>
            <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #00d4ff;">保护区列表:</h4>
                <div id="zones-list">
                    ${zones.map(zone => `
                        <div id="zone-${zone.id}" style="
                            margin-bottom: 8px;
                            padding: 8px;
                            border-radius: 5px;
                            border-left: 3px solid ${this.getZoneColor(zone.type)};
                            background: rgba(255,255,255,0.05);
                            opacity: 0.6;
                        ">
                            <div style="font-weight: bold; margin-bottom: 3px;">
                                ${this.getZoneIconInfo(zone.type).icon} ${zone.name}
                            </div>
                            <div style="font-size: 12px; color: #ccc;">
                                类型: ${this.getZoneTypeName(zone.type)} |
                                碰撞代价: ${zone.average_crash_cost ? zone.average_crash_cost.toFixed(4) : 'N/A'}/m² |
                                半径: ${zone.radius}m
                            </div>
                            <div style="font-size: 11px; color: ${this.getZoneIconInfo(zone.type).color}; margin-top: 2px;">
                                💰 代价均值: ${zone.average_crash_cost ? zone.average_crash_cost.toFixed(4) : 'N/A'}/m²
                            </div>
                            <div id="zone-status-${zone.id}" style="font-size: 11px; color: #999; margin-top: 3px;">
                                ○ 未参与运算
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        content.innerHTML = html;
    }

    /**
     * 更新保护区面板的活跃状态
     */
    updateProtectionZonesPanelStatus(activeZoneIds) {
        // 🔧 修复：处理不同的ID格式
        const normalizedActiveIds = activeZoneIds.map(id => {
            return id.replace(/^frontend_/, '');
        });

        const activeCountElement = document.getElementById('active-zones-count');
        if (activeCountElement) {
            activeCountElement.textContent = normalizedActiveIds.length;
        }

        console.log('🛡️ 更新面板状态，标准化后的活跃ID:', normalizedActiveIds);

        // 更新每个保护区的状态显示
        const allZones = document.querySelectorAll('[id^="zone-"]');
        allZones.forEach(zoneElement => {
            const zoneId = zoneElement.id.replace('zone-', '');
            const statusElement = document.getElementById(`zone-status-${zoneId}`);
            const isActive = normalizedActiveIds.includes(zoneId) || activeZoneIds.includes(zoneId);

            if (statusElement) {
                statusElement.innerHTML = isActive ?
                    '<span style="color: #28a745;">✓ 参与运算</span>' :
                    '<span style="color: #999;">○ 未参与运算</span>';
            }

            // 更新整个区域的透明度
            zoneElement.style.opacity = isActive ? '1.0' : '0.6';

            if (isActive) {
                console.log(`🛡️ 面板中保护区 ${zoneId} 标记为参与运算`);
            }
        });
    }

    /**
     * 获取保护区类型对应的颜色
     */
    getZoneColor(type) {
        const colors = {
            'park': '#28a745',
            'commercial': '#ffc107',
            'transport_hub': '#dc3545',
            'school': '#17a2b8',
            'hospital': '#6f42c1',
            'road_side': '#6c757d'
        };
        return colors[type] || '#007bff';
    }

    /**
     * 获取保护区类型的中文名称
     */
    getZoneTypeName(type) {
        const names = {
            'park': '公园',
            'commercial': '商业区',
            'transport_hub': '交通枢纽',
            'school': '学校',
            'hospital': '医院',
            'road_side': '道路',
            'residential': '住宅区',
            // 新增类型
            'road_traffic': '道路交通',
            'pedestrian_area': '人流密集区',
            'intersection': '交通路口',
            'bus_stop': '公交站',
            'subway_station': '地铁站'
        };
        return names[type] || type;
    }

    /**
     * 获取保护区图标信息
     */
    getZoneIconInfo(type) {
        const iconMapping = {
            'road_traffic': {
                icon: '🚗',
                color: '#ff6b35',
                description: '道路交通'
            },
            'pedestrian_area': {
                icon: '👥',
                color: '#4ecdc4',
                description: '人流密集区'
            },
            'intersection': {
                icon: '🚦',
                color: '#ffe66d',
                description: '交通路口'
            },
            'bus_stop': {
                icon: '🚌',
                color: '#a8e6cf',
                description: '公交站'
            },
            'subway_station': {
                icon: '🚇',
                color: '#ff8b94',
                description: '地铁站'
            },
            'park': {
                icon: '🌳',
                color: '#88d8b0',
                description: '公园'
            },
            'commercial': {
                icon: '🏢',
                color: '#ffd93d',
                description: '商业区'
            },
            'transport_hub': {
                icon: '🚉',
                color: '#6bcf7f',
                description: '交通枢纽'
            },
            'school': {
                icon: '🏫',
                color: '#74b9ff',
                description: '学校'
            },
            'hospital': {
                icon: '🏥',
                color: '#fd79a8',
                description: '医院'
            },
            'residential': {
                icon: '🏠',
                color: '#fdcb6e',
                description: '住宅区'
            },
            'road_side': {
                icon: '🛣️',
                color: '#636e72',
                description: '沿路区域'
            }
        };

        return iconMapping[type] || {
            icon: '📍',
            color: '#ddd',
            description: '未知类型'
        };
    }

    /**
     * 设置起点
     */
    setStartPoint(lngLat) {
        // 移除旧的起点标记
        if (this.map.getSource('start-point')) {
            this.map.removeLayer('start-point-layer');
            this.map.removeSource('start-point');
        }

        // 添加起点数据源
        this.map.addSource('start-point', {
            type: 'geojson',
            data: {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [lngLat.lng, lngLat.lat]
                }
            }
        });

        // 添加起点图层
        this.map.addLayer({
            id: 'start-point-layer',
            type: 'circle',
            source: 'start-point',
            paint: {
                'circle-radius': 8,
                'circle-color': '#00ff00',
                'circle-stroke-color': '#ffffff',
                'circle-stroke-width': 2
            }
        });

        // 保存起点坐标（起飞高度设为1米）
        this.startPoint = {
            lng: lngLat.lng,
            lat: lngLat.lat,
            alt: 1.0  // 起飞高度1米
        };

        // 更新UI
        const coords = `(${lngLat.lng.toFixed(4)}, ${lngLat.lat.toFixed(4)}, 1m)`;
        document.getElementById('start-point-status').textContent = coords;

        this.checkPathPlanningReady();
        this.log(`起点已设置: ${coords}`, 'success');
    }

    /**
     * 设置终点
     */
    setEndPoint(lngLat) {
        // 移除旧的终点标记
        if (this.map.getSource('end-point')) {
            this.map.removeLayer('end-point-layer');
            this.map.removeSource('end-point');
        }

        // 添加终点数据源
        this.map.addSource('end-point', {
            type: 'geojson',
            data: {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [lngLat.lng, lngLat.lat]
                }
            }
        });

        // 添加终点图层
        this.map.addLayer({
            id: 'end-point-layer',
            type: 'circle',
            source: 'end-point',
            paint: {
                'circle-radius': 8,
                'circle-color': '#ff0000',
                'circle-stroke-color': '#ffffff',
                'circle-stroke-width': 2
            }
        });

        // 保存终点坐标（降落高度设为1米）
        this.endPoint = {
            lng: lngLat.lng,
            lat: lngLat.lat,
            alt: 1.0  // 降落高度1米
        };

        // 更新UI
        const coords = `(${lngLat.lng.toFixed(4)}, ${lngLat.lat.toFixed(4)}, 1m)`;
        document.getElementById('end-point-status').textContent = coords;

        this.checkPathPlanningReady();
        this.log(`终点已设置: ${coords}`, 'success');
    }

    /**
     * 检查是否可以开始路径规划
     */
    checkPathPlanningReady() {
        const canPlan = this.startPoint && this.endPoint;
        const planBtn = document.getElementById('plan-path-btn');
        if (planBtn) {
            planBtn.disabled = !canPlan;
        }

        if (canPlan) {
            this.log('可以开始路径规划', 'info');
        }
    }

    /**
     * 规划路径 - 新增自动对比流程
     */
    async planPath() {
        if (!this.startPoint || !this.endPoint) {
            this.log('请先设置起点和终点', 'warning');
            return;
        }

        // 检查是否启用算法对比模式
        const enableComparison = document.getElementById('enable-comparison-checkbox');
        const shouldCompare = enableComparison && enableComparison.checked;

        if (shouldCompare && this.comparisonManager) {
            // 使用算法对比流程
            this.log('🔄 启动算法对比流程...', 'info');
            await this.comparisonManager.startComparison();
        } else {
            // 使用单一算法流程
            this.log('开始路径规划...', 'info');

            // 初始化性能监控
            if (this.panelManager) {
                this.panelManager.addPerformanceLog('开始路径规划算法', 'info');
                this.panelManager.updateAlgorithmProgress(0);
            }

            try {
                // 如果有Python客户端，使用Python后端算法
                if (this.pythonClient) {
                    await this.planPathWithPythonBackend();
                } else {
                    // 回退到原有的路径规划器
                    await this.planPathWithLegacyPlanner();
                }

            } catch (error) {
                this.log(`路径规划失败: ${error.message}`, 'error');
                if (this.panelManager) {
                    this.panelManager.addPerformanceLog(`路径规划失败: ${error.message}`, 'error');
                    this.panelManager.updateAlgorithmProgress(0);
                }
            }
        }
    }

    /**
     * 更新算法参数从控制面板
     */
    updateParametersFromUI() {
        // 获取飞行高度
        const flightHeightSlider = document.getElementById('flight-height');
        if (flightHeightSlider) {
            this.flightHeight = parseInt(flightHeightSlider.value);
        }

        // 获取安全距离
        const safetyDistanceSlider = document.getElementById('safety-distance');
        if (safetyDistanceSlider) {
            this.safetyDistance = parseInt(safetyDistanceSlider.value);
        }

        // 获取算法类型
        const algorithmSelect = document.getElementById('algorithm-select');
        if (algorithmSelect) {
            this.algorithm = algorithmSelect.value;
        }

        this.log(`📊 参数已更新: 高度=${this.flightHeight}m, 安全距离=${this.safetyDistance}m, 算法=${this.algorithm}`, 'info');
    }

    /**
     * 使用Python后端进行路径规划
     */
    async planPathWithPythonBackend() {
        try {
            // 更新参数
            this.updateParametersFromUI();

            // 获取当前视图中的建筑数据
            const buildingData = await this.getBuildingDataForPathPlanning();

            // 创建路径规划请求
            const mappedAlgorithm = this.mapAlgorithmName(this.algorithm);

            // 添加详细的调试信息
            console.log('🔧 路径规划调试信息:');
            console.log('  原始算法名:', this.algorithm);
            console.log('  映射后算法名:', mappedAlgorithm);
            console.log('  建筑数据数量:', buildingData ? buildingData.length : 0);
            console.log('  建筑数据示例:', buildingData ? buildingData.slice(0, 2) : []);

            this.log(`🚀 使用Python后端执行算法: ${mappedAlgorithm}`, 'info');
            this.log(`🏢 获取到 ${buildingData ? buildingData.length : 0} 个建筑数据`, 'info');

            // 转换建筑物数据为障碍物格式（不限制数量）
            const obstacles = buildingData.map(building => ({
                x: building.lng || building.longitude || 0,
                y: building.lat || building.latitude || 0,
                radius: Math.max(building.width || 10, building.height || 10) / 2,
                height: building.height || building.render_height || 20,
                type: 'building'
            }));

            // 验证坐标数据
            if (!this.startPoint || !this.endPoint) {
                throw new Error('起点或终点未设置');
            }

            if (!this.startPoint.lng || !this.startPoint.lat) {
                throw new Error('起点坐标无效');
            }

            if (!this.endPoint.lng || !this.endPoint.lat) {
                throw new Error('终点坐标无效');
            }

            // 获取交通密度数据
            const trafficData = this.trafficDensityManager ?
                this.trafficDensityManager.getAllTrafficData() : [];

            const requestData = {
                startPoint: {
                    lng: this.startPoint.lng,
                    lat: this.startPoint.lat,
                    alt: this.flightHeight
                },
                endPoint: {
                    lng: this.endPoint.lng,
                    lat: this.endPoint.lat,
                    alt: this.flightHeight
                },
                flightHeight: this.flightHeight,
                safetyDistance: this.safetyDistance,
                maxSpeed: 15,
                algorithm: mappedAlgorithm,
                parameters: this.getCurrentAlgorithmParameters(),
                obstacles: obstacles, // 使用转换后的障碍物数据
                buildings: buildingData, // 传递所有建筑数据（不再限制为10个）
                trafficData: trafficData // 添加交通密度数据
            };

            this.log(`🏢 传递 ${obstacles.length} 个建筑物作为障碍物`, 'info');
            this.log(`🚗 传递 ${trafficData.length} 个交通密度点`, 'info');
            if (trafficData.length > 0) {
                const totalVehicles = trafficData.reduce((sum, point) => sum + point.vehicles, 0);
                const totalPedestrians = trafficData.reduce((sum, point) => sum + point.pedestrians, 0);
                this.log(`🚗 总车辆: ${totalVehicles} 辆, 总行人: ${totalPedestrians} 人`, 'info');
            }
            this.log(`📍 起点: (${requestData.startPoint.lng.toFixed(6)}, ${requestData.startPoint.lat.toFixed(6)})`, 'info');
            this.log(`📍 终点: (${requestData.endPoint.lng.toFixed(6)}, ${requestData.endPoint.lat.toFixed(6)})`, 'info');

            // 验证请求数据
            const validation = this.pythonClient.validateRequest(requestData);
            if (!validation.isValid) {
                throw new Error(`请求验证失败: ${validation.errors.join(', ')}`);
            }

            // 调用Python后端
            const response = await this.pythonClient.calculatePath(requestData);

            // 调试：输出完整响应数据
            console.log('🔍 Python后端完整响应:', response);
            console.log('🔍 响应中的pathLength:', response ? response.pathLength : 'undefined');
            console.log('🔍 响应中的path_length:', response ? response.path_length : 'undefined');
            console.log('🔍 响应中的metadata:', response ? response.metadata : 'undefined');

            if (response && response.success) {
                // 格式化路径数据
                const formattedPath = this.pythonClient.formatPathData(response);
                this.currentPath = formattedPath;

                // 可视化路径
                this.visualizePath();

                this.log(`✅ 路径规划完成 (${mappedAlgorithm})`, 'success');
                this.log(`📍 路径点数: ${formattedPath.length}`, 'info');
                // 获取路径长度（优先使用pathLength，回退到path_length）
                const pathLengthMeters = response.pathLength || response.path_length || 0;
                this.log(`📏 路径长度: ${pathLengthMeters.toFixed(1)}米`, 'info');

                // 启用飞行按钮
                const flightBtn = document.getElementById('start-flight-btn');
                if (flightBtn) {
                    flightBtn.disabled = false;
                }

                // 启用保护区信息按钮
                const protectionZonesBtn = document.getElementById('show-protection-zones-btn');
                if (protectionZonesBtn) {
                    protectionZonesBtn.disabled = false;
                }

                // 更新状态面板信息
                this.updateAlgorithmStatus(response, mappedAlgorithm, formattedPath);

                // 更新路径长度显示
                const pathLengthKm = (pathLengthMeters / 1000).toFixed(1);
                const pathLengthElement = document.getElementById('path-length');
                if (pathLengthElement) {
                    pathLengthElement.textContent = `${pathLengthKm}km`;
                }

                // 显示质量信息
                if (response.quality) {
                    this.log(`📈 路径质量: 风险${response.quality.risk_score || 0}, 平滑度${response.quality.smoothness || 0}`, 'info');
                }

                // 保存详细信息用于详情面板
                this.lastPlanningDetails = {
                    algorithm: mappedAlgorithm,
                    response: response,
                    requestData: requestData,
                    timestamp: new Date(),
                    pathLength: response.pathLength || response.path_length || 0,
                    pathPoints: formattedPath.length,
                    buildingCount: obstacles.length
                };

                // 启用查看详情按钮 - 已注释
                /*
                const showDetailsBtn = document.getElementById('show-details-btn');
                if (showDetailsBtn) {
                    showDetailsBtn.disabled = false;
                }
                */

                // 启用算法详情按钮（仅对改进分簇算法）
                const showFormulaBtn = document.getElementById('show-formula-btn');
                if (showFormulaBtn) {
                    const isImprovedCluster = mappedAlgorithm === 'ImprovedClusterBased' ||
                                             (response.metadata && response.metadata.algorithm_type === 'improved_cluster_based');

                    // 添加调试信息
                    console.log('🔧 算法详情按钮调试信息:');
                    console.log('  mappedAlgorithm:', mappedAlgorithm);
                    console.log('  response.metadata:', response.metadata);
                    console.log('  isImprovedCluster:', isImprovedCluster);

                    showFormulaBtn.disabled = !isImprovedCluster;
                    this.log(`🔧 算法详情按钮${isImprovedCluster ? '已启用' : '已禁用'} (算法: ${mappedAlgorithm})`, 'info');
                }

            } else {
                const errorMsg = response ? response.error : '未知错误';
                this.log(`❌ Python后端路径规划失败: ${errorMsg}`, 'error');
                // 回退到传统规划器
                await this.planPathWithLegacyPlanner();
            }

        } catch (error) {
            this.log(`❌ Python后端路径规划失败: ${error.message}`, 'error');
            // 回退到传统规划器
            await this.planPathWithLegacyPlanner();
        }
    }

    /**
     * 获取用于路径规划的建筑数据
     */
    async getBuildingDataForPathPlanning() {
        try {
            const allBuildings = [];
            let buildingCount = 0;

            this.log('🔍 开始收集所有可用的建筑物数据...', 'info');

            // 1. 优先使用OSM建筑数据（增加数量限制）
            if (this.osmLoader && this.osmLoader.buildingData && this.osmLoader.buildingData.size > 0) {
                const osmBuildings = Array.from(this.osmLoader.buildingData.values());
                const osmCount = Math.min(osmBuildings.length, 100); // 增加到100个
                allBuildings.push(...osmBuildings.slice(0, osmCount));
                buildingCount += osmCount;
                this.log(`📊 收集了 ${osmCount} 个OSM建筑数据`, 'info');
            }

            // 2. 获取3D建筑图层数据（扩大查询范围）
            if (this.map && this.map.getLayer('3d-buildings')) {
                try {
                    // 扩大查询范围，不仅限于当前视图
                    const bounds = this.map.getBounds();
                    const features = this.map.queryRenderedFeatures({
                        layers: ['3d-buildings']
                    });

                    if (features.length > 0) {
                        const mapboxBuildings = this.convertMapboxBuildingsToPathPlanningFormat(features);
                        const mapboxCount = Math.min(mapboxBuildings.length, 150); // 增加到150个
                        allBuildings.push(...mapboxBuildings.slice(0, mapboxCount));
                        buildingCount += mapboxCount;
                        this.log(`📊 收集了 ${mapboxCount} 个Mapbox建筑数据`, 'info');
                    }
                } catch (mapboxError) {
                    this.log(`⚠️ Mapbox建筑数据获取失败: ${mapboxError.message}`, 'warning');
                }
            }

            // 3. 获取路径沿线的额外建筑数据
            if (this.startPoint && this.endPoint) {
                const pathBuildings = await this.getBuildingsAlongPath();
                if (pathBuildings.length > 0) {
                    allBuildings.push(...pathBuildings);
                    buildingCount += pathBuildings.length;
                    this.log(`📊 收集了 ${pathBuildings.length} 个路径沿线建筑数据`, 'info');
                }
            }

            // 4. 如果建筑数据仍然不足，生成高质量的模拟数据
            if (buildingCount < 50) {
                const simulatedBuildings = this.generateEnhancedSimulatedBuildingData();
                allBuildings.push(...simulatedBuildings);
                buildingCount += simulatedBuildings.length;
                this.log(`📊 补充了 ${simulatedBuildings.length} 个高质量模拟建筑数据`, 'info');
            }

            // 去重处理
            const uniqueBuildings = this.deduplicateBuildings(allBuildings);

            this.log(`✅ 总共收集了 ${uniqueBuildings.length} 个建筑物数据用于路径规划`, 'success');

            // 返回所有建筑数据（不再限制为10个）
            return uniqueBuildings;

        } catch (error) {
            this.log(`❌ 获取建筑数据失败: ${error.message}`, 'error');
            // 出错时返回基础模拟数据
            this.log('🔄 回退到基础模拟建筑数据', 'info');
            return this.generateEnhancedSimulatedBuildingData();
        }
    }

    /**
     * 获取路径沿线的建筑数据
     */
    async getBuildingsAlongPath() {
        const buildings = [];

        if (!this.startPoint || !this.endPoint) {
            return buildings;
        }

        try {
            // 创建路径缓冲区
            const pathPoints = this.generatePathPoints(this.startPoint, this.endPoint, 20);

            // 在每个路径点周围查询建筑
            for (const point of pathPoints) {
                const nearbyBuildings = await this.queryBuildingsNearPoint(point, 500); // 500米半径
                buildings.push(...nearbyBuildings);
            }

            return buildings;
        } catch (error) {
            this.log(`⚠️ 路径沿线建筑查询失败: ${error.message}`, 'warning');
            return buildings;
        }
    }

    /**
     * 生成路径点
     */
    generatePathPoints(start, end, numPoints) {
        const points = [];

        for (let i = 0; i <= numPoints; i++) {
            const ratio = i / numPoints;
            const lng = start.lng + (end.lng - start.lng) * ratio;
            const lat = start.lat + (end.lat - start.lat) * ratio;
            points.push({ lng, lat });
        }

        return points;
    }

    /**
     * 查询点附近的建筑
     */
    async queryBuildingsNearPoint(point, radius) {
        const buildings = [];

        try {
            // 如果有地图实例，查询该点附近的建筑
            if (this.map && this.map.getLayer('3d-buildings')) {
                // 转换半径为像素
                const pixelRadius = 50; // 简化处理

                const features = this.map.queryRenderedFeatures(
                    [
                        [point.lng - 0.01, point.lat - 0.01],
                        [point.lng + 0.01, point.lat + 0.01]
                    ],
                    { layers: ['3d-buildings'] }
                );

                const convertedBuildings = this.convertMapboxBuildingsToPathPlanningFormat(features);
                buildings.push(...convertedBuildings);
            }
        } catch (error) {
            // 忽略单点查询错误
        }

        return buildings;
    }

    /**
     * 建筑物去重
     */
    deduplicateBuildings(buildings) {
        const seen = new Set();
        const unique = [];

        for (const building of buildings) {
            // 使用坐标和类型作为唯一标识
            const key = `${Math.round((building.lng || building.longitude || 0) * 10000)}_${Math.round((building.lat || building.latitude || 0) * 10000)}_${building.type || 'unknown'}`;

            if (!seen.has(key)) {
                seen.add(key);
                unique.push(building);
            }
        }

        return unique;
    }

    /**
     * 转换Mapbox建筑数据为路径规划格式
     */
    convertMapboxBuildingsToPathPlanningFormat(features) {
        return features.map(feature => {
            const props = feature.properties;
            const geometry = feature.geometry;

            // 计算建筑中心点
            let centerLng = 0, centerLat = 0, pointCount = 0;

            if (geometry.type === 'Polygon' && geometry.coordinates[0]) {
                geometry.coordinates[0].forEach(coord => {
                    centerLng += coord[0];
                    centerLat += coord[1];
                    pointCount++;
                });
                centerLng /= pointCount;
                centerLat /= pointCount;
            }

            // 统一建筑物数据格式，确保与后端兼容
            return {
                id: feature.id || `building_${Math.random().toString(36).substring(2, 11)}`,
                x: centerLng,  // 后端期望的x坐标
                y: centerLat,  // 后端期望的y坐标
                z: 0,          // 地面高度
                lng: centerLng,
                lat: centerLat,
                longitude: centerLng,
                latitude: centerLat,
                height: props.height || props.render_height || 20,
                radius: (props.width || 20) / 2, // 后端期望的半径
                width: props.width || 20,
                type: props.type || props.building || 'building',
                geometry: geometry,
                properties: props
            };
        }).slice(0, 10); // 限制数量为10个以提高性能
    }

    /**
     * 生成模拟建筑数据用于演示代价计算功能
     */
    generateSimulatedBuildingData() {
        const buildings = [];

        // 获取当前地图中心点，如果没有则使用默认坐标
        let centerLng = 139.7671; // 东京默认经度
        let centerLat = 35.6812;  // 东京默认纬度

        if (this.map) {
            const center = this.map.getCenter();
            centerLng = center.lng;
            centerLat = center.lat;
        }

        // 在起点和终点之间生成一些建筑物
        if (this.startPoint && this.endPoint) {
            centerLng = (this.startPoint.lng + this.endPoint.lng) / 2;
            centerLat = (this.startPoint.lat + this.endPoint.lat) / 2;
        }

        // 生成10个模拟建筑物用于性能测试
        for (let i = 0; i < 10; i++) {
            // 在中心点周围随机分布建筑物
            const offsetLng = (Math.random() - 0.5) * 0.02; // ±0.01度范围
            const offsetLat = (Math.random() - 0.5) * 0.02; // ±0.01度范围

            const width = 10 + Math.random() * 20;  // 10-30米宽度
            const building = {
                id: `simulated_building_${i}`,
                x: centerLng + offsetLng,  // 后端期望的x坐标
                y: centerLat + offsetLat,  // 后端期望的y坐标
                z: 0,                      // 地面高度
                lng: centerLng + offsetLng,
                lat: centerLat + offsetLat,
                longitude: centerLng + offsetLng,
                latitude: centerLat + offsetLat,
                height: 20 + Math.random() * 80, // 20-100米高度
                radius: width / 2,         // 后端期望的半径
                width: width,
                type: ['residential', 'commercial', 'office', 'industrial'][Math.floor(Math.random() * 4)],
                simulated: true // 标记为模拟数据
            };

            buildings.push(building);
        }

        this.log(`🏗️ 生成了 ${buildings.length} 个模拟建筑物用于演示代价计算功能`, 'info');
        this.log(`📍 建筑物分布中心: ${centerLng.toFixed(6)}, ${centerLat.toFixed(6)}`, 'info');

        return buildings;
    }

    /**
     * 生成增强的模拟建筑数据（更多数量，更真实分布）
     */
    generateEnhancedSimulatedBuildingData() {
        const buildings = [];

        // 获取路径区域
        let centerLng = 139.7671; // 东京默认经度
        let centerLat = 35.6812;  // 东京默认纬度
        let areaSize = 0.02; // 默认区域大小

        if (this.startPoint && this.endPoint) {
            // 基于起点终点计算区域
            centerLng = (this.startPoint.lng + this.endPoint.lng) / 2;
            centerLat = (this.startPoint.lat + this.endPoint.lat) / 2;

            // 计算路径长度，调整区域大小
            const pathLength = Math.sqrt(
                Math.pow(this.endPoint.lng - this.startPoint.lng, 2) +
                Math.pow(this.endPoint.lat - this.startPoint.lat, 2)
            );
            areaSize = Math.max(0.01, Math.min(0.05, pathLength * 2)); // 动态调整区域大小
        } else if (this.map) {
            const center = this.map.getCenter();
            centerLng = center.lng;
            centerLat = center.lat;
        }

        // 生成更多建筑物（50-100个）
        const numBuildings = 50 + Math.floor(Math.random() * 50);

        // 东京地区真实建筑类型分布
        const buildingTypes = [
            { type: 'residential', weight: 0.45, heightRange: [8, 45], sizeRange: [60, 200] },
            { type: 'commercial', weight: 0.25, heightRange: [15, 80], sizeRange: [150, 800] },
            { type: 'office', weight: 0.15, heightRange: [25, 120], sizeRange: [400, 1500] },
            { type: 'industrial', weight: 0.08, heightRange: [8, 25], sizeRange: [800, 3000] },
            { type: 'school', weight: 0.04, heightRange: [12, 25], sizeRange: [800, 2500] },
            { type: 'hospital', weight: 0.03, heightRange: [20, 60], sizeRange: [1500, 6000] }
        ];

        for (let i = 0; i < numBuildings; i++) {
            // 选择建筑类型（基于权重）
            let selectedType = buildingTypes[0];
            const random = Math.random();
            let cumulativeWeight = 0;

            for (const typeInfo of buildingTypes) {
                cumulativeWeight += typeInfo.weight;
                if (random <= cumulativeWeight) {
                    selectedType = typeInfo;
                    break;
                }
            }

            // 生成位置（更真实的分布）
            let offsetLng, offsetLat;

            if (this.startPoint && this.endPoint) {
                // 沿路径分布建筑物
                const pathRatio = Math.random();
                const pathLng = this.startPoint.lng + (this.endPoint.lng - this.startPoint.lng) * pathRatio;
                const pathLat = this.startPoint.lat + (this.endPoint.lat - this.startPoint.lat) * pathRatio;

                // 在路径周围随机偏移
                offsetLng = pathLng + (Math.random() - 0.5) * areaSize;
                offsetLat = pathLat + (Math.random() - 0.5) * areaSize;
            } else {
                // 在中心点周围分布
                offsetLng = centerLng + (Math.random() - 0.5) * areaSize;
                offsetLat = centerLat + (Math.random() - 0.5) * areaSize;
            }

            // 生成建筑属性
            const height = selectedType.heightRange[0] +
                          Math.random() * (selectedType.heightRange[1] - selectedType.heightRange[0]);
            const area = selectedType.sizeRange[0] +
                        Math.random() * (selectedType.sizeRange[1] - selectedType.sizeRange[0]);
            const width = Math.sqrt(area); // 简化为正方形

            const building = {
                id: `enhanced_sim_building_${i}`,
                x: offsetLng,
                y: offsetLat,
                z: 0,
                lng: offsetLng,
                lat: offsetLat,
                longitude: offsetLng,
                latitude: offsetLat,
                height: height,
                area: area,
                radius: width / 2,
                width: width,
                type: selectedType.type,
                building: selectedType.type, // 兼容不同的字段名
                simulated: true,
                enhanced: true,
                // 添加更多属性以支持详细分析
                properties: {
                    building: selectedType.type,
                    height: height,
                    area: area,
                    name: `${selectedType.type}_building_${i}`
                },
                // GeoJSON格式支持
                geometry: {
                    type: 'Point',
                    coordinates: [offsetLng, offsetLat]
                }
            };

            buildings.push(building);
        }

        this.log(`🏗️ 生成了 ${buildings.length} 个增强模拟建筑物`, 'info');
        this.log(`📍 建筑物分布中心: ${centerLng.toFixed(6)}, ${centerLat.toFixed(6)}`, 'info');
        this.log(`📏 分布区域大小: ${areaSize.toFixed(4)}度`, 'info');

        // 按类型统计
        const typeStats = {};
        buildings.forEach(b => {
            typeStats[b.type] = (typeStats[b.type] || 0) + 1;
        });
        this.log(`📊 建筑类型分布: ${Object.entries(typeStats).map(([type, count]) => `${type}:${count}`).join(', ')}`, 'info');

        return buildings;
    }

    /**
     * 映射算法名称到新的算法接口
     */
    mapAlgorithmName(algorithmName) {
        // 如果已经是新格式的算法名称，直接返回
        const newFormatAlgorithms = ['StraightLine', 'AStar', 'RRT', 'RoadFollowing', 'Dijkstra', 'ImprovedClusterBased'];
        if (newFormatAlgorithms.includes(algorithmName)) {
            return algorithmName;
        }

        // 旧格式映射到新格式
        const algorithmMap = {
            'straight': 'StraightLine',
            'astar': 'AStar',
            'rrt': 'RRT',
            'road-following': 'RoadFollowing',
            'dijkstra': 'Dijkstra'
        };

        const mapped = algorithmMap[algorithmName];
        if (mapped) {
            this.log(`🔄 算法映射: ${algorithmName} -> ${mapped}`, 'info');
            return mapped;
        }

        // 如果没有找到映射，默认使用改进分簇算法
        this.log(`⚠️ 未知算法 ${algorithmName}，使用默认的改进分簇算法`, 'warning');
        return 'ImprovedClusterBased';
    }

    /**
     * 获取当前算法参数
     */
    getCurrentAlgorithmParameters() {
        // 如果有Python客户端，获取默认参数
        if (this.pythonClient) {
            const algorithmName = this.mapAlgorithmName(this.algorithm);
            const defaults = this.pythonClient.getDefaultParameters(algorithmName);
            if (Object.keys(defaults).length > 0) {
                return defaults;
            }
        }

        // 根据算法类型返回不同的参数
        const algorithmName = this.mapAlgorithmName(this.algorithm);

        switch (algorithmName) {
            case 'StraightLine':
                return {
                    pointCount: 20,
                    smoothing: true,
                    avoidBuildings: true
                };

            case 'AStar':
                return {
                    gridSize: 10,
                    heuristicWeight: 1.0,
                    maxIterations: 5000,
                    allowDiagonal: true
                };

            case 'RRT':
                return {
                    maxIterations: 3000,
                    stepSize: 50,
                    goalBias: 0.1,
                    goalTolerance: 20,
                    smoothPath: true
                };

            case 'RoadFollowing':
                return {
                    followRoads: true,
                    roadBuffer: 10,
                    pointCount: 25
                };

            case 'Dijkstra':
                return {
                    gridSize: 15,
                    maxIterations: 10000,
                    optimizeDistance: true
                };

            case 'ImprovedClusterBased':
                return {
                    flightHeight: 100,
                    safetyDistance: 30,
                    maxTurnAngle: 90,
                    riskEdgeDistance: 50,
                    kValue: 5,
                    enablePathSwitching: true,
                    collisionThreshold: 20
                };

            default:
                return {
                    pointCount: 20,
                    smoothing: true
                };
        }
    }

    /**
     * 使用传统路径规划器（回退方案）
     */
    async planPathWithLegacyPlanner() {
        // 更新参数
        this.updateParametersFromUI();

        // 准备起点和终点数据
        const start = {
            lng: this.startPoint.lng,
            lat: this.startPoint.lat,
            alt: this.flightHeight
        };

        const end = {
            lng: this.endPoint.lng,
            lat: this.endPoint.lat,
            alt: this.flightHeight
        };

        // 使用路径规划器
        this.currentPath = await this.pathPlanner.planPath(
            start, end, [], this.algorithm
        );

        if (this.currentPath.length > 0) {
            this.visualizePath();
            this.log(`路径规划完成 (${this.algorithm})`, 'success');

            const flightBtn = document.getElementById('start-flight-btn');
            if (flightBtn) {
                flightBtn.disabled = false;
            }

            // 计算路径长度
            const pathLength = this.calculatePathLength();
            const pathLengthElement = document.getElementById('path-length');
            if (pathLengthElement) {
                pathLengthElement.textContent = `${pathLength.toFixed(1)}km`;
            }

            this.log(`生成 ${this.currentPath.length} 个路径点`, 'info');
        } else {
            this.log('无法找到可行路径', 'error');
        }
    }

    /**
     * 设置路径数据（供算法集成器调用）
     */
    setPath(path) {
        if (!path || !Array.isArray(path)) {
            this.log('❌ 无效的路径数据', 'error');
            return;
        }

        this.currentPath = path;
        this.visualizePath();

        // 计算路径长度
        const pathLength = this.calculatePathLength();
        this.log(`路径可视化完成，${path.length} 个路径点`, 'success');

        if (pathLength > 0) {
            this.log(`路径总长度: ${(pathLength / 1000).toFixed(2)}km`, 'info');
        }
    }

    /**
     * 可视化路径
     */
    visualizePath() {
        // 清除旧路径
        this.clearPathVisualization();

        if (this.currentPath.length < 2) return;

        // 创建路径线的坐标（2D坐标，避免Mapbox错误）
        const coordinates = this.currentPath.map(point => [point.lng, point.lat]);

        // 添加路径数据源
        this.map.addSource('flight-path', {
            type: 'geojson',
            data: {
                type: 'Feature',
                geometry: {
                    type: 'LineString',
                    coordinates: coordinates
                }
            }
        });

        // 添加路径阴影图层（模拟地面投影）
        this.map.addLayer({
            id: 'flight-path-shadow',
            type: 'line',
            source: 'flight-path',
            layout: {
                'line-join': 'round',
                'line-cap': 'round'
            },
            paint: {
                'line-color': '#000000',
                'line-width': 6,
                'line-opacity': 0.3,
                'line-translate': [2, 2] // 偏移模拟阴影
            }
        });

        // 添加路径主体图层（明亮颜色表示高空飞行）
        this.map.addLayer({
            id: 'flight-path-layer',
            type: 'line',
            source: 'flight-path',
            layout: {
                'line-join': 'round',
                'line-cap': 'round'
            },
            paint: {
                'line-color': '#00ff88', // 绿色表示高空安全飞行
                'line-width': 4,
                'line-opacity': 0.9
            }
        });

        this.log(`路径可视化完成，${this.currentPath.length} 个路径点`, 'success');
    }

    /**
     * 清除路径可视化
     */
    clearPathVisualization() {
        // 清除路径主体图层
        if (this.map.getLayer('flight-path-layer')) {
            this.map.removeLayer('flight-path-layer');
        }

        // 清除路径阴影图层
        if (this.map.getLayer('flight-path-shadow')) {
            this.map.removeLayer('flight-path-shadow');
        }

        // 清除数据源
        if (this.map.getSource('flight-path')) {
            this.map.removeSource('flight-path');
        }
    }

    /**
     * 更新算法状态信息
     */
    updateAlgorithmStatus(response, algorithmName, pathData) {
        try {
            // 更新算法信息
            const currentAlgorithmElement = document.getElementById('current-algorithm');
            if (currentAlgorithmElement) {
                const algorithmDisplayName = this.getAlgorithmDisplayName(algorithmName);
                currentAlgorithmElement.textContent = algorithmDisplayName;
            }

            // 更新执行时间
            const executionTimeElement = document.getElementById('execution-time');
            if (executionTimeElement && response.executionTime) {
                const timeMs = (response.executionTime * 1000).toFixed(0);
                executionTimeElement.textContent = `${timeMs}ms`;
            }

            // 更新路径点数
            const pathPointsElement = document.getElementById('path-points');
            if (pathPointsElement && pathData) {
                pathPointsElement.textContent = pathData.length.toString();
            }

            // 更新预计飞行时间
            const estimatedTimeElement = document.getElementById('estimated-time');
            if (estimatedTimeElement && response.estimatedFlightTime) {
                const timeSeconds = Math.round(response.estimatedFlightTime);
                estimatedTimeElement.textContent = `${timeSeconds}s`;
            }

            // 更新改进分簇算法特有信息
            if (algorithmName === 'ImprovedClusterBased' && response.metadata) {
                // 分簇数量
                const clusterCountElement = document.getElementById('cluster-count');
                if (clusterCountElement) {
                    const clusterCount = response.metadata.clusters_count || '--';
                    clusterCountElement.textContent = clusterCount.toString();
                }

                // 初始路径数量
                const initialPathsElement = document.getElementById('initial-paths');
                if (initialPathsElement) {
                    const initialPaths = response.metadata.initial_paths_count || '--';
                    initialPathsElement.textContent = initialPaths.toString();
                }
            }

            // 更新网格点数（如果有的话）
            const gridPointsElement = document.getElementById('grid-points');
            if (gridPointsElement && response.metadata && response.metadata.grid_points) {
                gridPointsElement.textContent = response.metadata.grid_points.toString();
            }

            // 更新核心指标面板（新增）
            if (this.panelManager) {
                const coreMetrics = this.extractCoreMetrics(response);
                this.panelManager.updateCoreMetrics(coreMetrics);

                // 添加性能日志
                this.panelManager.addPerformanceLog(`算法执行完成: ${algorithmName}`, 'success');
                this.panelManager.addPerformanceLog(`路径长度: ${coreMetrics.pathLength.toFixed(1)}m`, 'info');
                this.panelManager.addPerformanceLog(`执行时间: ${(response.executionTime * 1000).toFixed(0)}ms`, 'info');

                // 更新算法进度为100%
                this.panelManager.updateAlgorithmProgress(100);
            }

            // 更新其他状态信息
            this.updateAdditionalStatus(response, algorithmName, pathData);

        } catch (error) {
            console.warn('更新算法状态失败:', error);
        }
    }

    /**
     * 获取算法显示名称
     */
    getAlgorithmDisplayName(algorithmName) {
        const algorithmNames = {
            'ImprovedClusterBased': '改进分簇算法',
            'AStar': 'A*算法',
            'RRT': 'RRT算法',
            'StraightLine': '直线算法'
        };
        return algorithmNames[algorithmName] || algorithmName;
    }

    /**
     * 提取核心指标数据
     */
    extractCoreMetrics(response) {
        const metrics = {
            pathLength: response.pathLength || response.path_length || 0,
            turningCost: 0,
            riskValue: 0,
            collisionCost: 0,
            finalCost: 0
        };

        // 从metadata中提取详细指标
        if (response.metadata) {
            metrics.turningCost = response.metadata.turning_cost || response.metadata.orientAdjustCost || 0;
            metrics.riskValue = response.metadata.risk_value || response.metadata.riskValue || 0;
            metrics.collisionCost = response.metadata.collision_cost || response.metadata.collisionCost || 0;
            metrics.finalCost = response.metadata.final_cost || response.metadata.totalCost || 0;
        }

        // 如果没有metadata，尝试从其他字段获取
        if (!response.metadata) {
            metrics.turningCost = response.turningCost || response.turning_cost || 0;
            metrics.riskValue = response.riskValue || response.risk_value || 0;
            metrics.collisionCost = response.collisionCost || response.collision_cost || 0;
            metrics.finalCost = response.finalCost || response.final_cost || response.totalCost || 0;
        }

        return metrics;
    }

    /**
     * 更新其他状态信息
     */
    updateAdditionalStatus(response, algorithmName, pathData) {
        // 更新选中路径信息
        const selectedPathElement = document.getElementById('selected-path');
        if (selectedPathElement && response.metadata) {
            const selectedPath = response.metadata.selected_path_id || response.metadata.best_path_index || '--';
            selectedPathElement.textContent = selectedPath.toString();
        }

        // 更新优化次数
        const optimizationCountElement = document.getElementById('optimization-count');
        if (optimizationCountElement && response.metadata) {
            const optimizationCount = response.metadata.optimization_iterations || response.metadata.iterations || '--';
            optimizationCountElement.textContent = optimizationCount.toString();
        }

        // 更新保护区数量
        const protectionZonesElement = document.getElementById('protection-zones-count');
        if (protectionZonesElement && response.metadata) {
            const zonesCount = response.metadata.protection_zones_count || response.metadata.zones_count || 0;
            protectionZonesElement.textContent = zonesCount.toString();
        }

        // 更新飞行高度状态
        const flightHeightElement = document.getElementById('flight-height-status');
        if (flightHeightElement) {
            flightHeightElement.textContent = `${this.flightHeight}m`;
        }
    }

    /**
     * 计算路径长度（公里）
     */
    calculatePathLength() {
        if (this.currentPath.length < 2) return 0;

        let totalLength = 0;
        for (let i = 1; i < this.currentPath.length; i++) {
            const prev = this.currentPath[i - 1];
            const curr = this.currentPath[i];

            // 使用Haversine公式计算地球表面两点间距离
            const R = 6371; // 地球半径（公里）
            const dLat = (curr.lat - prev.lat) * Math.PI / 180;
            const dLng = (curr.lng - prev.lng) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                     Math.cos(prev.lat * Math.PI / 180) * Math.cos(curr.lat * Math.PI / 180) *
                     Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            totalLength += R * c;
        }

        return totalLength;
    }

    /**
     * 开始飞行
     */
    startFlight() {
        if (this.currentPath.length === 0) {
            this.log('⚠️ 请先规划路径', 'warning');
            return;
        }

        if (!this.startPoint || !this.endPoint) {
            this.log('⚠️ 请先设置起点和终点', 'warning');
            return;
        }

        try {
            this.isFlying = true;
            this.flightProgress = 0;

            // 确保无人机图层存在
            if (!this.map.getLayer('drone-model')) {
                this.log('🔄 重新创建无人机模型...', 'info');
                this.createDrone();

                // 等待图层创建完成
                setTimeout(() => {
                    this.continueStartFlight();
                }, 200);
                return;
            }

            this.continueStartFlight();

        } catch (error) {
            this.log(`❌ 启动飞行失败: ${error.message}`, 'error');
            console.error('启动飞行时出错:', error);
        }
    }

    /**
     * 继续启动飞行（在确保图层存在后）
     */
    continueStartFlight() {
        try {
            // 显示无人机和阴影
            this.map.setLayoutProperty('drone-model', 'visibility', 'visible');
            if (this.map.getLayer('drone-shadow')) {
                this.map.setLayoutProperty('drone-shadow', 'visibility', 'visible');
            }

            // 重置高度显示为飞行状态
            this.updateAltitudeDisplay(this.flightHeight || 100);

            // 将无人机移动到起点
            this.updateDronePosition(this.startPoint);

            // 开始飞行动画
            this.startFlightAnimation();

            const flightBtn = document.getElementById('start-flight-btn');
            if (flightBtn) {
                flightBtn.textContent = '停止飞行';
            }

            this.log('🚁 无人机飞行开始', 'success');

        } catch (error) {
            this.log(`❌ 继续启动飞行失败: ${error.message}`, 'error');
            console.error('继续启动飞行时出错:', error);
        }
    }

    /**
     * 停止飞行
     */
    stopFlight() {
        this.isFlying = false;

        // 隐藏无人机和阴影
        this.map.setLayoutProperty('drone-model', 'visibility', 'none');
        if (this.map.getLayer('drone-shadow')) {
            this.map.setLayoutProperty('drone-shadow', 'visibility', 'none');
        }

        // 清除飞行轨迹
        try {
            this.clearFlightTrail();
        } catch (error) {
            console.error('清除飞行轨迹失败:', error);
            // 手动清除轨迹数据
            this.flightTrail = [];
        }

        // 更新高度显示为停止状态，但不移除
        this.updateAltitudeDisplayStopped();

        const flightBtn = document.getElementById('start-flight-btn');
        if (flightBtn) {
            flightBtn.textContent = '开始飞行';
        }

        document.getElementById('flight-progress').textContent = '--';
        this.log('飞行已停止', 'info');
    }

    /**
     * 开始飞行动画
     */
    startFlightAnimation() {
        try {
            // 初始化飞行轨迹
            this.flightTrail = [];

            const startTime = Date.now();
            const pathLength = this.calculatePathLength();

            if (pathLength === 0) {
                this.log('⚠️ 路径长度为0，无法开始飞行动画', 'warning');
                return;
            }

            const flightDuration = (pathLength / this.flightSpeed) * 1000; // 转换为毫秒
            this.log(`🚁 开始飞行动画，路径长度: ${pathLength.toFixed(2)}km，预计时长: ${(flightDuration/1000).toFixed(1)}秒`, 'info');

            // 初始化飞行进度监控
            if (this.panelManager) {
                this.panelManager.addPerformanceLog('开始飞行模拟', 'info');
                this.panelManager.updateFlightProgress(0);
            }

            // 绑定方法到当前实例
            const self = this;

            const animate = () => {
                try {
                    if (!self.isFlying) return;

                    const elapsed = Date.now() - startTime;
                    self.flightProgress = Math.min(elapsed / flightDuration, 1);

                    if (self.flightProgress >= 1) {
                        // 飞行完成
                        self.flightProgress = 1;
                        if (self.endPoint) {
                            self.updateDronePosition(self.endPoint);
                        }

                        // 更新飞行进度监控
                        if (self.panelManager) {
                            self.panelManager.updateFlightProgress(100);
                            self.panelManager.addPerformanceLog('飞行模拟完成', 'success');
                        }

                        self.stopFlight();
                        self.log('飞行完成！', 'success');
                        return;
                    }

                    // 更新飞行进度监控
                    if (self.panelManager) {
                        const progressPercent = self.flightProgress * 100;
                        self.panelManager.updateFlightProgress(progressPercent);
                    }

                    // 计算当前位置
                    const currentPos = self.interpolatePathPosition(self.flightProgress);
                    if (currentPos) {
                        self.updateDronePosition(currentPos);

                        // 直接内联实现轨迹功能，避免方法调用问题
                        try {
                            // 添加到飞行轨迹
                            if (!self.flightTrail) {
                                self.flightTrail = [];
                            }

                            if (currentPos && typeof currentPos.lat === 'number' && typeof currentPos.lng === 'number' &&
                                !isNaN(currentPos.lat) && !isNaN(currentPos.lng)) {

                                self.flightTrail.push({
                                    lat: currentPos.lat,
                                    lng: currentPos.lng,
                                    alt: currentPos.alt || self.flightHeight,
                                    timestamp: Date.now()
                                });

                                // 限制轨迹长度
                                if (self.flightTrail.length > (self.maxTrailLength || 50)) {
                                    self.flightTrail.shift();
                                }

                                // 绘制飞行轨迹
                                self.drawFlightTrailInline();
                            }
                        } catch (trailError) {
                            // 静默处理轨迹错误，不影响飞行
                        }
                    }

                    // 更新UI
                    const progressPercent = (self.flightProgress * 100).toFixed(1);
                    const progressElement = document.getElementById('flight-progress');
                    if (progressElement) {
                        progressElement.textContent = `${progressPercent}%`;
                    }

                    requestAnimationFrame(animate);
                } catch (error) {
                    self.log(`❌ 飞行动画错误: ${error.message}`, 'error');
                    console.error('飞行动画错误:', error);
                    self.stopFlight();
                }
            };

            animate();
        } catch (error) {
            this.log(`❌ 启动飞行动画失败: ${error.message}`, 'error');
            console.error('启动飞行动画失败:', error);
        }
    }

    /**
     * 在路径上插值计算位置
     */
    interpolatePathPosition(progress) {
        // 验证路径有效性
        if (!this.currentPath || this.currentPath.length === 0) {
            this.log('⚠️ 路径为空，无法插值计算位置', 'warning');
            return this.startPoint || { lng: 139.7670, lat: 35.6814 };
        }

        if (this.currentPath.length === 1) {
            return this.currentPath[0];
        }

        // 验证进度值
        progress = Math.max(0, Math.min(1, progress || 0));

        const totalLength = this.calculatePathLength();
        if (totalLength === 0) {
            return this.currentPath[0];
        }

        const targetDistance = progress * totalLength;
        let currentDistance = 0;

        for (let i = 1; i < this.currentPath.length; i++) {
            const prev = this.currentPath[i - 1];
            const curr = this.currentPath[i];

            // 验证路径点有效性
            if (!prev || !curr || typeof prev.lng !== 'number' || typeof prev.lat !== 'number' ||
                typeof curr.lng !== 'number' || typeof curr.lat !== 'number') {
                this.log(`⚠️ 路径点无效: prev=${JSON.stringify(prev)}, curr=${JSON.stringify(curr)}`, 'warning');
                continue;
            }

            // 计算这一段的长度
            const R = 6371;
            const dLat = (curr.lat - prev.lat) * Math.PI / 180;
            const dLng = (curr.lng - prev.lng) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                     Math.cos(prev.lat * Math.PI / 180) * Math.cos(curr.lat * Math.PI / 180) *
                     Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            const segmentLength = R * c;

            if (currentDistance + segmentLength >= targetDistance) {
                // 在这个线段上
                const segmentProgress = segmentLength > 0 ? (targetDistance - currentDistance) / segmentLength : 0;

                // 插值计算高度
                const prevAlt = prev.alt || prev.altitude || this.flightHeight || 100;
                const currAlt = curr.alt || curr.altitude || this.flightHeight || 100;

                const interpolatedPos = {
                    lng: prev.lng + (curr.lng - prev.lng) * segmentProgress,
                    lat: prev.lat + (curr.lat - prev.lat) * segmentProgress,
                    alt: prevAlt + (currAlt - prevAlt) * segmentProgress
                };

                // 验证插值结果
                if (isNaN(interpolatedPos.lng) || isNaN(interpolatedPos.lat)) {
                    this.log(`⚠️ 插值计算结果无效，返回当前点`, 'warning');
                    return curr;
                }

                return interpolatedPos;
            }

            currentDistance += segmentLength;
        }

        // 返回最后一个点
        const lastPoint = this.currentPath[this.currentPath.length - 1];
        return lastPoint || this.endPoint || { lng: 139.7670, lat: 35.6814 };
    }

    /**
     * 更新无人机位置
     */
    updateDronePosition(lngLat) {
        try {
            // 验证坐标有效性
            if (!lngLat || typeof lngLat.lng !== 'number' || typeof lngLat.lat !== 'number' ||
                isNaN(lngLat.lng) || isNaN(lngLat.lat)) {
                this.log(`⚠️ 无效的无人机坐标: ${JSON.stringify(lngLat)}`, 'warning');
                return;
            }

            // 获取飞行高度，确保是有效数值
            let altitude = this.flightHeight || 100;
            if (lngLat.alt && !isNaN(lngLat.alt)) {
                altitude = lngLat.alt;
            } else if (lngLat.altitude && !isNaN(lngLat.altitude)) {
                altitude = lngLat.altitude;
            }

            // 确保高度是有效的正数
            altitude = Math.max(10, Math.min(1000, altitude));

            console.log(`🚁 更新无人机位置: lng=${lngLat.lng.toFixed(6)}, lat=${lngLat.lat.toFixed(6)}, alt=${altitude}m`);

            // 简化无人机数据，只使用基本属性
            const droneData = {
                type: 'FeatureCollection',
                features: [{
                    type: 'Feature',
                    properties: {
                        altitude: altitude
                    },
                    geometry: {
                        type: 'Point',
                        coordinates: [lngLat.lng, lngLat.lat]
                    }
                }]
            };

            const droneSource = this.map.getSource('drone');
            if (droneSource) {
                droneSource.setData(droneData);
            } else {
                // 如果无人机数据源不存在，重新创建
                this.log('⚠️ 无人机数据源丢失，正在重新创建...', 'warning');
                this.createDrone();
                // 重新尝试更新位置
                setTimeout(() => {
                    const newDroneSource = this.map.getSource('drone');
                    if (newDroneSource) {
                        newDroneSource.setData(droneData);
                    }
                }, 100);
            }

            // 更新高度显示文本
            this.updateAltitudeDisplay(altitude);

            // 触发无人机位置更新事件（用于物体检测）
            this.emit('dronePositionUpdate', {
                lng: lngLat.lng,
                lat: lngLat.lat,
                alt: altitude,
                timestamp: Date.now()
            });

        } catch (error) {
            this.log(`❌ 更新无人机位置失败: ${error.message}`, 'error');
            console.error('更新无人机位置时出错:', error);
        }
    }

    /**
     * 更新高度显示（在UI中显示当前飞行高度）
     */
    updateAltitudeDisplay(altitude) {
        try {
            // 在页面上显示当前高度
            let altitudeDisplay = document.getElementById('altitude-display');
            if (!altitudeDisplay) {
                // 创建高度显示元素
                altitudeDisplay = document.createElement('div');
                altitudeDisplay.id = 'altitude-display';
                altitudeDisplay.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(0, 0, 0, 0.8);
                    color: #fff;
                    padding: 10px 15px;
                    border-radius: 5px;
                    font-family: monospace;
                    font-size: 14px;
                    z-index: 1000;
                    border: 2px solid #ff4444;
                `;
                document.body.appendChild(altitudeDisplay);
            }

            altitudeDisplay.innerHTML = `
                🚁 飞行高度: <strong>${altitude.toFixed(1)}m</strong><br>
                📍 状态: 高空飞行中<br>
                ⬆️ 高于地面: <strong>${altitude.toFixed(0)}米</strong><br>
                🛡️ 安全飞行区域
            `;
        } catch (error) {
            console.error('更新高度显示失败:', error);
        }
    }

    /**
     * 更新高度显示为停止状态
     */
    updateAltitudeDisplayStopped() {
        try {
            let altitudeDisplay = document.getElementById('altitude-display');
            if (!altitudeDisplay) {
                // 创建高度显示元素
                altitudeDisplay = document.createElement('div');
                altitudeDisplay.id = 'altitude-display';
                altitudeDisplay.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(0, 0, 0, 0.8);
                    color: #fff;
                    padding: 10px 15px;
                    border-radius: 5px;
                    font-family: monospace;
                    font-size: 14px;
                    z-index: 1000;
                    border: 2px solid #888;
                `;
                document.body.appendChild(altitudeDisplay);
            }

            // 更新边框颜色为灰色表示停止状态
            altitudeDisplay.style.borderColor = '#888';

            altitudeDisplay.innerHTML = `
                🚁 无人机: <strong>已降落</strong><br>
                📍 状态: 飞行完成<br>
                ⬇️ 高度: <strong>地面</strong><br>
                ✅ 任务完成
            `;
        } catch (error) {
            console.error('更新停止状态显示失败:', error);
        }
    }

    /**
     * 完全清除高度显示
     */
    clearAltitudeDisplay() {
        const altitudeDisplay = document.getElementById('altitude-display');
        if (altitudeDisplay) {
            altitudeDisplay.remove();
        }
    }

    /**
     * 清除所有路径规划数据
     */
    clearPath() {
        // 停止飞行
        if (this.isFlying) {
            this.stopFlight();
        }

        // 清除起点终点
        if (this.map.getSource('start-point')) {
            this.map.removeLayer('start-point-layer');
            this.map.removeSource('start-point');
        }

        if (this.map.getSource('end-point')) {
            this.map.removeLayer('end-point-layer');
            this.map.removeSource('end-point');
        }

        // 清除路径
        this.clearPathVisualization();
        this.currentPath = [];

        // 重置状态
        this.startPoint = null;
        this.endPoint = null;

        // 重置UI
        document.getElementById('start-point-status').textContent = '未设置';
        document.getElementById('end-point-status').textContent = '未设置';
        document.getElementById('path-length').textContent = '--';
        document.getElementById('flight-progress').textContent = '--';

        const planBtn = document.getElementById('plan-path-btn');
        const flightBtn = document.getElementById('start-flight-btn');
        const showDetailsBtn = document.getElementById('show-details-btn');

        if (planBtn) planBtn.disabled = true;
        if (flightBtn) {
            flightBtn.disabled = true;
            flightBtn.textContent = '开始飞行';
        }
        if (showDetailsBtn) {
            showDetailsBtn.disabled = true;
        }

        // 禁用对比图表按钮并清除对比数据
        const comparisonChartBtn = document.getElementById('show-comparison-chart-btn');
        if (comparisonChartBtn) {
            comparisonChartBtn.disabled = true;
        }

        // 清除面板管理器中的对比数据
        if (this.panelManager) {
            this.panelManager.resetComparisonData();
        }

        // 清除详情数据
        this.lastPlanningDetails = null;

        // 隐藏详情面板
        this.hidePathDetails();

        // 禁用保护区信息按钮并隐藏面板
        const protectionZonesBtn = document.getElementById('show-protection-zones-btn');
        if (protectionZonesBtn) {
            protectionZonesBtn.disabled = true;
        }
        this.hideProtectionZonesInfo();

        this.log('路径已清除', 'info');
    }

    /**
     * 销毁系统
     */
    destroy() {
        if (this.map) {
            this.map.remove();
        }
        
        this.buildingData.clear();
        this.osmCache.clear();
        this.loadedTiles.clear();
        this.eventListeners.clear();
        
        this.log('ModernCityManager已销毁', 'info');
    }
}

// ==================== 路径规划算法类 ====================

class PathPlanner {
    constructor() {
        this.gridSize = 0.001; // 经纬度网格大小
    }

    /**
     * 规划路径主函数
     */
    async planPath(start, end, obstacles, algorithm = 'straight') {
        switch (algorithm) {
            case 'straight':
                return this.straightLinePath(start, end);
            case 'road-following':
                return this.roadFollowingPath(start, end);
            case 'astar':
                return this.aStarPath(start, end, obstacles);
            case 'rrt':
                return this.rrtPath(start, end, obstacles);
            case 'dijkstra':
                return this.dijkstraPath(start, end, obstacles);
            default:
                return this.straightLinePath(start, end);
        }
    }

    /**
     * 直线算法 (演示用)
     */
    straightLinePath(start, end) {
        console.log('使用直线算法规划路径...');

        const path = [];
        const steps = 20;

        for (let i = 0; i <= steps; i++) {
            const t = i / steps;
            path.push({
                lng: start.lng + (end.lng - start.lng) * t,
                lat: start.lat + (end.lat - start.lat) * t,
                alt: start.alt
            });
        }

        console.log(`直线算法完成，生成 ${path.length} 个路径点`);
        return path;
    }

    /**
     * 沿路飞行算法
     * 创建L型路径，模拟沿着城市道路飞行
     */
    roadFollowingPath(start, end) {
        console.log('使用沿路飞行算法规划路径...');

        const path = [];
        const steps = 10;

        // 创建L型路径：先水平后垂直
        const midPoint = {
            lng: end.lng,
            lat: start.lat,
            alt: start.alt
        };

        // 第一段：起点到中间点（水平）
        for (let i = 0; i <= steps; i++) {
            const t = i / steps;
            path.push({
                lng: start.lng + (midPoint.lng - start.lng) * t,
                lat: start.lat,
                alt: start.alt
            });
        }

        // 第二段：中间点到终点（垂直）
        for (let i = 1; i <= steps; i++) {
            const t = i / steps;
            path.push({
                lng: midPoint.lng,
                lat: midPoint.lat + (end.lat - midPoint.lat) * t,
                alt: start.alt
            });
        }

        console.log(`沿路飞行算法完成，生成 ${path.length} 个路径点`);
        return path;
    }

    /**
     * A*算法 (真实实现)
     */
    aStarPath(start, end, obstacles) {
        console.log('使用A*算法规划路径...');

        try {
            // 网格参数
            const gridSize = 0.0005; // 约50米的网格
            const safetyDistance = 0.0002; // 约20米的安全距离

            // 计算网格边界
            const minLng = Math.min(start.lng, end.lng) - 0.002;
            const maxLng = Math.max(start.lng, end.lng) + 0.002;
            const minLat = Math.min(start.lat, end.lat) - 0.002;
            const maxLat = Math.max(start.lat, end.lat) + 0.002;

            // 网格尺寸
            const gridWidth = Math.ceil((maxLng - minLng) / gridSize);
            const gridHeight = Math.ceil((maxLat - minLat) / gridSize);

            console.log(`A*网格: ${gridWidth} x ${gridHeight}`);

            // 创建网格
            const grid = Array(gridHeight).fill().map(() => Array(gridWidth).fill(0));

            // 标记障碍物
            obstacles.forEach(obstacle => {
                const gridX = Math.floor((obstacle.lng - minLng) / gridSize);
                const gridY = Math.floor((obstacle.lat - minLat) / gridSize);

                // 在障碍物周围创建安全区域
                const safetyRadius = Math.ceil(safetyDistance / gridSize);
                for (let dy = -safetyRadius; dy <= safetyRadius; dy++) {
                    for (let dx = -safetyRadius; dx <= safetyRadius; dx++) {
                        const x = gridX + dx;
                        const y = gridY + dy;
                        if (x >= 0 && x < gridWidth && y >= 0 && y < gridHeight) {
                            grid[y][x] = 1; // 标记为障碍物
                        }
                    }
                }
            });

            // 转换坐标到网格
            const startGrid = {
                x: Math.floor((start.lng - minLng) / gridSize),
                y: Math.floor((start.lat - minLat) / gridSize)
            };
            const endGrid = {
                x: Math.floor((end.lng - minLng) / gridSize),
                y: Math.floor((end.lat - minLat) / gridSize)
            };

            // 确保起点和终点在网格内且不是障碍物
            if (startGrid.x < 0 || startGrid.x >= gridWidth || startGrid.y < 0 || startGrid.y >= gridHeight ||
                endGrid.x < 0 || endGrid.x >= gridWidth || endGrid.y < 0 || endGrid.y >= gridHeight) {
                throw new Error('起点或终点超出网格范围');
            }

            // 清除起点和终点的障碍物标记
            grid[startGrid.y][startGrid.x] = 0;
            grid[endGrid.y][endGrid.x] = 0;

            // A*算法核心
            const path = this.findAStarPath(grid, startGrid, endGrid);

            if (!path || path.length === 0) {
                throw new Error('A*算法未找到有效路径');
            }

            // 转换网格路径回地理坐标
            const geoPath = path.map(point => ({
                lng: minLng + point.x * gridSize,
                lat: minLat + point.y * gridSize
            }));

            console.log(`A*算法完成，路径点数: ${geoPath.length}`);
            return geoPath;

        } catch (error) {
            console.error('A*算法执行失败:', error);
            // 回退到简单路径
            return this.straightLinePath(start, end);
        }
    }

    /**
     * A*路径搜索核心算法
     */
    findAStarPath(grid, start, end) {
        const openSet = [];
        const closedSet = new Set();
        const cameFrom = new Map();
        const gScore = new Map();
        const fScore = new Map();

        // 初始化起点
        const startKey = `${start.x},${start.y}`;
        const endKey = `${end.x},${end.y}`;

        openSet.push(start);
        gScore.set(startKey, 0);
        fScore.set(startKey, this.heuristic(start, end));

        while (openSet.length > 0) {
            // 找到fScore最小的节点
            let current = openSet[0];
            let currentIndex = 0;
            for (let i = 1; i < openSet.length; i++) {
                const currentKey = `${openSet[i].x},${openSet[i].y}`;
                const bestKey = `${current.x},${current.y}`;
                if (fScore.get(currentKey) < fScore.get(bestKey)) {
                    current = openSet[i];
                    currentIndex = i;
                }
            }

            // 移除当前节点
            openSet.splice(currentIndex, 1);
            const currentKey = `${current.x},${current.y}`;
            closedSet.add(currentKey);

            // 检查是否到达终点
            if (current.x === end.x && current.y === end.y) {
                return this.reconstructPath(cameFrom, current);
            }

            // 检查邻居节点
            const neighbors = this.getNeighbors(current, grid);
            for (const neighbor of neighbors) {
                const neighborKey = `${neighbor.x},${neighbor.y}`;

                if (closedSet.has(neighborKey)) continue;

                const tentativeGScore = gScore.get(currentKey) + this.distance(current, neighbor);

                if (!openSet.some(n => n.x === neighbor.x && n.y === neighbor.y)) {
                    openSet.push(neighbor);
                } else if (tentativeGScore >= gScore.get(neighborKey)) {
                    continue;
                }

                cameFrom.set(neighborKey, current);
                gScore.set(neighborKey, tentativeGScore);
                fScore.set(neighborKey, tentativeGScore + this.heuristic(neighbor, end));
            }
        }

        return null; // 未找到路径
    }

    /**
     * 启发式函数（曼哈顿距离）
     */
    heuristic(a, b) {
        return Math.abs(a.x - b.x) + Math.abs(a.y - b.y);
    }

    /**
     * 计算两点间距离
     */
    distance(a, b) {
        return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2);
    }

    /**
     * 获取邻居节点
     */
    getNeighbors(node, grid) {
        const neighbors = [];
        const directions = [
            {x: -1, y: 0}, {x: 1, y: 0}, {x: 0, y: -1}, {x: 0, y: 1},
            {x: -1, y: -1}, {x: -1, y: 1}, {x: 1, y: -1}, {x: 1, y: 1}
        ];

        for (const dir of directions) {
            const x = node.x + dir.x;
            const y = node.y + dir.y;

            if (x >= 0 && x < grid[0].length && y >= 0 && y < grid.length && grid[y][x] === 0) {
                neighbors.push({x, y});
            }
        }

        return neighbors;
    }

    /**
     * 重构路径
     */
    reconstructPath(cameFrom, current) {
        const path = [current];
        let currentKey = `${current.x},${current.y}`;

        while (cameFrom.has(currentKey)) {
            current = cameFrom.get(currentKey);
            path.unshift(current);
            currentKey = `${current.x},${current.y}`;
        }

        return path;
    }

    /**
     * 简化的A*路径（备用方案）
     */
    simpleAStarPath(start, end, obstacles) {
        console.log('使用简化A*算法...');

        // 创建网格化路径，模拟A*的网格搜索
        const path = [];
        const steps = 15;

        // 计算方向向量
        const deltaLng = end.lng - start.lng;
        const deltaLat = end.lat - start.lat;

        // 创建阶梯状路径，模拟网格搜索的特点
        for (let i = 0; i <= steps; i++) {
            const t = i / steps;

            // 使用阶梯函数创建网格化效果
            const stepT = Math.floor(t * 8) / 8; // 8个网格段
            const smoothT = t; // 平滑过渡

            // 混合阶梯和平滑路径
            const mixedT = stepT * 0.7 + smoothT * 0.3;

            // 添加轻微的网格偏移
            const gridOffset = (Math.sin(t * Math.PI * 4) * 0.0005);

            path.push({
                lng: start.lng + deltaLng * mixedT + gridOffset,
                lat: start.lat + deltaLat * mixedT + gridOffset,
                alt: start.alt + Math.sin(t * Math.PI) * 20 // 轻微的高度变化
            });
        }

        console.log(`A*算法完成，生成 ${path.length} 个路径点`);
        return path;
    }

    /**
     * RRT算法 (改进版)
     */
    rrtPath(start, end, obstacles) {
        console.log('使用RRT算法规划路径...');

        // 模拟RRT的随机树探索特性
        const path = [];
        const steps = 20;

        // 创建更复杂的随机路径，模拟RRT的探索过程
        let currentPoint = { ...start };
        path.push(currentPoint);

        for (let i = 1; i < steps; i++) {
            const t = i / steps;

            // 目标偏向：70%朝向目标，30%随机探索
            const goalBias = 0.7;
            const randomExploration = 1 - goalBias;

            // 计算朝向目标的方向
            const toGoal = {
                lng: end.lng - currentPoint.lng,
                lat: end.lat - currentPoint.lat
            };

            // 添加随机探索分量
            const randomAngle = Math.random() * 2 * Math.PI;
            const randomMagnitude = (Math.random() * 0.003) * randomExploration;

            const randomComponent = {
                lng: Math.cos(randomAngle) * randomMagnitude,
                lat: Math.sin(randomAngle) * randomMagnitude
            };

            // 步长控制
            const stepSize = 0.002 + Math.random() * 0.001;

            // 计算新点
            const newPoint = {
                lng: currentPoint.lng +
                     toGoal.lng * stepSize * goalBias +
                     randomComponent.lng,
                lat: currentPoint.lat +
                     toGoal.lat * stepSize * goalBias +
                     randomComponent.lat,
                alt: start.alt + Math.sin(t * Math.PI * 2) * 15 + Math.random() * 10
            };

            path.push(newPoint);
            currentPoint = newPoint;
        }

        // 确保最后一点是目标点
        path.push(end);

        console.log(`RRT算法完成，生成 ${path.length} 个路径点`);
        return path;
    }

    /**
     * Dijkstra算法 (改进版)
     */
    dijkstraPath(start, end, obstacles) {
        console.log('使用Dijkstra算法规划路径...');

        // 模拟Dijkstra的最短路径特性
        const path = [];
        const steps = 12;

        // 创建多段式路径，模拟Dijkstra的节点访问
        const waypoints = [];

        // 添加中间节点，模拟图搜索
        const midPoint1 = {
            lng: start.lng + (end.lng - start.lng) * 0.3,
            lat: start.lat + (end.lat - start.lat) * 0.3,
            alt: start.alt + 25
        };

        const midPoint2 = {
            lng: start.lng + (end.lng - start.lng) * 0.7,
            lat: start.lat + (end.lat - start.lat) * 0.7,
            alt: start.alt + 30
        };

        waypoints.push(start, midPoint1, midPoint2, end);

        // 在每对waypoint之间创建平滑路径
        for (let w = 0; w < waypoints.length - 1; w++) {
            const segmentStart = waypoints[w];
            const segmentEnd = waypoints[w + 1];
            const segmentSteps = Math.floor(steps / (waypoints.length - 1));

            for (let i = 0; i <= segmentSteps; i++) {
                const t = i / segmentSteps;
                const smoothT = this.smoothStep(t); // 平滑插值

                path.push({
                    lng: segmentStart.lng + (segmentEnd.lng - segmentStart.lng) * smoothT,
                    lat: segmentStart.lat + (segmentEnd.lat - segmentStart.lat) * smoothT,
                    alt: segmentStart.alt + (segmentEnd.alt - segmentStart.alt) * smoothT
                });
            }
        }

        console.log(`Dijkstra算法完成，生成 ${path.length} 个路径点`);
        return path;
    }

    /**
     * 创建绕行路径
     */
    createDetourPath(start, end) {
        const path = [];
        const steps = 8;

        // 创建弧形绕行路径
        const midLng = (start.lng + end.lng) / 2;
        const midLat = (start.lat + end.lat) / 2;
        const offset = 0.005; // 绕行偏移

        const detourPoint = {
            lng: midLng + offset,
            lat: midLat + offset,
            alt: start.alt
        };

        // 第一段：起点到绕行点
        for (let i = 0; i <= steps; i++) {
            const t = i / steps;
            path.push({
                lng: start.lng + (detourPoint.lng - start.lng) * t,
                lat: start.lat + (detourPoint.lat - start.lat) * t,
                alt: start.alt
            });
        }

        // 第二段：绕行点到终点
        for (let i = 1; i <= steps; i++) {
            const t = i / steps;
            path.push({
                lng: detourPoint.lng + (end.lng - detourPoint.lng) * t,
                lat: detourPoint.lat + (end.lat - detourPoint.lat) * t,
                alt: start.alt
            });
        }

        return path;
    }

    /**
     * 创建优化路径
     */
    createOptimizedPath(start, end) {
        const path = [];
        const steps = 12;

        // 创建平滑的曲线路径
        for (let i = 0; i <= steps; i++) {
            const t = i / steps;
            const smoothT = this.smoothStep(t); // 平滑插值

            path.push({
                lng: start.lng + (end.lng - start.lng) * smoothT,
                lat: start.lat + (end.lat - start.lat) * smoothT,
                alt: start.alt
            });
        }

        return path;
    }

    /**
     * 平滑插值函数
     */
    smoothStep(t) {
        return t * t * (3 - 2 * t);
    }

    /**
     * 显示路径规划详情面板
     */
    showPathDetails() {
        console.log('showPathDetails 被调用');
        this.log('🔍 尝试显示路径详情面板...', 'info');

        if (!this.lastPlanningDetails) {
            this.log('没有可显示的路径规划详情', 'warning');
            return;
        }

        console.log('lastPlanningDetails:', this.lastPlanningDetails);

        const panel = document.getElementById('path-details-panel');
        const content = document.getElementById('path-details-content');

        console.log('panel:', panel, 'content:', content);

        if (!panel || !content) {
            this.log('详情面板元素未找到', 'error');
            return;
        }

        // 生成详情内容
        const details = this.lastPlanningDetails;
        const response = details.response;

        content.innerHTML = this.generateDetailsHTML(details);

        // 显示面板
        panel.style.display = 'block';

        this.log('路径规划详情已显示', 'info');
    }

    /**
     * 隐藏路径规划详情面板
     */
    hidePathDetails() {
        const panel = document.getElementById('path-details-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * 显示保护区详情面板
     */
    showProtectionZonesInfo() {
        console.log('showProtectionZonesInfo 被调用');
        this.log('🛡️ 尝试显示保护区详情面板...', 'info');

        if (!this.lastPlanningDetails) {
            this.log('没有可显示的路径规划详情', 'warning');
            return;
        }

        const response = this.lastPlanningDetails.response;
        if (!response.protectionZonesInfo) {
            this.log('当前路径规划结果中没有保护区信息', 'warning');
            return;
        }

        const panel = document.getElementById('protection-zones-details-panel');
        const content = document.getElementById('protection-zones-details-content');

        if (!panel || !content) {
            this.log('保护区详情面板元素未找到', 'error');
            return;
        }

        // 生成保护区信息内容
        content.innerHTML = this.generateProtectionZonesHTML(response.protectionZonesInfo);

        // 显示面板
        panel.style.display = 'block';

        this.log('保护区详情面板已显示', 'info');
    }

    /**
     * 隐藏保护区详情面板
     */
    hideProtectionZonesInfo() {
        const panel = document.getElementById('protection-zones-details-panel');
        if (panel) {
            panel.style.display = 'none';
            this.log('保护区详情面板已隐藏', 'info');
        }
    }

    /**
     * 显示公式详情（专门用于改进分簇算法）
     */
    showFormulaDetails() {
        console.log('showFormulaDetails 被调用');
        this.log('🧮 尝试显示公式详情面板...', 'info');

        if (!this.lastPlanningDetails) {
            this.log('没有可显示的路径详情', 'warning');
            return;
        }

        console.log('lastPlanningDetails:', this.lastPlanningDetails);

        const response = this.lastPlanningDetails.response;
        const isImprovedCluster = this.lastPlanningDetails.algorithm === 'ImprovedClusterBased' ||
                                 (response.metadata && response.metadata.algorithm_type === 'improved_cluster_based');

        console.log('algorithm:', this.lastPlanningDetails.algorithm);
        console.log('isImprovedCluster:', isImprovedCluster);

        if (!isImprovedCluster) {
            this.log('公式详情仅适用于改进分簇算法', 'warning');
            return;
        }

        const panel = document.getElementById('path-details-panel');
        const content = document.getElementById('path-details-content');

        if (!panel || !content) {
            this.log('详情面板元素未找到', 'error');
            return;
        }

        // 生成专门的公式详情HTML
        const formulaHTML = `
            <div class="detail-section">
                <h4>🧮 改进分簇算法 - 完整公式实现过程</h4>
                <div style="font-size: 11px; line-height: 1.4; color: #cccccc;">
                    <p>以下是严格按照项目需求文档实现的14个核心公式的完整计算过程：</p>
                </div>
            </div>
            ${this.generateFormulaImplementationHTML(response)}
        `;

        content.innerHTML = formulaHTML;
        panel.style.display = 'block';

        this.log('公式实现详情已显示', 'info');
    }

    /**
     * 生成保护区信息HTML
     */
    generateProtectionZonesHTML(protectionZonesInfo) {
        if (!protectionZonesInfo || !protectionZonesInfo.involved_zones) {
            return '';
        }

        const summary = protectionZonesInfo.summary || {};
        const breakdown = protectionZonesInfo.collision_cost_breakdown || {};
        const referenceValues = protectionZonesInfo.reference_values || {};

        let html = `
            <div class="detail-section">
                <h4>🛡️ 保护区影响分析</h4>
                <div class="protection-zones-summary">
                    <div class="detail-item">涉及保护区: <span class="detail-value">${summary.zones_count || 0} 个</span></div>
                    <div class="detail-item">影响航点: <span class="detail-value">${summary.waypoints_affected || 0} / ${summary.total_waypoints || 0}</span></div>
                    <div class="detail-item">覆盖率: <span class="detail-value">${summary.coverage_percentage || 0}%</span></div>
                    <div class="detail-item">总估计碰撞代价: <span class="detail-value">${protectionZonesInfo.total_estimated_collision_cost || 0}</span></div>
                </div>
            </div>
        `;

        // 参考值信息
        if (Object.keys(referenceValues).length > 0) {
            html += `
                <div class="detail-section">
                    <h4>📊 参考值计算</h4>
                    <div class="reference-values">
            `;

            if (referenceValues.collision_cost_reference) {
                html += `<div class="detail-item">碰撞代价参考值: <span class="detail-value">${referenceValues.collision_cost_reference}</span></div>`;
            }
            if (referenceValues.orientation_cost_reference) {
                html += `<div class="detail-item">转向成本参考值: <span class="detail-value">${referenceValues.orientation_cost_reference}</span></div>`;
            }
            if (referenceValues.risk_reference) {
                html += `<div class="detail-item">风险参考值: <span class="detail-value">${referenceValues.risk_reference}</span></div>`;
            }

            html += `
                    </div>
                </div>
            `;
        }

        // 保护区详细信息
        if (Object.keys(breakdown).length > 0) {
            html += `
                <div class="detail-section">
                    <h4>🏢 保护区详细信息</h4>
                    <div class="protection-zones-breakdown">
            `;

            Object.entries(breakdown).forEach(([zoneId, zoneData]) => {
                const affectedWaypoints = zoneData.waypoints_affected || [];
                html += `
                    <div class="zone-item" style="margin-bottom: 10px; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 4px;">
                        <div class="zone-header" style="font-weight: bold; color: #4fc3f7;">
                            ${zoneData.zone_name} (${zoneData.zone_type})
                        </div>
                        <div class="zone-details" style="font-size: 0.9em; color: #cccccc;">
                            <div>碰撞代价贡献: ${zoneData.total_cost.toFixed(4)}</div>
                            <div>平均碰撞代价: ${zoneData.average_crash_cost.toFixed(6)}/m²</div>
                            <div>影响航点: ${affectedWaypoints.length} 个 (${affectedWaypoints.join(', ')})</div>
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        // 航点级别的详细信息（可折叠）
        if (protectionZonesInfo.involved_zones && protectionZonesInfo.involved_zones.length > 0) {
            html += `
                <div class="detail-section">
                    <h4>📍 航点级别影响 <button onclick="this.nextElementSibling.style.display = this.nextElementSibling.style.display === 'none' ? 'block' : 'none'" style="background: none; border: 1px solid #666; color: #ccc; padding: 2px 6px; border-radius: 3px; cursor: pointer;">展开/收起</button></h4>
                    <div class="waypoint-zones-details" style="display: none; max-height: 300px; overflow-y: auto;">
            `;

            protectionZonesInfo.involved_zones.forEach((waypointInfo, index) => {
                const coords = waypointInfo.waypoint_coords;
                html += `
                    <div class="waypoint-item" style="margin-bottom: 8px; padding: 6px; background: rgba(255,255,255,0.03); border-radius: 3px;">
                        <div style="font-weight: bold; color: #81c784;">航点 ${waypointInfo.waypoint_index}</div>
                        <div style="font-size: 0.8em; color: #aaa;">
                            坐标: (${coords.lat.toFixed(6)}, ${coords.lng.toFixed(6)}, ${coords.alt}m)
                        </div>
                        <div style="font-size: 0.8em; color: #ccc;">
                            总代价: ${waypointInfo.total_cost} | 涉及 ${waypointInfo.zones.length} 个保护区
                        </div>
                `;

                waypointInfo.zones.forEach(zone => {
                    html += `
                        <div style="margin-left: 15px; font-size: 0.7em; color: #bbb;">
                            • ${zone.zone_name}: 距离${zone.distance_to_center}m, 代价${zone.collision_cost.toFixed(4)}
                        </div>
                    `;
                });

                html += `</div>`;
            });

            html += `
                    </div>
                </div>
            `;
        }

        return html;
    }

    /**
     * 生成详情HTML内容
     */
    generateDetailsHTML(details) {
        const response = details.response;
        const timestamp = details.timestamp.toLocaleString();

        // 检查是否是改进分簇算法
        const isImprovedCluster = details.algorithm === '改进分簇算法' ||
                                 (response.metadata && response.metadata.algorithm_type === 'improved_cluster_based');

        let html = `
            <div class="detail-section">
                <h4>📊 基本信息</h4>
                <div class="detail-item">算法: <span class="detail-value">${details.algorithm}</span></div>
                <div class="detail-item">规划时间: <span class="detail-value">${timestamp}</span></div>
                <div class="detail-item">路径长度: <span class="detail-value">${(details.pathLength / 1000).toFixed(2)} km</span></div>
                <div class="detail-item">路径点数: <span class="detail-value">${details.pathPoints}</span></div>
                <div class="detail-item">建筑物数量: <span class="detail-value">${details.buildingCount}</span></div>
            </div>
        `;

        // 添加保护区信息展示
        if (response.protectionZonesInfo) {
            html += this.generateProtectionZonesHTML(response.protectionZonesInfo);
        }

        // 如果是改进分簇算法，添加完整的公式实现过程
        if (isImprovedCluster) {
            html += this.generateFormulaImplementationHTML(response);
        }

        // 算法执行详情
        if (response.execution_time) {
            html += `
                <div class="detail-section">
                    <h4>⏱️ 执行性能</h4>
                    <div class="detail-item">执行时间: <span class="detail-value">${response.execution_time.toFixed(3)} 秒</span></div>
                </div>
            `;
        }

        // 路径质量信息
        if (response.quality) {
            html += `
                <div class="detail-section">
                    <h4>📈 路径质量</h4>
                    <div class="detail-item">风险评分: <span class="detail-value">${(response.quality.risk_score || 0).toFixed(2)}</span></div>
                    <div class="detail-item">平滑度: <span class="detail-value">${(response.quality.smoothness || 0).toFixed(2)}</span></div>
                    <div class="detail-item">安全性: <span class="detail-value">${(response.quality.safety || 0).toFixed(2)}</span></div>
                </div>
            `;
        }

        // 算法特定信息
        if (response.algorithm_info) {
            html += `
                <div class="detail-section">
                    <h4>🔧 算法详情</h4>
            `;

            const info = response.algorithm_info;
            if (info.clusters_generated) {
                html += `<div class="detail-item">生成分簇数: <span class="detail-value">${info.clusters_generated}</span></div>`;
            }
            if (info.paths_evaluated) {
                html += `<div class="detail-item">评估路径数: <span class="detail-value">${info.paths_evaluated}</span></div>`;
            }
            if (info.optimal_path_id) {
                html += `<div class="detail-item">最优路径ID: <span class="detail-value">${info.optimal_path_id}</span></div>`;
            }
            if (info.total_cost) {
                html += `<div class="detail-item">总代价: <span class="detail-value">${info.total_cost.toFixed(2)}</span></div>`;
            }

            html += `</div>`;
        }

        // 详细日志
        if (response.logs && response.logs.length > 0) {
            html += `
                <div class="detail-section">
                    <h4>📝 执行日志</h4>
                    <div style="max-height: 200px; overflow-y: auto; font-size: 10px;">
            `;

            response.logs.forEach(log => {
                const logClass = this.getLogClass(log.level || 'info');
                html += `<div class="detail-item ${logClass}">[${log.timestamp || ''}] ${log.message}</div>`;
            });

            html += `</div></div>`;
        }

        // 错误信息
        if (response.warnings && response.warnings.length > 0) {
            html += `
                <div class="detail-section">
                    <h4>⚠️ 警告信息</h4>
            `;
            response.warnings.forEach(warning => {
                html += `<div class="detail-item warning">${warning}</div>`;
            });
            html += `</div>`;
        }

        return html;
    }

    /**
     * 生成公式实现过程HTML内容（改进分簇算法专用）
     */
    generateFormulaImplementationHTML(response) {
        let html = `
            <div class="detail-section">
                <h4>🧮 公式实现过程详解</h4>
                <div style="font-family: 'Courier New', monospace; font-size: 10px; line-height: 1.3;">
        `;

        // 第一步：初始路径集生成（81条路径）
        html += this.generateInitialPathSetHTML(response);

        // 第二步：固定空间分簇（13个分簇）
        html += this.generateClusteringHTML(response);

        // 第三步：四个核心指标计算
        html += this.generateCoreMetricsHTML(response);

        // 第四步：目标函数计算
        html += this.generateObjectiveFunctionHTML(response);

        // 第五步：最优簇选择
        html += this.generateOptimalClusterHTML(response);

        html += `
                </div>
            </div>
        `;

        return html;
    }

    /**
     * 生成初始路径集HTML
     */
    generateInitialPathSetHTML(response) {
        const metadata = response.metadata || {};
        const details = metadata.algorithm_details || {};
        const initialPaths = metadata.initial_paths_count || 81;
        const gridPoints = metadata.grid_points || 0;
        const buildingCount = metadata.building_count || 0;

        return `
            <div style="margin-bottom: 15px; padding: 8px; background: rgba(0, 100, 200, 0.1); border-left: 3px solid #0064c8;">
                <strong>📍 步骤1: 初始路径集生成</strong><br>
                <span style="color: #00d4ff;">按照需求文档要求生成 ${initialPaths} 条初始路径</span><br>
                • 起飞方向: 9个方向 (每10度一个，总计90度扇形)<br>
                • 高度层: 9个高度层<br>
                • 路径总数: 9 × 9 = ${initialPaths} 条路径<br>
                • 中转点区域: 起点终点连线中垂线，30%长度参考线，向两侧延伸25米<br>
                • 生成方法: ${details.path_generation_method || 'A*算法生成从起点经中转点至终点的路径'}<br>
                • 环境信息: ${buildingCount} 个建筑物障碍物，${gridPoints} 个网格格点<br>
                <span style="color: #00ff88;">✅ 成功生成 ${initialPaths} 条初始路径</span>
            </div>
        `;
    }

    /**
     * 生成分簇过程HTML
     */
    generateClusteringHTML(response) {
        const metadata = response.metadata || {};
        const details = metadata.algorithm_details || {};
        const clusters = metadata.clusters_count || 13;
        const cluster3x3 = details.cluster_3x3_count || 9;
        const cluster4x4 = details.cluster_4x4_count || 4;
        const totalAssignments = details.total_path_assignments || 145;

        return `
            <div style="margin-bottom: 15px; padding: 8px; background: rgba(100, 0, 200, 0.1); border-left: 3px solid #6400c8;">
                <strong>🔗 步骤2: 固定空间分簇</strong><br>
                <span style="color: #00d4ff;">按照需求文档要求进行固定空间分簇</span><br>
                • 3×3簇: ${cluster3x3}个 [(1,1)-(3,3), (4,1)-(6,3), (7,1)-(9,3), (1,4)-(3,6), (4,4)-(6,6), (7,4)-(9,6), (1,7)-(3,9), (4,7)-(6,9), (7,7)-(9,9)]<br>
                • 4×4簇: ${cluster4x4}个 [(2,2)-(5,5), (5,2)-(8,5), (2,5)-(5,8), (5,5)-(8,8)]<br>
                • 总分簇数: ${cluster3x3} + ${cluster4x4} = ${clusters} 个分簇<br>
                • 分配方法: ${details.clustering_method || '根据路径中转点的空间分布进行分配'}<br>
                • 总分配次数: ${totalAssignments} 次<br>
                <span style="color: #00ff88;">✅ 成功创建 ${clusters} 个固定空间分簇</span>
            </div>
        `;
    }

    /**
     * 生成核心指标计算HTML
     */
    generateCoreMetricsHTML(response) {
        const pathLength = response.pathLength || 0;
        const executionTime = response.executionTime || 0;
        const metadata = response.metadata || {};

        return `
            <div style="margin-bottom: 15px; padding: 8px; background: rgba(200, 100, 0, 0.1); border-left: 3px solid #c86400;">
                <strong>📐 步骤3: 四个核心指标计算</strong><br>
                <span style="color: #00d4ff;">严格按照需求文档中的14个公式计算</span><br><br>

                <strong>公式1-2: 路径长度计算</strong><br>
                • 公式1: Length = Σ√[(x<sub>i+1</sub>-x<sub>i</sub>)² + (y<sub>i+1</sub>-y<sub>i</sub>)² + (z<sub>i+1</sub>-z<sub>i</sub>)²]<br>
                • 计算结果: ${pathLength.toFixed(2)} 米<br>
                • 公式2: Length<sub>manhattan</sub> = 3 × (|x<sub>end</sub> - x<sub>start</sub>| + |y<sub>end</sub> - y<sub>start</sub>| + |z<sub>end</sub> - z<sub>start</sub>|)<br><br>

                <strong>公式3-4: 转向成本计算</strong><br>
                • 公式3: OrientAdjustCost = Σ Δθ<br>
                • 公式4: OrientAdjustCost<sub>reference</sub> = 0.3 × (n - 2) × Δθ<sub>max</sub><br>
                • 转向成本: ${metadata.turning_cost || 0} (弧度)<br><br>

                <strong>公式5-9: 风险模型</strong><br>
                • 公式5: ZoneRisk = e^(ln(0.05)/d<sub>max</sub> × d)<br>
                • 公式6: PointRisk(x<sub>i</sub>,y<sub>i</sub>,z<sub>i</sub>) = Σ BuildingRisk<br>
                • 公式7: RiskSum = Σ PointRisk(x<sub>i</sub>,y<sub>i</sub>,z<sub>i</sub>)<br>
                • 公式8: RiskDensity = RiskSum / Length<br>
                • 公式9: RiskSum<sub>reference</sub> = Σ RiskSum / n<br>
                • 风险值: ${metadata.risk_value || 0}<br><br>

                <strong>公式10-13: 碰撞代价模型</strong><br>
                • 公式10: PointActualCrashCost = Σ ObstacleCrashCost<br>
                • 公式11: PointEstimateCrashCost = Σ(AverageCrashCost × S)<br>
                • 公式12: RoadCrashCost = Σ PointEstimateCrashCost<br>
                • 公式13: RoadCrashCost<sub>reference</sub> = (Σ AverageCrashCost / ProtectzoneNum) × 300<br>
                • 碰撞代价: ${metadata.collision_cost || 0}<br><br>

                <strong>路径平滑处理</strong><br>
                • 平滑方法: ${metadata.algorithm_details?.smoothing_method || '三次样条插值平滑'}<br>
                • 平滑路径数: ${metadata.algorithm_details?.smoothed_paths || 81} 条路径<br>

                <span style="color: #00ff88;">✅ 四个核心指标计算完成 (耗时: ${executionTime.toFixed(3)}秒)</span>
            </div>
        `;
    }

    /**
     * 生成目标函数计算HTML
     */
    generateObjectiveFunctionHTML(response) {
        const metadata = response.metadata || {};
        const finalCost = metadata.final_cost || 0;

        return `
            <div style="margin-bottom: 15px; padding: 8px; background: rgba(200, 0, 100, 0.1); border-left: 3px solid #c80064;">
                <strong>🎯 步骤4: 目标函数计算</strong><br>
                <span style="color: #00d4ff;">使用公式14-15计算最终代价</span><br><br>

                <strong>公式14: 目标函数</strong><br>
                PathFinalCost = α×(RiskSum/RiskSum<sub>ref</sub>) + β×(RoadCrashCost/RoadCrashCost<sub>ref</sub>) + γ×(Length/Length<sub>manhattan</sub>) + δ×(OrientAdjustCost/OrientAdjustCost<sub>ref</sub>)<br><br>

                <strong>公式15: 动态权重模型</strong><br>
                • α = 0.6 × (1 - e^(-k × RiskDensity))<br>
                • β = 0.3 × (1 - e^(-k × RiskDensity))<br>
                • γ = 0.7 × e^(-k × RiskDensity) + 0.1<br>
                • δ = 0.2 × e^(-k × RiskDensity)<br>
                • k = 0.1 (调节参数)<br><br>

                <strong>计算结果:</strong><br>
                • 最终代价: ${finalCost.toFixed(4)}<br>
                • 风险权重α: 动态计算<br>
                • 碰撞权重β: 动态计算<br>
                • 长度权重γ: 动态计算<br>
                • 转向权重δ: 动态计算<br>

                <span style="color: #00ff88;">✅ 目标函数计算完成</span>
            </div>
        `;
    }

    /**
     * 生成最优簇选择HTML
     */
    generateOptimalClusterHTML(response) {
        const metadata = response.metadata || {};
        const details = metadata.algorithm_details || {};
        const clusters = metadata.clusters_count || 13;
        const pathSwitches = metadata.path_switches || 0;
        const selectedPathId = details.selected_path_id || 0;
        const selectedCluster = details.selected_cluster || 'unknown';
        const finalCost = metadata.final_cost || 0;

        return `
            <div style="margin-bottom: 15px; padding: 8px; background: rgba(0, 200, 100, 0.1); border-left: 3px solid #00c864;">
                <strong>🏆 步骤5: 最优簇选择与换路策略</strong><br>
                <span style="color: #00d4ff;">使用公式16和换路策略选择最优路径</span><br><br>

                <strong>公式16: 簇代价计算</strong><br>
                ClusterFinalCost = (Σ PathFinalCost) / PathCount<br><br>

                <strong>最优簇选择:</strong><br>
                • 候选簇数量: ${clusters} 个<br>
                • 领导者种群: 最终代价平均值最低的簇<br>
                • 选中簇: ${selectedCluster}<br>
                • 选中路径ID: ${selectedPathId}<br>
                • 最终代价: ${finalCost.toFixed(4)}<br>
                • 优化方法: ${details.optimization_method || '公式14-15动态权重目标函数'}<br><br>

                <strong>换路策略:</strong><br>
                • 碰撞代价异常检测: 连续5个航点实际碰撞代价超过估算值20%<br>
                • 梯度场引导: 选择梯度降低方向的邻近簇<br>
                • 路径重规划: 生成当前位置到目标路径最近航点的新路径<br>
                • 换路次数: ${pathSwitches} 次<br>

                <span style="color: #00ff88;">✅ 最优路径选择完成</span>
            </div>
        `;
    }

    /**
     * 获取日志样式类
     */
    getLogClass(level) {
        switch (level.toLowerCase()) {
            case 'success': return 'success';
            case 'warning': return 'warning';
            case 'error': return 'error';
            case 'info': return 'info';
            default: return '';
        }
    }

    /**
     * 添加点到飞行轨迹
     */
    addToFlightTrail(position) {
        try {
            // 验证位置有效性
            if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number' ||
                isNaN(position.lat) || isNaN(position.lng)) {
                console.warn('无效的轨迹位置:', position);
                return;
            }

            // 确保轨迹数组存在
            if (!this.flightTrail) {
                this.flightTrail = [];
            }

            this.flightTrail.push({
                lat: position.lat,
                lng: position.lng,
                alt: position.alt || this.flightHeight,
                timestamp: Date.now()
            });

            // 限制轨迹长度
            if (this.flightTrail.length > this.maxTrailLength) {
                this.flightTrail.shift();
            }
        } catch (error) {
            console.error('添加飞行轨迹点失败:', error);
        }
    }

    /**
     * 内联绘制飞行轨迹（用于动画中）
     */
    drawFlightTrailInline() {
        try {
            if (!this.map || !this.flightTrail || this.flightTrail.length < 2) {
                return;
            }

            const validTrail = this.flightTrail.filter(point =>
                point && typeof point.lng === 'number' && typeof point.lat === 'number' &&
                !isNaN(point.lng) && !isNaN(point.lat)
            );

            if (validTrail.length < 2) return;

            const trailData = {
                type: 'Feature',
                properties: {},
                geometry: {
                    type: 'LineString',
                    coordinates: validTrail.map(point => [point.lng, point.lat])
                }
            };

            if (this.map.getSource('flight-trail')) {
                this.map.getSource('flight-trail').setData(trailData);
            } else {
                this.map.addSource('flight-trail', {
                    type: 'geojson',
                    data: trailData
                });

                if (!this.map.getLayer('flight-trail-line')) {
                    this.map.addLayer({
                        id: 'flight-trail-line',
                        type: 'line',
                        source: 'flight-trail',
                        layout: {
                            'line-join': 'round',
                            'line-cap': 'round'
                        },
                        paint: {
                            'line-color': '#ff6b6b',
                            'line-width': 4,
                            'line-opacity': 0.8,
                            'line-dasharray': [2, 1]
                        }
                    });
                }
            }
        } catch (error) {
            // 静默处理错误
        }
    }

    /**
     * 绘制飞行轨迹
     */
    drawFlightTrail() {
        try {
            // 检查地图和轨迹有效性
            if (!this.map || !this.flightTrail || this.flightTrail.length < 2) {
                return;
            }

            // 验证轨迹点有效性
            const validTrail = this.flightTrail.filter(point =>
                point && typeof point.lng === 'number' && typeof point.lat === 'number' &&
                !isNaN(point.lng) && !isNaN(point.lat)
            );

            if (validTrail.length < 2) {
                return;
            }

            // 使用Mapbox GL JS的方式添加轨迹
            const trailData = {
                type: 'Feature',
                properties: {},
                geometry: {
                    type: 'LineString',
                    coordinates: validTrail.map(point => [point.lng, point.lat])
                }
            };

            if (this.map.getSource('flight-trail')) {
                this.map.getSource('flight-trail').setData(trailData);
            } else {
                // 添加轨迹数据源
                this.map.addSource('flight-trail', {
                    type: 'geojson',
                    data: trailData
                });

                // 添加轨迹图层
                if (!this.map.getLayer('flight-trail-line')) {
                    this.map.addLayer({
                        id: 'flight-trail-line',
                        type: 'line',
                        source: 'flight-trail',
                        layout: {
                            'line-join': 'round',
                            'line-cap': 'round'
                        },
                        paint: {
                            'line-color': '#ff6b6b',
                            'line-width': 4,
                            'line-opacity': 0.8,
                            'line-dasharray': [2, 1]
                        }
                    });
                }
            }
        } catch (error) {
            console.error('绘制飞行轨迹失败:', error);
        }
    }

    /**
     * 绘制轨迹点
     */
    drawTrailPoints() {
        // 每5个点绘制一个标记
        const pointsToShow = this.flightTrail.filter((_, index) => index % 5 === 0);

        if (this.map.getSource('trail-points')) {
            this.map.getSource('trail-points').setData({
                type: 'FeatureCollection',
                features: pointsToShow.map((point, index) => ({
                    type: 'Feature',
                    properties: {
                        opacity: (index / pointsToShow.length) * 0.8 + 0.2
                    },
                    geometry: {
                        type: 'Point',
                        coordinates: [point.lng, point.lat]
                    }
                }))
            });
        } else {
            // 添加轨迹点数据源
            this.map.addSource('trail-points', {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: pointsToShow.map((point, index) => ({
                        type: 'Feature',
                        properties: {
                            opacity: (index / pointsToShow.length) * 0.8 + 0.2
                        },
                        geometry: {
                            type: 'Point',
                            coordinates: [point.lng, point.lat]
                        }
                    }))
                }
            });

            // 添加轨迹点图层
            this.map.addLayer({
                id: 'trail-points-layer',
                type: 'circle',
                source: 'trail-points',
                paint: {
                    'circle-radius': 3,
                    'circle-color': '#ff6b6b',
                    'circle-stroke-color': '#ffffff',
                    'circle-stroke-width': 1,
                    'circle-opacity': ['get', 'opacity']
                }
            });
        }
    }

    /**
     * 清除飞行轨迹
     */
    clearFlightTrail() {
        this.flightTrail = [];

        // 清除Mapbox图层
        if (this.map.getLayer('flight-trail-line')) {
            this.map.removeLayer('flight-trail-line');
        }
        if (this.map.getSource('flight-trail')) {
            this.map.removeSource('flight-trail');
        }

        if (this.map.getLayer('trail-points-layer')) {
            this.map.removeLayer('trail-points-layer');
        }
        if (this.map.getSource('trail-points')) {
            this.map.removeSource('trail-points');
        }
    }
}

// 导出
window.ModernCityManager = ModernCityManager;
window.PathPlanner = PathPlanner;
