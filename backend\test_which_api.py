#!/usr/bin/env python3
"""
测试哪个API被调用
"""

import requests
import json

def test_which_api():
    """测试哪个API被调用"""
    print("🔍 测试哪个API被调用...")
    
    try:
        # 调用API
        url = "http://127.0.0.1:5000/api/export_calculated_paths"
        data = {
            "timestamp": "2025-07-31T13:00:00",
            "request_type": "test_which_api"
        }
        
        print(f"📡 发送请求到: {url}")
        print(f"📦 请求数据: {data}")
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功!")
            print(f"📊 API返回数据结构:")
            
            # 检查返回的数据结构
            if 'data' in result:
                print("   🔍 发现'data'字段 - 这是旧的API实现")
                print("   🔍 可能调用的是export_all_paths_data函数")
            
            if 'filename' in result and 'source_json' in result:
                print("   🔍 发现'filename'和'source_json'字段 - 这是新的API实现")
                print("   🔍 调用的是app.py中的export_calculated_paths函数")
            
            # 显示所有字段
            print(f"   📋 所有字段: {list(result.keys())}")
            
            # 显示部分数据
            for key, value in result.items():
                if key == 'data':
                    print(f"   {key}: <大量数据，已省略>")
                else:
                    print(f"   {key}: {value}")
            
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('error', '未知错误')}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_which_api()
    if success:
        print("\n🎉 测试完成！")
    else:
        print("\n❌ 测试失败！")
