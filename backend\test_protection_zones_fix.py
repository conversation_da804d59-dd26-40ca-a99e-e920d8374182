#!/usr/bin/env python3
"""
测试保护区信息修复
"""

import glob
import json
import os

def test_protection_zones_in_json():
    """检查JSON文件中的保护区信息"""
    print("🔍 检查JSON文件中的保护区信息...")
    
    # 查找最新的JSON文件
    json_files = glob.glob('all_81_paths_data_*.json')
    if not json_files:
        print("❌ 没有找到JSON文件")
        return False
    
    latest_json = max(json_files, key=os.path.getctime)
    print(f"📂 检查JSON文件: {latest_json}")
    
    # 读取JSON数据
    with open(latest_json, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"\n🔍 检查保护区信息:")
    print(f"   总路径数: {len(data.get('all_paths', []))}")
    
    # 检查前几条路径的保护区信息
    for i, path in enumerate(data['all_paths'][:5]):
        path_id = path.get('path_id')
        zones_count = path.get('protection_zones_count', 0)
        zones_list = path.get('protection_zones_list', '')
        zones_types = path.get('protection_zones_types', '')
        
        print(f"   路径{path_id}: 保护区数量={zones_count}, 列表=\"{zones_list}\", 类型=\"{zones_types}\"")
        
        # 检查是否有protectionZonesInfo字段
        if 'protectionZonesInfo' in path:
            pzi = path['protectionZonesInfo']
            breakdown = pzi.get('collision_cost_breakdown', {})
            print(f"     -> protectionZonesInfo存在，breakdown有{len(breakdown)}个保护区")
        
        # 检查是否有metadata字段
        if 'metadata' in path:
            metadata = path['metadata']
            if 'protection_zones' in metadata:
                meta_zones = metadata['protection_zones']
                meta_breakdown = meta_zones.get('collision_cost_breakdown', {})
                print(f"     -> metadata.protection_zones存在，breakdown有{len(meta_breakdown)}个保护区")
    
    # 检查基准路径
    baseline_found = False
    for path in data['all_paths']:
        if path.get('path_id') == 'BASELINE_A*':
            baseline_found = True
            zones_count = path.get('protection_zones_count', 0)
            print(f"\n🎯 基准路径保护区信息: 数量={zones_count}")
            
            if 'metadata' in path and 'protection_zones' in path['metadata']:
                meta_zones = path['metadata']['protection_zones']
                breakdown = meta_zones.get('collision_cost_breakdown', {})
                print(f"   基准路径metadata中有{len(breakdown)}个保护区")
                for zone_id, zone_data in breakdown.items():
                    print(f"     - {zone_id}: {zone_data.get('zone_name', '未知')}")
            break
    
    if not baseline_found:
        print("\n❌ 未找到基准路径")
    
    return True

def test_protection_zones_extraction():
    """测试保护区信息提取函数"""
    print("\n🧪 测试保护区信息提取函数...")
    
    try:
        from export_all_paths_data import extract_protection_zones_info
        
        # 测试数据1：包含metadata格式的保护区信息
        test_data_1 = {
            'metadata': {
                'protection_zones': {
                    'collision_cost_breakdown': {
                        'tokyo_station': {
                            'zone_name': '东京站',
                            'zone_type': 'transport_hub',
                            'total_cost': 57200.0,
                            'average_crash_cost': 0.010345,
                            'waypoints_affected': [0, 1, 2, 3, 4, 5]
                        },
                        'imperial_palace': {
                            'zone_name': '皇居东御苑',
                            'zone_type': 'park',
                            'total_cost': 17430.0,
                            'average_crash_cost': 0.001585,
                            'waypoints_affected': [11, 12, 13, 14, 15, 16, 17, 18]
                        }
                    }
                }
            }
        }
        
        result_1 = extract_protection_zones_info(test_data_1)
        print(f"✅ 测试1结果:")
        print(f"   保护区数量: {result_1['protection_zones_count']}")
        print(f"   保护区列表: {result_1['protection_zones_list']}")
        print(f"   保护区类型: {result_1['protection_zones_types']}")
        print(f"   总代价: {result_1['total_protection_zone_cost']}")
        
        # 测试数据2：包含protectionZonesInfo格式的保护区信息
        test_data_2 = {
            'protectionZonesInfo': {
                'collision_cost_breakdown': {
                    'ginza_commercial': {
                        'zone_name': '银座商业区',
                        'zone_type': 'commercial',
                        'total_cost': 25000.0,
                        'average_crash_cost': 0.008234,
                        'waypoints_affected': [5, 6, 7, 8, 9]
                    }
                }
            }
        }
        
        result_2 = extract_protection_zones_info(test_data_2)
        print(f"\n✅ 测试2结果:")
        print(f"   保护区数量: {result_2['protection_zones_count']}")
        print(f"   保护区列表: {result_2['protection_zones_list']}")
        print(f"   保护区类型: {result_2['protection_zones_types']}")
        print(f"   总代价: {result_2['total_protection_zone_cost']}")
        
        # 测试数据3：空数据
        test_data_3 = {}
        result_3 = extract_protection_zones_info(test_data_3)
        print(f"\n✅ 测试3结果（空数据）:")
        print(f"   保护区数量: {result_3['protection_zones_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 保护区信息修复测试")
    print("=" * 50)
    
    # 检查JSON文件中的保护区信息
    json_ok = test_protection_zones_in_json()
    
    # 测试保护区信息提取函数
    extract_ok = test_protection_zones_extraction()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   JSON检查:   {'✅ 通过' if json_ok else '❌ 失败'}")
    print(f"   提取测试:   {'✅ 通过' if extract_ok else '❌ 失败'}")
    
    if json_ok and extract_ok:
        print("\n🎉 保护区信息修复测试通过！")
        print("\n📝 下一步:")
        print("   1. 重新运行算法对比生成新的JSON文件")
        print("   2. 测试CSV导出功能")
        return True
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
