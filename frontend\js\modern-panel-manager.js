/**
 * 现代化面板管理器
 * 管理所有UI面板的显示、隐藏、交互和数据更新
 * 按照论文要求实现完整的统计和图表功能
 */
class ModernPanelManager {
    constructor(cityManager) {
        this.cityManager = cityManager;
        this.panels = {
            comparisonChart: null,
            performanceMonitor: null,
            algorithmSteps: null
        };
        
        // 核心指标数据
        this.coreMetrics = {
            pathLength: 0,
            turningCost: 0,
            riskValue: 0,
            collisionCost: 0,
            finalCost: 0
        };
        
        // 对比数据
        this.comparisonData = {
            improved: null,
            baseline: null
        };
        
        // 性能监控数据
        this.performanceData = {
            algorithmProgress: 0,
            flightProgress: 0,
            cpuUsage: 0,
            memoryUsage: 0,
            logs: []
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.initializePanels();
        this.startPerformanceMonitoring();
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 对比图表按钮
        const showComparisonBtn = document.getElementById('show-comparison-chart-btn');
        if (showComparisonBtn) {
            showComparisonBtn.addEventListener('click', () => {
                this.showComparisonChart();
            });
        }
        
        // 关闭对比图表
        const closeComparisonBtn = document.getElementById('close-comparison-chart');
        if (closeComparisonBtn) {
            closeComparisonBtn.addEventListener('click', () => {
                this.hideComparisonChart();
            });
        }
        
        // 图表类型切换
        const chartTypeBtns = document.querySelectorAll('.chart-btn');
        chartTypeBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchChartType(e.target.id);
            });
        });
        
        // 性能监控面板
        const performanceBtn = document.getElementById('show-performance-monitor-btn');
        if (performanceBtn) {
            performanceBtn.addEventListener('click', () => {
                this.showPerformanceMonitor();
            });
        }
        
        const closePerformanceBtn = document.getElementById('close-performance-monitor');
        if (closePerformanceBtn) {
            closePerformanceBtn.addEventListener('click', () => {
                this.hidePerformanceMonitor();
            });
        }
        
        // 导出功能
        const exportDataBtn = document.getElementById('export-chart-data');
        if (exportDataBtn) {
            exportDataBtn.addEventListener('click', () => {
                this.exportComparisonData();
            });
        }
        
        const saveChartBtn = document.getElementById('save-chart-image');
        if (saveChartBtn) {
            saveChartBtn.addEventListener('click', () => {
                this.saveChartImage();
            });
        }
        
        const printReportBtn = document.getElementById('print-report');
        if (printReportBtn) {
            printReportBtn.addEventListener('click', () => {
                this.printReport();
            });
        }
    }
    
    /**
     * 初始化面板
     */
    initializePanels() {
        this.panels.comparisonChart = document.getElementById('comparison-chart-panel');
        this.panels.performanceMonitor = document.getElementById('performance-monitor-panel');
        this.panels.algorithmSteps = document.getElementById('algorithm-steps-modal');
    }
    
    /**
     * 更新核心指标显示
     * @param {Object} metrics - 指标数据
     */
    updateCoreMetrics(metrics) {
        this.coreMetrics = { ...this.coreMetrics, ...metrics };
        
        // 更新显示
        this.updateMetricDisplay('path-length-metric', metrics.pathLength, 'm');
        this.updateMetricDisplay('turning-cost-metric', metrics.turningCost, 'rad');
        this.updateMetricDisplay('risk-value-metric', metrics.riskValue, '');
        this.updateMetricDisplay('collision-cost-metric', metrics.collisionCost, '');
        this.updateMetricDisplay('final-cost-metric', metrics.finalCost, '');
        
        // 更新进度条
        this.updateMetricBar('path-length-bar', metrics.pathLength, 1000);
        this.updateMetricBar('turning-cost-bar', metrics.turningCost, 10);
        this.updateMetricBar('risk-value-bar', metrics.riskValue, 100);
        this.updateMetricBar('collision-cost-bar', metrics.collisionCost, 100);
        this.updateMetricBar('final-cost-bar', metrics.finalCost, 1000);
    }
    
    /**
     * 更新指标显示
     */
    updateMetricDisplay(elementId, value, unit) {
        const element = document.getElementById(elementId);
        if (element) {
            const formattedValue = typeof value === 'number' ? value.toFixed(2) : '--';
            element.textContent = `${formattedValue}${unit}`;
        }
    }
    
    /**
     * 更新指标进度条
     */
    updateMetricBar(elementId, value, maxValue) {
        const element = document.getElementById(elementId);
        if (element && typeof value === 'number') {
            const percentage = Math.min((value / maxValue) * 100, 100);
            element.style.width = `${percentage}%`;
        }
    }
    
    /**
     * 显示算法对比图表
     */
    showComparisonChart() {
        if (!this.panels.comparisonChart) return;

        // 检查是否有真实的对比数据
        if (!this.comparisonData || !this.comparisonData.improved || !this.comparisonData.baseline) {
            this.showNoDataMessage();
            return;
        }

        this.panels.comparisonChart.style.display = 'block';
        this.renderComparisonChart();
        this.updateComparisonTable();
        this.updateComparisonSummary();
    }

    /**
     * 显示无数据提示
     */
    showNoDataMessage() {
        if (!this.panels.comparisonChart) return;

        this.panels.comparisonChart.style.display = 'block';
        const chartContainer = document.querySelector('.chart-container');
        if (chartContainer) {
            chartContainer.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 400px; color: #cccccc;">
                    <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                    <h3 style="color: #ff6b35; margin-bottom: 15px;">暂无对比数据</h3>
                    <p style="text-align: center; line-height: 1.6; max-width: 400px;">
                        请先执行算法对比模式的路径规划，系统将自动运行改进算法和基准算法，然后生成对比图表。
                    </p>
                    <div style="margin-top: 20px; padding: 15px; background: rgba(255, 107, 53, 0.1); border-radius: 8px; border-left: 3px solid #ff6b35;">
                        <strong>操作步骤：</strong><br>
                        1. 在控制面板中勾选"启用算法对比模式"<br>
                        2. 设置起点和终点<br>
                        3. 点击"规划路径"按钮<br>
                        4. 等待算法执行完成后查看对比结果
                    </div>
                </div>
            `;
        }

        // 清空其他区域
        const summaryElement = document.getElementById('comparison-summary');
        if (summaryElement) {
            summaryElement.innerHTML = '<p style="color: #888; text-align: center;">等待算法执行完成...</p>';
        }

        const tableBody = document.getElementById('comparison-table-body');
        if (tableBody) {
            tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #888;">暂无数据</td></tr>';
        }
    }
    
    /**
     * 隐藏算法对比图表
     */
    hideComparisonChart() {
        if (this.panels.comparisonChart) {
            this.panels.comparisonChart.style.display = 'none';
        }
    }
    
    /**
     * 渲染对比图表
     */
    renderComparisonChart() {
        const canvas = document.getElementById('comparison-chart-canvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        // 清除画布
        ctx.clearRect(0, 0, width, height);

        // 根据当前选中的图表类型绘制
        const activeBtn = document.querySelector('.chart-btn.active');
        const chartType = activeBtn ? activeBtn.id : 'chart-type-bar';

        switch(chartType) {
            case 'chart-type-bar':
                this.drawBarChart(ctx, width, height);
                break;
            case 'chart-type-radar':
                this.drawRadarChart(ctx, width, height);
                break;
            case 'chart-type-line':
                this.drawLineChart(ctx, width, height);
                break;
            default:
                this.drawBarChart(ctx, width, height);
        }
    }
    
    /**
     * 绘制柱状图
     */
    drawBarChart(ctx, width, height) {
        const margin = { top: 60, right: 60, bottom: 100, left: 100 };
        const chartWidth = width - margin.left - margin.right;
        const chartHeight = height - margin.top - margin.bottom;

        // 检查数据有效性
        if (!this.comparisonData || !this.comparisonData.improved || !this.comparisonData.baseline) {
            this.drawNoDataChart(ctx, width, height);
            return;
        }

        const metrics = ['路径长度 (m)', '转向成本 (rad)', '风险值', '碰撞代价'];
        const improvedData = [
            this.comparisonData.improved.pathLength || 0,
            this.comparisonData.improved.turningCost || 0,
            this.comparisonData.improved.riskValue || 0,
            this.comparisonData.improved.collisionCost || 0
        ];

        const baselineData = [
            this.comparisonData.baseline.pathLength || 0,
            this.comparisonData.baseline.turningCost || 0,
            this.comparisonData.baseline.riskValue || 0,
            this.comparisonData.baseline.collisionCost || 0
        ];

        // 标准化数据以便比较
        const normalizedImproved = this.normalizeDataForChart(improvedData);
        const normalizedBaseline = this.normalizeDataForChart(baselineData);

        const barGroupWidth = chartWidth / metrics.length;
        const barWidth = barGroupWidth * 0.35;
        const maxValue = Math.max(...normalizedImproved, ...normalizedBaseline);

        // 绘制标题
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 18px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('算法性能对比分析 (论文四个核心指标)', width / 2, 30);

        // 绘制背景网格
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 1;
        for (let i = 0; i <= 10; i++) {
            const y = margin.top + (chartHeight / 10) * i;
            ctx.beginPath();
            ctx.moveTo(margin.left, y);
            ctx.lineTo(margin.left + chartWidth, y);
            ctx.stroke();
        }

        // Y轴标签
        ctx.fillStyle = '#cccccc';
        ctx.font = '12px Arial';
        ctx.textAlign = 'right';
        for (let i = 0; i <= 10; i++) {
            const y = margin.top + (chartHeight / 10) * i;
            const value = ((10 - i) / 10 * maxValue).toFixed(1);
            ctx.fillText(value, margin.left - 10, y + 4);
        }

        // 绘制柱状图
        metrics.forEach((metric, index) => {
            const x = margin.left + index * barGroupWidth + (barGroupWidth - barWidth * 2) / 2;

            // 改进算法柱子
            const improvedHeight = (normalizedImproved[index] / maxValue) * chartHeight;
            const gradient1 = ctx.createLinearGradient(0, margin.top + chartHeight - improvedHeight, 0, margin.top + chartHeight);
            gradient1.addColorStop(0, '#00d4ff');
            gradient1.addColorStop(1, '#0099cc');
            ctx.fillStyle = gradient1;
            ctx.fillRect(x, margin.top + chartHeight - improvedHeight, barWidth, improvedHeight);

            // 改进算法数值标签
            ctx.fillStyle = '#00d4ff';
            ctx.font = 'bold 10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(improvedData[index].toFixed(1), x + barWidth / 2, margin.top + chartHeight - improvedHeight - 5);

            // 基准算法柱子
            const baselineHeight = (normalizedBaseline[index] / maxValue) * chartHeight;
            const gradient2 = ctx.createLinearGradient(0, margin.top + chartHeight - baselineHeight, 0, margin.top + chartHeight);
            gradient2.addColorStop(0, '#ff6b35');
            gradient2.addColorStop(1, '#f7931e');
            ctx.fillStyle = gradient2;
            ctx.fillRect(x + barWidth + 5, margin.top + chartHeight - baselineHeight, barWidth, baselineHeight);

            // 基准算法数值标签
            ctx.fillStyle = '#ff6b35';
            ctx.font = 'bold 10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(baselineData[index].toFixed(1), x + barWidth + 5 + barWidth / 2, margin.top + chartHeight - baselineHeight - 5);

            // X轴标签
            ctx.fillStyle = '#ffffff';
            ctx.font = '11px Arial';
            ctx.textAlign = 'center';
            const lines = metric.split(' ');
            lines.forEach((line, lineIndex) => {
                ctx.fillText(line, x + barWidth + 2.5, height - 60 + lineIndex * 15);
            });

            // 改进百分比
            const improvement = ((baselineData[index] - improvedData[index]) / baselineData[index] * 100);
            const improvementColor = improvement > 0 ? '#00ff88' : '#ff4444';
            ctx.fillStyle = improvementColor;
            ctx.font = 'bold 12px Arial';
            ctx.fillText(`${improvement > 0 ? '-' : '+'}${Math.abs(improvement).toFixed(1)}%`,
                        x + barWidth + 2.5, height - 25);
        });

        // 增强图例
        const legendY = 50;
        ctx.fillStyle = '#00d4ff';
        ctx.fillRect(width - 200, legendY, 25, 15);
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('改进分簇算法', width - 170, legendY + 12);

        ctx.fillStyle = '#ff6b35';
        ctx.fillRect(width - 200, legendY + 25, 25, 15);
        ctx.fillStyle = '#ffffff';
        ctx.fillText('基准A*算法', width - 170, legendY + 37);

        // 添加说明文字
        ctx.fillStyle = '#cccccc';
        ctx.font = '10px Arial';
        ctx.fillText('绿色百分比表示改进，红色表示退化', width - 200, legendY + 60);
    }

    /**
     * 绘制无数据图表
     */
    drawNoDataChart(ctx, width, height) {
        // 清除画布
        ctx.clearRect(0, 0, width, height);

        // 绘制背景
        ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
        ctx.fillRect(0, 0, width, height);

        // 绘制提示信息
        ctx.fillStyle = '#cccccc';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('暂无对比数据', width / 2, height / 2 - 40);

        ctx.fillStyle = '#888888';
        ctx.font = '16px Arial';
        ctx.fillText('请先执行算法对比模式的路径规划', width / 2, height / 2);

        ctx.fillStyle = '#666666';
        ctx.font = '14px Arial';
        ctx.fillText('系统将自动运行改进算法和基准算法进行对比', width / 2, height / 2 + 30);
    }

    /**
     * 标准化数据用于图表显示
     */
    normalizeDataForChart(data) {
        // 对不同量级的数据进行标准化，使其在图表中可比较
        return data.map((value, index) => {
            switch(index) {
                case 0: return value / 10; // 路径长度除以10
                case 1: return value * 100; // 转向成本乘以100
                case 2: return value; // 风险值保持不变
                case 3: return value; // 碰撞代价保持不变
                default: return value;
            }
        });
    }

    /**
     * 绘制雷达图
     */
    drawRadarChart(ctx, width, height) {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) * 0.3;

        // 检查数据有效性
        if (!this.comparisonData || !this.comparisonData.improved || !this.comparisonData.baseline) {
            this.drawNoDataChart(ctx, width, height);
            return;
        }

        const metrics = ['路径长度', '转向成本', '风险值', '碰撞代价'];
        const improvedData = [
            this.comparisonData.improved.pathLength || 0,
            this.comparisonData.improved.turningCost || 0,
            this.comparisonData.improved.riskValue || 0,
            this.comparisonData.improved.collisionCost || 0
        ];

        const baselineData = [
            this.comparisonData.baseline.pathLength || 0,
            this.comparisonData.baseline.turningCost || 0,
            this.comparisonData.baseline.riskValue || 0,
            this.comparisonData.baseline.collisionCost || 0
        ];

        // 标准化数据到0-1范围
        const maxValues = [2000, 5, 50, 30]; // 各指标的最大值
        const normalizedImproved = improvedData.map((val, i) => val / maxValues[i]);
        const normalizedBaseline = baselineData.map((val, i) => val / maxValues[i]);

        // 绘制标题
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 18px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('算法性能雷达图对比', centerX, 30);

        // 绘制同心圆网格
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.lineWidth = 1;
        for (let i = 1; i <= 5; i++) {
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius * i / 5, 0, 2 * Math.PI);
            ctx.stroke();
        }

        // 绘制轴线和标签
        const angleStep = (2 * Math.PI) / metrics.length;
        metrics.forEach((metric, index) => {
            const angle = index * angleStep - Math.PI / 2;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;

            // 轴线
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.lineTo(x, y);
            ctx.stroke();

            // 标签
            const labelX = centerX + Math.cos(angle) * (radius + 30);
            const labelY = centerY + Math.sin(angle) * (radius + 30);
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(metric, labelX, labelY);
        });

        // 绘制改进算法数据
        ctx.beginPath();
        normalizedImproved.forEach((value, index) => {
            const angle = index * angleStep - Math.PI / 2;
            const x = centerX + Math.cos(angle) * radius * value;
            const y = centerY + Math.sin(angle) * radius * value;
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.closePath();
        ctx.fillStyle = 'rgba(0, 212, 255, 0.3)';
        ctx.fill();
        ctx.strokeStyle = '#00d4ff';
        ctx.lineWidth = 3;
        ctx.stroke();

        // 绘制基准算法数据
        ctx.beginPath();
        normalizedBaseline.forEach((value, index) => {
            const angle = index * angleStep - Math.PI / 2;
            const x = centerX + Math.cos(angle) * radius * value;
            const y = centerY + Math.sin(angle) * radius * value;
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.closePath();
        ctx.fillStyle = 'rgba(255, 107, 53, 0.3)';
        ctx.fill();
        ctx.strokeStyle = '#ff6b35';
        ctx.lineWidth = 3;
        ctx.stroke();

        // 图例
        const legendY = height - 60;
        ctx.fillStyle = '#00d4ff';
        ctx.fillRect(centerX - 100, legendY, 20, 15);
        ctx.fillStyle = '#ffffff';
        ctx.font = '14px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('改进分簇算法', centerX - 75, legendY + 12);

        ctx.fillStyle = '#ff6b35';
        ctx.fillRect(centerX - 100, legendY + 25, 20, 15);
        ctx.fillStyle = '#ffffff';
        ctx.fillText('基准A*算法', centerX - 75, legendY + 37);
    }

    /**
     * 绘制折线图
     */
    drawLineChart(ctx, width, height) {
        const margin = { top: 60, right: 60, bottom: 100, left: 100 };
        const chartWidth = width - margin.left - margin.right;
        const chartHeight = height - margin.top - margin.bottom;

        // 检查数据有效性
        if (!this.comparisonData || !this.comparisonData.improved || !this.comparisonData.baseline) {
            this.drawNoDataChart(ctx, width, height);
            return;
        }

        const metrics = ['路径长度', '转向成本', '风险值', '碰撞代价'];
        const improvedData = [
            this.comparisonData.improved.pathLength || 0,
            this.comparisonData.improved.turningCost || 0,
            this.comparisonData.improved.riskValue || 0,
            this.comparisonData.improved.collisionCost || 0
        ];

        const baselineData = [
            this.comparisonData.baseline.pathLength || 0,
            this.comparisonData.baseline.turningCost || 0,
            this.comparisonData.baseline.riskValue || 0,
            this.comparisonData.baseline.collisionCost || 0
        ];

        // 标准化数据
        const normalizedImproved = this.normalizeDataForChart(improvedData);
        const normalizedBaseline = this.normalizeDataForChart(baselineData);
        const maxValue = Math.max(...normalizedImproved, ...normalizedBaseline);

        // 绘制标题
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 18px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('算法性能趋势对比', width / 2, 30);

        // 绘制网格
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 1;
        for (let i = 0; i <= 10; i++) {
            const y = margin.top + (chartHeight / 10) * i;
            ctx.beginPath();
            ctx.moveTo(margin.left, y);
            ctx.lineTo(margin.left + chartWidth, y);
            ctx.stroke();
        }

        for (let i = 0; i < metrics.length; i++) {
            const x = margin.left + (chartWidth / (metrics.length - 1)) * i;
            ctx.beginPath();
            ctx.moveTo(x, margin.top);
            ctx.lineTo(x, margin.top + chartHeight);
            ctx.stroke();
        }

        // 绘制改进算法折线
        ctx.beginPath();
        normalizedImproved.forEach((value, index) => {
            const x = margin.left + (chartWidth / (metrics.length - 1)) * index;
            const y = margin.top + chartHeight - (value / maxValue) * chartHeight;
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.strokeStyle = '#00d4ff';
        ctx.lineWidth = 4;
        ctx.stroke();

        // 绘制改进算法数据点
        normalizedImproved.forEach((value, index) => {
            const x = margin.left + (chartWidth / (metrics.length - 1)) * index;
            const y = margin.top + chartHeight - (value / maxValue) * chartHeight;
            ctx.beginPath();
            ctx.arc(x, y, 6, 0, 2 * Math.PI);
            ctx.fillStyle = '#00d4ff';
            ctx.fill();
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.stroke();
        });

        // 绘制基准算法折线
        ctx.beginPath();
        normalizedBaseline.forEach((value, index) => {
            const x = margin.left + (chartWidth / (metrics.length - 1)) * index;
            const y = margin.top + chartHeight - (value / maxValue) * chartHeight;
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.strokeStyle = '#ff6b35';
        ctx.lineWidth = 4;
        ctx.stroke();

        // 绘制基准算法数据点
        normalizedBaseline.forEach((value, index) => {
            const x = margin.left + (chartWidth / (metrics.length - 1)) * index;
            const y = margin.top + chartHeight - (value / maxValue) * chartHeight;
            ctx.beginPath();
            ctx.arc(x, y, 6, 0, 2 * Math.PI);
            ctx.fillStyle = '#ff6b35';
            ctx.fill();
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.stroke();
        });

        // X轴标签
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        metrics.forEach((metric, index) => {
            const x = margin.left + (chartWidth / (metrics.length - 1)) * index;
            ctx.fillText(metric, x, height - 40);
        });

        // 图例
        const legendY = 50;
        ctx.fillStyle = '#00d4ff';
        ctx.fillRect(width - 200, legendY, 25, 15);
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('改进分簇算法', width - 170, legendY + 12);

        ctx.fillStyle = '#ff6b35';
        ctx.fillRect(width - 200, legendY + 25, 25, 15);
        ctx.fillStyle = '#ffffff';
        ctx.fillText('基准A*算法', width - 170, legendY + 37);
    }
    
    /**
     * 更新对比表格
     */
    updateComparisonTable() {
        const tableBody = document.getElementById('comparison-table-body');
        if (!tableBody) return;

        // 检查数据有效性
        if (!this.comparisonData || !this.comparisonData.improved || !this.comparisonData.baseline) {
            tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #888;">暂无对比数据，请先执行算法对比模式</td></tr>';
            return;
        }

        const improvedMetrics = this.comparisonData.improved;
        const baselineMetrics = this.comparisonData.baseline;

        const comparisonData = [
            {
                metric: '路径长度 (Length)',
                formula: 'Σ√[(x_{i+1}-x_i)² + (y_{i+1}-y_i)² + (z_{i+1}-z_i)²]',
                improved: `${improvedMetrics.pathLength.toFixed(1)}m`,
                baseline: `${baselineMetrics.pathLength.toFixed(1)}m`,
                improvement: this.calculateImprovement(improvedMetrics.pathLength, baselineMetrics.pathLength),
                status: improvedMetrics.pathLength < baselineMetrics.pathLength ? '优化' : '退化',
                significance: '高'
            },
            {
                metric: '转向成本 (Turning Cost)',
                formula: 'Σ Δθ (角度变化累积)',
                improved: `${improvedMetrics.turningCost.toFixed(2)}rad`,
                baseline: `${baselineMetrics.turningCost.toFixed(2)}rad`,
                improvement: this.calculateImprovement(improvedMetrics.turningCost, baselineMetrics.turningCost),
                status: improvedMetrics.turningCost < baselineMetrics.turningCost ? '优化' : '退化',
                significance: '中'
            },
            {
                metric: '风险值 (Risk Value)',
                formula: '基于环境危险因子的综合评估',
                improved: `${improvedMetrics.riskValue.toFixed(1)}`,
                baseline: `${baselineMetrics.riskValue.toFixed(1)}`,
                improvement: this.calculateImprovement(improvedMetrics.riskValue, baselineMetrics.riskValue),
                status: improvedMetrics.riskValue < baselineMetrics.riskValue ? '优化' : '退化',
                significance: '高'
            },
            {
                metric: '碰撞代价 (Collision Cost)',
                formula: '与障碍物碰撞风险的量化评估',
                improved: `${improvedMetrics.collisionCost.toFixed(1)}`,
                baseline: `${baselineMetrics.collisionCost.toFixed(1)}`,
                improvement: this.calculateImprovement(improvedMetrics.collisionCost, baselineMetrics.collisionCost),
                status: improvedMetrics.collisionCost < baselineMetrics.collisionCost ? '优化' : '退化',
                significance: '高'
            }
        ];

        tableBody.innerHTML = comparisonData.map(row => `
            <tr>
                <td style="color: #000000;">
                    <strong>${row.metric}</strong><br>
                    <small style="color: #666; font-style: italic;">${row.formula}</small>
                </td>
                <td style="color: #0066cc; font-weight: bold;">${row.improved}</td>
                <td style="color: #cc4400; font-weight: bold;">${row.baseline}</td>
                <td style="color: ${row.improvement.startsWith('-') ? '#006600' : '#cc0000'}; font-weight: bold;">
                    ${row.improvement}
                </td>
                <td style="color: #000000;">
                    <span style="color: ${row.status === '优化' ? '#006600' : '#cc0000'}; font-weight: bold;">
                        ${row.status}
                    </span><br>
                    <small style="color: #666;">重要性: ${row.significance}</small>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 计算改进百分比 - 🔧 修复：统一计算逻辑
     */
    calculateImprovement(improved, baseline) {
        if (baseline === 0) return '0.0%';

        // 统一计算逻辑：(baseline - improved) / baseline * 100
        // 正数表示改进（减少），负数表示退化（增加）
        const improvement = ((baseline - improved) / baseline) * 100;

        // 格式化显示：改进用绿色负号，退化用红色正号
        if (improvement > 0) {
            return `-${improvement.toFixed(1)}%`; // 改进：显示为负数（实际是减少了）
        } else if (improvement < 0) {
            return `+${Math.abs(improvement).toFixed(1)}%`; // 退化：显示为正数（实际是增加了）
        } else {
            return '0.0%'; // 无变化
        }
    }

    /**
     * 计算最终代价改进百分比 - 🔧 新增：与自动弹出对比保持一致
     * @param {Object} improvedData - 改进算法数据
     * @param {Object} baselineData - 基准算法数据
     * @returns {number} 最终代价改进百分比
     */
    calculateFinalCostImprovement(improvedData, baselineData) {
        // 获取最终代价数据
        const improvedFinalCost = Number(improvedData.finalCost) || 0;
        const baselineFinalCost = Number(baselineData.finalCost) || 0;

        console.log('🔍 ModernPanelManager - 计算最终代价改进率:', {
            improved: improvedFinalCost,
            baseline: baselineFinalCost
        });

        // 避免除以零
        if (baselineFinalCost === 0) {
            if (improvedFinalCost === 0) return 0; // 两者都为零，无变化
            return improvedFinalCost > 0 ? -100 : 100; // 基准为0但改进不为0
        }

        // 计算最终代价改进百分比（越小越好）
        const finalCostImprovement = ((baselineFinalCost - improvedFinalCost) / baselineFinalCost) * 100;

        console.log('🔍 ModernPanelManager - 最终代价改进率:', finalCostImprovement.toFixed(2) + '%');

        return finalCostImprovement;
    }
    
    /**
     * 更新对比摘要
     */
    updateComparisonSummary() {
        const summaryElement = document.getElementById('comparison-summary');
        if (!summaryElement) return;

        // 检查数据有效性
        if (!this.comparisonData || !this.comparisonData.improved || !this.comparisonData.baseline) {
            summaryElement.innerHTML = `
                <h4>📈 算法性能对比分析报告</h4>
                <div style="text-align: center; color: #888; padding: 40px;">
                    <p>暂无对比数据</p>
                    <p>请先执行算法对比模式的路径规划</p>
                </div>
            `;
            return;
        }

        // 使用真实的对比数据
        const improvedMetrics = this.comparisonData.improved;
        const baselineMetrics = this.comparisonData.baseline;

        const improvements = {
            pathLength: ((baselineMetrics.pathLength - improvedMetrics.pathLength) / baselineMetrics.pathLength * 100),
            turningCost: ((baselineMetrics.turningCost - improvedMetrics.turningCost) / baselineMetrics.turningCost * 100),
            riskValue: ((baselineMetrics.riskValue - improvedMetrics.riskValue) / baselineMetrics.riskValue * 100),
            collisionCost: ((baselineMetrics.collisionCost - improvedMetrics.collisionCost) / baselineMetrics.collisionCost * 100)
        };

        // 调试：输出各项改进率
        console.log('🔍 各项改进率计算结果:');
        console.log('  路径长度改进率:', improvements.pathLength.toFixed(2) + '%');
        console.log('  转向成本改进率:', improvements.turningCost.toFixed(2) + '%');
        console.log('  风险值改进率:', improvements.riskValue.toFixed(2) + '%');
        console.log('  碰撞代价改进率:', improvements.collisionCost.toFixed(2) + '%');

        // 🔧 修复：使用最终代价计算总体改进率，与自动弹出的对比保持一致
        const finalCostImprovement = this.calculateFinalCostImprovement(improvedMetrics, baselineMetrics);
        const overallImprovement = finalCostImprovement;

        console.log('🔍 ModernPanelManager - 最终代价改进率:', overallImprovement.toFixed(2) + '%');
        console.log('🔍 ModernPanelManager - 改进判断:', overallImprovement > 0 ? '改进' : '退化');
        console.log('🔍 各项指标改进率（仅供参考）:');
        console.log('  路径长度:', improvements.pathLength.toFixed(2) + '%');
        console.log('  转向成本:', improvements.turningCost.toFixed(2) + '%');
        console.log('  风险值:', improvements.riskValue.toFixed(2) + '%');
        console.log('  碰撞代价:', improvements.collisionCost.toFixed(2) + '%');

        // 判断总体表现
        const isOverallImproved = overallImprovement > 0;
        const overallStatus = isOverallImproved ? '改进' : '退化';
        const overallColor = isOverallImproved ? '#00ff88' : '#ff4444';

        // 找到最佳改进指标（最大的正值改进）
        const positiveImprovements = Object.entries(improvements).filter(([key, value]) => value > 0);
        let bestImprovement = 0;
        let bestMetric = null;

        if (positiveImprovements.length > 0) {
            const [metric, value] = positiveImprovements.reduce((max, current) =>
                current[1] > max[1] ? current : max
            );
            bestImprovement = value;
            bestMetric = metric;
        } else {
            // 如果没有正改进，找到最小的负改进（退化最少的）
            const [metric, value] = Object.entries(improvements).reduce((min, current) =>
                current[1] > min[1] ? current : min
            );
            bestImprovement = value;
            bestMetric = metric;
        }

        const metricNames = {
            pathLength: '路径长度',
            turningCost: '转向成本',
            riskValue: '风险值',
            collisionCost: '碰撞代价'
        };

        summaryElement.innerHTML = `
            <h4>📈 算法性能对比分析报告</h4>
            <div style="margin: 15px 0; padding: 12px; background: ${isOverallImproved ? 'rgba(0, 255, 136, 0.1)' : 'rgba(255, 68, 68, 0.1)'}; border-radius: 8px;">
                <p><strong>🎯 总体评估：</strong>改进分簇算法相比基准A*算法，最终代价${isOverallImproved ? '降低' : '增加'} <span style="color: ${overallColor}; font-weight: bold;">${Math.abs(overallImprovement).toFixed(1)}%</span></p>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
                <div style="padding: 10px; background: rgba(0, 212, 255, 0.1); border-radius: 6px;">
                    <strong>${bestImprovement > 0 ? '🏆 最佳改进指标' : '⚠️ 最小退化指标'}</strong><br>
                    <span style="color: #00d4ff;">${bestMetric ? metricNames[bestMetric] : '无'}</span><br>
                    <span style="color: ${bestImprovement > 0 ? '#00ff88' : '#ff4444'}; font-weight: bold;">
                        ${bestImprovement > 0 ? '提升' : '退化'} ${Math.abs(bestImprovement).toFixed(1)}%
                    </span>
                </div>
                <div style="padding: 10px; background: rgba(255, 107, 53, 0.1); border-radius: 6px;">
                    <strong>📊 算法表现</strong><br>
                    <span style="color: #ff6b35;">
                        ${positiveImprovements.length}项改进，${4 - positiveImprovements.length}项退化
                    </span><br>
                    <span style="color: ${isOverallImproved ? '#00ff88' : '#ff4444'}; font-weight: bold;">
                        总体${overallStatus}
                    </span>
                </div>
            </div>

            <div style="margin: 15px 0;">
                <h5 style="color: #00d4ff; margin-bottom: 10px;">🔍 详细分析</h5>
                <ul style="margin: 0; padding-left: 20px; color: #cccccc;">
                    <li><strong>路径效率：</strong>路径长度${improvements.pathLength > 0 ? '减少' : '增加'} ${Math.abs(improvements.pathLength).toFixed(1)}%，${improvements.pathLength > 0 ? '提高' : '降低'}飞行效率</li>
                    <li><strong>飞行平稳性：</strong>转向成本${improvements.turningCost > 0 ? '降低' : '增加'} ${Math.abs(improvements.turningCost).toFixed(1)}%，飞行${improvements.turningCost > 0 ? '更加平稳' : '稳定性下降'}</li>
                    <li><strong>安全性能：</strong>风险值${improvements.riskValue > 0 ? '减少' : '增加'} ${Math.abs(improvements.riskValue).toFixed(1)}%，${improvements.riskValue > 0 ? '提升' : '降低'}整体安全性</li>
                    <li><strong>避障能力：</strong>碰撞代价${improvements.collisionCost > 0 ? '降低' : '增加'} ${Math.abs(improvements.collisionCost).toFixed(1)}%，避障性能${improvements.collisionCost > 0 ? '显著提升' : '有所下降'}</li>
                </ul>
            </div>

            <div style="margin: 15px 0; padding: 10px; background: rgba(255, 255, 255, 0.05); border-radius: 6px; border-left: 3px solid ${isOverallImproved ? '#00ff88' : '#ff4444'};">
                <strong style="color: ${isOverallImproved ? '#00ff88' : '#ff4444'};">
                    ${isOverallImproved ? '✅ 结论：' : '⚠️ 结论：'}
                </strong>
                ${isOverallImproved ?
                    `改进分簇算法相比基准算法总体表现优异，最终代价降低${overallImprovement.toFixed(1)}%，验证了算法的有效性。` :
                    `改进分簇算法相比基准算法总体表现有待优化，最终代价增加${Math.abs(overallImprovement).toFixed(1)}%，需要进一步调优参数。`
                }
                ${bestMetric ? `特别是在${metricNames[bestMetric]}方面的表现${bestImprovement > 0 ? '突出' : '需要改进'}。` : ''}
            </div>
        `;
    }
    
    /**
     * 开始性能监控
     */
    startPerformanceMonitoring() {
        setInterval(() => {
            this.updatePerformanceMetrics();
        }, 1000);
    }
    
    /**
     * 更新性能指标
     */
    updatePerformanceMetrics() {
        // 模拟性能数据更新
        this.performanceData.cpuUsage = Math.random() * 100;
        this.performanceData.memoryUsage = 60 + Math.random() * 30;
        
        this.updatePerformanceDisplay();
    }
    
    /**
     * 更新性能显示
     */
    updatePerformanceDisplay() {
        this.updateProgressBar('cpu-usage-bar', 'cpu-usage-text', this.performanceData.cpuUsage);
        this.updateProgressBar('memory-usage-bar', 'memory-usage-text', this.performanceData.memoryUsage);
    }
    
    /**
     * 更新进度条
     */
    updateProgressBar(barId, textId, value) {
        const bar = document.getElementById(barId);
        const text = document.getElementById(textId);
        
        if (bar) bar.style.width = `${value}%`;
        if (text) text.textContent = `${value.toFixed(1)}%`;
    }
    
    /**
     * 显示性能监控面板
     */
    showPerformanceMonitor() {
        if (this.panels.performanceMonitor) {
            this.panels.performanceMonitor.style.display = 'block';
        }
    }
    
    /**
     * 隐藏性能监控面板
     */
    hidePerformanceMonitor() {
        if (this.panels.performanceMonitor) {
            this.panels.performanceMonitor.style.display = 'none';
        }
    }
    
    /**
     * 导出对比数据
     */
    exportComparisonData() {
        console.log('📊 开始导出路径对比数据...');

        // 检查是否有对比数据
        if (!this.comparisonData || !this.comparisonData.improved || !this.comparisonData.baseline) {
            alert('❌ 没有可导出的对比数据，请先执行算法对比！');
            return;
        }

        // 调用后端导出API
        fetch('/api/export-comparison-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.comparisonData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`✅ 路径对比数据已导出到CSV文件！\n\n文件位置: ${data.filepath}\n包含数据: ${data.pathCount}条路径`);
                console.log('✅ CSV导出成功:', data);
            } else {
                alert(`❌ 导出失败: ${data.error}`);
                console.error('❌ CSV导出失败:', data.error);
            }
        })
        .catch(error => {
            console.error('❌ 导出请求失败:', error);
            alert('❌ 导出请求失败，请检查网络连接！');
        });
    }
    
    /**
     * 保存图表图像
     */
    saveChartImage() {
        const canvas = document.getElementById('comparison-chart-canvas');
        if (canvas) {
            const link = document.createElement('a');
            link.download = `comparison_chart_${new Date().toISOString().split('T')[0]}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
    }
    
    /**
     * 打印报告
     */
    printReport() {
        window.print();
    }
    
    /**
     * 设置对比数据
     * @param {Object} improvedData - 改进算法数据
     * @param {Object} baselineData - 基准算法数据
     */
    setComparisonData(improvedData, baselineData) {
        console.log('🔍 ModernPanelManager.setComparisonData 被调用');
        console.log('🔍 改进算法数据:', improvedData);
        console.log('🔍 基准算法数据:', baselineData);

        this.comparisonData = {
            improved: improvedData,
            baseline: baselineData
        };

        // 如果对比图表正在显示，更新它
        if (this.panels.comparisonChart && this.panels.comparisonChart.style.display !== 'none') {
            this.renderComparisonChart();
            this.updateComparisonTable();
            this.updateComparisonSummary();
        }
    }

    /**
     * 获取对比数据
     */
    getComparisonData() {
        return this.comparisonData;
    }

    /**
     * 重置对比数据
     */
    resetComparisonData() {
        this.comparisonData = {
            improved: null,
            baseline: null
        };
    }

    /**
     * 切换图表类型
     */
    switchChartType(chartType) {
        // 移除所有活动状态
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // 添加活动状态
        document.getElementById(chartType).classList.add('active');

        // 重新渲染图表
        this.renderComparisonChart();
    }

    /**
     * 添加性能日志
     * @param {string} message - 日志消息
     * @param {string} type - 日志类型 (info, success, warning, error)
     */
    addPerformanceLog(message, type = 'info') {
        const logContent = document.getElementById('performance-log-content');
        if (!logContent) return;

        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;
        logEntry.textContent = `[${timestamp}] ${message}`;

        logContent.appendChild(logEntry);

        // 保持日志数量在合理范围内
        while (logContent.children.length > 100) {
            logContent.removeChild(logContent.firstChild);
        }

        // 滚动到底部
        logContent.scrollTop = logContent.scrollHeight;

        // 同时添加到内存中
        this.performanceData.logs.push({
            timestamp: new Date(),
            message,
            type
        });
    }

    /**
     * 更新算法进度
     * @param {number} progress - 进度百分比 (0-100)
     */
    updateAlgorithmProgress(progress) {
        this.performanceData.algorithmProgress = progress;
        this.updateProgressBar('algorithm-progress-bar', 'algorithm-progress-text', progress);

        if (progress === 100) {
            this.addPerformanceLog('算法执行完成', 'success');
        }
    }

    /**
     * 更新飞行进度
     * @param {number} progress - 进度百分比 (0-100)
     */
    updateFlightProgress(progress) {
        this.performanceData.flightProgress = progress;
        this.updateProgressBar('flight-progress-bar', 'flight-progress-text', progress);

        if (progress === 100) {
            this.addPerformanceLog('飞行模拟完成', 'success');
        }
    }
}

// 全局实例
let modernPanelManager = null;

// 初始化函数
function initModernPanelManager(cityManager) {
    modernPanelManager = new ModernPanelManager(cityManager);
    return modernPanelManager;
}
