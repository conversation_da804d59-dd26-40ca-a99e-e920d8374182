/* 无人机路径规划系统样式文件 */

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
}

/* 主容器布局 */
#container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
}

/* 顶部导航栏 */
#header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 1000;
}

#header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ecf0f1;
}

#status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

#connection-status {
    padding: 5px 12px;
    background-color: #e74c3c;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

#connection-status.connected {
    background-color: #27ae60;
}

/* 主要内容区域 */
#main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 左侧控制面板 */
#control-panel {
    width: 280px;
    background-color: #2c3e50;
    padding: 20px;
    overflow-y: auto;
    border-right: 1px solid #34495e;
}

.panel-section {
    margin-bottom: 25px;
    padding: 15px;
    background-color: #34495e;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.panel-section h3 {
    color: #3498db;
    margin-bottom: 15px;
    font-size: 1.1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    margin-bottom: 5px;
    color: #bdc3c7;
    font-size: 0.9rem;
}

.control-group input,
.control-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #7f8c8d;
    border-radius: 4px;
    background-color: #2c3e50;
    color: #ecf0f1;
    font-size: 0.9rem;
}

.control-group input:focus,
.control-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-right: 5px;
    margin-bottom: 5px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-success {
    background-color: #27ae60;
    color: white;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
}

/* 3D场景容器 */
#scene-container {
    flex: 1;
    position: relative;
    background-color: #1a1a1a;
}

#three-canvas {
    width: 100%;
    height: 100%;
}

/* 场景工具栏 */
#scene-toolbar {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 5px;
    z-index: 100;
}

.toolbar-btn {
    width: 40px;
    height: 40px;
    background-color: rgba(52, 73, 94, 0.8);
    border: none;
    border-radius: 6px;
    color: #ecf0f1;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toolbar-btn:hover {
    background-color: rgba(52, 152, 219, 0.8);
    transform: scale(1.1);
}

/* 坐标显示 */
#coordinates-display {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 8px 12px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #3498db;
}

/* 右侧信息面板 */
#info-panel {
    width: 300px;
    background-color: #2c3e50;
    padding: 20px;
    overflow-y: auto;
    border-left: 1px solid #34495e;
}

/* 路径指标显示 */
#path-metrics {
    background-color: #34495e;
    padding: 15px;
    border-radius: 6px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #7f8c8d;
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-item label {
    color: #bdc3c7;
    font-size: 0.9rem;
}

.metric-item span {
    color: #3498db;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

/* 日志容器 */
#log-container {
    background-color: #1a1a1a;
    padding: 10px;
    border-radius: 4px;
    height: 150px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

.log-entry {
    margin-bottom: 5px;
    color: #95a5a6;
    padding: 2px 0;
}

.log-entry.success {
    color: #27ae60;
}

.log-entry.error {
    color: #e74c3c;
}

.log-entry.warning {
    color: #f39c12;
}

/* 对象信息面板 */
#object-info {
    background-color: #34495e;
    padding: 15px;
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
}

.info-placeholder {
    color: #7f8c8d;
    text-align: center;
    font-style: italic;
}

.object-info-item {
    margin-bottom: 8px;
    padding: 4px 0;
    border-bottom: 1px solid #7f8c8d;
}

.object-info-item:last-child {
    border-bottom: none;
}

.object-info-label {
    color: #bdc3c7;
    font-weight: 600;
}

.object-info-value {
    color: #3498db;
    margin-left: 10px;
}

/* 路径集结果 */
#path-set-results {
    background-color: #34495e;
    padding: 15px;
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
}

.results-placeholder {
    color: #7f8c8d;
    text-align: center;
    font-style: italic;
}

/* 底部状态栏 */
#footer {
    background-color: #2c3e50;
    padding: 8px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #34495e;
    font-size: 0.8rem;
}

#performance-stats {
    color: #3498db;
    font-family: 'Courier New', monospace;
}

#system-info {
    color: #95a5a6;
}

/* 加载覆盖层 */
#loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #34495e;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    color: #3498db;
    font-size: 1.1rem;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 复选框样式 */
input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2c3e50;
}

::-webkit-scrollbar-thumb {
    background: #7f8c8d;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #95a5a6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    #control-panel {
        width: 250px;
    }
    
    #info-panel {
        width: 250px;
    }
}

@media (max-width: 768px) {
    #main-content {
        flex-direction: column;
    }

    #control-panel,
    #info-panel {
        width: 100%;
        height: 200px;
    }
}

/* 飞行轨迹样式 */
.flight-trail {
    animation: trailPulse 2s ease-in-out infinite;
}

@keyframes trailPulse {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

/* 轨迹点动画 */
.trail-point {
    animation: pointGlow 1.5s ease-in-out infinite;
}

@keyframes pointGlow {
    0%, 100% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 飞行状态指示器 */
.flight-status {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: #00d4ff;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 0.9em;
    z-index: 1000;
}

.flight-status.flying {
    background: rgba(76, 175, 80, 0.8);
    color: #ffffff;
}

/* 轨迹信息面板 */
.trail-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 10px;
    border-radius: 8px;
    font-size: 0.8em;
    z-index: 1000;
    max-width: 200px;
}

.trail-info h4 {
    color: #ff6b6b;
    margin-bottom: 5px;
}

.trail-info .trail-stat {
    margin-bottom: 3px;
}
