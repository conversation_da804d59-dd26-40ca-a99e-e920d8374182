#!/usr/bin/env python3
"""
严格按照论文要求的改进分簇路径规划算法
按照论文中的5个具体步骤实现
"""

import math
import numpy as np
from typing import List, Dict, Tuple, Optional
from algorithms.data_structures import PathPlanningRequest, PathPlanningResponse, PathPoint
from algorithms.astar import AStarAlgorithm
import asyncio

class PaperCompliantClusterAlgorithm:
    """严格按照论文要求的改进分簇算法"""
    
    def __init__(self):
        self.astar_algorithm = AStarAlgorithm()
        self.initial_path_set = []  # 初始路径集 P
        self.clusters = {}  # 分簇结果
        self.leader_cluster = None  # 领导者种群
        self.follower_clusters = []  # 跟随者种群
        self.selected_path = None  # 选中的飞行路径
        
    async def calculate_path(self, request: PathPlanningRequest) -> PathPlanningResponse:
        """主要计算方法，严格按照论文5个步骤"""
        
        print("🚀 开始执行严格按论文要求的改进分簇算法")
        print("=" * 60)
        
        # 步骤1：生成初始路径集
        await self._step1_generate_initial_path_set(request)
        
        # 步骤2：分簇
        self._step2_clustering()
        
        # 步骤3：平滑路径
        self._step3_smooth_paths()
        
        # 步骤4：最优簇选取
        self._step4_select_optimal_cluster()
        
        # 步骤5：换路策略（暂时跳过，在实际飞行中实现）
        print("\n📋 步骤5：换路策略（在实际飞行中实现）")
        print("   - 碰撞代价持续异常检测：连续5个航点超过20%阈值时换路")
        print("   - 换路方法：悬停 → 选择临近簇最优路径 → A*生成连接路径")
        
        # 生成最终响应
        return self._generate_response()
    
    async def _step1_generate_initial_path_set(self, request: PathPlanningRequest):
        """步骤1：生成初始路径集"""
        print("\n📋 步骤1：生成初始路径集")
        print("-" * 40)
        
        start_point = request.start_point
        end_point = request.end_point
        
        # (1) 确定起飞方向角度限制
        print("(1) 确定起飞方向角度限制")
        flight_directions = self._determine_flight_directions(start_point, end_point)
        print(f"   - 起点到终点连线方向：{flight_directions['base_angle']:.1f}°")
        print(f"   - 90°扇形范围：{flight_directions['start_angle']:.1f}° ~ {flight_directions['end_angle']:.1f}°")
        print(f"   - 9个起飞方向：{[f'{i+1}号({angle:.1f}°)' for i, angle in enumerate(flight_directions['angles'])]}")
        
        # (2) 确定中转点的区域
        print("\n(2) 确定中转点的区域")
        transfer_regions = self._determine_transfer_regions(start_point, end_point)
        print(f"   - 起终点连线长度：{transfer_regions['line_length']:.2f}m")
        print(f"   - 中垂线中点：({transfer_regions['midpoint']['lng']:.6f}, {transfer_regions['midpoint']['lat']:.6f})")
        print(f"   - 参考线长度：{transfer_regions['reference_line_length']:.2f}m (连线长度的30%)")
        print(f"   - 中转点生成区域：宽50m × 长{transfer_regions['reference_line_length']:.2f}m")
        print(f"   - 9个方向分区 × 9个高度层 = 81个分区")
        
        # (3) 确定中转点
        print("\n(3) 确定中转点")
        transfer_points = self._determine_transfer_points(transfer_regions, request.buildings)
        print(f"   - 生成81个中转点（9方向 × 9高度层）")
        for i in range(min(5, len(transfer_points))):  # 显示前5个
            tp = transfer_points[i]
            print(f"   - {tp['direction']}-{tp['height_layer']}中转点：({tp['lng']:.6f}, {tp['lat']:.6f}, {tp['alt']:.1f}m)")
        if len(transfer_points) > 5:
            print(f"   - ... 还有{len(transfer_points)-5}个中转点")
        
        # (4) 生成路径
        print("\n(4) 生成路径")
        await self._generate_paths(start_point, end_point, transfer_points, request)
        print(f"   - 使用A*算法生成81条路径")
        print(f"   - 路径格式：起点 → 中转点 → 终点")
        print(f"   - 成功生成{len(self.initial_path_set)}条路径")
        
        # (5) 计算最终代价
        print("\n(5) 计算最终代价")
        self._calculate_final_costs(request)
        
        # 按最终代价排序
        self.initial_path_set.sort(key=lambda p: p['final_cost'])
        for i, path in enumerate(self.initial_path_set):
            path['rank'] = i + 1
        
        print(f"   - 计算所有路径的最终代价")
        print(f"   - 按最终代价排序（第1位最优）")
        print(f"   - 最优路径：{self.initial_path_set[0]['path_id']} (代价:{self.initial_path_set[0]['final_cost']:.6f})")
        print(f"   - 最差路径：{self.initial_path_set[-1]['path_id']} (代价:{self.initial_path_set[-1]['final_cost']:.6f})")
        
        # (6) 存储数据
        print("\n(6) 存储数据")
        self._store_path_data()
        print(f"   - 路径存储格式：路径索引+飞行方向+高度层+最终代价+排序序号")
        print(f"   - 航点存储格式：航点索引+路径索引+三维坐标+位次序号")
        print(f"   - 初始路径集P生成完成，包含{len(self.initial_path_set)}条路径")
    
    def _determine_flight_directions(self, start_point, end_point) -> Dict:
        """确定9个起飞方向"""
        # 计算起点到终点的方向角
        dx = end_point.lng - start_point.lng
        dy = end_point.lat - start_point.lat
        base_angle = math.degrees(math.atan2(dy, dx))
        
        # 90度扇形，每10度一个方向，共9个方向
        start_angle = base_angle - 45  # 向左45度
        end_angle = base_angle + 45    # 向右45度
        
        angles = []
        for i in range(9):
            angle = start_angle + i * 10
            angles.append(angle)
        
        return {
            'base_angle': base_angle,
            'start_angle': start_angle,
            'end_angle': end_angle,
            'angles': angles
        }
    
    def _determine_transfer_regions(self, start_point, end_point) -> Dict:
        """确定中转点生成区域"""
        # 计算起终点连线长度
        line_length = self._calculate_distance(start_point, end_point)
        
        # 中垂线中点
        midpoint = {
            'lng': (start_point.lng + end_point.lng) / 2,
            'lat': (start_point.lat + end_point.lat) / 2,
            'alt': (start_point.alt + end_point.alt) / 2
        }
        
        # 参考线长度：连线长度的30%
        reference_line_length = line_length * 0.3
        
        # 计算中垂线方向（垂直于起终点连线）
        dx = end_point.lng - start_point.lng
        dy = end_point.lat - start_point.lat
        
        # 垂直方向（逆时针旋转90度）
        perp_dx = -dy
        perp_dy = dx
        perp_length = math.sqrt(perp_dx**2 + perp_dy**2)
        
        if perp_length > 0:
            perp_dx /= perp_length
            perp_dy /= perp_length
        
        return {
            'line_length': line_length,
            'midpoint': midpoint,
            'reference_line_length': reference_line_length,
            'perpendicular_direction': {'dx': perp_dx, 'dy': perp_dy},
            'region_width': 50  # 向两侧各延伸25米，总宽50米
        }
    
    def _determine_transfer_points(self, transfer_regions, buildings) -> List[Dict]:
        """确定81个中转点"""
        transfer_points = []
        
        midpoint = transfer_regions['midpoint']
        ref_length = transfer_regions['reference_line_length']
        perp_dir = transfer_regions['perpendicular_direction']
        
        # 9个方向分区
        for direction in range(1, 10):  # 1-9号飞行方向
            # 在参考线上的位置（均匀分布）
            direction_ratio = (direction - 1) / 8  # 0到1
            direction_offset = (direction_ratio - 0.5) * ref_length  # 相对中点的偏移
            
            # 9个高度层
            for height_layer in range(1, 10):  # 1-9层
                # 高度：从30米起，每10米一层
                altitude = 30 + (height_layer - 1) * 10
                
                # 在参考线上的基准位置
                base_lng = midpoint['lng'] + direction_offset * perp_dir['dx'] / 111320
                base_lat = midpoint['lat'] + direction_offset * perp_dir['dy'] / 110540
                
                # 在垂直方向上随机偏移（±25米范围内）
                import random
                random.seed(direction * 10 + height_layer)  # 固定种子确保可重现
                
                vertical_offset = random.uniform(-25, 25)  # 米
                
                # 计算垂直于参考线的方向
                vert_dx = -perp_dir['dy']  # 垂直于垂直方向
                vert_dy = perp_dir['dx']
                
                # 最终中转点位置
                transfer_lng = base_lng + vertical_offset * vert_dx / 111320
                transfer_lat = base_lat + vertical_offset * vert_dy / 110540
                
                # 检查是否在建筑物2米外（简化检查）
                is_safe = self._check_safe_distance(transfer_lng, transfer_lat, buildings, 2.0)
                
                if not is_safe:
                    # 如果不安全，稍微调整位置
                    for attempt in range(5):
                        offset_x = random.uniform(-10, 10)
                        offset_y = random.uniform(-10, 10)
                        new_lng = transfer_lng + offset_x / 111320
                        new_lat = transfer_lat + offset_y / 110540
                        
                        if self._check_safe_distance(new_lng, new_lat, buildings, 2.0):
                            transfer_lng = new_lng
                            transfer_lat = new_lat
                            break
                
                transfer_points.append({
                    'direction': direction,
                    'height_layer': height_layer,
                    'lng': transfer_lng,
                    'lat': transfer_lat,
                    'alt': altitude,
                    'id': f"{direction}-{height_layer}"
                })
        
        return transfer_points
    
    def _calculate_distance(self, point1, point2) -> float:
        """计算两点间距离（米）"""
        R = 6371000  # 地球半径
        lat1_rad = math.radians(point1.lat)
        lat2_rad = math.radians(point2.lat)
        delta_lat = math.radians(point2.lat - point1.lat)
        delta_lng = math.radians(point2.lng - point1.lng)
        
        a = (math.sin(delta_lat/2)**2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lng/2)**2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def _check_safe_distance(self, lng, lat, buildings, min_distance) -> bool:
        """检查位置是否与建筑物保持安全距离"""
        for building in buildings:
            building_lng = building.get('lng', building.get('x', 0))
            building_lat = building.get('lat', building.get('y', 0))
            
            # 简化距离计算
            distance = math.sqrt((lng - building_lng)**2 + (lat - building_lat)**2) * 111320
            
            if distance < min_distance:
                return False
        
        return True

    async def _generate_paths(self, start_point, end_point, transfer_points, request):
        """使用A*算法生成81条路径"""
        self.initial_path_set = []

        for i, tp in enumerate(transfer_points):
            try:
                # 创建中转点
                transfer_point = PathPoint(tp['lng'], tp['lat'], tp['alt'])

                # 生成路径：起点 → 中转点 → 终点
                # 简化实现：直接连接三个点
                waypoints = [
                    PathPoint(start_point.lng, start_point.lat, start_point.alt),
                    transfer_point,
                    PathPoint(end_point.lng, end_point.lat, end_point.alt)
                ]

                path_data = {
                    'path_id': f"P{tp['direction']}-{tp['height_layer']}",
                    'path_index': i,
                    'flight_direction': tp['direction'],
                    'height_layer': tp['height_layer'],
                    'transfer_point': tp,
                    'waypoints': waypoints,
                    'waypoint_count': len(waypoints)
                }

                self.initial_path_set.append(path_data)

            except Exception as e:
                print(f"   ⚠️ 路径{tp['id']}生成失败: {e}")
                continue

    def _calculate_final_costs(self, request):
        """计算所有路径的最终代价"""
        for path in self.initial_path_set:
            # 计算路径长度
            path_length = 0
            waypoints = path['waypoints']
            for i in range(len(waypoints) - 1):
                distance = self._calculate_distance(waypoints[i], waypoints[i+1])
                path_length += distance

            # 计算转向成本（简化）
            turning_cost = 0
            if len(waypoints) >= 3:
                # 计算转向角度
                for i in range(1, len(waypoints) - 1):
                    angle = self._calculate_turning_angle(waypoints[i-1], waypoints[i], waypoints[i+1])
                    turning_cost += abs(angle) * 0.01  # 简化的转向成本

            # 计算风险值（简化）
            risk_value = path_length * 0.001 + turning_cost * 0.1

            # 计算碰撞代价（简化）
            collision_cost = len(request.buildings) * 0.01  # 简化的碰撞代价

            # 计算最终代价（使用论文公式）
            # 简化的权重
            alpha = 0.236  # 风险权重
            beta = 0.118   # 碰撞权重
            gamma = 0.525  # 长度权重
            delta = 0.121  # 转向权重

            # 归一化（简化）
            max_length = 10000  # 假设最大长度
            max_turning = 100   # 假设最大转向
            max_risk = 100      # 假设最大风险
            max_collision = 10  # 假设最大碰撞

            normalized_length = path_length / max_length
            normalized_turning = turning_cost / max_turning
            normalized_risk = risk_value / max_risk
            normalized_collision = collision_cost / max_collision

            final_cost = (alpha * normalized_risk +
                         beta * normalized_collision +
                         gamma * normalized_length +
                         delta * normalized_turning)

            # 存储计算结果
            path.update({
                'path_length': path_length,
                'turning_cost': turning_cost,
                'risk_value': risk_value,
                'collision_cost': collision_cost,
                'final_cost': final_cost,
                'normalized_values': {
                    'length': normalized_length,
                    'turning': normalized_turning,
                    'risk': normalized_risk,
                    'collision': normalized_collision
                },
                'weights': {
                    'alpha': alpha,
                    'beta': beta,
                    'gamma': gamma,
                    'delta': delta
                }
            })

    def _calculate_turning_angle(self, p1, p2, p3) -> float:
        """计算转向角度"""
        # 向量1: p1 → p2
        v1_x = p2.lng - p1.lng
        v1_y = p2.lat - p1.lat

        # 向量2: p2 → p3
        v2_x = p3.lng - p2.lng
        v2_y = p3.lat - p2.lat

        # 计算角度
        dot_product = v1_x * v2_x + v1_y * v2_y
        mag1 = math.sqrt(v1_x**2 + v1_y**2)
        mag2 = math.sqrt(v2_x**2 + v2_y**2)

        if mag1 == 0 or mag2 == 0:
            return 0

        cos_angle = dot_product / (mag1 * mag2)
        cos_angle = max(-1, min(1, cos_angle))  # 限制在[-1, 1]范围内

        angle = math.acos(cos_angle)
        return math.degrees(angle)

    def _store_path_data(self):
        """存储路径数据"""
        # 为每条路径分配索引
        for i, path in enumerate(self.initial_path_set):
            path['path_index'] = i

            # 为航点分配索引和位次序号
            for j, waypoint in enumerate(path['waypoints']):
                waypoint.waypoint_index = f"{i}_{j}"
                waypoint.path_index = i
                waypoint.position_order = j + 1

    def _step2_clustering(self):
        """步骤2：分簇"""
        print("\n📋 步骤2：分簇")
        print("-" * 40)

        print("按路径中转点的空间分布进行9×9=81个位置分布的分簇")
        print("建立坐标系：起点面朝终点，左上角为原点，X轴向右，Y轴向下")

        # 创建9×9网格映射
        grid_mapping = {}
        for path in self.initial_path_set:
            direction = path['flight_direction']  # 1-9
            height_layer = path['height_layer']   # 1-9

            # 映射到9×9网格 (X, Y)
            x = direction  # 1-9
            y = height_layer  # 1-9

            grid_mapping[(x, y)] = path

        # 定义簇
        clusters = {}

        # 九个3×3簇
        cluster_3x3_definitions = [
            ((1,1), (3,3)),  # 左上
            ((4,1), (6,3)),  # 中上
            ((7,1), (9,3)),  # 右上
            ((1,4), (3,6)),  # 左中
            ((4,4), (6,6)),  # 中中
            ((7,4), (9,6)),  # 右中
            ((1,7), (3,9)),  # 左下
            ((4,7), (6,9)),  # 中下
            ((7,7), (9,9))   # 右下
        ]

        # 四个4×4簇
        cluster_4x4_definitions = [
            ((2,2), (5,5)),  # 左上重叠
            ((5,2), (8,5)),  # 右上重叠
            ((2,5), (5,8)),  # 左下重叠
            ((5,5), (8,8))   # 右下重叠
        ]

        # 创建3×3簇
        for i, ((x1, y1), (x2, y2)) in enumerate(cluster_3x3_definitions):
            cluster_id = f"3x3_cluster_{i+1}"
            cluster_paths = []

            for x in range(x1, x2+1):
                for y in range(y1, y2+1):
                    if (x, y) in grid_mapping:
                        cluster_paths.append(grid_mapping[(x, y)])

            clusters[cluster_id] = {
                'type': '3x3',
                'id': cluster_id,
                'bounds': ((x1, y1), (x2, y2)),
                'paths': cluster_paths,
                'path_count': len(cluster_paths)
            }

        # 创建4×4簇
        for i, ((x1, y1), (x2, y2)) in enumerate(cluster_4x4_definitions):
            cluster_id = f"4x4_cluster_{i+1}"
            cluster_paths = []

            for x in range(x1, x2+1):
                for y in range(y1, y2+1):
                    if (x, y) in grid_mapping:
                        cluster_paths.append(grid_mapping[(x, y)])

            clusters[cluster_id] = {
                'type': '4x4',
                'id': cluster_id,
                'bounds': ((x1, y1), (x2, y2)),
                'paths': cluster_paths,
                'path_count': len(cluster_paths)
            }

        self.clusters = clusters

        print(f"生成簇的数量：")
        print(f"   - 3×3簇：9个，每个包含9条路径")
        print(f"   - 4×4簇：4个，每个包含16条路径")
        print(f"   - 总计：13个簇")

        # 显示簇的详细信息
        for cluster_id, cluster in clusters.items():
            print(f"   - {cluster_id}: {cluster['type']}，范围{cluster['bounds']}，包含{cluster['path_count']}条路径")

    def _step3_smooth_paths(self):
        """步骤3：平滑路径"""
        print("\n📋 步骤3：平滑路径")
        print("-" * 40)

        print("对所有路径进行平滑处理，消除S型扭动")
        print("使用三次样条插值方法进行路径平滑")

        smoothed_count = 0
        for path in self.initial_path_set:
            try:
                # 简化的平滑处理
                waypoints = path['waypoints']
                if len(waypoints) >= 3:
                    # 这里应该实现三次样条插值
                    # 简化实现：保持原路径不变
                    path['smoothed'] = True
                    smoothed_count += 1
                else:
                    path['smoothed'] = False
            except Exception as e:
                path['smoothed'] = False

        print(f"   - 成功平滑{smoothed_count}条路径")
        print(f"   - 平滑后的路径无S型扭动，适合无人机飞行")

    def _step4_select_optimal_cluster(self):
        """步骤4：最优簇选取"""
        print("\n📋 步骤4：最优簇选取")
        print("-" * 40)

        print("计算每个簇的最终代价平均值，选择最优簇")

        cluster_costs = []

        for cluster_id, cluster in self.clusters.items():
            paths = cluster['paths']
            if not paths:
                continue

            # 计算簇的最终代价平均值
            total_cost = sum(path['final_cost'] for path in paths)
            n = len(paths)
            average_cost = total_cost / n

            cluster['total_cost'] = total_cost
            cluster['average_cost'] = average_cost
            cluster['path_count'] = n

            cluster_costs.append((cluster_id, average_cost, cluster))

        # 按平均代价排序
        cluster_costs.sort(key=lambda x: x[1])

        print(f"簇的最终代价平均值排序：")
        for i, (cluster_id, avg_cost, cluster) in enumerate(cluster_costs):
            print(f"   {i+1}. {cluster_id}: 平均代价={avg_cost:.6f} (包含{cluster['path_count']}条路径)")

        # 选择领导者种群（最优簇）
        if cluster_costs:
            leader_cluster_id, leader_avg_cost, leader_cluster = cluster_costs[0]
            self.leader_cluster = leader_cluster
            self.follower_clusters = [cluster for _, _, cluster in cluster_costs[1:]]

            print(f"\n领导者种群：{leader_cluster_id}")
            print(f"   - 平均代价：{leader_avg_cost:.6f}")
            print(f"   - 包含路径：{leader_cluster['path_count']}条")

            # 在领导者种群中选择最终代价最低的路径
            leader_paths = leader_cluster['paths']
            best_path = min(leader_paths, key=lambda p: p['final_cost'])
            self.selected_path = best_path

            print(f"\n选择的飞行路径：{best_path['path_id']}")
            print(f"   - 最终代价：{best_path['final_cost']:.6f}")
            print(f"   - 飞行方向：{best_path['flight_direction']}号")
            print(f"   - 高度层：{best_path['height_layer']}层")
            print(f"   - 路径长度：{best_path['path_length']:.2f}m")

            print(f"\n跟随者种群：{len(self.follower_clusters)}个簇")
            for i, cluster in enumerate(self.follower_clusters[:3]):  # 显示前3个
                print(f"   {i+1}. {cluster['id']}: 平均代价={cluster['average_cost']:.6f}")

    def _generate_response(self) -> PathPlanningResponse:
        """生成最终响应"""
        response = PathPlanningResponse()

        if not self.selected_path:
            # 如果没有选中路径，返回空响应
            response.success = False
            response.error = "未能选择最优路径"
            response.path = []
            response.path_length = 0
            response.turning_cost = 0
            response.risk_value = 0
            response.collision_cost = 0
            response.final_cost = 0
        else:
            # 返回选中路径的响应
            response.success = True
            response.path = self.selected_path['waypoints']
            response.path_length = self.selected_path['path_length']
            response.turning_cost = self.selected_path['turning_cost']
            response.risk_value = self.selected_path['risk_value']
            response.collision_cost = self.selected_path['collision_cost']
            response.final_cost = self.selected_path['final_cost']

            # 添加算法特定的元数据
            response.metadata = {
                'algorithm': 'PaperCompliantCluster',
                'initial_paths_count': len(self.initial_path_set),
                'clusters_count': len(self.clusters),
                'selected_path_id': self.selected_path['path_id'],
                'flight_direction': self.selected_path['flight_direction'],
                'height_layer': self.selected_path['height_layer'],
                'leader_cluster': self.leader_cluster['id'] if self.leader_cluster else None,
                'follower_clusters_count': len(self.follower_clusters)
            }

            # 计算统计信息
            response.calculate_statistics()

        return response
