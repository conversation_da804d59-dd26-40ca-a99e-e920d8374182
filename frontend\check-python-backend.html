<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python后端状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .status-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-success { border-left: 5px solid #28a745; }
        .status-error { border-left: 5px solid #dc3545; }
        .status-warning { border-left: 5px solid #ffc107; }
        .status-info { border-left: 5px solid #17a2b8; }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .algorithm-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .algorithm-item {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        
        .algorithm-item.available {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>🐍 Python后端状态检查</h1>
    
    <div class="status-card status-info">
        <h3>📋 检查清单</h3>
        <p>此页面将检查Python算法后端是否正常运行，以及各个算法是否可用。</p>
        <button class="test-button" onclick="runAllChecks()">运行所有检查</button>
        <button class="test-button" onclick="clearResults()">清空结果</button>
    </div>
    
    <div class="status-card" id="connection-status">
        <h3>🔗 连接状态</h3>
        <button class="test-button" onclick="checkConnection()">检查连接</button>
        <div id="connection-result" class="result-area" style="display: none;"></div>
    </div>
    
    <div class="status-card" id="algorithm-status">
        <h3>🧠 算法状态</h3>
        <button class="test-button" onclick="checkAlgorithms()">检查算法</button>
        <div id="algorithm-result" class="result-area" style="display: none;"></div>
        <div id="algorithm-list" class="algorithm-list" style="display: none;"></div>
    </div>
    
    <div class="status-card" id="test-status">
        <h3>🧪 算法测试</h3>
        <button class="test-button" onclick="testAlgorithm('StraightLine')">测试直线算法</button>
        <button class="test-button" onclick="testAlgorithm('AStar')">测试A*算法</button>
        <button class="test-button" onclick="testAlgorithm('RRT')">测试RRT算法</button>
        <div id="test-result" class="result-area" style="display: none;"></div>
    </div>
    
    <div class="status-card" id="solution-status">
        <h3>💡 解决方案</h3>
        <div id="solution-content">
            <p>如果Python后端未运行，请按以下步骤启动：</p>
            <ol>
                <li><strong>Windows用户</strong>：双击运行 <code>start_python_backend.bat</code></li>
                <li><strong>Linux/macOS用户</strong>：运行 <code>./start_python_backend.sh</code></li>
                <li><strong>手动启动</strong>：
                    <pre>cd backend
pip install -r requirements.txt
python simple_server.py</pre>
                </li>
            </ol>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api/pathfinding';
        
        function log(message) {
            console.log(message);
        }
        
        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = content;
            element.style.color = isError ? '#dc3545' : '#333';
        }
        
        function updateCardStatus(cardId, status) {
            const card = document.getElementById(cardId);
            card.className = `status-card status-${status}`;
        }
        
        async function checkConnection() {
            try {
                showResult('connection-result', '正在检查连接...');
                updateCardStatus('connection-status', 'info');
                
                const response = await fetch(`${API_BASE_URL}/health`, {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.success) {
                        updateCardStatus('connection-status', 'success');
                        showResult('connection-result', 
                            `✅ Python后端连接成功\n` +
                            `状态: ${data.status}\n` +
                            `消息: ${data.message}\n` +
                            `可用算法数量: ${data.available_algorithms}\n` +
                            `算法列表: ${data.algorithms.join(', ')}`
                        );
                        return true;
                    } else {
                        updateCardStatus('connection-status', 'error');
                        showResult('connection-result', `❌ 后端状态异常: ${data.error}`, true);
                        return false;
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                updateCardStatus('connection-status', 'error');
                showResult('connection-result', 
                    `❌ 连接失败: ${error.message}\n\n` +
                    `可能的原因:\n` +
                    `1. Python后端未启动\n` +
                    `2. 端口5000被占用\n` +
                    `3. 防火墙阻止连接\n` +
                    `4. CORS配置问题`, true);
                return false;
            }
        }
        
        async function checkAlgorithms() {
            try {
                showResult('algorithm-result', '正在检查算法...');
                updateCardStatus('algorithm-status', 'info');
                
                const response = await fetch(`${API_BASE_URL}/algorithms`);
                const data = await response.json();
                
                if (data.success) {
                    updateCardStatus('algorithm-status', 'success');
                    
                    let result = `✅ 算法检查成功\n总数量: ${data.total_count}\n\n`;
                    
                    const algorithmList = document.getElementById('algorithm-list');
                    algorithmList.innerHTML = '';
                    algorithmList.style.display = 'grid';
                    
                    data.algorithms.forEach(alg => {
                        result += `算法: ${alg.name}\n`;
                        result += `描述: ${alg.info.description}\n`;
                        result += `版本: ${alg.info.version}\n`;
                        result += `类别: ${alg.info.category}\n`;
                        result += `执行次数: ${alg.info.stats.total_executions}\n`;
                        result += `成功率: ${(alg.info.stats.success_rate * 100).toFixed(1)}%\n`;
                        result += '---\n';
                        
                        // 创建算法卡片
                        const algorithmItem = document.createElement('div');
                        algorithmItem.className = 'algorithm-item available';
                        algorithmItem.innerHTML = `
                            <strong>${alg.name}</strong><br>
                            <small>${alg.info.description}</small>
                        `;
                        algorithmList.appendChild(algorithmItem);
                    });
                    
                    showResult('algorithm-result', result);
                    return true;
                } else {
                    updateCardStatus('algorithm-status', 'error');
                    showResult('algorithm-result', `❌ 算法检查失败: ${data.error}`, true);
                    return false;
                }
                
            } catch (error) {
                updateCardStatus('algorithm-status', 'error');
                showResult('algorithm-result', `❌ 算法检查错误: ${error.message}`, true);
                return false;
            }
        }
        
        async function testAlgorithm(algorithmName) {
            try {
                showResult('test-result', `正在测试${algorithmName}算法...`);
                updateCardStatus('test-status', 'info');
                
                const requestData = {
                    startPoint: { lng: 139.767, lat: 35.681, alt: 0 },
                    endPoint: { lng: 139.777, lat: 35.691, alt: 0 },
                    flightHeight: 100,
                    algorithm: algorithmName,
                    parameters: getDefaultParameters(algorithmName)
                };
                
                const startTime = Date.now();
                
                const response = await fetch(`${API_BASE_URL}/calculate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                const executionTime = Date.now() - startTime;
                
                let result = `算法: ${algorithmName}\n`;
                result += `网络请求时间: ${executionTime}ms\n`;
                
                if (data.success) {
                    updateCardStatus('test-status', 'success');
                    result += `✅ 测试成功\n`;
                    result += `算法执行时间: ${data.executionTime.toFixed(1)}ms\n`;
                    result += `路径点数量: ${data.path.length}\n`;
                    result += `路径长度: ${data.pathLength.toFixed(1)}m\n`;
                    result += `预计飞行时间: ${data.estimatedFlightTime.toFixed(1)}s\n`;
                    
                    if (data.quality) {
                        result += `质量评分:\n`;
                        result += `  风险评分: ${data.quality.risk_score}\n`;
                        result += `  平滑度: ${data.quality.smoothness}\n`;
                        result += `  效率: ${data.quality.efficiency}\n`;
                    }
                } else {
                    updateCardStatus('test-status', 'error');
                    result += `❌ 测试失败: ${data.error}\n`;
                }
                
                showResult('test-result', result, !data.success);
                
            } catch (error) {
                updateCardStatus('test-status', 'error');
                showResult('test-result', `❌ 测试${algorithmName}算法失败: ${error.message}`, true);
            }
        }
        
        function getDefaultParameters(algorithmName) {
            switch (algorithmName) {
                case 'StraightLine':
                    return { pointCount: 20, smoothing: true };
                case 'AStar':
                    return { gridSize: 10, heuristicWeight: 1.0, maxIterations: 5000 };
                case 'RRT':
                    return { maxIterations: 3000, stepSize: 50, goalBias: 0.1 };
                default:
                    return {};
            }
        }
        
        async function runAllChecks() {
            const buttons = document.querySelectorAll('.test-button');
            buttons.forEach(btn => btn.disabled = true);
            
            try {
                const connectionOk = await checkConnection();
                
                if (connectionOk) {
                    await checkAlgorithms();
                    await testAlgorithm('StraightLine');
                }
            } finally {
                buttons.forEach(btn => btn.disabled = false);
            }
        }
        
        function clearResults() {
            const results = document.querySelectorAll('.result-area');
            results.forEach(result => {
                result.style.display = 'none';
                result.textContent = '';
            });
            
            const cards = document.querySelectorAll('.status-card');
            cards.forEach(card => {
                if (card.id !== 'solution-status') {
                    card.className = 'status-card';
                }
            });
            
            const algorithmList = document.getElementById('algorithm-list');
            algorithmList.style.display = 'none';
            algorithmList.innerHTML = '';
        }
        
        // 页面加载时自动运行检查
        window.addEventListener('load', () => {
            setTimeout(runAllChecks, 1000);
        });
    </script>
</body>
</html>
