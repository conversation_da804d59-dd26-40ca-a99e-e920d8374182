#!/usr/bin/env python3
"""
路径平滑算法
实现三次样条路径平滑，确保飞行路径平滑无S型扭动
"""

import numpy as np
import math
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass
from scipy.interpolate import CubicSpline, splprep, splev
from scipy.optimize import minimize_scalar

from algorithms.data_structures import Point3D
from algorithms.improved_cluster_pathfinding import ImprovedPathPoint


@dataclass
class SmoothingParameters:
    """平滑参数配置"""
    smoothing_factor: float = 0.1          # 平滑因子 (0-1)
    min_segment_length: float = 5.0        # 最小段长度 (m)
    max_curvature: float = 0.1             # 最大曲率 (1/m)
    continuity_order: int = 2              # 连续性阶数 (0=位置, 1=速度, 2=加速度)
    interpolation_points: int = 50         # 插值点数量
    tension_parameter: float = 0.0         # 张力参数 (0-1)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'smoothingFactor': self.smoothing_factor,
            'minSegmentLength': self.min_segment_length,
            'maxCurvature': self.max_curvature,
            'continuityOrder': self.continuity_order,
            'interpolationPoints': self.interpolation_points,
            'tensionParameter': self.tension_parameter
        }


@dataclass
class SmoothingResult:
    """平滑结果"""
    original_path: List[Point3D]           # 原始路径
    smoothed_path: List[Point3D]           # 平滑后路径
    curvature_profile: List[float]         # 曲率分布
    smoothness_metrics: Dict[str, float]   # 平滑度指标
    processing_time: float                 # 处理时间
    success: bool                          # 是否成功
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'originalPath': [{'x': p.x, 'y': p.y, 'z': p.z} for p in self.original_path],
            'smoothedPath': [{'x': p.x, 'y': p.y, 'z': p.z} for p in self.smoothed_path],
            'curvatureProfile': self.curvature_profile,
            'smoothnessMetrics': self.smoothness_metrics,
            'processingTime': self.processing_time,
            'success': self.success
        }


class PathSmoother:
    """路径平滑器"""
    
    def __init__(self, parameters: Optional[SmoothingParameters] = None):
        self.parameters = parameters or SmoothingParameters()
        self.smoothing_history: List[SmoothingResult] = []
    
    def smooth_path(self, waypoints: List[ImprovedPathPoint],
                   smoothing_factor: float = None, interpolation_points: int = None,
                   max_curvature: float = None) -> SmoothingResult:
        """
        对路径进行三次样条平滑

        Args:
            waypoints: 原始航点列表
            smoothing_factor: 平滑因子 (可选，覆盖默认值)
            interpolation_points: 插值点数 (可选，覆盖默认值)
            max_curvature: 最大曲率 (可选，覆盖默认值)

        Returns:
            平滑结果
        """
        # 参数验证和设置
        if not isinstance(waypoints, list) or not waypoints:
            raise ValueError("waypoints 必须是非空的航点列表")

        # 使用提供的参数或默认值
        current_params = SmoothingParameters(
            smoothing_factor=smoothing_factor if smoothing_factor is not None else self.parameters.smoothing_factor,
            interpolation_points=interpolation_points if interpolation_points is not None else self.parameters.interpolation_points,
            max_curvature=max_curvature if max_curvature is not None else self.parameters.max_curvature
        )
        import time
        start_time = time.time()

        try:
            # 转换为Point3D格式
            original_path = [
                Point3D(lng=wp.x, lat=wp.y, alt=wp.z, x=wp.x, y=wp.y, z=wp.z)
                for wp in waypoints
            ]
            
            if len(original_path) < 3:
                return SmoothingResult(
                    original_path=original_path,
                    smoothed_path=original_path,
                    curvature_profile=[],
                    smoothness_metrics={},
                    processing_time=time.time() - start_time,
                    success=False
                )
            
            # 执行三次样条平滑
            smoothed_path = self._cubic_spline_smoothing(original_path, current_params)
            
            # 计算曲率分布
            curvature_profile = self._calculate_curvature_profile(smoothed_path)
            
            # 计算平滑度指标
            smoothness_metrics = self._calculate_smoothness_metrics(
                original_path, smoothed_path, curvature_profile, current_params
            )
            
            # 检查是否有S型扭动
            has_s_curves = self._detect_s_curves(smoothed_path, curvature_profile)
            
            # 如果检测到S型扭动，进行修正
            if has_s_curves:
                smoothed_path = self._correct_s_curves(smoothed_path)
                curvature_profile = self._calculate_curvature_profile(smoothed_path)
                smoothness_metrics = self._calculate_smoothness_metrics(
                    original_path, smoothed_path, curvature_profile, current_params
                )
            
            result = SmoothingResult(
                original_path=original_path,
                smoothed_path=smoothed_path,
                curvature_profile=curvature_profile,
                smoothness_metrics=smoothness_metrics,
                processing_time=time.time() - start_time,
                success=True
            )
            
            self.smoothing_history.append(result)
            return result
            
        except Exception as e:
            print(f"路径平滑失败: {e}")
            return SmoothingResult(
                original_path=original_path if 'original_path' in locals() else [],
                smoothed_path=[],
                curvature_profile=[],
                smoothness_metrics={},
                processing_time=time.time() - start_time,
                success=False
            )
    
    def _cubic_spline_smoothing(self, path: List[Point3D], params: SmoothingParameters = None) -> List[Point3D]:
        """三次样条平滑"""
        if len(path) < 3:
            return path
        
        # 提取坐标
        x_coords = [p.x for p in path]
        y_coords = [p.y for p in path]
        z_coords = [p.z for p in path]
        
        # 计算累积弧长作为参数
        t = [0.0]
        for i in range(1, len(path)):
            dx = x_coords[i] - x_coords[i-1]
            dy = y_coords[i] - y_coords[i-1]
            dz = z_coords[i] - z_coords[i-1]
            dist = math.sqrt(dx*dx + dy*dy + dz*dz)
            t.append(t[-1] + dist)
        
        # 归一化参数
        t_max = t[-1]
        if t_max > 0:
            t = [ti / t_max for ti in t]
        
        try:
            # 创建三次样条插值
            cs_x = CubicSpline(t, x_coords, bc_type='natural')
            cs_y = CubicSpline(t, y_coords, bc_type='natural')
            cs_z = CubicSpline(t, z_coords, bc_type='natural')
            
            # 生成平滑路径
            interpolation_points = params.interpolation_points if params else self.parameters.interpolation_points
            t_smooth = np.linspace(0, 1, interpolation_points)
            
            smoothed_path = []
            for ti in t_smooth:
                x_smooth = float(cs_x(ti))
                y_smooth = float(cs_y(ti))
                z_smooth = float(cs_z(ti))
                
                smoothed_path.append(Point3D(
                    lng=x_smooth, lat=y_smooth, alt=z_smooth,
                    x=x_smooth, y=y_smooth, z=z_smooth
                ))
            
            return smoothed_path
            
        except Exception as e:
            print(f"三次样条插值失败: {e}")
            return path
    
    def _calculate_curvature_profile(self, path: List[Point3D]) -> List[float]:
        """计算路径曲率分布"""
        if len(path) < 3:
            return []
        
        curvatures = []
        
        for i in range(1, len(path) - 1):
            # 获取三个连续点
            p1 = path[i-1]
            p2 = path[i]
            p3 = path[i+1]
            
            # 计算向量
            v1 = np.array([p2.x - p1.x, p2.y - p1.y, p2.z - p1.z])
            v2 = np.array([p3.x - p2.x, p3.y - p2.y, p3.z - p2.z])
            
            # 计算曲率
            cross_product = np.cross(v1, v2)
            cross_magnitude = np.linalg.norm(cross_product)
            
            v1_magnitude = np.linalg.norm(v1)
            v2_magnitude = np.linalg.norm(v2)
            
            if v1_magnitude > 0 and v2_magnitude > 0:
                # 曲率公式: κ = |v1 × v2| / (|v1| * |v2| * |v1 + v2|)
                v_sum = v1 + v2
                v_sum_magnitude = np.linalg.norm(v_sum)
                
                if v_sum_magnitude > 0:
                    curvature = cross_magnitude / (v1_magnitude * v2_magnitude * v_sum_magnitude)
                else:
                    curvature = 0.0
            else:
                curvature = 0.0
            
            curvatures.append(curvature)
        
        return curvatures
    
    def _calculate_smoothness_metrics(self, original_path: List[Point3D],
                                    smoothed_path: List[Point3D],
                                    curvature_profile: List[float],
                                    params: SmoothingParameters) -> Dict[str, float]:
        """计算平滑度指标"""
        metrics = {}
        
        # 路径长度变化
        original_length = self._calculate_path_length(original_path)
        smoothed_length = self._calculate_path_length(smoothed_path)
        
        metrics['original_length'] = original_length
        metrics['smoothed_length'] = smoothed_length
        metrics['length_change_ratio'] = (smoothed_length - original_length) / original_length if original_length > 0 else 0
        
        # 曲率统计
        if curvature_profile:
            metrics['max_curvature'] = max(curvature_profile)
            metrics['avg_curvature'] = sum(curvature_profile) / len(curvature_profile)
            metrics['curvature_variance'] = np.var(curvature_profile)
        else:
            metrics['max_curvature'] = 0.0
            metrics['avg_curvature'] = 0.0
            metrics['curvature_variance'] = 0.0
        
        # 转向角度变化
        original_turns = self._calculate_turning_angles(original_path)
        smoothed_turns = self._calculate_turning_angles(smoothed_path)
        
        metrics['original_total_turning'] = sum(abs(angle) for angle in original_turns)
        metrics['smoothed_total_turning'] = sum(abs(angle) for angle in smoothed_turns)
        
        # 平滑度评分 (0-1, 1为最平滑)
        max_curvature_penalty = min(1.0, metrics['max_curvature'] / params.max_curvature)
        variance_penalty = min(1.0, metrics['curvature_variance'] * 10)
        
        metrics['smoothness_score'] = max(0.0, 1.0 - max_curvature_penalty - variance_penalty)
        
        return metrics
    
    def _detect_s_curves(self, path: List[Point3D], curvature_profile: List[float]) -> bool:
        """检测S型扭动"""
        if len(curvature_profile) < 5:
            return False
        
        # 检测连续的正负曲率变化
        sign_changes = 0
        prev_sign = 0
        
        for curvature in curvature_profile:
            current_sign = 1 if curvature > 0.01 else -1 if curvature < -0.01 else 0
            
            if current_sign != 0 and prev_sign != 0 and current_sign != prev_sign:
                sign_changes += 1
            
            if current_sign != 0:
                prev_sign = current_sign
        
        # 如果符号变化过多，可能存在S型扭动
        return sign_changes > len(curvature_profile) * 0.3
    
    def _correct_s_curves(self, path: List[Point3D]) -> List[Point3D]:
        """修正S型扭动"""
        if len(path) < 5:
            return path
        
        # 使用更强的平滑参数重新平滑
        corrected_path = []
        window_size = 5
        
        for i in range(len(path)):
            if i < window_size // 2 or i >= len(path) - window_size // 2:
                corrected_path.append(path[i])
            else:
                # 局部平均平滑
                start_idx = i - window_size // 2
                end_idx = i + window_size // 2 + 1
                
                avg_x = sum(path[j].x for j in range(start_idx, end_idx)) / window_size
                avg_y = sum(path[j].y for j in range(start_idx, end_idx)) / window_size
                avg_z = sum(path[j].z for j in range(start_idx, end_idx)) / window_size
                
                corrected_path.append(Point3D(
                    lng=avg_x, lat=avg_y, alt=avg_z,
                    x=avg_x, y=avg_y, z=avg_z
                ))
        
        return corrected_path
    
    def _calculate_path_length(self, path: List[Point3D]) -> float:
        """计算路径长度"""
        if len(path) < 2:
            return 0.0
        
        total_length = 0.0
        for i in range(len(path) - 1):
            dx = path[i+1].x - path[i].x
            dy = path[i+1].y - path[i].y
            dz = path[i+1].z - path[i].z
            total_length += math.sqrt(dx*dx + dy*dy + dz*dz)
        
        return total_length
    
    def _calculate_turning_angles(self, path: List[Point3D]) -> List[float]:
        """计算转向角度"""
        if len(path) < 3:
            return []
        
        angles = []
        for i in range(1, len(path) - 1):
            # 计算前后两段的方向向量
            v1 = np.array([path[i].x - path[i-1].x, path[i].y - path[i-1].y])
            v2 = np.array([path[i+1].x - path[i].x, path[i+1].y - path[i].y])
            
            # 计算角度
            v1_norm = np.linalg.norm(v1)
            v2_norm = np.linalg.norm(v2)
            
            if v1_norm > 0 and v2_norm > 0:
                cos_angle = np.dot(v1, v2) / (v1_norm * v2_norm)
                cos_angle = np.clip(cos_angle, -1.0, 1.0)
                angle = math.acos(cos_angle)
                
                # 计算转向方向
                cross_product = np.cross(v1, v2)
                if cross_product < 0:
                    angle = -angle
                
                angles.append(angle)
            else:
                angles.append(0.0)
        
        return angles
    
    def get_smoothing_statistics(self) -> Dict[str, Any]:
        """获取平滑统计信息"""
        if not self.smoothing_history:
            return {}
        
        successful_smoothings = [r for r in self.smoothing_history if r.success]
        
        if not successful_smoothings:
            return {'total_smoothings': len(self.smoothing_history), 'success_rate': 0.0}
        
        avg_processing_time = sum(r.processing_time for r in successful_smoothings) / len(successful_smoothings)
        avg_smoothness_score = sum(r.smoothness_metrics.get('smoothness_score', 0) for r in successful_smoothings) / len(successful_smoothings)
        
        return {
            'total_smoothings': len(self.smoothing_history),
            'successful_smoothings': len(successful_smoothings),
            'success_rate': len(successful_smoothings) / len(self.smoothing_history),
            'average_processing_time': avg_processing_time,
            'average_smoothness_score': avg_smoothness_score,
            'parameters': self.parameters.to_dict()
        }
    
    def clear_history(self):
        """清空平滑历史"""
        self.smoothing_history.clear()


# 便捷函数
def smooth_waypoint_path(waypoints: List[ImprovedPathPoint], 
                        parameters: Optional[SmoothingParameters] = None) -> SmoothingResult:
    """平滑航点路径的便捷函数"""
    smoother = PathSmoother(parameters)
    return smoother.smooth_path(waypoints)


if __name__ == "__main__":
    # 测试用例
    from algorithms.improved_cluster_pathfinding import ImprovedPathPoint
    
    # 创建测试路径（包含急转弯）
    test_waypoints = [
        ImprovedPathPoint(x=0, y=0, z=100, waypoint_index=0),
        ImprovedPathPoint(x=50, y=10, z=110, waypoint_index=1),
        ImprovedPathPoint(x=100, y=0, z=120, waypoint_index=2),
        ImprovedPathPoint(x=150, y=50, z=130, waypoint_index=3),
        ImprovedPathPoint(x=200, y=100, z=140, waypoint_index=4)
    ]
    
    # 执行平滑
    smoother = PathSmoother()
    result = smoother.smooth_path(test_waypoints)
    
    print(f"平滑结果:")
    print(f"  成功: {result.success}")
    print(f"  原始路径长度: {result.smoothness_metrics.get('original_length', 0):.1f}m")
    print(f"  平滑路径长度: {result.smoothness_metrics.get('smoothed_length', 0):.1f}m")
    print(f"  最大曲率: {result.smoothness_metrics.get('max_curvature', 0):.4f}")
    print(f"  平滑度评分: {result.smoothness_metrics.get('smoothness_score', 0):.2f}")
    print(f"  处理时间: {result.processing_time:.3f}s")
