#!/usr/bin/env python3
"""
物体识别模拟器
模拟无人机在航点处的物体检测和碰撞代价计算
"""

import random
import math
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from algorithms.data_structures import Point3D
from algorithms.improved_cluster_pathfinding import ImprovedPathPoint, ObjectType


@dataclass
class DetectedObject:
    """检测到的物体"""
    object_type: ObjectType                # 物体类型
    position: Point3D                      # 物体位置
    confidence: float                      # 检测置信度 (0-1)
    size: Tuple[float, float, float]       # 物体尺寸 (长, 宽, 高)
    velocity: Optional[Tuple[float, float]] = None  # 物体速度 (vx, vy)
    distance_to_waypoint: float = 0.0      # 到航点的距离
    threat_level: str = "low"              # 威胁等级: low, medium, high
    detection_time: float = 0.0            # 检测时间戳
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'objectType': self.object_type.value,
            'position': {
                'x': self.position.x,
                'y': self.position.y,
                'z': self.position.z
            },
            'confidence': self.confidence,
            'size': {
                'length': self.size[0],
                'width': self.size[1],
                'height': self.size[2]
            },
            'velocity': {
                'vx': self.velocity[0] if self.velocity else 0,
                'vy': self.velocity[1] if self.velocity else 0
            } if self.velocity else None,
            'distanceToWaypoint': self.distance_to_waypoint,
            'threatLevel': self.threat_level,
            'detectionTime': self.detection_time
        }


@dataclass
class DetectionEnvironment:
    """检测环境配置"""
    detection_radius: float = 100.0        # 检测半径 (m)
    weather_condition: str = "clear"       # 天气条件: clear, cloudy, rainy, foggy
    time_of_day: str = "day"              # 时间: day, night, dawn, dusk
    visibility: float = 1.0               # 能见度 (0-1)
    noise_level: float = 0.1              # 噪声水平 (0-1)
    
    def get_detection_modifier(self) -> float:
        """获取检测修正因子"""
        modifier = 1.0
        
        # 天气影响
        weather_modifiers = {
            "clear": 1.0,
            "cloudy": 0.9,
            "rainy": 0.7,
            "foggy": 0.5
        }
        modifier *= weather_modifiers.get(self.weather_condition, 1.0)
        
        # 时间影响
        time_modifiers = {
            "day": 1.0,
            "dawn": 0.8,
            "dusk": 0.8,
            "night": 0.6
        }
        modifier *= time_modifiers.get(self.time_of_day, 1.0)
        
        # 能见度影响
        modifier *= self.visibility
        
        # 噪声影响
        modifier *= (1.0 - self.noise_level * 0.3)
        
        return max(0.1, modifier)


class ObjectDetectionSimulator:
    """物体检测模拟器"""
    
    def __init__(self, environment: Optional[DetectionEnvironment] = None):
        self.environment = environment or DetectionEnvironment()
        self.object_database = self._initialize_object_database()
        self.detection_history: List[Dict[str, Any]] = []
        
        # 检测参数
        self.base_detection_probability = 0.85  # 基础检测概率
        self.false_positive_rate = 0.05         # 误检率
        self.detection_latency = 0.1            # 检测延迟 (s)
        
    def _initialize_object_database(self) -> Dict[ObjectType, Dict[str, Any]]:
        """初始化物体数据库"""
        return {
            ObjectType.PEDESTRIAN: {
                'typical_size': (0.6, 0.4, 1.7),
                'speed_range': (0.5, 2.0),
                'detection_difficulty': 0.8,
                'threat_base': 'medium'
            },
            ObjectType.BICYCLE: {
                'typical_size': (1.8, 0.6, 1.2),
                'speed_range': (2.0, 8.0),
                'detection_difficulty': 0.7,
                'threat_base': 'medium'
            },
            ObjectType.VEHICLE: {
                'typical_size': (4.5, 1.8, 1.5),
                'speed_range': (0.0, 15.0),
                'detection_difficulty': 0.9,
                'threat_base': 'high'
            },
            ObjectType.BUILDING: {
                'typical_size': (10.0, 10.0, 20.0),
                'speed_range': (0.0, 0.0),
                'detection_difficulty': 1.0,
                'threat_base': 'high'
            }
        }
    
    def simulate_detection_at_waypoint(self, waypoint: ImprovedPathPoint,
                                     real_objects: Optional[List[Dict]] = None) -> List[DetectedObject]:
        """
        模拟在航点处的物体检测
        
        Args:
            waypoint: 目标航点
            real_objects: 真实存在的物体列表（用于模拟真实环境）
            
        Returns:
            检测到的物体列表
        """
        detected_objects = []
        detection_time = time.time()
        
        # 如果没有提供真实物体，生成随机物体
        if real_objects is None:
            real_objects = self._generate_random_objects_around_waypoint(waypoint)
        
        # 模拟检测过程
        for real_obj in real_objects:
            detected_obj = self._simulate_object_detection(waypoint, real_obj, detection_time)
            if detected_obj:
                detected_objects.append(detected_obj)
        
        # 模拟误检（假阳性）
        false_positives = self._generate_false_positives(waypoint, detection_time)
        detected_objects.extend(false_positives)
        
        # 记录检测历史
        self.detection_history.append({
            'waypoint': waypoint.to_dict(),
            'detected_objects': [obj.to_dict() for obj in detected_objects],
            'detection_time': detection_time,
            'environment': {
                'weather': self.environment.weather_condition,
                'time_of_day': self.environment.time_of_day,
                'visibility': self.environment.visibility
            }
        })
        
        return detected_objects
    
    def _generate_random_objects_around_waypoint(self, waypoint: ImprovedPathPoint) -> List[Dict]:
        """在航点周围生成随机物体"""
        objects = []
        
        # 随机生成1-5个物体
        num_objects = random.randint(1, 5)
        
        for _ in range(num_objects):
            # 随机选择物体类型
            object_type = random.choice(list(ObjectType))
            
            # 在检测半径内随机生成位置
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(10, self.environment.detection_radius)
            
            obj_x = waypoint.x + distance * math.cos(angle)
            obj_y = waypoint.y + distance * math.sin(angle)
            obj_z = random.uniform(0, 50)  # 地面到50米高度
            
            # 随机生成速度
            obj_data = self.object_database[object_type]
            speed_min, speed_max = obj_data['speed_range']
            speed = random.uniform(speed_min, speed_max)
            velocity_angle = random.uniform(0, 2 * math.pi)
            
            objects.append({
                'type': object_type,
                'position': {'x': obj_x, 'y': obj_y, 'z': obj_z},
                'velocity': {
                    'vx': speed * math.cos(velocity_angle),
                    'vy': speed * math.sin(velocity_angle)
                },
                'size': obj_data['typical_size']
            })
        
        return objects
    
    def _simulate_object_detection(self, waypoint: ImprovedPathPoint, real_obj: Dict,
                                 detection_time: float) -> Optional[DetectedObject]:
        """模拟单个物体的检测过程"""
        object_type = real_obj['type']
        obj_pos = real_obj['position']
        
        # 计算距离
        dx = obj_pos['x'] - waypoint.x
        dy = obj_pos['y'] - waypoint.y
        dz = obj_pos['z'] - waypoint.z
        distance = math.sqrt(dx*dx + dy*dy + dz*dz)
        
        # 检查是否在检测范围内
        if distance > self.environment.detection_radius:
            return None
        
        # 计算检测概率
        obj_data = self.object_database[object_type]
        detection_difficulty = obj_data['detection_difficulty']
        environment_modifier = self.environment.get_detection_modifier()
        distance_modifier = max(0.1, 1.0 - distance / self.environment.detection_radius)
        
        detection_probability = (self.base_detection_probability * 
                               detection_difficulty * 
                               environment_modifier * 
                               distance_modifier)
        
        # 判断是否检测到
        if random.random() > detection_probability:
            return None  # 漏检
        
        # 生成检测置信度
        base_confidence = detection_probability
        noise = random.uniform(-0.1, 0.1) * self.environment.noise_level
        confidence = max(0.1, min(1.0, base_confidence + noise))
        
        # 添加位置噪声
        position_noise = self.environment.noise_level * 5.0  # 最大5米误差
        noisy_x = obj_pos['x'] + random.uniform(-position_noise, position_noise)
        noisy_y = obj_pos['y'] + random.uniform(-position_noise, position_noise)
        noisy_z = obj_pos['z'] + random.uniform(-position_noise/2, position_noise/2)
        
        # 确定威胁等级
        threat_level = self._calculate_threat_level(distance, object_type, real_obj.get('velocity'))
        
        return DetectedObject(
            object_type=object_type,
            position=Point3D(lng=noisy_x, lat=noisy_y, alt=noisy_z, x=noisy_x, y=noisy_y, z=noisy_z),
            confidence=confidence,
            size=real_obj.get('size', obj_data['typical_size']),
            velocity=real_obj.get('velocity', {}).values() if real_obj.get('velocity') else None,
            distance_to_waypoint=distance,
            threat_level=threat_level,
            detection_time=detection_time
        )
    
    def _generate_false_positives(self, waypoint: ImprovedPathPoint, 
                                detection_time: float) -> List[DetectedObject]:
        """生成误检物体"""
        false_positives = []
        
        # 根据误检率决定是否生成误检
        if random.random() < self.false_positive_rate:
            # 生成1个误检物体
            object_type = random.choice(list(ObjectType))
            
            # 随机位置
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(20, self.environment.detection_radius * 0.8)
            
            fp_x = waypoint.x + distance * math.cos(angle)
            fp_y = waypoint.y + distance * math.sin(angle)
            fp_z = random.uniform(0, 30)
            
            # 误检的置信度通常较低
            confidence = random.uniform(0.3, 0.7)
            
            obj_data = self.object_database[object_type]
            
            false_positive = DetectedObject(
                object_type=object_type,
                position=Point3D(lng=fp_x, lat=fp_y, alt=fp_z, x=fp_x, y=fp_y, z=fp_z),
                confidence=confidence,
                size=obj_data['typical_size'],
                velocity=None,
                distance_to_waypoint=distance,
                threat_level="low",
                detection_time=detection_time
            )
            
            false_positives.append(false_positive)
        
        return false_positives
    
    def _calculate_threat_level(self, distance: float, object_type: ObjectType, 
                              velocity: Optional[Dict]) -> str:
        """计算威胁等级"""
        base_threat = self.object_database[object_type]['threat_base']
        
        # 距离因子
        if distance < 20:
            distance_factor = "high"
        elif distance < 50:
            distance_factor = "medium"
        else:
            distance_factor = "low"
        
        # 速度因子
        speed_factor = "low"
        if velocity:
            speed = math.sqrt(velocity.get('vx', 0)**2 + velocity.get('vy', 0)**2)
            if speed > 5.0:
                speed_factor = "high"
            elif speed > 2.0:
                speed_factor = "medium"
        
        # 综合威胁等级
        threat_levels = ["low", "medium", "high"]
        base_level = threat_levels.index(base_threat)
        distance_level = threat_levels.index(distance_factor)
        speed_level = threat_levels.index(speed_factor)
        
        # 取最高威胁等级
        final_level = max(base_level, distance_level, speed_level)
        return threat_levels[final_level]
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        if not self.detection_history:
            return {}
        
        total_detections = sum(len(record['detected_objects']) for record in self.detection_history)
        total_waypoints = len(self.detection_history)
        
        # 按物体类型统计
        type_counts = {}
        threat_counts = {"low": 0, "medium": 0, "high": 0}
        confidence_sum = 0.0
        
        for record in self.detection_history:
            for obj in record['detected_objects']:
                obj_type = obj['objectType']
                type_counts[obj_type] = type_counts.get(obj_type, 0) + 1
                threat_counts[obj['threatLevel']] += 1
                confidence_sum += obj['confidence']
        
        return {
            'total_waypoints_scanned': total_waypoints,
            'total_objects_detected': total_detections,
            'average_objects_per_waypoint': total_detections / total_waypoints,
            'object_type_distribution': type_counts,
            'threat_level_distribution': threat_counts,
            'average_confidence': confidence_sum / total_detections if total_detections > 0 else 0,
            'detection_environment': {
                'weather': self.environment.weather_condition,
                'time_of_day': self.environment.time_of_day,
                'visibility': self.environment.visibility,
                'detection_radius': self.environment.detection_radius
            }
        }
    
    def clear_detection_history(self):
        """清空检测历史"""
        self.detection_history.clear()


# 便捷函数
def simulate_waypoint_detection(waypoint: ImprovedPathPoint, 
                              environment: Optional[DetectionEnvironment] = None) -> List[DetectedObject]:
    """为单个航点模拟物体检测"""
    simulator = ObjectDetectionSimulator(environment)
    return simulator.simulate_detection_at_waypoint(waypoint)


if __name__ == "__main__":
    # 测试用例
    test_waypoint = ImprovedPathPoint(x=100, y=100, z=50, waypoint_index=1)
    test_environment = DetectionEnvironment(
        weather_condition="clear",
        time_of_day="day",
        visibility=0.9
    )
    
    simulator = ObjectDetectionSimulator(test_environment)
    detected_objects = simulator.simulate_detection_at_waypoint(test_waypoint)
    
    print(f"检测到 {len(detected_objects)} 个物体:")
    for i, obj in enumerate(detected_objects):
        print(f"  物体{i+1}: {obj.object_type.value} "
              f"距离{obj.distance_to_waypoint:.1f}m "
              f"置信度{obj.confidence:.2f} "
              f"威胁等级{obj.threat_level}")
    
    stats = simulator.get_detection_statistics()
    print(f"\n检测统计: {stats}")
