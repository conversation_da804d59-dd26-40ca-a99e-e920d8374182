"""
后端日志系统
严格按步骤记录后端输出，支持终端和文件双重输出
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
from enum import Enum


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class StepType(Enum):
    """步骤类型枚举"""
    ALGORITHM_START = "算法开始"
    ALGORITHM_END = "算法结束"
    PATH_GENERATION = "路径生成"
    CLUSTERING = "分簇处理"
    COST_CALCULATION = "代价计算"
    PATH_SWITCHING = "路径切换"
    ERROR_HANDLING = "错误处理"
    API_REQUEST = "API请求"
    API_RESPONSE = "API响应"
    DATABASE_OPERATION = "数据库操作"
    FILE_OPERATION = "文件操作"
    SYSTEM_INFO = "系统信息"


class DroneLogger:
    """无人机后端专用日志器"""
    
    def __init__(self, name: str = "drone_backend"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 确保日志目录存在
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置日志文件路径
        self.setup_handlers()
        
        # 步骤计数器
        self.step_counter = 0
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 当前执行的算法信息
        self.current_algorithm = None
        self.current_request_id = None
        
    def setup_handlers(self):
        """设置日志处理器"""
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)8s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器 - 按日期分文件
        today = datetime.now().strftime("%Y%m%d")
        log_file = self.log_dir / f"drone_backend_{today}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # 错误日志单独文件
        error_log_file = self.log_dir / f"drone_errors_{today}.log"
        error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        self.logger.addHandler(error_handler)
        
        # 步骤日志 - JSON格式
        self.step_log_file = self.log_dir / f"drone_steps_{today}.json"
        
    def log_step(self, step_type: StepType, message: str, level: LogLevel = LogLevel.INFO, 
                 details: Optional[Dict[str, Any]] = None, duration: Optional[float] = None):
        """记录步骤日志"""
        self.step_counter += 1
        
        # 创建步骤记录
        step_record = {
            "session_id": self.session_id,
            "step_number": self.step_counter,
            "timestamp": datetime.now().isoformat(),
            "step_type": step_type.value,
            "level": level.value,
            "message": message,
            "algorithm": self.current_algorithm,
            "request_id": self.current_request_id,
            "details": details or {},
            "duration_ms": duration
        }
        
        # 写入步骤日志文件
        self._write_step_log(step_record)
        
        # 同时输出到标准日志
        log_message = f"步骤{self.step_counter:03d} | {step_type.value} | {message}"
        if duration:
            log_message += f" | 耗时: {duration:.2f}ms"
            
        getattr(self.logger, level.value.lower())(log_message)
        
    def _write_step_log(self, step_record: Dict[str, Any]):
        """写入步骤日志到JSON文件"""
        try:
            # 读取现有日志
            if self.step_log_file.exists():
                with open(self.step_log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            else:
                logs = []
            
            # 添加新记录
            logs.append(step_record)
            
            # 写回文件
            with open(self.step_log_file, 'w', encoding='utf-8') as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"写入步骤日志失败: {e}")
    
    def start_algorithm(self, algorithm_name: str, request_id: str = None, params: Dict[str, Any] = None):
        """开始算法执行"""
        self.current_algorithm = algorithm_name
        self.current_request_id = request_id or f"req_{self.session_id}_{self.step_counter}"
        
        self.log_step(
            StepType.ALGORITHM_START,
            f"开始执行算法: {algorithm_name}",
            LogLevel.INFO,
            {
                "algorithm_name": algorithm_name,
                "request_id": self.current_request_id,
                "parameters": params or {}
            }
        )
    
    def end_algorithm(self, success: bool = True, result: Dict[str, Any] = None, error: str = None):
        """结束算法执行"""
        level = LogLevel.INFO if success else LogLevel.ERROR
        message = f"算法执行{'成功' if success else '失败'}: {self.current_algorithm}"
        
        details = {
            "algorithm_name": self.current_algorithm,
            "request_id": self.current_request_id,
            "success": success
        }
        
        if result:
            details["result"] = result
        if error:
            details["error"] = error
            
        self.log_step(StepType.ALGORITHM_END, message, level, details)
        
        # 重置当前算法信息
        self.current_algorithm = None
        self.current_request_id = None
    
    def log_path_generation(self, path_count: int, generation_time: float, details: Dict[str, Any] = None):
        """记录路径生成步骤"""
        self.log_step(
            StepType.PATH_GENERATION,
            f"生成路径 {path_count} 条",
            LogLevel.INFO,
            {
                "path_count": path_count,
                "generation_details": details or {}
            },
            generation_time
        )
    
    def log_clustering(self, cluster_count: int, path_count: int, clustering_time: float):
        """记录分簇步骤"""
        self.log_step(
            StepType.CLUSTERING,
            f"完成分簇: {cluster_count} 个簇，{path_count} 条路径",
            LogLevel.INFO,
            {
                "cluster_count": cluster_count,
                "path_count": path_count
            },
            clustering_time
        )
    
    def log_cost_calculation(self, calculation_type: str, cost_value: float, calculation_time: float):
        """记录代价计算步骤"""
        self.log_step(
            StepType.COST_CALCULATION,
            f"{calculation_type}计算完成: {cost_value:.6f}",
            LogLevel.INFO,
            {
                "calculation_type": calculation_type,
                "cost_value": cost_value
            },
            calculation_time
        )
    
    def log_path_switching(self, from_path: str, to_path: str, reason: str, switching_time: float):
        """记录路径切换步骤"""
        self.log_step(
            StepType.PATH_SWITCHING,
            f"路径切换: {from_path} -> {to_path}",
            LogLevel.INFO,
            {
                "from_path": from_path,
                "to_path": to_path,
                "reason": reason
            },
            switching_time
        )
    
    def log_api_request(self, endpoint: str, method: str, params: Dict[str, Any] = None):
        """记录API请求"""
        self.log_step(
            StepType.API_REQUEST,
            f"{method} {endpoint}",
            LogLevel.INFO,
            {
                "endpoint": endpoint,
                "method": method,
                "parameters": params or {}
            }
        )
    
    def log_api_response(self, endpoint: str, status_code: int, response_time: float, 
                        response_size: int = None):
        """记录API响应"""
        level = LogLevel.INFO if 200 <= status_code < 400 else LogLevel.WARNING
        self.log_step(
            StepType.API_RESPONSE,
            f"{endpoint} 响应: {status_code}",
            level,
            {
                "endpoint": endpoint,
                "status_code": status_code,
                "response_size": response_size
            },
            response_time
        )
    
    def log_error(self, error: Exception, context: str = None):
        """记录错误"""
        error_details = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc(),
            "context": context
        }
        
        self.log_step(
            StepType.ERROR_HANDLING,
            f"发生错误: {type(error).__name__}: {str(error)}",
            LogLevel.ERROR,
            error_details
        )
    
    def get_session_summary(self) -> Dict[str, Any]:
        """获取当前会话的日志摘要"""
        try:
            if not self.step_log_file.exists():
                return {"total_steps": 0, "session_id": self.session_id}
            
            with open(self.step_log_file, 'r', encoding='utf-8') as f:
                logs = json.load(f)
            
            session_logs = [log for log in logs if log.get("session_id") == self.session_id]
            
            summary = {
                "session_id": self.session_id,
                "total_steps": len(session_logs),
                "step_types": {},
                "error_count": 0,
                "algorithms_executed": set()
            }
            
            for log in session_logs:
                step_type = log.get("step_type", "未知")
                summary["step_types"][step_type] = summary["step_types"].get(step_type, 0) + 1
                
                if log.get("level") == "ERROR":
                    summary["error_count"] += 1
                
                if log.get("algorithm"):
                    summary["algorithms_executed"].add(log["algorithm"])
            
            summary["algorithms_executed"] = list(summary["algorithms_executed"])
            return summary
            
        except Exception as e:
            self.logger.error(f"获取会话摘要失败: {e}")
            return {"error": str(e)}


# 全局日志器实例
drone_logger = DroneLogger()


def get_logger() -> DroneLogger:
    """获取全局日志器实例"""
    return drone_logger


# 便捷函数
def log_step(step_type: StepType, message: str, level: LogLevel = LogLevel.INFO, 
             details: Optional[Dict[str, Any]] = None, duration: Optional[float] = None):
    """便捷的步骤日志函数"""
    drone_logger.log_step(step_type, message, level, details, duration)


def log_error(error: Exception, context: str = None):
    """便捷的错误日志函数"""
    drone_logger.log_error(error, context)
