#!/usr/bin/env python3
"""
获取实际的算法对比数据
分析为什么总体评估与各项指标不一致
"""

def get_actual_comparison_data():
    """获取实际的算法对比数据"""
    
    print("🔍 获取实际的算法对比数据")
    print("=" * 60)
    
    try:
        from algorithm_comparison_api import get_latest_improved_result, get_latest_baseline_result
        
        # 获取最新的算法对比结果
        improved_result = get_latest_improved_result()
        baseline_result = get_latest_baseline_result()
        
        print(f"📊 改进算法结果: {'✅ 可用' if improved_result else '❌ 不可用'}")
        print(f"📊 基准算法结果: {'✅ 可用' if baseline_result else '❌ 不可用'}")
        
        if not improved_result or not baseline_result:
            print("❌ 缺少算法对比数据，请先运行算法对比")
            return None
        
        # 提取关键数据
        improved_data = {
            "path_length": improved_result.get('path_length', 0),
            "turning_cost": improved_result.get('turning_cost', 0),
            "risk_value": improved_result.get('risk_value', 0),
            "collision_cost": improved_result.get('collision_cost', 0),
            "final_cost": improved_result.get('final_cost', 0)
        }
        
        baseline_data = {
            "path_length": baseline_result.get('path_length', 0),
            "turning_cost": baseline_result.get('turning_cost', 0),
            "risk_value": baseline_result.get('risk_value', 0),
            "collision_cost": baseline_result.get('collision_cost', 0),
            "final_cost": baseline_result.get('final_cost', 0)
        }
        
        print(f"\n📊 改进算法实际数据：")
        for metric, value in improved_data.items():
            print(f"   {metric}: {value}")
        
        print(f"\n📊 基准算法实际数据：")
        for metric, value in baseline_data.items():
            print(f"   {metric}: {value}")
        
        # 计算改进率
        print(f"\n📈 实际改进率：")
        improvements = {}
        for metric in improved_data.keys():
            baseline_value = baseline_data[metric]
            improved_value = improved_data[metric]
            
            if baseline_value > 0:
                improvement = ((baseline_value - improved_value) / baseline_value) * 100
            else:
                improvement = 0
            
            improvements[metric] = improvement
            status = "改进" if improvement > 0 else "退化"
            print(f"   {metric}: {improvement:+.2f}% ({status})")
        
        # 分析最终代价计算
        print(f"\n🧮 最终代价分析：")
        
        # 使用系统的权重
        weights = {
            "alpha": 0.5,   # 风险权重
            "beta": 0.4,    # 碰撞权重
            "gamma": 0.05,  # 长度权重
            "delta": 0.05   # 转向权重
        }
        
        print(f"权重设置：")
        for name, weight in weights.items():
            print(f"   {name}: {weight} ({weight*100:.0f}%)")
        
        # 检查参考值（这可能是问题所在）
        print(f"\n🔍 检查参考值计算：")
        
        # 从改进算法结果中查找参考值信息
        if 'metadata' in improved_result:
            metadata = improved_result['metadata']
            print(f"改进算法metadata: {metadata}")
        
        if 'metadata' in baseline_result:
            metadata = baseline_result['metadata']
            print(f"基准算法metadata: {metadata}")
        
        # 手动计算最终代价（使用系统的方法）
        def calculate_final_cost_manually(data, weights):
            # 使用动态参考值计算（与系统一致）
            risk_reference = max(100.0, data["risk_value"] * 2.0)
            collision_reference = max(50.0, data["collision_cost"] * 2.0)
            turning_reference = max(30.0, data["turning_cost"] * 1.5)
            manhattan_length = data["path_length"] * 1.5
            
            risk_term = weights["alpha"] * (data["risk_value"] / risk_reference)
            collision_term = weights["beta"] * (data["collision_cost"] / collision_reference)
            length_term = weights["gamma"] * (data["path_length"] / manhattan_length)
            turning_term = weights["delta"] * (data["turning_cost"] / turning_reference)
            
            final_cost = risk_term + collision_term + length_term + turning_term
            
            return {
                "risk_reference": risk_reference,
                "collision_reference": collision_reference,
                "turning_reference": turning_reference,
                "manhattan_length": manhattan_length,
                "risk_term": risk_term,
                "collision_term": collision_term,
                "length_term": length_term,
                "turning_term": turning_term,
                "final_cost": final_cost
            }
        
        print(f"\n🧮 手动计算基准算法最终代价：")
        baseline_calc = calculate_final_cost_manually(baseline_data, weights)
        for key, value in baseline_calc.items():
            print(f"   {key}: {value:.6f}")
        
        print(f"\n🧮 手动计算改进算法最终代价：")
        improved_calc = calculate_final_cost_manually(improved_data, weights)
        for key, value in improved_calc.items():
            print(f"   {key}: {value:.6f}")
        
        # 计算手动计算的改进率
        manual_improvement = ((baseline_calc["final_cost"] - improved_calc["final_cost"]) / baseline_calc["final_cost"]) * 100
        print(f"\n🎯 手动计算的最终代价改进率: {manual_improvement:+.2f}%")
        
        # 对比系统报告的最终代价
        system_improvement = improvements["final_cost"]
        print(f"🎯 系统报告的最终代价改进率: {system_improvement:+.2f}%")
        
        if abs(manual_improvement - system_improvement) > 1:
            print(f"⚠️ 手动计算与系统报告不一致！差异: {abs(manual_improvement - system_improvement):.2f}%")
        else:
            print(f"✅ 手动计算与系统报告一致")
        
        # 分析问题
        print(f"\n🔍 问题分析：")
        
        if system_improvement < -50:  # 如果退化超过50%
            print(f"1. 最终代价严重退化 ({system_improvement:.1f}%)，可能原因：")
            print(f"   - 参考值计算有问题")
            print(f"   - 某项指标的绝对值差异很大")
            print(f"   - 权重设置不合理")
            
            # 检查各项的绝对贡献
            print(f"\n📊 各项对最终代价的绝对贡献：")
            print(f"基准算法：")
            for term in ["risk_term", "collision_term", "length_term", "turning_term"]:
                print(f"   {term}: {baseline_calc[term]:.6f}")
            
            print(f"改进算法：")
            for term in ["risk_term", "collision_term", "length_term", "turning_term"]:
                print(f"   {term}: {improved_calc[term]:.6f}")
                
            print(f"\n📈 各项贡献的变化：")
            for term in ["risk_term", "collision_term", "length_term", "turning_term"]:
                baseline_term = baseline_calc[term]
                improved_term = improved_calc[term]
                change = improved_term - baseline_term
                print(f"   {term}: {change:+.6f}")
        
        return {
            "improved_data": improved_data,
            "baseline_data": baseline_data,
            "improvements": improvements,
            "manual_improvement": manual_improvement,
            "system_improvement": system_improvement
        }
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = get_actual_comparison_data()
    if result:
        print(f"\n✅ 数据分析完成")
        print(f"   系统报告改进率: {result['system_improvement']:+.2f}%")
        print(f"   手动计算改进率: {result['manual_improvement']:+.2f}%")
    else:
        print(f"\n❌ 无法获取数据，请先运行算法对比")
