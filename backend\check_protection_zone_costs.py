#!/usr/bin/env python3
"""
检查保护区的碰撞代价计算
"""

from protection_zones import ProtectionZoneManager
import math

def check_protection_zone_costs():
    """检查保护区的碰撞代价"""
    
    print("🔍 检查保护区碰撞代价计算")
    print("=" * 80)
    
    manager = ProtectionZoneManager()
    
    print(f"总保护区数: {len(manager.zones)}")
    print()
    
    for i, zone in enumerate(manager.zones[:5]):
        print(f"保护区 {i+1}: {zone.name}")
        print(f"  类型: {zone.zone_type.value}")
        print(f"  人数: {zone.people_count}")
        print(f"  车辆: {zone.vehicle_count}")
        print(f"  半径: {zone.radius}m")
        
        # 计算面积
        area = math.pi * (zone.radius ** 2)
        print(f"  面积: {area:.0f}m²")
        
        # 计算代价
        total_cost = zone.total_crash_cost
        avg_cost = zone.average_crash_cost
        
        print(f"  总碰撞代价: {total_cost}")
        print(f"  平均碰撞代价(每m²): {avg_cost:.8f}")
        
        # 模拟30米检测范围的碰撞代价
        detection_area = math.pi * 30 * 30  # 30米圆的面积
        simulated_cost = avg_cost * detection_area
        
        print(f"  30米检测范围面积: {detection_area:.0f}m²")
        print(f"  30米检测范围碰撞代价: {simulated_cost:.4f}")
        
        # 测试实际的get_collision_cost方法
        # 假设无人机在保护区中心
        actual_cost = zone.get_collision_cost(zone.center[0], zone.center[1])
        print(f"  实际get_collision_cost(中心点): {actual_cost:.4f}")
        
        print()

if __name__ == "__main__":
    check_protection_zone_costs()
