#!/usr/bin/env python3
"""
测试修复后的参考值计算
验证使用固定参考值后，最终代价计算是否合理
"""

def test_fixed_reference_values():
    """测试固定参考值的最终代价计算"""
    
    print("🔍 测试修复后的参考值计算")
    print("=" * 60)
    
    # 使用实际的算法对比数据
    improved_data = {
        "path_length": 8247.562820058358,
        "turning_cost": 0.13739757359607488,
        "risk_value": 82.66662758011446,
        "collision_cost": 56.13324509127044
    }
    
    baseline_data = {
        "path_length": 11352.748393058726,
        "turning_cost": 1.6178121866023263,
        "risk_value": 113.52910174277388,
        "collision_cost": 56.84463257462375
    }
    
    print("📊 实际数据：")
    print("改进算法：")
    for metric, value in improved_data.items():
        print(f"   {metric}: {value:.6f}")
    
    print("基准算法：")
    for metric, value in baseline_data.items():
        print(f"   {metric}: {value:.6f}")
    
    # 使用系统权重
    weights = {
        "alpha": 0.5,   # 风险权重
        "beta": 0.4,    # 碰撞权重
        "gamma": 0.05,  # 长度权重
        "delta": 0.05   # 转向权重
    }
    
    print(f"\n⚖️ 权重设置：")
    for name, weight in weights.items():
        print(f"   {name}: {weight} ({weight*100:.0f}%)")
    
    # 修复前：使用动态参考值
    print(f"\n🔧 修复前：使用动态参考值")
    
    def calculate_final_cost_dynamic(data, weights):
        # 动态参考值（修复前的方法）
        risk_reference = max(100.0, data["risk_value"] * 2.0)
        collision_reference = max(50.0, data["collision_cost"] * 2.0)
        turning_reference = max(30.0, data["turning_cost"] * 1.5)
        manhattan_length = data["path_length"] * 1.5
        
        risk_term = weights["alpha"] * (data["risk_value"] / risk_reference)
        collision_term = weights["beta"] * (data["collision_cost"] / collision_reference)
        length_term = weights["gamma"] * (data["path_length"] / manhattan_length)
        turning_term = weights["delta"] * (data["turning_cost"] / turning_reference)
        
        final_cost = risk_term + collision_term + length_term + turning_term
        
        return {
            "risk_reference": risk_reference,
            "collision_reference": collision_reference,
            "turning_reference": turning_reference,
            "manhattan_length": manhattan_length,
            "final_cost": final_cost
        }
    
    baseline_dynamic = calculate_final_cost_dynamic(baseline_data, weights)
    improved_dynamic = calculate_final_cost_dynamic(improved_data, weights)
    
    print(f"基准算法（动态参考值）：")
    print(f"   参考值: risk={baseline_dynamic['risk_reference']:.2f}, collision={baseline_dynamic['collision_reference']:.2f}")
    print(f"   最终代价: {baseline_dynamic['final_cost']:.6f}")
    
    print(f"改进算法（动态参考值）：")
    print(f"   参考值: risk={improved_dynamic['risk_reference']:.2f}, collision={improved_dynamic['collision_reference']:.2f}")
    print(f"   最终代价: {improved_dynamic['final_cost']:.6f}")
    
    dynamic_improvement = ((baseline_dynamic['final_cost'] - improved_dynamic['final_cost']) / baseline_dynamic['final_cost']) * 100
    print(f"   动态参考值改进率: {dynamic_improvement:+.2f}%")
    
    # 修复后：使用固定参考值
    print(f"\n✅ 修复后：使用固定参考值")
    
    def calculate_final_cost_fixed(data, weights):
        # 固定参考值（修复后的方法）
        risk_reference = 100.0
        collision_reference = 50.0
        turning_reference = 30.0
        manhattan_length = data["path_length"] * 1.5
        
        risk_term = weights["alpha"] * (data["risk_value"] / risk_reference)
        collision_term = weights["beta"] * (data["collision_cost"] / collision_reference)
        length_term = weights["gamma"] * (data["path_length"] / manhattan_length)
        turning_term = weights["delta"] * (data["turning_cost"] / turning_reference)
        
        final_cost = risk_term + collision_term + length_term + turning_term
        
        return {
            "risk_reference": risk_reference,
            "collision_reference": collision_reference,
            "turning_reference": turning_reference,
            "manhattan_length": manhattan_length,
            "risk_term": risk_term,
            "collision_term": collision_term,
            "length_term": length_term,
            "turning_term": turning_term,
            "final_cost": final_cost
        }
    
    baseline_fixed = calculate_final_cost_fixed(baseline_data, weights)
    improved_fixed = calculate_final_cost_fixed(improved_data, weights)
    
    print(f"基准算法（固定参考值）：")
    print(f"   参考值: risk={baseline_fixed['risk_reference']:.2f}, collision={baseline_fixed['collision_reference']:.2f}")
    print(f"   最终代价: {baseline_fixed['final_cost']:.6f}")
    
    print(f"改进算法（固定参考值）：")
    print(f"   参考值: risk={improved_fixed['risk_reference']:.2f}, collision={improved_fixed['collision_reference']:.2f}")
    print(f"   最终代价: {improved_fixed['final_cost']:.6f}")
    
    fixed_improvement = ((baseline_fixed['final_cost'] - improved_fixed['final_cost']) / baseline_fixed['final_cost']) * 100
    print(f"   固定参考值改进率: {fixed_improvement:+.2f}%")
    
    # 详细分析各项贡献
    print(f"\n📊 详细分析（固定参考值）：")
    print(f"基准算法各项贡献：")
    for term in ["risk_term", "collision_term", "length_term", "turning_term"]:
        print(f"   {term}: {baseline_fixed[term]:.6f}")
    
    print(f"改进算法各项贡献：")
    for term in ["risk_term", "collision_term", "length_term", "turning_term"]:
        print(f"   {term}: {improved_fixed[term]:.6f}")
    
    print(f"各项贡献变化：")
    for term in ["risk_term", "collision_term", "length_term", "turning_term"]:
        change = improved_fixed[term] - baseline_fixed[term]
        print(f"   {term}: {change:+.6f}")
    
    # 计算各项指标的改进率
    print(f"\n📈 各项指标改进率：")
    for metric in improved_data.keys():
        baseline_value = baseline_data[metric]
        improved_value = improved_data[metric]
        improvement = ((baseline_value - improved_value) / baseline_value) * 100
        status = "改进" if improvement > 0 else "退化"
        print(f"   {metric}: {improvement:+.2f}% ({status})")
    
    # 对比修复前后
    print(f"\n🔧 修复效果对比：")
    print(f"   修复前（动态参考值）改进率: {dynamic_improvement:+.2f}%")
    print(f"   修复后（固定参考值）改进率: {fixed_improvement:+.2f}%")
    print(f"   差异: {abs(fixed_improvement - dynamic_improvement):.2f}%")
    
    if abs(fixed_improvement - dynamic_improvement) > 50:
        print(f"   ✅ 修复有效：消除了参考值不一致导致的巨大差异")
    else:
        print(f"   ⚠️ 差异较小，可能还有其他问题")
    
    # 判断修复后的结果是否合理
    print(f"\n🎯 修复后结果评估：")
    if fixed_improvement > 0:
        print(f"   ✅ 改进算法表现更好，最终代价降低{fixed_improvement:.2f}%")
        print(f"   这与各项指标的改进趋势一致")
    else:
        print(f"   ⚠️ 改进算法表现仍然较差，最终代价增加{abs(fixed_improvement):.2f}%")
        print(f"   需要进一步分析权重设置或指标计算")
    
    return {
        "dynamic_improvement": dynamic_improvement,
        "fixed_improvement": fixed_improvement,
        "baseline_fixed_cost": baseline_fixed['final_cost'],
        "improved_fixed_cost": improved_fixed['final_cost']
    }

if __name__ == "__main__":
    try:
        result = test_fixed_reference_values()
        print(f"\n✅ 测试完成")
        print(f"   修复前改进率: {result['dynamic_improvement']:+.2f}%")
        print(f"   修复后改进率: {result['fixed_improvement']:+.2f}%")
        print(f"\n💡 结论：")
        if result['fixed_improvement'] > 0:
            print(f"   修复成功！改进算法确实表现更好")
        else:
            print(f"   参考值问题已修复，但可能需要调整权重设置")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
