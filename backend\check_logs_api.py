"""
通过API检查日志
"""

import requests
import json

def check_logs_via_api():
    """通过API检查日志"""
    print("🔍 通过API检查日志...")
    
    try:
        # 检查日志健康状态
        response = requests.get("http://localhost:5000/api/logs/health", timeout=10)
        if response.status_code == 200:
            health = response.json()
            print(f"✅ 日志系统状态: {health.get('status', 'unknown')}")
            print(f"📁 日志目录存在: {health.get('log_directory_exists', False)}")
            print(f"📅 可用日期数: {health.get('available_dates', 0)}")
        else:
            print(f"❌ 日志健康检查失败: {response.status_code}")
            return
            
        # 获取可用日期
        response = requests.get("http://localhost:5000/api/logs/dates", timeout=10)
        if response.status_code == 200:
            dates = response.json()
            print(f"📅 可用日志日期: {dates}")
            
            if dates:
                # 获取最新日期的日志
                latest_date = dates[0]
                response = requests.get(f"http://localhost:5000/api/logs/steps/{latest_date}", timeout=10)
                if response.status_code == 200:
                    steps = response.json()
                    print(f"📋 今日日志步骤数: {len(steps)}")
                    
                    # 显示最近的几个步骤
                    if steps:
                        print("📋 最近的日志步骤:")
                        for step in steps[-10:]:  # 显示最后10个步骤
                            timestamp = step.get('timestamp', '')[:19]  # 只显示到秒
                            step_type = step.get('step_type', '')
                            message = step.get('message', '')
                            algorithm = step.get('algorithm', '')
                            print(f"   {timestamp} | {algorithm} | {step_type} | {message}")
                            
                        # 检查是否有新的算法日志
                        recent_algorithms = set()
                        for step in steps[-20:]:  # 检查最近20个步骤
                            algorithm = step.get('algorithm', '')
                            if algorithm and algorithm != 'TestAlgorithm':
                                recent_algorithms.add(algorithm)
                        
                        if recent_algorithms:
                            print(f"🎯 发现最近执行的算法: {', '.join(recent_algorithms)}")
                        else:
                            print("⚠️ 没有发现新的算法执行日志")
                            
                else:
                    print(f"❌ 获取日志步骤失败: {response.status_code}")
        else:
            print(f"❌ 获取日志日期失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API检查失败: {e}")

if __name__ == "__main__":
    check_logs_via_api()
