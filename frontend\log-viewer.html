<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📋 后端日志查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        select, button {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        select {
            background: rgba(255,255,255,0.2);
            color: white;
            backdrop-filter: blur(10px);
        }

        select option {
            background: #333;
            color: white;
        }

        button {
            background: rgba(255,255,255,0.2);
            color: white;
            backdrop-filter: blur(10px);
        }

        button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .log-content {
            padding: 30px;
        }

        .log-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .log-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .log-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }

        .log-table tr:hover {
            background-color: #f8f9fa;
            cursor: pointer;
        }

        .log-table tr.error-row {
            background-color: #fff5f5;
        }

        .log-table tr.warning-row {
            background-color: #fffbf0;
        }

        .level-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .level-info { background: #e3f2fd; color: #1976d2; }
        .level-warning { background: #fff3e0; color: #f57c00; }
        .level-error { background: #ffebee; color: #d32f2f; }
        .level-debug { background: #f3e5f5; color: #7b1fa2; }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #000;
        }

        .detail-section {
            margin-bottom: 20px;
        }

        .detail-section h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .detail-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }

        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .error-message {
            background: #ffebee;
            color: #d32f2f;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #d32f2f;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }

            .stats {
                grid-template-columns: 1fr;
            }

            .log-table {
                font-size: 0.9em;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 后端日志查看器</h1>
            <div class="controls">
                <div class="control-group">
                    <label>选择日期</label>
                    <select id="dateSelect">
                        <option value="">加载中...</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>日志级别</label>
                    <select id="levelFilter">
                        <option value="">全部</option>
                        <option value="DEBUG">DEBUG</option>
                        <option value="INFO">INFO</option>
                        <option value="WARNING">WARNING</option>
                        <option value="ERROR">ERROR</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>步骤类型</label>
                    <select id="stepTypeFilter">
                        <option value="">全部</option>
                        <option value="算法开始">算法开始</option>
                        <option value="路径生成">路径生成</option>
                        <option value="分簇处理">分簇处理</option>
                        <option value="代价计算">代价计算</option>
                        <option value="路径切换">路径切换</option>
                        <option value="错误处理">错误处理</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>操作</label>
                    <button onclick="refreshLogs()">🔄 刷新</button>
                </div>
            </div>
        </div>

        <div class="stats" id="statsContainer">
            <div class="stat-card">
                <div class="stat-number" id="totalSteps">-</div>
                <div class="stat-label">总步骤数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="errorCount">-</div>
                <div class="stat-label">错误数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="sessionCount">-</div>
                <div class="stat-label">会话数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="algorithmCount">-</div>
                <div class="stat-label">算法执行次数</div>
            </div>
        </div>

        <div class="log-content">
            <div id="loadingMessage" class="loading">
                📡 正在加载日志数据...
            </div>
            <div id="errorMessage" class="error-message" style="display: none;">
                ❌ 加载日志失败，请检查后端服务是否正常运行
            </div>
            <table class="log-table" id="logTable" style="display: none;">
                <thead>
                    <tr>
                        <th>步骤</th>
                        <th>时间</th>
                        <th>级别</th>
                        <th>步骤类型</th>
                        <th>算法</th>
                        <th>消息</th>
                        <th>耗时(ms)</th>
                    </tr>
                </thead>
                <tbody id="logTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📋 日志详情</h2>
                <span class="close" onclick="closeDetailModal()">&times;</span>
            </div>
            <div id="detailContent">
                <!-- 详情内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script src="log-viewer.js"></script>
</body>
</html>
