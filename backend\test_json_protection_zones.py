#!/usr/bin/env python3
"""
测试JSON文件中的保护区信息
"""

import glob
import json
import os

def test_json_protection_zones():
    """测试JSON文件中的保护区信息"""
    print("🔍 测试JSON文件中的保护区信息...")
    
    # 查找最新的JSON文件
    json_files = glob.glob('all_81_paths_data_*.json')
    if not json_files:
        print("❌ 没有找到JSON文件")
        return False
    
    latest_json = max(json_files, key=os.path.getctime)
    print(f"📂 使用JSON文件: {latest_json}")
    
    # 读取JSON数据
    with open(latest_json, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 检查基准路径的保护区信息
    baseline_path = None
    for path in data['all_paths']:
        if path.get('path_id') == 'BASELINE_A*':
            baseline_path = path
            break
    
    if baseline_path:
        print(f"\n🎯 基准路径保护区信息:")
        print(f"   protection_zones_count: {baseline_path.get('protection_zones_count', '未找到')}")
        
        # 检查metadata
        if 'metadata' in baseline_path:
            metadata = baseline_path['metadata']
            if 'protection_zones' in metadata:
                pz = metadata['protection_zones']
                breakdown = pz.get('collision_cost_breakdown', {})
                print(f"   metadata中的保护区数量: {len(breakdown)}")
                for zone_id, zone_data in breakdown.items():
                    print(f"     - {zone_id}: {zone_data.get('zone_name', '未知')} ({zone_data.get('zone_type', '未知')})")
            else:
                print("   metadata中没有protection_zones字段")
        else:
            print("   没有metadata字段")
    
    # 检查改进算法路径的保护区信息
    improved_path = None
    for path in data['all_paths']:
        if path.get('path_id') == 1:
            improved_path = path
            break
    
    if improved_path:
        print(f"\n🚀 改进算法路径1保护区信息:")
        print(f"   protection_zones_count: {improved_path.get('protection_zones_count', '未找到')}")
        
        # 检查是否有protectionZonesInfo字段
        if 'protectionZonesInfo' in improved_path:
            pzi = improved_path['protectionZonesInfo']
            breakdown = pzi.get('collision_cost_breakdown', {})
            print(f"   protectionZonesInfo中的保护区数量: {len(breakdown)}")
            for zone_id, zone_data in breakdown.items():
                print(f"     - {zone_id}: {zone_data.get('zone_name', '未知')} ({zone_data.get('zone_type', '未知')})")
        else:
            print("   没有protectionZonesInfo字段")
    
    # 测试保护区信息提取函数
    print(f"\n🧪 测试保护区信息提取:")
    from export_all_paths_data import extract_protection_zones_info
    
    if baseline_path:
        result = extract_protection_zones_info(baseline_path)
        print(f"   基准路径提取结果:")
        print(f"     保护区数量: {result['protection_zones_count']}")
        print(f"     保护区列表: {result['protection_zones_list']}")
        print(f"     保护区类型: {result['protection_zones_types']}")
    
    if improved_path:
        result = extract_protection_zones_info(improved_path)
        print(f"   改进路径提取结果:")
        print(f"     保护区数量: {result['protection_zones_count']}")
        print(f"     保护区列表: {result['protection_zones_list']}")
        print(f"     保护区类型: {result['protection_zones_types']}")
    
    return True

def test_csv_export():
    """测试CSV导出功能"""
    print("\n📊 测试CSV导出功能...")
    
    try:
        # 查找最新的JSON文件
        json_files = glob.glob('all_81_paths_data_*.json')
        latest_json = max(json_files, key=os.path.getctime)
        
        # 读取JSON数据
        with open(latest_json, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 获取选中路径ID
        selected_path_id = data.get('selected_path', {}).get('selected_path_id')
        print(f"🎯 选中路径ID: {selected_path_id}")
        
        # 获取起点和终点坐标
        metadata = data.get('metadata', {})
        start_point = metadata.get('start_point', {})
        end_point = metadata.get('end_point', {})
        
        print(f"📍 起点: ({start_point.get('lng', 0):.6f}, {start_point.get('lat', 0):.6f}, {start_point.get('alt', 0):.2f})")
        print(f"📍 终点: ({end_point.get('lng', 0):.6f}, {end_point.get('lat', 0):.6f}, {end_point.get('alt', 0):.2f})")
        
        # 统计保护区信息
        protection_zones_found = 0
        total_paths = len(data['all_paths'])
        
        for path_data in data['all_paths']:
            from export_all_paths_data import extract_protection_zones_info
            protection_info = extract_protection_zones_info(path_data)
            
            if protection_info['protection_zones_count'] > 0:
                protection_zones_found += 1
                path_id = path_data.get('path_id')
                print(f"   路径{path_id}: {protection_info['protection_zones_count']}个保护区 - {protection_info['protection_zones_list']}")
        
        print(f"\n📊 统计结果:")
        print(f"   总路径数: {total_paths}")
        print(f"   有保护区的路径数: {protection_zones_found}")
        print(f"   保护区覆盖率: {protection_zones_found/total_paths*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV导出测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 JSON保护区信息测试")
    print("=" * 60)
    
    # 测试JSON中的保护区信息
    json_ok = test_json_protection_zones()
    
    # 测试CSV导出功能
    csv_ok = test_csv_export()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   JSON检查:   {'✅ 通过' if json_ok else '❌ 失败'}")
    print(f"   CSV测试:    {'✅ 通过' if csv_ok else '❌ 失败'}")
    
    if json_ok and csv_ok:
        print("\n🎉 测试通过！")
        return True
    else:
        print("\n❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
