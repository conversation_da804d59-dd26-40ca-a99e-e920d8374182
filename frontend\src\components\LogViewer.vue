<template>
  <div class="log-viewer">
    <div class="log-header">
      <h2>📋 后端日志查看器</h2>
      <div class="log-controls">
        <el-select v-model="selectedDate" placeholder="选择日期" @change="loadLogs">
          <el-option
            v-for="date in availableDates"
            :key="date"
            :label="formatDate(date)"
            :value="date">
          </el-option>
        </el-select>
        <el-select v-model="filterLevel" placeholder="日志级别" @change="filterLogs">
          <el-option label="全部" value=""></el-option>
          <el-option label="DEBUG" value="DEBUG"></el-option>
          <el-option label="INFO" value="INFO"></el-option>
          <el-option label="WARNING" value="WARNING"></el-option>
          <el-option label="ERROR" value="ERROR"></el-option>
        </el-select>
        <el-select v-model="filterStepType" placeholder="步骤类型" @change="filterLogs">
          <el-option label="全部" value=""></el-option>
          <el-option label="算法开始" value="算法开始"></el-option>
          <el-option label="路径生成" value="路径生成"></el-option>
          <el-option label="分簇处理" value="分簇处理"></el-option>
          <el-option label="代价计算" value="代价计算"></el-option>
          <el-option label="路径切换" value="路径切换"></el-option>
          <el-option label="错误处理" value="错误处理"></el-option>
        </el-select>
        <el-button @click="refreshLogs" type="primary" icon="el-icon-refresh">刷新</el-button>
      </div>
    </div>

    <div class="log-stats" v-if="logStats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-number">{{ logStats.totalSteps }}</div>
            <div class="stat-label">总步骤数</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-number">{{ logStats.errorCount }}</div>
            <div class="stat-label">错误数量</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-number">{{ logStats.sessionCount }}</div>
            <div class="stat-label">会话数量</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-number">{{ logStats.algorithmCount }}</div>
            <div class="stat-label">算法执行次数</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="log-content">
      <el-table
        :data="filteredLogs"
        height="600"
        stripe
        :row-class-name="getRowClassName"
        @row-click="showLogDetail">
        <el-table-column prop="step_number" label="步骤" width="80" sortable></el-table-column>
        <el-table-column prop="timestamp" label="时间" width="180" sortable>
          <template #default="scope">
            {{ formatTimestamp(scope.row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="level" label="级别" width="80">
          <template #default="scope">
            <el-tag :type="getLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="step_type" label="步骤类型" width="120"></el-table-column>
        <el-table-column prop="algorithm" label="算法" width="150"></el-table-column>
        <el-table-column prop="message" label="消息" min-width="300"></el-table-column>
        <el-table-column prop="duration_ms" label="耗时(ms)" width="100" sortable>
          <template #default="scope">
            <span v-if="scope.row.duration_ms">{{ scope.row.duration_ms.toFixed(2) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      title="日志详情"
      v-model="showDetailDialog"
      width="80%"
      :before-close="closeDetailDialog">
      <div v-if="selectedLog">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="步骤编号">{{ selectedLog.step_number }}</el-descriptions-item>
          <el-descriptions-item label="时间戳">{{ formatTimestamp(selectedLog.timestamp) }}</el-descriptions-item>
          <el-descriptions-item label="日志级别">
            <el-tag :type="getLevelType(selectedLog.level)">{{ selectedLog.level }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="步骤类型">{{ selectedLog.step_type }}</el-descriptions-item>
          <el-descriptions-item label="算法名称">{{ selectedLog.algorithm || '无' }}</el-descriptions-item>
          <el-descriptions-item label="请求ID">{{ selectedLog.request_id || '无' }}</el-descriptions-item>
          <el-descriptions-item label="耗时">{{ selectedLog.duration_ms ? selectedLog.duration_ms.toFixed(2) + 'ms' : '无' }}</el-descriptions-item>
          <el-descriptions-item label="会话ID">{{ selectedLog.session_id }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="log-message">
          <h4>消息内容</h4>
          <pre>{{ selectedLog.message }}</pre>
        </div>
        
        <div class="log-details" v-if="selectedLog.details && Object.keys(selectedLog.details).length > 0">
          <h4>详细信息</h4>
          <pre>{{ JSON.stringify(selectedLog.details, null, 2) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'LogViewer',
  setup() {
    const availableDates = ref([])
    const selectedDate = ref('')
    const allLogs = ref([])
    const filteredLogs = ref([])
    const filterLevel = ref('')
    const filterStepType = ref('')
    const showDetailDialog = ref(false)
    const selectedLog = ref(null)
    
    const logStats = computed(() => {
      if (!allLogs.value.length) return null
      
      const sessions = new Set()
      const algorithms = new Set()
      let errorCount = 0
      
      allLogs.value.forEach(log => {
        if (log.session_id) sessions.add(log.session_id)
        if (log.algorithm) algorithms.add(log.algorithm)
        if (log.level === 'ERROR') errorCount++
      })
      
      return {
        totalSteps: allLogs.value.length,
        errorCount,
        sessionCount: sessions.size,
        algorithmCount: algorithms.size
      }
    })

    const loadAvailableDates = async () => {
      try {
        const response = await fetch('/api/logs/dates')
        if (response.ok) {
          const dates = await response.json()
          availableDates.value = dates
          if (dates.length > 0) {
            selectedDate.value = dates[0] // 选择最新日期
            await loadLogs()
          }
        }
      } catch (error) {
        console.error('加载可用日期失败:', error)
        ElMessage.error('加载可用日期失败')
      }
    }

    const loadLogs = async () => {
      if (!selectedDate.value) return
      
      try {
        const response = await fetch(`/api/logs/steps/${selectedDate.value}`)
        if (response.ok) {
          const logs = await response.json()
          allLogs.value = logs
          filterLogs()
        }
      } catch (error) {
        console.error('加载日志失败:', error)
        ElMessage.error('加载日志失败')
      }
    }

    const filterLogs = () => {
      let filtered = [...allLogs.value]
      
      if (filterLevel.value) {
        filtered = filtered.filter(log => log.level === filterLevel.value)
      }
      
      if (filterStepType.value) {
        filtered = filtered.filter(log => log.step_type === filterStepType.value)
      }
      
      filteredLogs.value = filtered
    }

    const refreshLogs = () => {
      loadLogs()
    }

    const formatDate = (dateStr) => {
      if (dateStr.length === 8) {
        return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`
      }
      return dateStr
    }

    const formatTimestamp = (timestamp) => {
      return new Date(timestamp).toLocaleString('zh-CN')
    }

    const getLevelType = (level) => {
      const types = {
        'DEBUG': '',
        'INFO': 'success',
        'WARNING': 'warning',
        'ERROR': 'danger',
        'CRITICAL': 'danger'
      }
      return types[level] || ''
    }

    const getRowClassName = ({ row }) => {
      if (row.level === 'ERROR' || row.level === 'CRITICAL') {
        return 'error-row'
      }
      if (row.level === 'WARNING') {
        return 'warning-row'
      }
      return ''
    }

    const showLogDetail = (row) => {
      selectedLog.value = row
      showDetailDialog.value = true
    }

    const closeDetailDialog = () => {
      showDetailDialog.value = false
      selectedLog.value = null
    }

    onMounted(() => {
      loadAvailableDates()
    })

    return {
      availableDates,
      selectedDate,
      allLogs,
      filteredLogs,
      filterLevel,
      filterStepType,
      showDetailDialog,
      selectedLog,
      logStats,
      loadLogs,
      filterLogs,
      refreshLogs,
      formatDate,
      formatTimestamp,
      getLevelType,
      getRowClassName,
      showLogDetail,
      closeDetailDialog
    }
  }
}
</script>

<style scoped>
.log-viewer {
  padding: 20px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.log-controls {
  display: flex;
  gap: 10px;
}

.log-stats {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  color: #666;
  margin-top: 5px;
}

.log-content {
  background: white;
  border-radius: 4px;
}

.log-message {
  margin-top: 20px;
}

.log-message h4,
.log-details h4 {
  margin-bottom: 10px;
  color: #333;
}

.log-message pre,
.log-details pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
}

.log-details {
  margin-top: 20px;
}

:deep(.error-row) {
  background-color: #fef0f0;
}

:deep(.warning-row) {
  background-color: #fdf6ec;
}

:deep(.el-table__row:hover) {
  cursor: pointer;
}
</style>
