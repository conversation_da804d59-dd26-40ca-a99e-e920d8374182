[{"session_id": "20250727_081248", "step_number": 1, "timestamp": "2025-07-27T08:12:48.560336", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a721fb96-5082-4d37-95f1-d8a8d1162ef5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a721fb96-5082-4d37-95f1-d8a8d1162ef5", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(904.2244808212655, 1105.4000000005656, 100)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_081248", "step_number": 2, "timestamp": "2025-07-27T08:12:48.562535", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a721fb96-5082-4d37-95f1-d8a8d1162ef5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a721fb96-5082-4d37-95f1-d8a8d1162ef5", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(904.2244808212655, 1105.4000000005656, 100)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_081248", "step_number": 3, "timestamp": "2025-07-27T08:17:13.914977", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 265351.43852233887}, {"session_id": "20250727_081248", "step_number": 4, "timestamp": "2025-07-27T08:17:13.916481", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 265352.94246673584}, {"session_id": "20250727_081248", "step_number": 5, "timestamp": "2025-07-27T08:17:13.925486", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 7.984399795532227}, {"session_id": "20250727_081248", "step_number": 6, "timestamp": "2025-07-27T08:17:13.926474", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 8.97216796875}, {"session_id": "20250727_083707", "step_number": 1, "timestamp": "2025-07-27T08:37:07.419236", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5ee51a35-e361-4bdb-ae30-71c614840276", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5ee51a35-e361-4bdb-ae30-71c614840276", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-556.3306501802804, 1637.5106199812913, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 65}}, "duration_ms": null}, {"session_id": "20250727_083707", "step_number": 2, "timestamp": "2025-07-27T08:37:07.431383", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5ee51a35-e361-4bdb-ae30-71c614840276", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5ee51a35-e361-4bdb-ae30-71c614840276", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-556.3306501802804, 1637.5106199812913, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_083707", "step_number": 3, "timestamp": "2025-07-27T08:41:16.459990", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 249022.57990837097}, {"session_id": "20250727_083707", "step_number": 4, "timestamp": "2025-07-27T08:41:16.461416", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 249024.00588989258}, {"session_id": "20250727_083707", "step_number": 5, "timestamp": "2025-07-27T08:41:16.472098", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.682106018066406}, {"session_id": "20250727_083707", "step_number": 6, "timestamp": "2025-07-27T08:41:16.473603", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.187480926513672}, {"session_id": "20250727_083707", "step_number": 7, "timestamp": "2025-07-27T08:54:36.001334", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d5a5f579-ca9e-4797-9801-c23349afe666", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d5a5f579-ca9e-4797-9801-c23349afe666", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-690.3768286459934, 1210.388136492606, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 105}}, "duration_ms": null}, {"session_id": "20250727_083707", "step_number": 8, "timestamp": "2025-07-27T08:54:36.011213", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d5a5f579-ca9e-4797-9801-c23349afe666", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d5a5f579-ca9e-4797-9801-c23349afe666", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-690.3768286459934, 1210.388136492606, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_090053", "step_number": 1, "timestamp": "2025-07-27T09:00:53.650666", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a671d77b-34a4-4c76-9ca9-bc712e74c454", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a671d77b-34a4-4c76-9ca9-bc712e74c454", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-1174.893812165113, 1870.3599926168647, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_090053", "step_number": 2, "timestamp": "2025-07-27T09:00:53.652865", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a671d77b-34a4-4c76-9ca9-bc712e74c454", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a671d77b-34a4-4c76-9ca9-bc712e74c454", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-1174.893812165113, 1870.3599926168647, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_090053", "step_number": 3, "timestamp": "2025-07-27T09:01:00.697410", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "447f47fc-5097-4d4d-b5fe-5f080c37dc22", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "447f47fc-5097-4d4d-b5fe-5f080c37dc22", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-1174.893812165113, 1870.3599926168647, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_090053", "step_number": 4, "timestamp": "2025-07-27T09:01:00.699404", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "447f47fc-5097-4d4d-b5fe-5f080c37dc22", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "447f47fc-5097-4d4d-b5fe-5f080c37dc22", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-1174.893812165113, 1870.3599926168647, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_090053", "step_number": 5, "timestamp": "2025-07-27T09:01:32.688058", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a2093e23-e758-4305-93b2-3e3a2b025896", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a2093e23-e758-4305-93b2-3e3a2b025896", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-1174.893812165113, 1870.3599926168647, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_090053", "step_number": 6, "timestamp": "2025-07-27T09:01:32.690122", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a2093e23-e758-4305-93b2-3e3a2b025896", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a2093e23-e758-4305-93b2-3e3a2b025896", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-1174.893812165113, 1870.3599926168647, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_090053", "step_number": 7, "timestamp": "2025-07-27T09:07:34.249454", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 400594.0685272217}, {"session_id": "20250727_090053", "step_number": 8, "timestamp": "2025-07-27T09:07:34.254471", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 400599.08628463745}, {"session_id": "20250727_090053", "step_number": 9, "timestamp": "2025-07-27T09:07:34.270050", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 14.457225799560547}, {"session_id": "20250727_090053", "step_number": 10, "timestamp": "2025-07-27T09:07:34.272380", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.786813735961914}, {"session_id": "20250727_090053", "step_number": 11, "timestamp": "2025-07-27T09:07:47.167091", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 406465.6925201416}, {"session_id": "20250727_090053", "step_number": 12, "timestamp": "2025-07-27T09:07:47.169904", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 406468.505859375}, {"session_id": "20250727_090053", "step_number": 13, "timestamp": "2025-07-27T09:07:47.186712", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.792369842529297}, {"session_id": "20250727_090053", "step_number": 14, "timestamp": "2025-07-27T09:07:47.189437", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.51797103881836}, {"session_id": "20250727_090053", "step_number": 15, "timestamp": "2025-07-27T09:08:33.432637", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 420740.5092716217}, {"session_id": "20250727_090053", "step_number": 16, "timestamp": "2025-07-27T09:08:33.433638", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 420741.5096759796}, {"session_id": "20250727_090053", "step_number": 17, "timestamp": "2025-07-27T09:08:33.449834", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.204191207885742}, {"session_id": "20250727_090053", "step_number": 18, "timestamp": "2025-07-27T09:08:33.452041", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.4102783203125}, {"session_id": "20250727_091553", "step_number": 1, "timestamp": "2025-07-27T09:15:53.628626", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "baa55d36-9ef0-4ec4-86fc-f0edc8176972", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "baa55d36-9ef0-4ec4-86fc-f0edc8176972", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-856.9212255830372, 1230.0585781953846, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_091553", "step_number": 2, "timestamp": "2025-07-27T09:15:53.631616", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "baa55d36-9ef0-4ec4-86fc-f0edc8176972", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "baa55d36-9ef0-4ec4-86fc-f0edc8176972", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-856.9212255830372, 1230.0585781953846, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_091553", "step_number": 3, "timestamp": "2025-07-27T09:15:53.679734", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 45.111894607543945}, {"session_id": "20250727_091553", "step_number": 4, "timestamp": "2025-07-27T09:15:53.681236", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 46.61393165588379}, {"session_id": "20250727_091553", "step_number": 5, "timestamp": "2025-07-27T09:15:53.692181", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.90605354309082}, {"session_id": "20250727_091553", "step_number": 6, "timestamp": "2025-07-27T09:15:53.695562", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.288021087646484}, {"session_id": "20250727_091553", "step_number": 7, "timestamp": "2025-07-27T09:21:49.157107", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "65fb040b-aaca-4079-b2e9-76f5e0c3f8be", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "65fb040b-aaca-4079-b2e9-76f5e0c3f8be", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-816.2762704258342, 1334.883299568608, 1)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_091553", "step_number": 8, "timestamp": "2025-07-27T09:21:49.164404", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "65fb040b-aaca-4079-b2e9-76f5e0c3f8be", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "65fb040b-aaca-4079-b2e9-76f5e0c3f8be", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-816.2762704258342, 1334.883299568608, 1)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_091553", "step_number": 9, "timestamp": "2025-07-27T09:21:49.253992", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 83.59074592590332}, {"session_id": "20250727_091553", "step_number": 10, "timestamp": "2025-07-27T09:21:49.256504", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 86.10200881958008}, {"session_id": "20250727_091553", "step_number": 11, "timestamp": "2025-07-27T09:21:49.276721", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.208980560302734}, {"session_id": "20250727_091553", "step_number": 12, "timestamp": "2025-07-27T09:21:49.279832", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.31962776184082}, {"session_id": "20250727_091553", "step_number": 13, "timestamp": "2025-07-27T09:26:00.019217", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6f78697c-070b-4f99-886b-6b200e1784b2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6f78697c-070b-4f99-886b-6b200e1784b2", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-982.6767243912602, -394.66814398986173, 1)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_091553", "step_number": 14, "timestamp": "2025-07-27T09:26:00.022786", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6f78697c-070b-4f99-886b-6b200e1784b2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6f78697c-070b-4f99-886b-6b200e1784b2", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-982.6767243912602, -394.66814398986173, 1)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_091553", "step_number": 15, "timestamp": "2025-07-27T09:26:00.065749", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 38.544654846191406}, {"session_id": "20250727_091553", "step_number": 16, "timestamp": "2025-07-27T09:26:00.069005", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 41.79978370666504}, {"session_id": "20250727_091553", "step_number": 17, "timestamp": "2025-07-27T09:26:00.082869", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.57181167602539}, {"session_id": "20250727_091553", "step_number": 18, "timestamp": "2025-07-27T09:26:00.086104", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.80667495727539}, {"session_id": "20250727_093809", "step_number": 1, "timestamp": "2025-07-27T09:38:09.782326", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "295ee146-5218-48b0-a7db-e2c6f681aa74", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "295ee146-5218-48b0-a7db-e2c6f681aa74", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-645.2779312316651, 1513.7547714933894, 1)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_093809", "step_number": 2, "timestamp": "2025-07-27T09:38:09.786316", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "295ee146-5218-48b0-a7db-e2c6f681aa74", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "295ee146-5218-48b0-a7db-e2c6f681aa74", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-645.2779312316651, 1513.7547714933894, 1)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_093809", "step_number": 3, "timestamp": "2025-07-27T09:38:09.810539", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: KeyError: 'x'", "algorithm": null, "request_id": null, "details": {"error_type": "KeyError", "error_message": "'x'", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250727_094546", "step_number": 1, "timestamp": "2025-07-27T09:45:46.030489", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "cfdf47bc-2b6a-4ac6-aa0e-50a33e64ab6f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "cfdf47bc-2b6a-4ac6-aa0e-50a33e64ab6f", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-838.6016913683566, 1290.8965018630554, 1)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_094546", "step_number": 2, "timestamp": "2025-07-27T09:45:46.034498", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "cfdf47bc-2b6a-4ac6-aa0e-50a33e64ab6f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "cfdf47bc-2b6a-4ac6-aa0e-50a33e64ab6f", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-838.6016913683566, 1290.8965018630554, 1)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_094546", "step_number": 3, "timestamp": "2025-07-27T09:45:46.138129", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 99.45464134216309}, {"session_id": "20250727_094546", "step_number": 4, "timestamp": "2025-07-27T09:45:46.141196", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 103.51729393005371}, {"session_id": "20250727_094546", "step_number": 5, "timestamp": "2025-07-27T09:45:46.153581", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.347604751586914}, {"session_id": "20250727_094546", "step_number": 6, "timestamp": "2025-07-27T09:45:46.155617", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.383222579956055}, {"session_id": "20250727_114225", "step_number": 1, "timestamp": "2025-07-27T11:42:25.384231", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "820ba7dc-5f3f-4093-ab08-07b24e0054f1", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "820ba7dc-5f3f-4093-ab08-07b24e0054f1", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-473.56746436931235, 1097.736691864664, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 107}}, "duration_ms": null}, {"session_id": "20250727_114225", "step_number": 2, "timestamp": "2025-07-27T11:42:25.400861", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "820ba7dc-5f3f-4093-ab08-07b24e0054f1", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "820ba7dc-5f3f-4093-ab08-07b24e0054f1", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-473.56746436931235, 1097.736691864664, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_114225", "step_number": 3, "timestamp": "2025-07-27T11:42:27.336546", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1921.9691753387451}, {"session_id": "20250727_114225", "step_number": 4, "timestamp": "2025-07-27T11:42:27.340533", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1925.9562492370605}, {"session_id": "20250727_114225", "step_number": 5, "timestamp": "2025-07-27T11:42:27.353729", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.699987411499023}, {"session_id": "20250727_114225", "step_number": 6, "timestamp": "2025-07-27T11:42:27.355735", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.705326080322266}, {"session_id": "20250727_115908", "step_number": 1, "timestamp": "2025-07-27T11:59:08.279650", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4c47b5cc-84be-46d8-9294-b227710f4e24", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4c47b5cc-84be-46d8-9294-b227710f4e24", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1312.1367322556628, 2070.584150996373, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 76}}, "duration_ms": null}, {"session_id": "20250727_115908", "step_number": 2, "timestamp": "2025-07-27T11:59:08.291525", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4c47b5cc-84be-46d8-9294-b227710f4e24", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4c47b5cc-84be-46d8-9294-b227710f4e24", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1312.1367322556628, 2070.584150996373, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_115908", "step_number": 3, "timestamp": "2025-07-27T11:59:11.948935", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3646.942615509033}, {"session_id": "20250727_115908", "step_number": 4, "timestamp": "2025-07-27T11:59:11.952922", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 3650.9296894073486}, {"session_id": "20250727_115908", "step_number": 5, "timestamp": "2025-07-27T11:59:11.985071", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.128385543823242}, {"session_id": "20250727_115908", "step_number": 6, "timestamp": "2025-07-27T11:59:11.990713", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.77103805541992}, {"session_id": "20250727_120448", "step_number": 1, "timestamp": "2025-07-27T12:04:48.134853", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "dd57fa9b-7045-43e4-a528-4abda92175ab", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "dd57fa9b-7045-43e4-a528-4abda92175ab", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-877.0977463982723, 917.4820000006107, 120)", "flight_height": 120, "safety_distance": 15, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_120448", "step_number": 2, "timestamp": "2025-07-27T12:04:48.140110", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "dd57fa9b-7045-43e4-a528-4abda92175ab", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "dd57fa9b-7045-43e4-a528-4abda92175ab", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-877.0977463982723, 917.4820000006107, 120)", "flight_height": 120, "safety_distance": 15}}, "duration_ms": null}, {"session_id": "20250727_120448", "step_number": 3, "timestamp": "2025-07-27T12:04:49.435005", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1292.8993701934814}, {"session_id": "20250727_120448", "step_number": 4, "timestamp": "2025-07-27T12:04:49.438467", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 15}}, "duration_ms": 1296.3612079620361}, {"session_id": "20250727_120448", "step_number": 5, "timestamp": "2025-07-27T12:04:49.448693", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 7.942676544189453}, {"session_id": "20250727_120448", "step_number": 6, "timestamp": "2025-07-27T12:04:49.450709", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.958982467651367}, {"session_id": "20250727_163459", "step_number": 1, "timestamp": "2025-07-27T16:34:59.751466", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3d796c42-5df5-406c-80e4-2e713f371a93", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3d796c42-5df5-406c-80e4-2e713f371a93", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1008.6071577240152, 1413.721434556953, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_163459", "step_number": 2, "timestamp": "2025-07-27T16:34:59.767454", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3d796c42-5df5-406c-80e4-2e713f371a93", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3d796c42-5df5-406c-80e4-2e713f371a93", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1008.6071577240152, 1413.721434556953, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_163459", "step_number": 3, "timestamp": "2025-07-27T16:35:02.598745", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2818.727731704712}, {"session_id": "20250727_163459", "step_number": 4, "timestamp": "2025-07-27T16:35:02.602417", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2822.399854660034}, {"session_id": "20250727_163459", "step_number": 5, "timestamp": "2025-07-27T16:35:02.622961", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.9827938079834}, {"session_id": "20250727_163459", "step_number": 6, "timestamp": "2025-07-27T16:35:02.628754", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.77541160583496}, {"session_id": "20250727_171228", "step_number": 1, "timestamp": "2025-07-27T17:12:28.220233", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d053ff7c-d47e-4503-8818-e93a53ac619c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d053ff7c-d47e-4503-8818-e93a53ac619c", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-896.4104902421262, 1241.6153601617848, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_171228", "step_number": 2, "timestamp": "2025-07-27T17:12:28.232356", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d053ff7c-d47e-4503-8818-e93a53ac619c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d053ff7c-d47e-4503-8818-e93a53ac619c", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-896.4104902421262, 1241.6153601617848, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_171228", "step_number": 3, "timestamp": "2025-07-27T17:12:31.112199", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2872.4896907806396}, {"session_id": "20250727_171228", "step_number": 4, "timestamp": "2025-07-27T17:12:31.117327", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2877.6180744171143}, {"session_id": "20250727_171228", "step_number": 5, "timestamp": "2025-07-27T17:12:31.144371", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.411895751953125}, {"session_id": "20250727_171228", "step_number": 6, "timestamp": "2025-07-27T17:12:31.152839", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.879331588745117}, {"session_id": "20250727_171228", "step_number": 7, "timestamp": "2025-07-27T17:17:19.931885", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7ec2a89c-6563-4b3d-9e9b-4b05a6c666d5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7ec2a89c-6563-4b3d-9e9b-4b05a6c666d5", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-809.583507724546, 1589.1568127890105, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_171228", "step_number": 8, "timestamp": "2025-07-27T17:17:19.937606", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7ec2a89c-6563-4b3d-9e9b-4b05a6c666d5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7ec2a89c-6563-4b3d-9e9b-4b05a6c666d5", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-809.583507724546, 1589.1568127890105, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_171228", "step_number": 9, "timestamp": "2025-07-27T17:17:23.316928", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3372.635841369629}, {"session_id": "20250727_171228", "step_number": 10, "timestamp": "2025-07-27T17:17:23.322291", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 3377.9983520507812}, {"session_id": "20250727_171228", "step_number": 11, "timestamp": "2025-07-27T17:17:23.352903", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 24.80459213256836}, {"session_id": "20250727_171228", "step_number": 12, "timestamp": "2025-07-27T17:17:23.357680", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.58202362060547}, {"session_id": "20250727_172522", "step_number": 1, "timestamp": "2025-07-27T17:25:22.425910", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "37dc9c36-0b67-47d1-b31a-075692701c07", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "37dc9c36-0b67-47d1-b31a-075692701c07", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-843.259680741682, 1202.57326877728, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_172522", "step_number": 2, "timestamp": "2025-07-27T17:25:22.437082", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "37dc9c36-0b67-47d1-b31a-075692701c07", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "37dc9c36-0b67-47d1-b31a-075692701c07", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-843.259680741682, 1202.57326877728, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_172522", "step_number": 3, "timestamp": "2025-07-27T17:25:25.907688", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3459.078550338745}, {"session_id": "20250727_172522", "step_number": 4, "timestamp": "2025-07-27T17:25:25.913494", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 3464.8847579956055}, {"session_id": "20250727_172522", "step_number": 5, "timestamp": "2025-07-27T17:25:25.940893", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.948575973510742}, {"session_id": "20250727_172522", "step_number": 6, "timestamp": "2025-07-27T17:25:25.946657", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 27.712583541870117}, {"session_id": "20250727_173036", "step_number": 1, "timestamp": "2025-07-27T17:30:36.934944", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "71c8c702-8eff-4568-bd08-58f5f4abfe6a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "71c8c702-8eff-4568-bd08-58f5f4abfe6a", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(904.2244808212655, 1105.4000000005656, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_173036", "step_number": 2, "timestamp": "2025-07-27T17:30:36.940179", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "71c8c702-8eff-4568-bd08-58f5f4abfe6a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "71c8c702-8eff-4568-bd08-58f5f4abfe6a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(904.2244808212655, 1105.4000000005656, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_173036", "step_number": 3, "timestamp": "2025-07-27T17:30:38.411063", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1466.2141799926758}, {"session_id": "20250727_173036", "step_number": 4, "timestamp": "2025-07-27T17:30:38.413068", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1468.2188034057617}, {"session_id": "20250727_173036", "step_number": 5, "timestamp": "2025-07-27T17:30:38.423814", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 7.758378982543945}, {"session_id": "20250727_173036", "step_number": 6, "timestamp": "2025-07-27T17:30:38.425816", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.760141372680664}, {"session_id": "20250727_173323", "step_number": 1, "timestamp": "2025-07-27T17:33:23.941129", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "035e18ab-0420-4daf-8957-8861f5dbb6e9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "035e18ab-0420-4daf-8957-8861f5dbb6e9", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(904.2244808212655, 1105.4000000005656, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_173323", "step_number": 2, "timestamp": "2025-07-27T17:33:23.947307", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "035e18ab-0420-4daf-8957-8861f5dbb6e9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "035e18ab-0420-4daf-8957-8861f5dbb6e9", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(904.2244808212655, 1105.4000000005656, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_173323", "step_number": 3, "timestamp": "2025-07-27T17:33:25.322004", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1371.7074394226074}, {"session_id": "20250727_173323", "step_number": 4, "timestamp": "2025-07-27T17:33:25.325002", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1374.7057914733887}, {"session_id": "20250727_173323", "step_number": 5, "timestamp": "2025-07-27T17:33:25.340581", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.48114013671875}, {"session_id": "20250727_173323", "step_number": 6, "timestamp": "2025-07-27T17:33:25.343571", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.471385955810547}, {"session_id": "20250727_174744", "step_number": 1, "timestamp": "2025-07-27T17:47:44.047678", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "44f657c4-0751-40cb-b366-27ae674eb7b2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "44f657c4-0751-40cb-b366-27ae674eb7b2", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(904.2244808212655, 1105.4000000005656, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_174744", "step_number": 2, "timestamp": "2025-07-27T17:47:44.051664", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "44f657c4-0751-40cb-b366-27ae674eb7b2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "44f657c4-0751-40cb-b366-27ae674eb7b2", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(904.2244808212655, 1105.4000000005656, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_174744", "step_number": 3, "timestamp": "2025-07-27T17:47:45.411577", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1356.3411235809326}, {"session_id": "20250727_174744", "step_number": 4, "timestamp": "2025-07-27T17:47:45.414759", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1359.5237731933594}, {"session_id": "20250727_174744", "step_number": 5, "timestamp": "2025-07-27T17:47:45.425304", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 7.512331008911133}, {"session_id": "20250727_174744", "step_number": 6, "timestamp": "2025-07-27T17:47:45.427296", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.504079818725586}, {"session_id": "20250727_181308", "step_number": 1, "timestamp": "2025-07-27T18:13:08.030768", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "92d4f5b9-cec4-4c6b-aee7-5304caa2fb54", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "92d4f5b9-cec4-4c6b-aee7-5304caa2fb54", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1258.8521369437033, 1659.709981690927, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_181308", "step_number": 2, "timestamp": "2025-07-27T18:13:08.042776", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "92d4f5b9-cec4-4c6b-aee7-5304caa2fb54", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "92d4f5b9-cec4-4c6b-aee7-5304caa2fb54", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1258.8521369437033, 1659.709981690927, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_181308", "step_number": 3, "timestamp": "2025-07-27T18:13:10.310533", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2259.5322132110596}, {"session_id": "20250727_181308", "step_number": 4, "timestamp": "2025-07-27T18:13:10.315771", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2264.7705078125}, {"session_id": "20250727_181308", "step_number": 5, "timestamp": "2025-07-27T18:13:10.339828", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.50414276123047}, {"session_id": "20250727_181308", "step_number": 6, "timestamp": "2025-07-27T18:13:10.343372", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.048234939575195}, {"session_id": "20250727_182144", "step_number": 1, "timestamp": "2025-07-27T18:21:44.083643", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "cf65fdb8-1db3-4920-b6bc-1ee1737ac43e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "cf65fdb8-1db3-4920-b6bc-1ee1737ac43e", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-985.7893240649391, 1432.6323989524708, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_182144", "step_number": 2, "timestamp": "2025-07-27T18:21:44.089496", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "cf65fdb8-1db3-4920-b6bc-1ee1737ac43e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "cf65fdb8-1db3-4920-b6bc-1ee1737ac43e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-985.7893240649391, 1432.6323989524708, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_182144", "step_number": 3, "timestamp": "2025-07-27T18:21:45.816346", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1719.310998916626}, {"session_id": "20250727_182144", "step_number": 4, "timestamp": "2025-07-27T18:21:45.819429", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1722.3939895629883}, {"session_id": "20250727_182144", "step_number": 5, "timestamp": "2025-07-27T18:21:45.840292", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.81153678894043}, {"session_id": "20250727_182144", "step_number": 6, "timestamp": "2025-07-27T18:21:45.844985", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.50385284423828}, {"session_id": "20250727_182144", "step_number": 7, "timestamp": "2025-07-27T18:25:30.677362", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ca3be3e4-bea6-4620-918f-5fbd55e29778", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ca3be3e4-bea6-4620-918f-5fbd55e29778", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 1}}, "duration_ms": null}, {"session_id": "20250727_182144", "step_number": 8, "timestamp": "2025-07-27T18:25:30.683739", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ca3be3e4-bea6-4620-918f-5fbd55e29778", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ca3be3e4-bea6-4620-918f-5fbd55e29778", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_182144", "step_number": 9, "timestamp": "2025-07-27T18:25:32.868028", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2179.044485092163}, {"session_id": "20250727_182144", "step_number": 10, "timestamp": "2025-07-27T18:25:32.871413", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2182.429075241089}, {"session_id": "20250727_182144", "step_number": 11, "timestamp": "2025-07-27T18:25:32.889653", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.165090560913086}, {"session_id": "20250727_182144", "step_number": 12, "timestamp": "2025-07-27T18:25:32.893154", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.666982650756836}, {"session_id": "20250727_182642", "step_number": 1, "timestamp": "2025-07-27T18:26:42.921514", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "af6be031-126f-4efb-a1f8-01b46b38a2a5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "af6be031-126f-4efb-a1f8-01b46b38a2a5", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 1}}, "duration_ms": null}, {"session_id": "20250727_182642", "step_number": 2, "timestamp": "2025-07-27T18:26:42.925955", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "af6be031-126f-4efb-a1f8-01b46b38a2a5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "af6be031-126f-4efb-a1f8-01b46b38a2a5", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_182642", "step_number": 3, "timestamp": "2025-07-27T18:26:44.880797", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1950.514316558838}, {"session_id": "20250727_182642", "step_number": 4, "timestamp": "2025-07-27T18:26:44.883325", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1953.0417919158936}, {"session_id": "20250727_182642", "step_number": 5, "timestamp": "2025-07-27T18:26:44.898219", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.576414108276367}, {"session_id": "20250727_182642", "step_number": 6, "timestamp": "2025-07-27T18:26:44.901732", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.089273452758789}, {"session_id": "20250727_182642", "step_number": 7, "timestamp": "2025-07-27T18:28:48.428726", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0dfd4c86-dc27-48f4-8c92-164efe71325e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0dfd4c86-dc27-48f4-8c92-164efe71325e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_182642", "step_number": 8, "timestamp": "2025-07-27T18:28:48.434275", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0dfd4c86-dc27-48f4-8c92-164efe71325e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0dfd4c86-dc27-48f4-8c92-164efe71325e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_182642", "step_number": 9, "timestamp": "2025-07-27T18:28:48.713581", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 274.3229866027832}, {"session_id": "20250727_182642", "step_number": 10, "timestamp": "2025-07-27T18:28:48.716900", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 277.64153480529785}, {"session_id": "20250727_182642", "step_number": 11, "timestamp": "2025-07-27T18:28:48.721656", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 1.5869140625}, {"session_id": "20250727_182642", "step_number": 12, "timestamp": "2025-07-27T18:28:48.726382", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 6.313085556030273}, {"session_id": "20250727_182642", "step_number": 13, "timestamp": "2025-07-27T18:29:29.978491", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "889e3109-81f3-43e1-8e05-8dda26eba486", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "889e3109-81f3-43e1-8e05-8dda26eba486", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_182642", "step_number": 14, "timestamp": "2025-07-27T18:29:29.984099", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "889e3109-81f3-43e1-8e05-8dda26eba486", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "889e3109-81f3-43e1-8e05-8dda26eba486", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_182642", "step_number": 15, "timestamp": "2025-07-27T18:29:30.208408", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 218.65463256835938}, {"session_id": "20250727_182642", "step_number": 16, "timestamp": "2025-07-27T18:29:30.211905", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 222.1517562866211}, {"session_id": "20250727_182642", "step_number": 17, "timestamp": "2025-07-27T18:29:30.217785", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 1.8124580383300781}, {"session_id": "20250727_182642", "step_number": 18, "timestamp": "2025-07-27T18:29:30.221649", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 5.67626953125}, {"session_id": "20250727_182642", "step_number": 19, "timestamp": "2025-07-27T18:30:20.171336", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8dc8f1de-b9f2-4883-a573-6eb652545aa8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8dc8f1de-b9f2-4883-a573-6eb652545aa8", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_182642", "step_number": 20, "timestamp": "2025-07-27T18:30:20.175814", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8dc8f1de-b9f2-4883-a573-6eb652545aa8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8dc8f1de-b9f2-4883-a573-6eb652545aa8", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_182642", "step_number": 21, "timestamp": "2025-07-27T18:30:20.403199", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 222.15652465820312}, {"session_id": "20250727_182642", "step_number": 22, "timestamp": "2025-07-27T18:30:20.406313", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 225.27027130126953}, {"session_id": "20250727_182642", "step_number": 23, "timestamp": "2025-07-27T18:30:20.412237", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 2.1758079528808594}, {"session_id": "20250727_182642", "step_number": 24, "timestamp": "2025-07-27T18:30:20.415262", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 5.200624465942383}, {"session_id": "20250727_183234", "step_number": 1, "timestamp": "2025-07-27T18:32:34.121732", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c13b38dc-4c85-4d4d-9403-a43dc7c7f8ac", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c13b38dc-4c85-4d4d-9403-a43dc7c7f8ac", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 1}}, "duration_ms": null}, {"session_id": "20250727_183234", "step_number": 2, "timestamp": "2025-07-27T18:32:34.126873", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c13b38dc-4c85-4d4d-9403-a43dc7c7f8ac", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c13b38dc-4c85-4d4d-9403-a43dc7c7f8ac", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_183234", "step_number": 3, "timestamp": "2025-07-27T18:32:36.129647", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1997.4920749664307}, {"session_id": "20250727_183234", "step_number": 4, "timestamp": "2025-07-27T18:32:36.133633", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2001.4784336090088}, {"session_id": "20250727_183234", "step_number": 5, "timestamp": "2025-07-27T18:32:36.150484", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.503074645996094}, {"session_id": "20250727_183234", "step_number": 6, "timestamp": "2025-07-27T18:32:36.155172", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.190383911132812}, {"session_id": "20250727_185433", "step_number": 1, "timestamp": "2025-07-27T18:54:33.672665", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f089a881-90d0-4a80-83f2-d3f8fbbbe7fe", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f089a881-90d0-4a80-83f2-d3f8fbbbe7fe", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_185433", "step_number": 2, "timestamp": "2025-07-27T18:54:33.680631", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f089a881-90d0-4a80-83f2-d3f8fbbbe7fe", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f089a881-90d0-4a80-83f2-d3f8fbbbe7fe", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_185433", "step_number": 3, "timestamp": "2025-07-27T18:54:35.267386", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1578.2737731933594}, {"session_id": "20250727_185433", "step_number": 4, "timestamp": "2025-07-27T18:54:35.271623", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1582.5104713439941}, {"session_id": "20250727_185433", "step_number": 5, "timestamp": "2025-07-27T18:54:35.284931", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.603500366210938}, {"session_id": "20250727_185433", "step_number": 6, "timestamp": "2025-07-27T18:54:35.288915", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.587236404418945}, {"session_id": "20250727_185433", "step_number": 7, "timestamp": "2025-07-27T18:56:28.764479", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "660b4e32-7e36-4314-a3b5-ff82565273dd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "660b4e32-7e36-4314-a3b5-ff82565273dd", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 2}}, "duration_ms": null}, {"session_id": "20250727_185433", "step_number": 8, "timestamp": "2025-07-27T18:56:28.771256", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "660b4e32-7e36-4314-a3b5-ff82565273dd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "660b4e32-7e36-4314-a3b5-ff82565273dd", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_185433", "step_number": 9, "timestamp": "2025-07-27T18:56:30.824526", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2048.424243927002}, {"session_id": "20250727_185433", "step_number": 10, "timestamp": "2025-07-27T18:56:30.829082", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2052.9801845550537}, {"session_id": "20250727_185433", "step_number": 11, "timestamp": "2025-07-27T18:56:30.845163", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.091875076293945}, {"session_id": "20250727_185433", "step_number": 12, "timestamp": "2025-07-27T18:56:30.850664", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.592668533325195}, {"session_id": "20250727_185759", "step_number": 1, "timestamp": "2025-07-27T18:57:59.727634", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "99f4a547-c9b4-4441-bfe5-a1a94c5a2629", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "99f4a547-c9b4-4441-bfe5-a1a94c5a2629", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 2}}, "duration_ms": null}, {"session_id": "20250727_185759", "step_number": 2, "timestamp": "2025-07-27T18:57:59.732875", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "99f4a547-c9b4-4441-bfe5-a1a94c5a2629", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "99f4a547-c9b4-4441-bfe5-a1a94c5a2629", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_185759", "step_number": 3, "timestamp": "2025-07-27T18:58:01.719005", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1981.7051887512207}, {"session_id": "20250727_185759", "step_number": 4, "timestamp": "2025-07-27T18:58:01.722825", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1985.525131225586}, {"session_id": "20250727_185759", "step_number": 5, "timestamp": "2025-07-27T18:58:01.738395", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.213302612304688}, {"session_id": "20250727_185759", "step_number": 6, "timestamp": "2025-07-27T18:58:01.742996", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.814065933227539}, {"session_id": "20250727_185921", "step_number": 1, "timestamp": "2025-07-27T18:59:21.092293", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b6c6e847-604c-4c10-bfc6-59b1807956ac", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b6c6e847-604c-4c10-bfc6-59b1807956ac", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 2}}, "duration_ms": null}, {"session_id": "20250727_185921", "step_number": 2, "timestamp": "2025-07-27T18:59:21.100872", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b6c6e847-604c-4c10-bfc6-59b1807956ac", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b6c6e847-604c-4c10-bfc6-59b1807956ac", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_185921", "step_number": 3, "timestamp": "2025-07-27T18:59:23.064263", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1956.5954208374023}, {"session_id": "20250727_185921", "step_number": 4, "timestamp": "2025-07-27T18:59:23.068747", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1961.0791206359863}, {"session_id": "20250727_185921", "step_number": 5, "timestamp": "2025-07-27T18:59:23.085096", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.99960708618164}, {"session_id": "20250727_185921", "step_number": 6, "timestamp": "2025-07-27T18:59:23.089463", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.366243362426758}, {"session_id": "20250727_185921", "step_number": 7, "timestamp": "2025-07-27T19:40:27.313890", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7f569c5d-7c68-453d-8a9a-c76a7f8a0200", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7f569c5d-7c68-453d-8a9a-c76a7f8a0200", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_185921", "step_number": 8, "timestamp": "2025-07-27T19:40:27.320865", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7f569c5d-7c68-453d-8a9a-c76a7f8a0200", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7f569c5d-7c68-453d-8a9a-c76a7f8a0200", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250727_185921", "step_number": 9, "timestamp": "2025-07-27T19:40:28.999163", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1670.0541973114014}, {"session_id": "20250727_185921", "step_number": 10, "timestamp": "2025-07-27T19:40:29.003736", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1674.6277809143066}, {"session_id": "20250727_185921", "step_number": 11, "timestamp": "2025-07-27T19:40:29.018192", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.469675064086914}, {"session_id": "20250727_185921", "step_number": 12, "timestamp": "2025-07-27T19:40:29.022680", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 14.957427978515625}, {"session_id": "20250727_185921", "step_number": 13, "timestamp": "2025-07-27T19:52:44.726948", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5c641412-a0a3-4497-99c6-580b00d76fc6", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5c641412-a0a3-4497-99c6-580b00d76fc6", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-744.9829822921603, 1276.1424377657172, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_185921", "step_number": 14, "timestamp": "2025-07-27T19:52:44.734932", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5c641412-a0a3-4497-99c6-580b00d76fc6", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5c641412-a0a3-4497-99c6-580b00d76fc6", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-744.9829822921603, 1276.1424377657172, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_185921", "step_number": 15, "timestamp": "2025-07-27T19:52:46.288454", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1547.0001697540283}, {"session_id": "20250727_185921", "step_number": 16, "timestamp": "2025-07-27T19:52:46.294516", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1553.0622005462646}, {"session_id": "20250727_185921", "step_number": 17, "timestamp": "2025-07-27T19:52:46.310451", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.532379150390625}, {"session_id": "20250727_185921", "step_number": 18, "timestamp": "2025-07-27T19:52:46.317344", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.94743537902832}, {"session_id": "20250727_201111", "step_number": 1, "timestamp": "2025-07-27T20:11:11.783701", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b4708001-cf9a-43ed-8010-97949ee2f8d9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b4708001-cf9a-43ed-8010-97949ee2f8d9", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-987.8577671566591, 1235.7178373617626, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_201111", "step_number": 2, "timestamp": "2025-07-27T20:11:11.793390", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b4708001-cf9a-43ed-8010-97949ee2f8d9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b4708001-cf9a-43ed-8010-97949ee2f8d9", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-987.8577671566591, 1235.7178373617626, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_201111", "step_number": 3, "timestamp": "2025-07-27T20:11:13.394970", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1593.5938358306885}, {"session_id": "20250727_201111", "step_number": 4, "timestamp": "2025-07-27T20:11:13.401031", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1599.6553897857666}, {"session_id": "20250727_201111", "step_number": 5, "timestamp": "2025-07-27T20:11:13.419978", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.314558029174805}, {"session_id": "20250727_201111", "step_number": 6, "timestamp": "2025-07-27T20:11:13.428680", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.017074584960938}, {"session_id": "20250727_201753", "step_number": 1, "timestamp": "2025-07-27T20:17:53.777027", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0c23b3ea-5e97-4b15-b189-7be2d86511da", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0c23b3ea-5e97-4b15-b189-7be2d86511da", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-931.8051211327299, 1207.1246330846564, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_201753", "step_number": 2, "timestamp": "2025-07-27T20:17:53.786420", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0c23b3ea-5e97-4b15-b189-7be2d86511da", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0c23b3ea-5e97-4b15-b189-7be2d86511da", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-931.8051211327299, 1207.1246330846564, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_201753", "step_number": 3, "timestamp": "2025-07-27T20:17:55.251435", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1454.9963474273682}, {"session_id": "20250727_201753", "step_number": 4, "timestamp": "2025-07-27T20:17:55.256856", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1460.4167938232422}, {"session_id": "20250727_201753", "step_number": 5, "timestamp": "2025-07-27T20:17:55.270824", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.394168853759766}, {"session_id": "20250727_201753", "step_number": 6, "timestamp": "2025-07-27T20:17:55.274855", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.425827026367188}, {"session_id": "20250727_201753", "step_number": 7, "timestamp": "2025-07-27T20:19:05.672197", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6c52e70c-b911-4635-aa88-7709f3a0fac0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6c52e70c-b911-4635-aa88-7709f3a0fac0", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-932.0486113903629, 1171.122768237362, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_201753", "step_number": 8, "timestamp": "2025-07-27T20:19:05.680196", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6c52e70c-b911-4635-aa88-7709f3a0fac0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6c52e70c-b911-4635-aa88-7709f3a0fac0", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-932.0486113903629, 1171.122768237362, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_201753", "step_number": 9, "timestamp": "2025-07-27T20:19:07.165677", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1478.5029888153076}, {"session_id": "20250727_201753", "step_number": 10, "timestamp": "2025-07-27T20:19:07.170199", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1483.0243587493896}, {"session_id": "20250727_201753", "step_number": 11, "timestamp": "2025-07-27T20:19:07.185780", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.608673095703125}, {"session_id": "20250727_201753", "step_number": 12, "timestamp": "2025-07-27T20:19:07.190504", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.332221984863281}, {"session_id": "20250727_202331", "step_number": 1, "timestamp": "2025-07-27T20:23:31.089098", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bcdc776d-d666-4fb2-9699-0902f2bda714", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bcdc776d-d666-4fb2-9699-0902f2bda714", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1008.3029211621542, 1283.8405076605031, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_202331", "step_number": 2, "timestamp": "2025-07-27T20:23:31.098803", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bcdc776d-d666-4fb2-9699-0902f2bda714", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bcdc776d-d666-4fb2-9699-0902f2bda714", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1008.3029211621542, 1283.8405076605031, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_202331", "step_number": 3, "timestamp": "2025-07-27T20:23:33.029004", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1922.823190689087}, {"session_id": "20250727_202331", "step_number": 4, "timestamp": "2025-07-27T20:23:33.036106", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1929.9254417419434}, {"session_id": "20250727_202331", "step_number": 5, "timestamp": "2025-07-27T20:23:33.053500", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.371135711669922}, {"session_id": "20250727_202331", "step_number": 6, "timestamp": "2025-07-27T20:23:33.061493", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.363880157470703}, {"session_id": "20250727_203555", "step_number": 1, "timestamp": "2025-07-27T20:35:55.862683", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b98161dc-8b03-452b-83ca-85f4cfb3fdc0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b98161dc-8b03-452b-83ca-85f4cfb3fdc0", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-996.558484331958, 1687.6821114928175, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_203555", "step_number": 2, "timestamp": "2025-07-27T20:35:55.873633", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b98161dc-8b03-452b-83ca-85f4cfb3fdc0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b98161dc-8b03-452b-83ca-85f4cfb3fdc0", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-996.558484331958, 1687.6821114928175, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_203555", "step_number": 3, "timestamp": "2025-07-27T20:35:58.212490", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2331.0585021972656}, {"session_id": "20250727_203555", "step_number": 4, "timestamp": "2025-07-27T20:35:58.221015", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2339.583158493042}, {"session_id": "20250727_203555", "step_number": 5, "timestamp": "2025-07-27T20:35:58.240395", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.652801513671875}, {"session_id": "20250727_203555", "step_number": 6, "timestamp": "2025-07-27T20:35:58.248246", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.503448486328125}, {"session_id": "20250727_204103", "step_number": 1, "timestamp": "2025-07-27T20:41:03.668231", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c7c3f4d0-e846-4e72-aeda-324df475d2c1", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c7c3f4d0-e846-4e72-aeda-324df475d2c1", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-785.2438401220585, 1332.9539559418718, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_204103", "step_number": 2, "timestamp": "2025-07-27T20:41:03.678323", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c7c3f4d0-e846-4e72-aeda-324df475d2c1", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c7c3f4d0-e846-4e72-aeda-324df475d2c1", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-785.2438401220585, 1332.9539559418718, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_204103", "step_number": 3, "timestamp": "2025-07-27T20:41:05.641846", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1955.9803009033203}, {"session_id": "20250727_204103", "step_number": 4, "timestamp": "2025-07-27T20:41:05.647391", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1961.5259170532227}, {"session_id": "20250727_204103", "step_number": 5, "timestamp": "2025-07-27T20:41:05.665019", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.12277603149414}, {"session_id": "20250727_204103", "step_number": 6, "timestamp": "2025-07-27T20:41:05.673093", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.196582794189453}, {"session_id": "20250727_204625", "step_number": 1, "timestamp": "2025-07-27T20:46:25.357364", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d457e59c-fd5c-4e36-9ab9-adcfbdb65172", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d457e59c-fd5c-4e36-9ab9-adcfbdb65172", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1139.003133191313, 1712.536172958637, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_204625", "step_number": 2, "timestamp": "2025-07-27T20:46:25.369326", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d457e59c-fd5c-4e36-9ab9-adcfbdb65172", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d457e59c-fd5c-4e36-9ab9-adcfbdb65172", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1139.003133191313, 1712.536172958637, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_204625", "step_number": 3, "timestamp": "2025-07-27T20:46:28.623378", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3241.316080093384}, {"session_id": "20250727_204625", "step_number": 4, "timestamp": "2025-07-27T20:46:28.634920", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 3252.8579235076904}, {"session_id": "20250727_204625", "step_number": 5, "timestamp": "2025-07-27T20:46:28.663129", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.64712142944336}, {"session_id": "20250727_204625", "step_number": 6, "timestamp": "2025-07-27T20:46:28.671698", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.216123580932617}, {"session_id": "20250727_204625", "step_number": 7, "timestamp": "2025-07-27T20:50:17.294883", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1c0f8ee3-2a03-417d-a7ca-feaf5c8164cb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1c0f8ee3-2a03-417d-a7ca-feaf5c8164cb", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-878.8312277465841, 1178.3623546701829, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_204625", "step_number": 8, "timestamp": "2025-07-27T20:50:17.312404", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1c0f8ee3-2a03-417d-a7ca-feaf5c8164cb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1c0f8ee3-2a03-417d-a7ca-feaf5c8164cb", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-878.8312277465841, 1178.3623546701829, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_204625", "step_number": 9, "timestamp": "2025-07-27T20:50:19.451403", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2126.443386077881}, {"session_id": "20250727_204625", "step_number": 10, "timestamp": "2025-07-27T20:50:19.460288", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2135.328769683838}, {"session_id": "20250727_204625", "step_number": 11, "timestamp": "2025-07-27T20:50:19.484818", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.713930130004883}, {"session_id": "20250727_204625", "step_number": 12, "timestamp": "2025-07-27T20:50:19.493456", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 24.352312088012695}, {"session_id": "20250727_210000", "step_number": 1, "timestamp": "2025-07-27T21:00:00.218092", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "67923101-7e11-4e62-bf38-154180ea8f4a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "67923101-7e11-4e62-bf38-154180ea8f4a", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1165.2562821243896, 1639.4316327772726, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_210000", "step_number": 2, "timestamp": "2025-07-27T21:00:00.229294", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "67923101-7e11-4e62-bf38-154180ea8f4a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "67923101-7e11-4e62-bf38-154180ea8f4a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1165.2562821243896, 1639.4316327772726, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_210000", "step_number": 3, "timestamp": "2025-07-27T21:00:02.573238", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2333.7080478668213}, {"session_id": "20250727_210000", "step_number": 4, "timestamp": "2025-07-27T21:00:02.580020", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2340.4905796051025}, {"session_id": "20250727_210000", "step_number": 5, "timestamp": "2025-07-27T21:00:02.602384", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.931512832641602}, {"session_id": "20250727_210000", "step_number": 6, "timestamp": "2025-07-27T21:00:02.609879", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.426677703857422}, {"session_id": "20250727_210630", "step_number": 1, "timestamp": "2025-07-27T21:06:30.452751", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8959fd6c-b4f7-43ac-b490-04d796e47a2e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8959fd6c-b4f7-43ac-b490-04d796e47a2e", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1232.064206954981, 1710.9489880716046, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_210630", "step_number": 2, "timestamp": "2025-07-27T21:06:30.472356", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8959fd6c-b4f7-43ac-b490-04d796e47a2e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8959fd6c-b4f7-43ac-b490-04d796e47a2e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1232.064206954981, 1710.9489880716046, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_210630", "step_number": 3, "timestamp": "2025-07-27T21:06:33.016399", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2524.2233276367188}, {"session_id": "20250727_210630", "step_number": 4, "timestamp": "2025-07-27T21:06:33.024941", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2532.7658653259277}, {"session_id": "20250727_210630", "step_number": 5, "timestamp": "2025-07-27T21:06:33.047438", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.249252319335938}, {"session_id": "20250727_210630", "step_number": 6, "timestamp": "2025-07-27T21:06:33.056947", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 24.758577346801758}, {"session_id": "20250727_211401", "step_number": 1, "timestamp": "2025-07-27T21:14:01.522317", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8ce0089e-b34e-4446-b62c-a60eee1f14a4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8ce0089e-b34e-4446-b62c-a60eee1f14a4", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1050.8201478373744, 1494.122547372434, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_211401", "step_number": 2, "timestamp": "2025-07-27T21:14:01.547457", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8ce0089e-b34e-4446-b62c-a60eee1f14a4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8ce0089e-b34e-4446-b62c-a60eee1f14a4", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1050.8201478373744, 1494.122547372434, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_211401", "step_number": 3, "timestamp": "2025-07-27T21:14:04.150284", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2581.848621368408}, {"session_id": "20250727_211401", "step_number": 4, "timestamp": "2025-07-27T21:14:04.157964", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2589.528799057007}, {"session_id": "20250727_211401", "step_number": 5, "timestamp": "2025-07-27T21:14:04.182420", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.942262649536133}, {"session_id": "20250727_211401", "step_number": 6, "timestamp": "2025-07-27T21:14:04.188944", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 23.465871810913086}, {"session_id": "20250727_212122", "step_number": 1, "timestamp": "2025-07-27T21:21:22.134291", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "31e6b8bb-ba99-4378-9940-6d7382d94ab5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "31e6b8bb-ba99-4378-9940-6d7382d94ab5", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-909.9134832568457, 1375.6669907829742, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_212122", "step_number": 2, "timestamp": "2025-07-27T21:21:22.143960", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "31e6b8bb-ba99-4378-9940-6d7382d94ab5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "31e6b8bb-ba99-4378-9940-6d7382d94ab5", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-909.9134832568457, 1375.6669907829742, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_212122", "step_number": 3, "timestamp": "2025-07-27T21:21:24.271087", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2117.433547973633}, {"session_id": "20250727_212122", "step_number": 4, "timestamp": "2025-07-27T21:21:24.280108", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2126.4541149139404}, {"session_id": "20250727_212122", "step_number": 5, "timestamp": "2025-07-27T21:21:24.300796", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.520862579345703}, {"session_id": "20250727_212122", "step_number": 6, "timestamp": "2025-07-27T21:21:24.310823", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.55811882019043}, {"session_id": "20250727_212844", "step_number": 1, "timestamp": "2025-07-27T21:28:44.026703", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a1c801c9-50ac-4888-b1cb-e572c3d3484e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a1c801c9-50ac-4888-b1cb-e572c3d3484e", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-779.5297159602474, 1459.4827631728197, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_212844", "step_number": 2, "timestamp": "2025-07-27T21:28:44.038009", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a1c801c9-50ac-4888-b1cb-e572c3d3484e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a1c801c9-50ac-4888-b1cb-e572c3d3484e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-779.5297159602474, 1459.4827631728197, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_212844", "step_number": 3, "timestamp": "2025-07-27T21:28:45.926334", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1880.486011505127}, {"session_id": "20250727_212844", "step_number": 4, "timestamp": "2025-07-27T21:28:45.933132", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1887.284278869629}, {"session_id": "20250727_212844", "step_number": 5, "timestamp": "2025-07-27T21:28:45.952078", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.844945907592773}, {"session_id": "20250727_212844", "step_number": 6, "timestamp": "2025-07-27T21:28:45.960165", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.932104110717773}, {"session_id": "20250727_213126", "step_number": 1, "timestamp": "2025-07-27T21:31:26.232630", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4ef04a8b-0205-4017-9eee-70724832e8cf", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4ef04a8b-0205-4017-9eee-70724832e8cf", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-779.5297159602474, 1459.4827631728197, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_213126", "step_number": 2, "timestamp": "2025-07-27T21:31:26.247517", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4ef04a8b-0205-4017-9eee-70724832e8cf", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4ef04a8b-0205-4017-9eee-70724832e8cf", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-779.5297159602474, 1459.4827631728197, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_213126", "step_number": 3, "timestamp": "2025-07-27T21:31:28.379970", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2116.4615154266357}, {"session_id": "20250727_213126", "step_number": 4, "timestamp": "2025-07-27T21:31:28.389248", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2125.739574432373}, {"session_id": "20250727_213126", "step_number": 5, "timestamp": "2025-07-27T21:31:28.410980", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 14.327049255371094}, {"session_id": "20250727_213126", "step_number": 6, "timestamp": "2025-07-27T21:31:28.420362", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 23.708820343017578}, {"session_id": "20250727_213532", "step_number": 1, "timestamp": "2025-07-27T21:35:32.435899", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fde947f2-9423-4d25-82b8-f8dcb24fa0dd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fde947f2-9423-4d25-82b8-f8dcb24fa0dd", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-877.2213256985765, 1240.4775736725994, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_213532", "step_number": 2, "timestamp": "2025-07-27T21:35:32.450432", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fde947f2-9423-4d25-82b8-f8dcb24fa0dd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fde947f2-9423-4d25-82b8-f8dcb24fa0dd", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-877.2213256985765, 1240.4775736725994, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_213532", "step_number": 3, "timestamp": "2025-07-27T21:35:34.322703", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1861.1423969268799}, {"session_id": "20250727_213532", "step_number": 4, "timestamp": "2025-07-27T21:35:34.330278", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1868.718147277832}, {"session_id": "20250727_213532", "step_number": 5, "timestamp": "2025-07-27T21:35:34.348383", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.790824890136719}, {"session_id": "20250727_213532", "step_number": 6, "timestamp": "2025-07-27T21:35:34.356781", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.188642501831055}, {"session_id": "20250727_213953", "step_number": 1, "timestamp": "2025-07-27T21:39:53.694475", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "cd70804e-46b7-44df-a754-5409b2f34b12", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "cd70804e-46b7-44df-a754-5409b2f34b12", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-786.5270150295228, 1247.0727377784876, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_213953", "step_number": 2, "timestamp": "2025-07-27T21:39:53.706778", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "cd70804e-46b7-44df-a754-5409b2f34b12", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "cd70804e-46b7-44df-a754-5409b2f34b12", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-786.5270150295228, 1247.0727377784876, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_213953", "step_number": 3, "timestamp": "2025-07-27T21:39:55.466113", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1751.2855529785156}, {"session_id": "20250727_213953", "step_number": 4, "timestamp": "2025-07-27T21:39:55.474447", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1759.619951248169}, {"session_id": "20250727_213953", "step_number": 5, "timestamp": "2025-07-27T21:39:55.493178", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.682271957397461}, {"session_id": "20250727_213953", "step_number": 6, "timestamp": "2025-07-27T21:39:55.501247", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.750595092773438}, {"session_id": "20250727_214356", "step_number": 1, "timestamp": "2025-07-27T21:43:56.051547", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "54ce76a8-b35e-46c5-9cf6-7cf817f1e034", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "54ce76a8-b35e-46c5-9cf6-7cf817f1e034", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-904.6862267973279, 1143.2603042487879, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_214356", "step_number": 2, "timestamp": "2025-07-27T21:43:56.067762", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "54ce76a8-b35e-46c5-9cf6-7cf817f1e034", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "54ce76a8-b35e-46c5-9cf6-7cf817f1e034", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-904.6862267973279, 1143.2603042487879, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_214356", "step_number": 3, "timestamp": "2025-07-27T21:43:58.127641", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2046.6244220733643}, {"session_id": "20250727_214356", "step_number": 4, "timestamp": "2025-07-27T21:43:58.137590", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2056.572675704956}, {"session_id": "20250727_214356", "step_number": 5, "timestamp": "2025-07-27T21:43:58.159497", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.274669647216797}, {"session_id": "20250727_214356", "step_number": 6, "timestamp": "2025-07-27T21:43:58.169074", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.85170555114746}, {"session_id": "20250727_214953", "step_number": 1, "timestamp": "2025-07-27T21:49:53.340990", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b317b4f3-28b2-46d2-9377-9ce5bb49ec1c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b317b4f3-28b2-46d2-9377-9ce5bb49ec1c", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-766.2212956186116, 1437.378694137003, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_214953", "step_number": 2, "timestamp": "2025-07-27T21:49:53.353456", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b317b4f3-28b2-46d2-9377-9ce5bb49ec1c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b317b4f3-28b2-46d2-9377-9ce5bb49ec1c", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-766.2212956186116, 1437.378694137003, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_214953", "step_number": 3, "timestamp": "2025-07-27T21:49:55.005818", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1640.4564380645752}, {"session_id": "20250727_214953", "step_number": 4, "timestamp": "2025-07-27T21:49:55.013164", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1648.8099098205566}, {"session_id": "20250727_214953", "step_number": 5, "timestamp": "2025-07-27T21:49:55.029296", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.063720703125}, {"session_id": "20250727_214953", "step_number": 6, "timestamp": "2025-07-27T21:49:55.036261", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.028165817260742}, {"session_id": "20250727_215404", "step_number": 1, "timestamp": "2025-07-27T21:54:04.951837", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "11d19cc1-e244-4adb-b057-551cc2708aee", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "11d19cc1-e244-4adb-b057-551cc2708aee", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-823.192605236844, 1388.5959597499282, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_215404", "step_number": 2, "timestamp": "2025-07-27T21:54:04.965206", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "11d19cc1-e244-4adb-b057-551cc2708aee", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "11d19cc1-e244-4adb-b057-551cc2708aee", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-823.192605236844, 1388.5959597499282, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_215404", "step_number": 3, "timestamp": "2025-07-27T21:54:07.300542", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2323.7521648406982}, {"session_id": "20250727_215404", "step_number": 4, "timestamp": "2025-07-27T21:54:07.314008", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2337.2182846069336}, {"session_id": "20250727_215404", "step_number": 5, "timestamp": "2025-07-27T21:54:07.346339", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.170066833496094}, {"session_id": "20250727_215404", "step_number": 6, "timestamp": "2025-07-27T21:54:07.358530", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.360647201538086}, {"session_id": "20250727_220700", "step_number": 1, "timestamp": "2025-07-27T22:07:00.955348", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b66f7194-e881-457d-85a7-598da02cafad", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b66f7194-e881-457d-85a7-598da02cafad", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1135.966004681656, 1677.128536732529, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_220700", "step_number": 2, "timestamp": "2025-07-27T22:07:00.970612", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b66f7194-e881-457d-85a7-598da02cafad", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b66f7194-e881-457d-85a7-598da02cafad", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1135.966004681656, 1677.128536732529, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_220700", "step_number": 3, "timestamp": "2025-07-27T22:07:02.966303", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1986.823320388794}, {"session_id": "20250727_220700", "step_number": 4, "timestamp": "2025-07-27T22:07:02.973829", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1994.3487644195557}, {"session_id": "20250727_220700", "step_number": 5, "timestamp": "2025-07-27T22:07:02.994015", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.688875198364258}, {"session_id": "20250727_220700", "step_number": 6, "timestamp": "2025-07-27T22:07:03.000968", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.641637802124023}, {"session_id": "20250727_221000", "step_number": 1, "timestamp": "2025-07-27T22:10:00.657418", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "220d5725-a9d5-4eab-9122-2c2e06f40eef", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "220d5725-a9d5-4eab-9122-2c2e06f40eef", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-711.3169366832856, 1090.8297490823472, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_221000", "step_number": 2, "timestamp": "2025-07-27T22:10:00.668049", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "220d5725-a9d5-4eab-9122-2c2e06f40eef", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "220d5725-a9d5-4eab-9122-2c2e06f40eef", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-711.3169366832856, 1090.8297490823472, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_221000", "step_number": 3, "timestamp": "2025-07-27T22:10:02.195597", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1517.615795135498}, {"session_id": "20250727_221000", "step_number": 4, "timestamp": "2025-07-27T22:10:02.203004", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1525.022268295288}, {"session_id": "20250727_221000", "step_number": 5, "timestamp": "2025-07-27T22:10:02.218785", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 8.529186248779297}, {"session_id": "20250727_221000", "step_number": 6, "timestamp": "2025-07-27T22:10:02.226222", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.96689224243164}, {"session_id": "20250727_221529", "step_number": 1, "timestamp": "2025-07-27T22:15:29.690104", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "592bde6a-4ff1-4a45-8304-07bd7d108136", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "592bde6a-4ff1-4a45-8304-07bd7d108136", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-993.4026972005453, 1335.071687438594, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_221529", "step_number": 2, "timestamp": "2025-07-27T22:15:29.701087", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "592bde6a-4ff1-4a45-8304-07bd7d108136", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "592bde6a-4ff1-4a45-8304-07bd7d108136", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-993.4026972005453, 1335.071687438594, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_221529", "step_number": 3, "timestamp": "2025-07-27T22:15:31.411566", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1695.3110694885254}, {"session_id": "20250727_221529", "step_number": 4, "timestamp": "2025-07-27T22:15:31.419296", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1703.0415534973145}, {"session_id": "20250727_221529", "step_number": 5, "timestamp": "2025-07-27T22:15:31.438395", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.514663696289062}, {"session_id": "20250727_221529", "step_number": 6, "timestamp": "2025-07-27T22:15:31.446639", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.759416580200195}, {"session_id": "20250727_223009", "step_number": 1, "timestamp": "2025-07-27T22:30:09.719868", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b6ff33f1-f15c-4c15-897f-77d298ac9779", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b6ff33f1-f15c-4c15-897f-77d298ac9779", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1000.662343265389, 1046.2130158510752, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_223009", "step_number": 2, "timestamp": "2025-07-27T22:30:09.734316", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b6ff33f1-f15c-4c15-897f-77d298ac9779", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b6ff33f1-f15c-4c15-897f-77d298ac9779", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1000.662343265389, 1046.2130158510752, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_223009", "step_number": 3, "timestamp": "2025-07-27T22:30:11.436565", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1688.9147758483887}, {"session_id": "20250727_223009", "step_number": 4, "timestamp": "2025-07-27T22:30:11.445098", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1697.4480152130127}, {"session_id": "20250727_223009", "step_number": 5, "timestamp": "2025-07-27T22:30:11.464563", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.484695434570312}, {"session_id": "20250727_223009", "step_number": 6, "timestamp": "2025-07-27T22:30:11.475711", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.632671356201172}, {"session_id": "20250727_223009", "step_number": 7, "timestamp": "2025-07-27T22:31:43.182777", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d542bc36-4942-475b-82fe-47ba3348fd02", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d542bc36-4942-475b-82fe-47ba3348fd02", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1000.662343265389, 1046.2130158510752, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_223009", "step_number": 8, "timestamp": "2025-07-27T22:31:43.194177", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d542bc36-4942-475b-82fe-47ba3348fd02", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d542bc36-4942-475b-82fe-47ba3348fd02", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1000.662343265389, 1046.2130158510752, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_223009", "step_number": 9, "timestamp": "2025-07-27T22:31:44.897400", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1689.2774105072021}, {"session_id": "20250727_223009", "step_number": 10, "timestamp": "2025-07-27T22:31:44.905425", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1697.3016262054443}, {"session_id": "20250727_223009", "step_number": 11, "timestamp": "2025-07-27T22:31:44.928894", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.657663345336914}, {"session_id": "20250727_223009", "step_number": 12, "timestamp": "2025-07-27T22:31:44.938111", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 24.874448776245117}, {"session_id": "20250727_231019", "step_number": 1, "timestamp": "2025-07-27T23:10:19.702408", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8b6d9c01-d40c-4112-be07-455b9c236b30", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8b6d9c01-d40c-4112-be07-455b9c236b30", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-878.9242911587003, 1069.4166173187018, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 105}}, "duration_ms": null}, {"session_id": "20250727_231019", "step_number": 2, "timestamp": "2025-07-27T23:10:19.724933", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8b6d9c01-d40c-4112-be07-455b9c236b30", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8b6d9c01-d40c-4112-be07-455b9c236b30", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-878.9242911587003, 1069.4166173187018, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_231019", "step_number": 3, "timestamp": "2025-07-27T23:10:21.638318", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1894.7162628173828}, {"session_id": "20250727_231019", "step_number": 4, "timestamp": "2025-07-27T23:10:21.647823", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1904.2208194732666}, {"session_id": "20250727_231019", "step_number": 5, "timestamp": "2025-07-27T23:10:21.668604", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.956121444702148}, {"session_id": "20250727_231019", "step_number": 6, "timestamp": "2025-07-27T23:10:21.679192", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.544767379760742}, {"session_id": "20250727_232029", "step_number": 1, "timestamp": "2025-07-27T23:20:29.467117", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "227a76c4-a56f-4578-a820-12706deba73a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "227a76c4-a56f-4578-a820-12706deba73a", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-924.5197010735735, 1418.1622970178475, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_232029", "step_number": 2, "timestamp": "2025-07-27T23:20:29.484920", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "227a76c4-a56f-4578-a820-12706deba73a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "227a76c4-a56f-4578-a820-12706deba73a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-924.5197010735735, 1418.1622970178475, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_232029", "step_number": 3, "timestamp": "2025-07-27T23:20:31.680071", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2180.8712482452393}, {"session_id": "20250727_232029", "step_number": 4, "timestamp": "2025-07-27T23:20:31.691307", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2192.1072006225586}, {"session_id": "20250727_232029", "step_number": 5, "timestamp": "2025-07-27T23:20:31.713983", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.62576675415039}, {"session_id": "20250727_232029", "step_number": 6, "timestamp": "2025-07-27T23:20:31.723855", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.49796485900879}, {"session_id": "20250727_233105", "step_number": 1, "timestamp": "2025-07-27T23:31:05.697082", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7efa0da3-5c47-4e11-9fab-3e0c00c8b0c0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7efa0da3-5c47-4e11-9fab-3e0c00c8b0c0", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-880.4649043868112, 1205.5847759350372, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_233105", "step_number": 2, "timestamp": "2025-07-27T23:31:05.716588", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7efa0da3-5c47-4e11-9fab-3e0c00c8b0c0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7efa0da3-5c47-4e11-9fab-3e0c00c8b0c0", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-880.4649043868112, 1205.5847759350372, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_233105", "step_number": 3, "timestamp": "2025-07-27T23:31:07.562894", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1827.3580074310303}, {"session_id": "20250727_233105", "step_number": 4, "timestamp": "2025-07-27T23:31:07.572073", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1836.5375995635986}, {"session_id": "20250727_233105", "step_number": 5, "timestamp": "2025-07-27T23:31:07.591468", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.833501815795898}, {"session_id": "20250727_233105", "step_number": 6, "timestamp": "2025-07-27T23:31:07.600544", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.90962028503418}, {"session_id": "20250727_233447", "step_number": 1, "timestamp": "2025-07-27T23:34:47.584367", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a2320319-20a3-47d7-8d67-29250dc84932", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a2320319-20a3-47d7-8d67-29250dc84932", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-874.3875898092607, 1076.2624708478006, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_233447", "step_number": 2, "timestamp": "2025-07-27T23:34:47.601537", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a2320319-20a3-47d7-8d67-29250dc84932", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a2320319-20a3-47d7-8d67-29250dc84932", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-874.3875898092607, 1076.2624708478006, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_233447", "step_number": 3, "timestamp": "2025-07-27T23:34:49.455823", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1834.2766761779785}, {"session_id": "20250727_233447", "step_number": 4, "timestamp": "2025-07-27T23:34:49.465391", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1843.8451290130615}, {"session_id": "20250727_233447", "step_number": 5, "timestamp": "2025-07-27T23:34:49.485982", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.660886764526367}, {"session_id": "20250727_233447", "step_number": 6, "timestamp": "2025-07-27T23:34:49.495550", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.229101181030273}, {"session_id": "20250727_234028", "step_number": 1, "timestamp": "2025-07-27T23:40:28.568780", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f6db9441-3d57-4a9b-877d-f5663bb6d6f8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f6db9441-3d57-4a9b-877d-f5663bb6d6f8", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1105.9942906875647, 1191.6806134698713, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_234028", "step_number": 2, "timestamp": "2025-07-27T23:40:28.584250", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f6db9441-3d57-4a9b-877d-f5663bb6d6f8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f6db9441-3d57-4a9b-877d-f5663bb6d6f8", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1105.9942906875647, 1191.6806134698713, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_234028", "step_number": 3, "timestamp": "2025-07-27T23:40:31.041075", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2442.4996376037598}, {"session_id": "20250727_234028", "step_number": 4, "timestamp": "2025-07-27T23:40:31.051650", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2453.0746936798096}, {"session_id": "20250727_234028", "step_number": 5, "timestamp": "2025-07-27T23:40:31.074461", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.234210968017578}, {"session_id": "20250727_234028", "step_number": 6, "timestamp": "2025-07-27T23:40:31.083492", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.265506744384766}, {"session_id": "20250727_234255", "step_number": 1, "timestamp": "2025-07-27T23:42:55.184932", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7aa9aa77-910c-4d1e-9fa0-b270ffc545b3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7aa9aa77-910c-4d1e-9fa0-b270ffc545b3", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-884.57817855594, 1066.1714490602287, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_234255", "step_number": 2, "timestamp": "2025-07-27T23:42:55.197998", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7aa9aa77-910c-4d1e-9fa0-b270ffc545b3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7aa9aa77-910c-4d1e-9fa0-b270ffc545b3", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-884.57817855594, 1066.1714490602287, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_234255", "step_number": 3, "timestamp": "2025-07-27T23:42:56.648864", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1439.9845600128174}, {"session_id": "20250727_234255", "step_number": 4, "timestamp": "2025-07-27T23:42:56.658067", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1449.1877555847168}, {"session_id": "20250727_234255", "step_number": 5, "timestamp": "2025-07-27T23:42:56.674811", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.060382843017578}, {"session_id": "20250727_234255", "step_number": 6, "timestamp": "2025-07-27T23:42:56.683545", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.795085906982422}, {"session_id": "20250727_235442", "step_number": 1, "timestamp": "2025-07-27T23:54:42.322688", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3c3aab3f-7b61-4b3d-bedd-23ad3fc8324b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3c3aab3f-7b61-4b3d-bedd-23ad3fc8324b", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-919.0338473679483, 1313.3366939557545, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_235442", "step_number": 2, "timestamp": "2025-07-27T23:54:42.336790", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3c3aab3f-7b61-4b3d-bedd-23ad3fc8324b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3c3aab3f-7b61-4b3d-bedd-23ad3fc8324b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-919.0338473679483, 1313.3366939557545, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_235442", "step_number": 3, "timestamp": "2025-07-27T23:54:44.311387", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1962.3336791992188}, {"session_id": "20250727_235442", "step_number": 4, "timestamp": "2025-07-27T23:54:44.321128", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1972.074270248413}, {"session_id": "20250727_235442", "step_number": 5, "timestamp": "2025-07-27T23:54:44.344082", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.226032257080078}, {"session_id": "20250727_235442", "step_number": 6, "timestamp": "2025-07-27T23:54:44.354907", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 24.05095100402832}]