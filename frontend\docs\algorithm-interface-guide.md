# 🧠 路径规划算法接口系统 - 开发指南

## 📋 概述

本系统为客户提供的路径规划算法预留了完整的接口和管理系统。客户可以轻松集成自己的算法，无需修改核心系统代码。

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    算法接口系统                              │
├─────────────────────────────────────────────────────────────┤
│  AlgorithmIntegrator (算法集成器)                           │
│  ├── AlgorithmManager (算法管理器)                          │
│  ├── PathPlanningRequest (请求数据结构)                     │
│  ├── PathPlanningResponse (响应数据结构)                    │
│  └── PathPlanningAlgorithm (算法基类)                       │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 创建自定义算法

```javascript
class MyCustomAlgorithm extends PathPlanningAlgorithm {
    constructor() {
        super();
        this.name = 'MyCustom';
        this.version = '1.0.0';
        this.description = '我的自定义路径规划算法';
        this.author = '客户名称';
        this.category = 'custom';
        
        // 定义算法参数
        this.requiredParameters = [
            {
                name: 'resolution',
                type: 'number',
                description: '算法分辨率',
                defaultValue: 10,
                validation: (value) => value >= 1 && value <= 100
            }
        ];
    }
    
    async calculatePath(request) {
        const response = new PathPlanningResponse();
        
        try {
            // 实现您的算法逻辑
            const path = await this.myAlgorithmLogic(request);
            
            response.success = true;
            response.path = path;
            response.calculateStatistics();
            
            return response;
            
        } catch (error) {
            response.success = false;
            response.error = error.message;
            return response;
        }
    }
    
    async myAlgorithmLogic(request) {
        // 您的算法实现
        const path = [];
        
        // 示例：创建路径点
        for (let i = 0; i < 10; i++) {
            path.push({
                lng: request.startPoint.lng + i * 0.001,
                lat: request.startPoint.lat + i * 0.001,
                alt: request.flightHeight,
                timestamp: Date.now() + i * 1000,
                speed: 10,
                heading: 45
            });
        }
        
        return path;
    }
}
```

### 2. 注册算法

```javascript
// 在系统初始化后注册算法
const myAlgorithm = new MyCustomAlgorithm();
cityManager.algorithmIntegrator.algorithmManager.registerAlgorithm(myAlgorithm);
```

## 📊 数据结构详解

### PathPlanningRequest (请求数据)

```javascript
const request = new PathPlanningRequest({
    // 基本路径参数
    startPoint: { lng: 139.767, lat: 35.681, alt: 0 },
    endPoint: { lng: 139.777, lat: 35.691, alt: 0 },
    
    // 飞行参数
    flightHeight: 100,        // 飞行高度(米)
    safetyDistance: 20,       // 安全距离(米)
    maxSpeed: 15,            // 最大速度(m/s)
    
    // 无人机性能参数
    droneSpecs: {
        maxFlightTime: 1800,     // 最大飞行时间(秒)
        batteryCapacity: 5000,   // 电池容量(mAh)
        payload: 0,              // 载重(kg)
        windResistance: 10       // 抗风能力(m/s)
    },
    
    // 环境数据
    buildings: [],           // 建筑物数据
    obstacles: [],           // 障碍物数据
    noFlyZones: [],         // 禁飞区域
    
    // 天气条件
    weather: {
        windSpeed: 0,           // 风速(m/s)
        windDirection: 0,       // 风向(度)
        visibility: 10000,      // 能见度(米)
        precipitation: 0,       // 降水量(mm/h)
        temperature: 20         // 温度(°C)
    },
    
    // 算法配置
    algorithm: 'MyCustom',
    parameters: {
        resolution: 10
    },
    optimization: 'distance',  // 优化目标
    
    // 约束条件
    constraints: {
        maxComputeTime: 30000,     // 最大计算时间(ms)
        maxMemoryUsage: 512,       // 最大内存使用(MB)
        smoothnessWeight: 0.3,     // 平滑度权重
        safetyWeight: 0.4,         // 安全性权重
        efficiencyWeight: 0.3      // 效率权重
    }
});
```

### PathPlanningResponse (响应数据)

```javascript
const response = new PathPlanningResponse();

// 设置执行状态
response.success = true;
response.executionTime = 1500; // 执行时间(ms)

// 设置路径数据
response.path = [
    {
        lng: 139.767,
        lat: 35.681,
        alt: 100,
        timestamp: Date.now(),
        speed: 10,
        heading: 45,
        action: 'fly'  // fly, hover, land, takeoff
    }
    // ... 更多路径点
];

// 设置质量评估
response.quality = {
    riskScore: 25,        // 风险评分(0-100)
    smoothness: 85,       // 平滑度(0-100)
    efficiency: 90,       // 效率评分(0-100)
    safetyMargin: 15,     // 安全余量(米)
    complexity: 'medium'  // 路径复杂度
};

// 计算统计信息
response.calculateStatistics();
```

## 🔧 算法基类方法

### 必须实现的方法

```javascript
class MyAlgorithm extends PathPlanningAlgorithm {
    // 主要计算接口 - 必须实现
    async calculatePath(request) {
        // 返回 PathPlanningResponse 对象
    }
}
```

### 可选重写的方法

```javascript
class MyAlgorithm extends PathPlanningAlgorithm {
    // 验证输入参数
    validateInput(request) {
        const validation = super.validateInput(request);
        
        // 添加自定义验证逻辑
        if (request.parameters.resolution < 1) {
            validation.errors.push('分辨率必须大于0');
            validation.isValid = false;
        }
        
        return validation;
    }
    
    // 估算执行时间
    estimateExecutionTime(request) {
        const complexity = request.estimateComplexity();
        // 根据算法特性返回估算时间
        return complexity === 'high' ? 20000 : 5000;
    }
}
```

## 📈 性能监控

### 获取算法性能统计

```javascript
// 获取单个算法统计
const algorithmInfo = algorithmManager.getAlgorithmInfo('MyCustom');
console.log('算法统计:', algorithmInfo.stats);

// 获取全局性能统计
const performanceStats = algorithmManager.getPerformanceStats();
console.log('全局统计:', performanceStats);
```

### 性能指标说明

- `totalExecutions`: 总执行次数
- `averageExecutionTime`: 平均执行时间(ms)
- `successRate`: 成功率(0-1)
- `maxExecutionTime`: 最大执行时间(ms)
- `minExecutionTime`: 最小执行时间(ms)

## 🎛️ 算法管理

### 注册和注销算法

```javascript
// 注册算法
algorithmManager.registerAlgorithm(new MyCustomAlgorithm());

// 注销算法
algorithmManager.unregisterAlgorithm('MyCustom');

// 获取算法列表
const algorithms = algorithmManager.listAlgorithms();
```

### 执行算法

```javascript
// 创建请求
const request = new PathPlanningRequest({
    startPoint: { lng: 139.767, lat: 35.681, alt: 0 },
    endPoint: { lng: 139.777, lat: 35.691, alt: 0 },
    algorithm: 'MyCustom',
    parameters: { resolution: 10 }
});

// 执行算法
const response = await algorithmManager.executeAlgorithm('MyCustom', request);

if (response.success) {
    console.log('路径规划成功:', response.path);
} else {
    console.error('路径规划失败:', response.error);
}
```

## 🔄 缓存系统

系统自动缓存算法结果以提高性能：

```javascript
// 启用/禁用缓存
algorithmManager.cacheEnabled = true;

// 清除缓存
algorithmManager.clearCache();

// 跳过缓存执行
const response = await algorithmManager.executeAlgorithm('MyCustom', request, {
    skipCache: true
});
```

## 🎯 优化目标

支持的优化目标：

- `distance`: 最短距离
- `time`: 最短时间
- `energy`: 最低能耗
- `safety`: 最高安全性

```javascript
const request = new PathPlanningRequest({
    // ... 其他参数
    optimization: 'safety'  // 优化目标
});
```

## 🚨 错误处理

### 常见错误类型

1. **输入验证错误**: 参数不符合要求
2. **算法执行错误**: 算法内部逻辑错误
3. **超时错误**: 执行时间超过限制
4. **内存错误**: 内存使用超过限制

### 错误处理最佳实践

```javascript
async calculatePath(request) {
    const response = new PathPlanningResponse();
    
    try {
        // 检查取消状态
        if (this.isCancelled) {
            throw new Error('算法执行已取消');
        }
        
        // 更新进度
        this.progress = 50;
        
        // 执行算法逻辑
        const path = await this.myAlgorithmLogic(request);
        
        response.success = true;
        response.path = path;
        
    } catch (error) {
        response.success = false;
        response.error = error.message;
        
        // 记录调试信息
        response.debugInfo.warnings.push(error.message);
    }
    
    return response;
}
```

## 🔧 调试和测试

### 启用调试模式

```javascript
// 启用调试模式
algorithmIntegrator.config.enableDebugMode = true;

// 查看执行历史
const history = algorithmIntegrator.getExecutionHistory();
console.log('执行历史:', history);
```

### 性能基准测试

```javascript
// 创建标准测试用例
const testRequest = new PathPlanningRequest({
    startPoint: { lng: 139.767, lat: 35.681, alt: 0 },
    endPoint: { lng: 139.777, lat: 35.691, alt: 0 },
    flightHeight: 100,
    safetyDistance: 20
});

// 测试算法性能
const startTime = Date.now();
const response = await algorithmManager.executeAlgorithm('MyCustom', testRequest);
const executionTime = Date.now() - startTime;

console.log(`算法执行时间: ${executionTime}ms`);
console.log(`路径质量评分: ${response.quality.efficiency}`);
```

## 📚 示例算法

系统提供了三个示例算法供参考：

1. **StraightLineAlgorithm**: 直线路径算法
2. **AStarAlgorithm**: A*寻路算法
3. **RRTAlgorithm**: 快速随机树算法

这些示例展示了如何实现不同复杂度的路径规划算法。

## 🤝 技术支持

如需技术支持或有任何问题，请联系开发团队。我们提供：

- 算法集成指导
- 性能优化建议
- 自定义功能开发
- 技术培训服务

---

**注意**: 本接口系统设计为高度可扩展和向后兼容。未来版本的更新不会破坏现有的算法实现。
