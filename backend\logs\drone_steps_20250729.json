[{"session_id": "20250729_010002", "step_number": 1, "timestamp": "2025-07-29T01:00:02.519104", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "dc6a62f6-643f-4b34-9c2a-b47fa0b00b61", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "dc6a62f6-643f-4b34-9c2a-b47fa0b00b61", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-990.5985724836994, 1173.1839189737886, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 101}}, "duration_ms": null}, {"session_id": "20250729_010002", "step_number": 2, "timestamp": "2025-07-29T01:00:02.528563", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "dc6a62f6-643f-4b34-9c2a-b47fa0b00b61", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "dc6a62f6-643f-4b34-9c2a-b47fa0b00b61", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-990.5985724836994, 1173.1839189737886, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_010002", "step_number": 3, "timestamp": "2025-07-29T01:00:04.979321", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2444.3225860595703}, {"session_id": "20250729_010002", "step_number": 4, "timestamp": "2025-07-29T01:00:04.980331", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2445.333242416382}, {"session_id": "20250729_010002", "step_number": 5, "timestamp": "2025-07-29T01:00:05.018822", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.97793388366699}, {"session_id": "20250729_010002", "step_number": 6, "timestamp": "2025-07-29T01:00:05.019939", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.095163345336914}, {"session_id": "20250729_010457", "step_number": 1, "timestamp": "2025-07-29T01:04:57.229012", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "06fc17c1-7de2-4527-9a7d-5ec335fc2a0e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "06fc17c1-7de2-4527-9a7d-5ec335fc2a0e", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-990.5985724836994, 1173.1839189737886, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 85}}, "duration_ms": null}, {"session_id": "20250729_010457", "step_number": 2, "timestamp": "2025-07-29T01:04:57.234525", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "06fc17c1-7de2-4527-9a7d-5ec335fc2a0e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "06fc17c1-7de2-4527-9a7d-5ec335fc2a0e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-990.5985724836994, 1173.1839189737886, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_010457", "step_number": 3, "timestamp": "2025-07-29T01:04:59.376469", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2135.927438735962}, {"session_id": "20250729_010457", "step_number": 4, "timestamp": "2025-07-29T01:04:59.377634", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2138.08012008667}, {"session_id": "20250729_010457", "step_number": 5, "timestamp": "2025-07-29T01:04:59.412060", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.92505645751953}, {"session_id": "20250729_010457", "step_number": 6, "timestamp": "2025-07-29T01:04:59.412570", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.43503379821777}, {"session_id": "20250729_010457", "step_number": 7, "timestamp": "2025-07-29T01:05:17.176935", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "890520fc-8738-4e4d-a19e-c0eaaceb043b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "890520fc-8738-4e4d-a19e-c0eaaceb043b", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-861.0256747213948, 1055.2267816371204, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 89}}, "duration_ms": null}, {"session_id": "20250729_010457", "step_number": 8, "timestamp": "2025-07-29T01:05:17.186201", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "890520fc-8738-4e4d-a19e-c0eaaceb043b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "890520fc-8738-4e4d-a19e-c0eaaceb043b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-861.0256747213948, 1055.2267816371204, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_010457", "step_number": 9, "timestamp": "2025-07-29T01:05:19.264171", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2067.4960613250732}, {"session_id": "20250729_010457", "step_number": 10, "timestamp": "2025-07-29T01:05:19.265199", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2068.5248374938965}, {"session_id": "20250729_010457", "step_number": 11, "timestamp": "2025-07-29T01:05:19.292252", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.027441024780273}, {"session_id": "20250729_010457", "step_number": 12, "timestamp": "2025-07-29T01:05:19.292936", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.711463928222656}, {"session_id": "20250729_021242", "step_number": 1, "timestamp": "2025-07-29T02:12:42.489773", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "73341606-37d9-4c6f-b222-f43f34e10329", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "73341606-37d9-4c6f-b222-f43f34e10329", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 1, "timestamp": "2025-07-29T02:15:09.026843", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "27517a40-26c1-4d90-9029-c937458f33f2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "27517a40-26c1-4d90-9029-c937458f33f2", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 2, "timestamp": "2025-07-29T02:15:09.030442", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "27517a40-26c1-4d90-9029-c937458f33f2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "27517a40-26c1-4d90-9029-c937458f33f2", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 3, "timestamp": "2025-07-29T02:15:11.604570", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2570.8463191986084}, {"session_id": "20250729_021509", "step_number": 4, "timestamp": "2025-07-29T02:15:11.606160", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2572.4360942840576}, {"session_id": "20250729_021509", "step_number": 5, "timestamp": "2025-07-29T02:15:11.640222", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.96041488647461}, {"session_id": "20250729_021509", "step_number": 6, "timestamp": "2025-07-29T02:15:11.642460", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.19773483276367}, {"session_id": "20250729_021509", "step_number": 7, "timestamp": "2025-07-29T02:19:22.183254", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d0227b32-098c-4ca9-82f9-2a32de059494", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d0227b32-098c-4ca9-82f9-2a32de059494", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 8, "timestamp": "2025-07-29T02:19:22.188716", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d0227b32-098c-4ca9-82f9-2a32de059494", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d0227b32-098c-4ca9-82f9-2a32de059494", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 9, "timestamp": "2025-07-29T02:19:24.240774", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2045.5446243286133}, {"session_id": "20250729_021509", "step_number": 10, "timestamp": "2025-07-29T02:19:24.242275", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2047.046422958374}, {"session_id": "20250729_021509", "step_number": 11, "timestamp": "2025-07-29T02:19:24.270160", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.437997817993164}, {"session_id": "20250729_021509", "step_number": 12, "timestamp": "2025-07-29T02:19:24.271921", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.199434280395508}, {"session_id": "20250729_021509", "step_number": 13, "timestamp": "2025-07-29T02:26:50.689600", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "040c0aa5-9db0-4aa2-b4f4-7688822683b1", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "040c0aa5-9db0-4aa2-b4f4-7688822683b1", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 14, "timestamp": "2025-07-29T02:26:50.693941", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "040c0aa5-9db0-4aa2-b4f4-7688822683b1", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "040c0aa5-9db0-4aa2-b4f4-7688822683b1", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 15, "timestamp": "2025-07-29T02:26:52.853861", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2156.1973094940186}, {"session_id": "20250729_021509", "step_number": 16, "timestamp": "2025-07-29T02:26:52.854880", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2157.2165489196777}, {"session_id": "20250729_021509", "step_number": 17, "timestamp": "2025-07-29T02:26:52.894386", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.49227523803711}, {"session_id": "20250729_021509", "step_number": 18, "timestamp": "2025-07-29T02:26:52.896410", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.51597213745117}, {"session_id": "20250729_021509", "step_number": 19, "timestamp": "2025-07-29T02:27:17.649927", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6e402819-0247-4484-af3d-3f0856137425", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6e402819-0247-4484-af3d-3f0856137425", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 20, "timestamp": "2025-07-29T02:27:17.652906", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6e402819-0247-4484-af3d-3f0856137425", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6e402819-0247-4484-af3d-3f0856137425", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 21, "timestamp": "2025-07-29T02:27:19.726722", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2071.8231201171875}, {"session_id": "20250729_021509", "step_number": 22, "timestamp": "2025-07-29T02:27:19.728235", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2073.336124420166}, {"session_id": "20250729_021509", "step_number": 23, "timestamp": "2025-07-29T02:27:19.769589", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.10517692565918}, {"session_id": "20250729_021509", "step_number": 24, "timestamp": "2025-07-29T02:27:19.771425", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 40.94076156616211}, {"session_id": "20250729_021509", "step_number": 25, "timestamp": "2025-07-29T02:28:02.917436", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "741f77e7-9573-43d1-a0c6-20d8947b71fc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "741f77e7-9573-43d1-a0c6-20d8947b71fc", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 26, "timestamp": "2025-07-29T02:28:02.921216", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "741f77e7-9573-43d1-a0c6-20d8947b71fc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "741f77e7-9573-43d1-a0c6-20d8947b71fc", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 27, "timestamp": "2025-07-29T02:28:05.180668", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2255.079984664917}, {"session_id": "20250729_021509", "step_number": 28, "timestamp": "2025-07-29T02:28:05.182186", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2256.598472595215}, {"session_id": "20250729_021509", "step_number": 29, "timestamp": "2025-07-29T02:28:05.215183", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.984567642211914}, {"session_id": "20250729_021509", "step_number": 30, "timestamp": "2025-07-29T02:28:05.216749", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.55073928833008}, {"session_id": "20250729_021509", "step_number": 31, "timestamp": "2025-07-29T02:28:25.292824", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ebf70ba9-9077-4306-847a-e57bb5c87b97", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ebf70ba9-9077-4306-847a-e57bb5c87b97", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 32, "timestamp": "2025-07-29T02:28:25.297970", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ebf70ba9-9077-4306-847a-e57bb5c87b97", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ebf70ba9-9077-4306-847a-e57bb5c87b97", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 33, "timestamp": "2025-07-29T02:28:27.776147", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2472.3024368286133}, {"session_id": "20250729_021509", "step_number": 34, "timestamp": "2025-07-29T02:28:27.777733", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2473.888158798218}, {"session_id": "20250729_021509", "step_number": 35, "timestamp": "2025-07-29T02:28:27.811355", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.03010559082031}, {"session_id": "20250729_021509", "step_number": 36, "timestamp": "2025-07-29T02:28:27.812539", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.21361541748047}, {"session_id": "20250729_021509", "step_number": 37, "timestamp": "2025-07-29T02:31:23.331248", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "2558e645-14d5-4cee-be6f-b27b487f2179", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "2558e645-14d5-4cee-be6f-b27b487f2179", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 38, "timestamp": "2025-07-29T02:31:23.334032", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "2558e645-14d5-4cee-be6f-b27b487f2179", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "2558e645-14d5-4cee-be6f-b27b487f2179", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_021509", "step_number": 39, "timestamp": "2025-07-29T02:31:25.103233", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1764.9052143096924}, {"session_id": "20250729_021509", "step_number": 40, "timestamp": "2025-07-29T02:31:25.105145", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1766.8178081512451}, {"session_id": "20250729_021509", "step_number": 41, "timestamp": "2025-07-29T02:31:25.139136", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.44280815124512}, {"session_id": "20250729_021509", "step_number": 42, "timestamp": "2025-07-29T02:31:25.141130", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.43717956542969}, {"session_id": "20250729_023432", "step_number": 1, "timestamp": "2025-07-29T02:34:32.429821", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "258e3c48-1516-4886-bc70-8f56c78aec8e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "258e3c48-1516-4886-bc70-8f56c78aec8e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 2, "timestamp": "2025-07-29T02:34:32.435588", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "258e3c48-1516-4886-bc70-8f56c78aec8e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "258e3c48-1516-4886-bc70-8f56c78aec8e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 3, "timestamp": "2025-07-29T02:34:34.589871", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2151.317358016968}, {"session_id": "20250729_023432", "step_number": 4, "timestamp": "2025-07-29T02:34:34.592730", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2154.1762351989746}, {"session_id": "20250729_023432", "step_number": 5, "timestamp": "2025-07-29T02:34:34.628182", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.33473205566406}, {"session_id": "20250729_023432", "step_number": 6, "timestamp": "2025-07-29T02:34:34.630272", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.42447090148926}, {"session_id": "20250729_023432", "step_number": 7, "timestamp": "2025-07-29T02:35:13.202936", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6771dbdd-0f46-447e-840d-7435f5f45339", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6771dbdd-0f46-447e-840d-7435f5f45339", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 8, "timestamp": "2025-07-29T02:35:13.208612", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6771dbdd-0f46-447e-840d-7435f5f45339", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6771dbdd-0f46-447e-840d-7435f5f45339", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 9, "timestamp": "2025-07-29T02:35:15.440741", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2227.1463871002197}, {"session_id": "20250729_023432", "step_number": 10, "timestamp": "2025-07-29T02:35:15.443438", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2229.8431396484375}, {"session_id": "20250729_023432", "step_number": 11, "timestamp": "2025-07-29T02:35:15.476357", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 30.811548233032227}, {"session_id": "20250729_023432", "step_number": 12, "timestamp": "2025-07-29T02:35:15.479628", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.08241271972656}, {"session_id": "20250729_023432", "step_number": 13, "timestamp": "2025-07-29T02:37:39.084502", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8531504f-b3c9-4d64-9274-f573303ea46a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8531504f-b3c9-4d64-9274-f573303ea46a", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 14, "timestamp": "2025-07-29T02:37:39.088545", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8531504f-b3c9-4d64-9274-f573303ea46a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8531504f-b3c9-4d64-9274-f573303ea46a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 15, "timestamp": "2025-07-29T02:37:41.298337", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2204.5364379882812}, {"session_id": "20250729_023432", "step_number": 16, "timestamp": "2025-07-29T02:37:41.300330", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2206.5296173095703}, {"session_id": "20250729_023432", "step_number": 17, "timestamp": "2025-07-29T02:37:41.349003", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 46.67973518371582}, {"session_id": "20250729_023432", "step_number": 18, "timestamp": "2025-07-29T02:37:41.351084", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 48.760414123535156}, {"session_id": "20250729_023432", "step_number": 19, "timestamp": "2025-07-29T02:38:29.503754", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "43e1fc0b-3d88-4805-881a-3ef84380a8ad", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "43e1fc0b-3d88-4805-881a-3ef84380a8ad", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 20, "timestamp": "2025-07-29T02:38:29.509197", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "43e1fc0b-3d88-4805-881a-3ef84380a8ad", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "43e1fc0b-3d88-4805-881a-3ef84380a8ad", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 21, "timestamp": "2025-07-29T02:38:32.802340", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3288.676977157593}, {"session_id": "20250729_023432", "step_number": 22, "timestamp": "2025-07-29T02:38:32.804596", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3290.9328937530518}, {"session_id": "20250729_023432", "step_number": 23, "timestamp": "2025-07-29T02:38:32.862278", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 54.69465255737305}, {"session_id": "20250729_023432", "step_number": 24, "timestamp": "2025-07-29T02:38:32.865459", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 57.8761100769043}, {"session_id": "20250729_023432", "step_number": 25, "timestamp": "2025-07-29T02:40:21.060907", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "40aef7ca-5600-44e8-bcd0-7240ead27ff8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "40aef7ca-5600-44e8-bcd0-7240ead27ff8", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 26, "timestamp": "2025-07-29T02:40:21.066078", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "40aef7ca-5600-44e8-bcd0-7240ead27ff8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "40aef7ca-5600-44e8-bcd0-7240ead27ff8", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 27, "timestamp": "2025-07-29T02:40:23.028842", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1959.106206893921}, {"session_id": "20250729_023432", "step_number": 28, "timestamp": "2025-07-29T02:40:23.030838", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1961.1015319824219}, {"session_id": "20250729_023432", "step_number": 29, "timestamp": "2025-07-29T02:40:23.066222", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.39274024963379}, {"session_id": "20250729_023432", "step_number": 30, "timestamp": "2025-07-29T02:40:23.068760", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.93094444274902}, {"session_id": "20250729_023432", "step_number": 31, "timestamp": "2025-07-29T02:45:54.223650", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "643500f7-7b48-4274-b4f7-80028f8fd931", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "643500f7-7b48-4274-b4f7-80028f8fd931", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 32, "timestamp": "2025-07-29T02:45:54.229030", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "643500f7-7b48-4274-b4f7-80028f8fd931", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "643500f7-7b48-4274-b4f7-80028f8fd931", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 33, "timestamp": "2025-07-29T02:45:56.511731", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2277.399778366089}, {"session_id": "20250729_023432", "step_number": 34, "timestamp": "2025-07-29T02:45:56.514342", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2280.01070022583}, {"session_id": "20250729_023432", "step_number": 35, "timestamp": "2025-07-29T02:45:56.551091", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.381534576416016}, {"session_id": "20250729_023432", "step_number": 36, "timestamp": "2025-07-29T02:45:56.553888", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.17913818359375}, {"session_id": "20250729_023432", "step_number": 37, "timestamp": "2025-07-29T02:51:58.571698", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fe8cceef-ad56-483e-bbed-6d660c11a45b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fe8cceef-ad56-483e-bbed-6d660c11a45b", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 38, "timestamp": "2025-07-29T02:51:58.579548", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fe8cceef-ad56-483e-bbed-6d660c11a45b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fe8cceef-ad56-483e-bbed-6d660c11a45b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 39, "timestamp": "2025-07-29T02:52:00.780552", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2195.8680152893066}, {"session_id": "20250729_023432", "step_number": 40, "timestamp": "2025-07-29T02:52:00.782722", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2198.0385780334473}, {"session_id": "20250729_023432", "step_number": 41, "timestamp": "2025-07-29T02:52:00.814662", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.245376586914062}, {"session_id": "20250729_023432", "step_number": 42, "timestamp": "2025-07-29T02:52:00.816675", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.258344650268555}, {"session_id": "20250729_023432", "step_number": 43, "timestamp": "2025-07-29T02:55:39.787339", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9af01d80-d473-4b24-9936-2894599f78c9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9af01d80-d473-4b24-9936-2894599f78c9", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 44, "timestamp": "2025-07-29T02:55:39.793920", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9af01d80-d473-4b24-9936-2894599f78c9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9af01d80-d473-4b24-9936-2894599f78c9", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 45, "timestamp": "2025-07-29T02:55:42.286900", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2487.6956939697266}, {"session_id": "20250729_023432", "step_number": 46, "timestamp": "2025-07-29T02:55:42.290062", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2490.8576011657715}, {"session_id": "20250729_023432", "step_number": 47, "timestamp": "2025-07-29T02:55:42.325474", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.202409744262695}, {"session_id": "20250729_023432", "step_number": 48, "timestamp": "2025-07-29T02:55:42.328263", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.99143028259277}, {"session_id": "20250729_023432", "step_number": 49, "timestamp": "2025-07-29T02:59:44.853554", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0f0041f4-83fc-4fa4-889b-025540c09407", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0f0041f4-83fc-4fa4-889b-025540c09407", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 50, "timestamp": "2025-07-29T02:59:44.859337", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0f0041f4-83fc-4fa4-889b-025540c09407", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0f0041f4-83fc-4fa4-889b-025540c09407", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 51, "timestamp": "2025-07-29T02:59:46.989246", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2124.6280670166016}, {"session_id": "20250729_023432", "step_number": 52, "timestamp": "2025-07-29T02:59:46.992611", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2127.993106842041}, {"session_id": "20250729_023432", "step_number": 53, "timestamp": "2025-07-29T02:59:47.024635", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.32572364807129}, {"session_id": "20250729_023432", "step_number": 54, "timestamp": "2025-07-29T02:59:47.027192", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.882524490356445}, {"session_id": "20250729_023432", "step_number": 55, "timestamp": "2025-07-29T03:03:01.573459", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e62e1ba3-d0b9-4649-b9fb-f6d999d35360", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e62e1ba3-d0b9-4649-b9fb-f6d999d35360", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 56, "timestamp": "2025-07-29T03:03:01.581706", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e62e1ba3-d0b9-4649-b9fb-f6d999d35360", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e62e1ba3-d0b9-4649-b9fb-f6d999d35360", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_023432", "step_number": 57, "timestamp": "2025-07-29T03:03:04.718779", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3126.5714168548584}, {"session_id": "20250729_023432", "step_number": 58, "timestamp": "2025-07-29T03:03:04.722755", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3130.5477619171143}, {"session_id": "20250729_023432", "step_number": 59, "timestamp": "2025-07-29T03:03:04.771817", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 44.370412826538086}, {"session_id": "20250729_023432", "step_number": 60, "timestamp": "2025-07-29T03:03:04.775041", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 47.59502410888672}, {"session_id": "20250729_031352", "step_number": 1, "timestamp": "2025-07-29T03:13:52.040388", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ae85fe73-16fa-4265-930b-e1d1158f5182", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ae85fe73-16fa-4265-930b-e1d1158f5182", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_031352", "step_number": 2, "timestamp": "2025-07-29T03:13:52.046304", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ae85fe73-16fa-4265-930b-e1d1158f5182", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ae85fe73-16fa-4265-930b-e1d1158f5182", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_031352", "step_number": 3, "timestamp": "2025-07-29T03:13:54.393555", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2341.270923614502}, {"session_id": "20250729_031352", "step_number": 4, "timestamp": "2025-07-29T03:13:54.397230", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2344.9459075927734}, {"session_id": "20250729_031352", "step_number": 5, "timestamp": "2025-07-29T03:13:54.434578", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.41245651245117}, {"session_id": "20250729_031352", "step_number": 6, "timestamp": "2025-07-29T03:13:54.439569", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.40327262878418}, {"session_id": "20250729_031535", "step_number": 1, "timestamp": "2025-07-29T03:15:35.274470", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0465dbce-bdfe-42a1-a07a-bf4c995b0d47", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0465dbce-bdfe-42a1-a07a-bf4c995b0d47", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_031535", "step_number": 2, "timestamp": "2025-07-29T03:15:35.281028", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0465dbce-bdfe-42a1-a07a-bf4c995b0d47", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0465dbce-bdfe-42a1-a07a-bf4c995b0d47", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_031535", "step_number": 3, "timestamp": "2025-07-29T03:15:37.431964", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2146.5988159179688}, {"session_id": "20250729_031535", "step_number": 4, "timestamp": "2025-07-29T03:15:37.435505", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2150.139808654785}, {"session_id": "20250729_031535", "step_number": 5, "timestamp": "2025-07-29T03:15:37.473713", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.73258018493652}, {"session_id": "20250729_031535", "step_number": 6, "timestamp": "2025-07-29T03:15:37.477106", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.12527656555176}, {"session_id": "20250729_032450", "step_number": 1, "timestamp": "2025-07-29T03:24:50.841986", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "061de892-75d6-4f6a-9a72-b7a4d0e7fe76", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "061de892-75d6-4f6a-9a72-b7a4d0e7fe76", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_032450", "step_number": 2, "timestamp": "2025-07-29T03:24:50.849346", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "061de892-75d6-4f6a-9a72-b7a4d0e7fe76", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "061de892-75d6-4f6a-9a72-b7a4d0e7fe76", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_032450", "step_number": 3, "timestamp": "2025-07-29T03:24:52.978125", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2124.4301795959473}, {"session_id": "20250729_032450", "step_number": 4, "timestamp": "2025-07-29T03:24:52.981963", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2128.267765045166}, {"session_id": "20250729_032450", "step_number": 5, "timestamp": "2025-07-29T03:24:53.018908", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.37669372558594}, {"session_id": "20250729_032450", "step_number": 6, "timestamp": "2025-07-29T03:24:53.024056", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.52438926696777}, {"session_id": "20250729_032450", "step_number": 7, "timestamp": "2025-07-29T03:26:51.788935", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b0eaefc8-72d0-4ceb-94b7-fb9936fbe180", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b0eaefc8-72d0-4ceb-94b7-fb9936fbe180", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_032450", "step_number": 8, "timestamp": "2025-07-29T03:26:51.795423", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b0eaefc8-72d0-4ceb-94b7-fb9936fbe180", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b0eaefc8-72d0-4ceb-94b7-fb9936fbe180", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_032450", "step_number": 9, "timestamp": "2025-07-29T03:26:54.393873", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2592.2834873199463}, {"session_id": "20250729_032450", "step_number": 10, "timestamp": "2025-07-29T03:26:54.398079", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2596.4884757995605}, {"session_id": "20250729_032450", "step_number": 11, "timestamp": "2025-07-29T03:26:54.444239", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 42.34123229980469}, {"session_id": "20250729_032450", "step_number": 12, "timestamp": "2025-07-29T03:26:54.448033", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 46.135663986206055}, {"session_id": "20250729_032450", "step_number": 13, "timestamp": "2025-07-29T03:29:23.629453", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "34eb0672-c14b-4b2c-b2ec-01cee3591250", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "34eb0672-c14b-4b2c-b2ec-01cee3591250", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_032450", "step_number": 14, "timestamp": "2025-07-29T03:29:23.636121", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "34eb0672-c14b-4b2c-b2ec-01cee3591250", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "34eb0672-c14b-4b2c-b2ec-01cee3591250", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_032450", "step_number": 15, "timestamp": "2025-07-29T03:29:25.674312", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2032.2377681732178}, {"session_id": "20250729_032450", "step_number": 16, "timestamp": "2025-07-29T03:29:25.678049", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2035.9745025634766}, {"session_id": "20250729_032450", "step_number": 17, "timestamp": "2025-07-29T03:29:25.712620", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 30.965089797973633}, {"session_id": "20250729_032450", "step_number": 18, "timestamp": "2025-07-29T03:29:25.716131", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.47604179382324}, {"session_id": "20250729_032450", "step_number": 19, "timestamp": "2025-07-29T03:33:48.179748", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a2d90de3-7271-41d6-a70a-85eb07b04962", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a2d90de3-7271-41d6-a70a-85eb07b04962", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_032450", "step_number": 20, "timestamp": "2025-07-29T03:33:48.184545", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a2d90de3-7271-41d6-a70a-85eb07b04962", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a2d90de3-7271-41d6-a70a-85eb07b04962", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_032450", "step_number": 21, "timestamp": "2025-07-29T03:33:50.072163", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1880.7246685028076}, {"session_id": "20250729_032450", "step_number": 22, "timestamp": "2025-07-29T03:33:50.076497", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1885.0584030151367}, {"session_id": "20250729_032450", "step_number": 23, "timestamp": "2025-07-29T03:33:50.110966", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 30.939340591430664}, {"session_id": "20250729_032450", "step_number": 24, "timestamp": "2025-07-29T03:33:50.114279", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.25288200378418}, {"session_id": "20250729_033548", "step_number": 1, "timestamp": "2025-07-29T03:35:48.387979", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6f81082f-8fd7-4f32-aaff-8471647739a5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6f81082f-8fd7-4f32-aaff-8471647739a5", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_033548", "step_number": 2, "timestamp": "2025-07-29T03:35:48.394514", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6f81082f-8fd7-4f32-aaff-8471647739a5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6f81082f-8fd7-4f32-aaff-8471647739a5", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_033548", "step_number": 3, "timestamp": "2025-07-29T03:35:50.641623", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2241.7736053466797}, {"session_id": "20250729_033548", "step_number": 4, "timestamp": "2025-07-29T03:35:50.646366", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2246.516704559326}, {"session_id": "20250729_033548", "step_number": 5, "timestamp": "2025-07-29T03:35:50.679483", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.870820999145508}, {"session_id": "20250729_033548", "step_number": 6, "timestamp": "2025-07-29T03:35:50.683478", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.86600112915039}, {"session_id": "20250729_034020", "step_number": 1, "timestamp": "2025-07-29T03:40:20.613225", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3d41a3be-d107-4157-865f-ff980045dc73", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3d41a3be-d107-4157-865f-ff980045dc73", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_034020", "step_number": 2, "timestamp": "2025-07-29T03:40:20.620345", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3d41a3be-d107-4157-865f-ff980045dc73", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3d41a3be-d107-4157-865f-ff980045dc73", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_034020", "step_number": 3, "timestamp": "2025-07-29T03:40:22.817056", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2191.8516159057617}, {"session_id": "20250729_034020", "step_number": 4, "timestamp": "2025-07-29T03:40:22.820645", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2195.4405307769775}, {"session_id": "20250729_034020", "step_number": 5, "timestamp": "2025-07-29T03:40:22.860558", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.39586067199707}, {"session_id": "20250729_034020", "step_number": 6, "timestamp": "2025-07-29T03:40:22.867045", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.88346862792969}, {"session_id": "20250729_034020", "step_number": 7, "timestamp": "2025-07-29T03:42:08.587376", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c72bf5aa-d503-4581-9bcd-136abcd2f61e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c72bf5aa-d503-4581-9bcd-136abcd2f61e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_034020", "step_number": 8, "timestamp": "2025-07-29T03:42:08.596106", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c72bf5aa-d503-4581-9bcd-136abcd2f61e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c72bf5aa-d503-4581-9bcd-136abcd2f61e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_034020", "step_number": 9, "timestamp": "2025-07-29T03:42:12.412768", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3809.253692626953}, {"session_id": "20250729_034020", "step_number": 10, "timestamp": "2025-07-29T03:42:12.420036", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3816.521406173706}, {"session_id": "20250729_034020", "step_number": 11, "timestamp": "2025-07-29T03:42:12.465928", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.66078567504883}, {"session_id": "20250729_034020", "step_number": 12, "timestamp": "2025-07-29T03:42:12.470589", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 46.32139205932617}, {"session_id": "20250729_034020", "step_number": 13, "timestamp": "2025-07-29T03:43:08.955740", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9b547baa-de46-4615-aee7-027f1015f5ca", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9b547baa-de46-4615-aee7-027f1015f5ca", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_034020", "step_number": 14, "timestamp": "2025-07-29T03:43:08.966250", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9b547baa-de46-4615-aee7-027f1015f5ca", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9b547baa-de46-4615-aee7-027f1015f5ca", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_034020", "step_number": 15, "timestamp": "2025-07-29T03:43:11.217921", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2239.478826522827}, {"session_id": "20250729_034020", "step_number": 16, "timestamp": "2025-07-29T03:43:11.222408", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2243.9663410186768}, {"session_id": "20250729_034020", "step_number": 17, "timestamp": "2025-07-29T03:43:11.269322", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 42.49429702758789}, {"session_id": "20250729_034020", "step_number": 18, "timestamp": "2025-07-29T03:43:11.273878", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 47.051191329956055}, {"session_id": "20250729_034020", "step_number": 19, "timestamp": "2025-07-29T03:46:48.395548", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3149b5eb-4496-4ec6-aa82-e9b5ec29ebef", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3149b5eb-4496-4ec6-aa82-e9b5ec29ebef", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_034020", "step_number": 20, "timestamp": "2025-07-29T03:46:48.402647", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3149b5eb-4496-4ec6-aa82-e9b5ec29ebef", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3149b5eb-4496-4ec6-aa82-e9b5ec29ebef", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_034020", "step_number": 21, "timestamp": "2025-07-29T03:46:51.896512", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3486.8667125701904}, {"session_id": "20250729_034020", "step_number": 22, "timestamp": "2025-07-29T03:46:51.901234", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3491.588592529297}, {"session_id": "20250729_034020", "step_number": 23, "timestamp": "2025-07-29T03:46:51.959047", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 53.51376533508301}, {"session_id": "20250729_034020", "step_number": 24, "timestamp": "2025-07-29T03:46:51.963493", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 57.95907974243164}, {"session_id": "20250729_034930", "step_number": 1, "timestamp": "2025-07-29T03:49:30.272080", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "49780584-9d9b-480d-8af5-853c16a33cca", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "49780584-9d9b-480d-8af5-853c16a33cca", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_034930", "step_number": 2, "timestamp": "2025-07-29T03:49:30.280064", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "49780584-9d9b-480d-8af5-853c16a33cca", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "49780584-9d9b-480d-8af5-853c16a33cca", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_034930", "step_number": 3, "timestamp": "2025-07-29T03:49:32.154036", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1867.9921627044678}, {"session_id": "20250729_034930", "step_number": 4, "timestamp": "2025-07-29T03:49:32.158631", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1872.5864887237549}, {"session_id": "20250729_034930", "step_number": 5, "timestamp": "2025-07-29T03:49:32.192061", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.32882308959961}, {"session_id": "20250729_034930", "step_number": 6, "timestamp": "2025-07-29T03:49:32.195844", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.1113338470459}, {"session_id": "20250729_034930", "step_number": 7, "timestamp": "2025-07-29T03:53:14.737785", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a9439806-a4d1-4a85-9fae-a31a51150b45", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a9439806-a4d1-4a85-9fae-a31a51150b45", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_034930", "step_number": 8, "timestamp": "2025-07-29T03:53:14.748104", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a9439806-a4d1-4a85-9fae-a31a51150b45", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a9439806-a4d1-4a85-9fae-a31a51150b45", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_034930", "step_number": 9, "timestamp": "2025-07-29T03:53:16.795448", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2039.7560596466064}, {"session_id": "20250729_034930", "step_number": 10, "timestamp": "2025-07-29T03:53:16.799847", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2044.1551208496094}, {"session_id": "20250729_034930", "step_number": 11, "timestamp": "2025-07-29T03:53:16.837635", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.50384330749512}, {"session_id": "20250729_034930", "step_number": 12, "timestamp": "2025-07-29T03:53:16.842898", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.76693344116211}, {"session_id": "20250729_035612", "step_number": 1, "timestamp": "2025-07-29T03:56:12.561983", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "cd1bb627-8281-4226-b40d-816240985691", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "cd1bb627-8281-4226-b40d-816240985691", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_035612", "step_number": 2, "timestamp": "2025-07-29T03:56:12.570173", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "cd1bb627-8281-4226-b40d-816240985691", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "cd1bb627-8281-4226-b40d-816240985691", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_035612", "step_number": 3, "timestamp": "2025-07-29T03:56:14.725347", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2147.624969482422}, {"session_id": "20250729_035612", "step_number": 4, "timestamp": "2025-07-29T03:56:14.729894", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2152.171850204468}, {"session_id": "20250729_035612", "step_number": 5, "timestamp": "2025-07-29T03:56:14.763012", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.243303298950195}, {"session_id": "20250729_035612", "step_number": 6, "timestamp": "2025-07-29T03:56:14.767307", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.53793716430664}, {"session_id": "20250729_035726", "step_number": 1, "timestamp": "2025-07-29T03:57:26.459157", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0d897296-0979-43d8-82ec-c9e49d7e796a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0d897296-0979-43d8-82ec-c9e49d7e796a", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_035726", "step_number": 2, "timestamp": "2025-07-29T03:57:26.468173", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0d897296-0979-43d8-82ec-c9e49d7e796a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0d897296-0979-43d8-82ec-c9e49d7e796a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_035726", "step_number": 3, "timestamp": "2025-07-29T03:57:29.025776", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2550.859212875366}, {"session_id": "20250729_035726", "step_number": 4, "timestamp": "2025-07-29T03:57:29.030754", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2555.837392807007}, {"session_id": "20250729_035726", "step_number": 5, "timestamp": "2025-07-29T03:57:29.085345", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.210548400878906}, {"session_id": "20250729_035726", "step_number": 6, "timestamp": "2025-07-29T03:57:29.090648", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 54.514169692993164}, {"session_id": "20250729_040117", "step_number": 1, "timestamp": "2025-07-29T04:01:17.901187", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a1afb30e-a1e0-488b-adbe-bde4727af74f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a1afb30e-a1e0-488b-adbe-bde4727af74f", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_040117", "step_number": 2, "timestamp": "2025-07-29T04:01:17.907531", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a1afb30e-a1e0-488b-adbe-bde4727af74f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a1afb30e-a1e0-488b-adbe-bde4727af74f", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_040117", "step_number": 3, "timestamp": "2025-07-29T04:01:20.092890", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2178.863286972046}, {"session_id": "20250729_040117", "step_number": 4, "timestamp": "2025-07-29T04:01:20.098609", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2184.581995010376}, {"session_id": "20250729_040117", "step_number": 5, "timestamp": "2025-07-29T04:01:20.129533", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.48305892944336}, {"session_id": "20250729_040117", "step_number": 6, "timestamp": "2025-07-29T04:01:20.134472", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.421422958374023}, {"session_id": "20250729_040117", "step_number": 7, "timestamp": "2025-07-29T04:02:25.597304", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f4c748dd-ccc5-4daa-8b44-2aab5b74371d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f4c748dd-ccc5-4daa-8b44-2aab5b74371d", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_040117", "step_number": 8, "timestamp": "2025-07-29T04:02:25.608799", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f4c748dd-ccc5-4daa-8b44-2aab5b74371d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f4c748dd-ccc5-4daa-8b44-2aab5b74371d", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_040117", "step_number": 9, "timestamp": "2025-07-29T04:02:26.992574", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1375.7705688476562}, {"session_id": "20250729_040117", "step_number": 10, "timestamp": "2025-07-29T04:02:26.997507", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1380.7039260864258}, {"session_id": "20250729_040117", "step_number": 11, "timestamp": "2025-07-29T04:02:27.026199", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 23.8802433013916}, {"session_id": "20250729_040117", "step_number": 12, "timestamp": "2025-07-29T04:02:27.031766", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.447555541992188}, {"session_id": "20250729_063824", "step_number": 1, "timestamp": "2025-07-29T06:38:24.285082", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "74e2207a-fb40-468c-a84c-020dc5940970", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "74e2207a-fb40-468c-a84c-020dc5940970", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_063824", "step_number": 2, "timestamp": "2025-07-29T06:38:24.302156", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "74e2207a-fb40-468c-a84c-020dc5940970", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "74e2207a-fb40-468c-a84c-020dc5940970", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_063824", "step_number": 3, "timestamp": "2025-07-29T06:38:26.549574", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2241.438388824463}, {"session_id": "20250729_063824", "step_number": 4, "timestamp": "2025-07-29T06:38:26.554726", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2246.5903759002686}, {"session_id": "20250729_063824", "step_number": 5, "timestamp": "2025-07-29T06:38:26.586075", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 27.354717254638672}, {"session_id": "20250729_063824", "step_number": 6, "timestamp": "2025-07-29T06:38:26.591904", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.18309783935547}, {"session_id": "20250729_064231", "step_number": 1, "timestamp": "2025-07-29T06:42:31.727286", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b9612fff-9161-49f3-b335-c0882710525d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b9612fff-9161-49f3-b335-c0882710525d", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_064231", "step_number": 2, "timestamp": "2025-07-29T06:42:31.734236", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b9612fff-9161-49f3-b335-c0882710525d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b9612fff-9161-49f3-b335-c0882710525d", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250729_064231", "step_number": 3, "timestamp": "2025-07-29T06:42:36.618064", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 4875.612258911133}, {"session_id": "20250729_064231", "step_number": 4, "timestamp": "2025-07-29T06:42:36.623048", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 4880.596399307251}, {"session_id": "20250729_064231", "step_number": 5, "timestamp": "2025-07-29T06:42:36.675649", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 47.6224422454834}, {"session_id": "20250729_064231", "step_number": 6, "timestamp": "2025-07-29T06:42:36.681405", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 53.37834358215332}, {"session_id": "20250729_064401", "step_number": 1, "timestamp": "2025-07-29T06:44:01.266359", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5296b0af-746d-494a-bace-99c14174a56d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5296b0af-746d-494a-bace-99c14174a56d", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_064401", "step_number": 2, "timestamp": "2025-07-29T06:44:01.274063", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5296b0af-746d-494a-bace-99c14174a56d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5296b0af-746d-494a-bace-99c14174a56d", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250729_064401", "step_number": 3, "timestamp": "2025-07-29T06:44:06.393068", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 5112.4937534332275}, {"session_id": "20250729_064401", "step_number": 4, "timestamp": "2025-07-29T06:44:06.398873", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 5118.298292160034}, {"session_id": "20250729_064401", "step_number": 5, "timestamp": "2025-07-29T06:44:06.461797", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 56.94293975830078}, {"session_id": "20250729_064401", "step_number": 6, "timestamp": "2025-07-29T06:44:06.467691", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 62.836408615112305}, {"session_id": "20250729_064630", "step_number": 1, "timestamp": "2025-07-29T06:46:30.307443", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7d023918-97b7-44c2-b9ff-b469548a678b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7d023918-97b7-44c2-b9ff-b469548a678b", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_064630", "step_number": 2, "timestamp": "2025-07-29T06:46:30.315950", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7d023918-97b7-44c2-b9ff-b469548a678b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7d023918-97b7-44c2-b9ff-b469548a678b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_064630", "step_number": 3, "timestamp": "2025-07-29T06:46:32.706134", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2383.084774017334}, {"session_id": "20250729_064630", "step_number": 4, "timestamp": "2025-07-29T06:46:32.711321", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2388.272285461426}, {"session_id": "20250729_064630", "step_number": 5, "timestamp": "2025-07-29T06:46:32.748091", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.02199935913086}, {"session_id": "20250729_064630", "step_number": 6, "timestamp": "2025-07-29T06:46:32.753346", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.2772216796875}, {"session_id": "20250729_064939", "step_number": 1, "timestamp": "2025-07-29T06:49:39.189690", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d6b11ead-d9eb-44eb-b564-fb47467dbea3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d6b11ead-d9eb-44eb-b564-fb47467dbea3", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_064939", "step_number": 2, "timestamp": "2025-07-29T06:49:39.197638", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d6b11ead-d9eb-44eb-b564-fb47467dbea3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d6b11ead-d9eb-44eb-b564-fb47467dbea3", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250729_064939", "step_number": 3, "timestamp": "2025-07-29T06:49:44.220779", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 5013.071298599243}, {"session_id": "20250729_064939", "step_number": 4, "timestamp": "2025-07-29T06:49:44.233190", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 5024.485349655151}, {"session_id": "20250729_064939", "step_number": 5, "timestamp": "2025-07-29T06:49:44.298094", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 59.30924415588379}, {"session_id": "20250729_064939", "step_number": 6, "timestamp": "2025-07-29T06:49:44.303865", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 65.0796890258789}, {"session_id": "20250729_065107", "step_number": 1, "timestamp": "2025-07-29T06:51:07.851339", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7c02500a-8b9a-4f28-a6cd-5ec62633f3d3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7c02500a-8b9a-4f28-a6cd-5ec62633f3d3", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 12}}, "duration_ms": null}, {"session_id": "20250729_065107", "step_number": 2, "timestamp": "2025-07-29T06:51:07.860815", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7c02500a-8b9a-4f28-a6cd-5ec62633f3d3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7c02500a-8b9a-4f28-a6cd-5ec62633f3d3", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_065107", "step_number": 3, "timestamp": "2025-07-29T06:51:09.146717", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1273.742437362671}, {"session_id": "20250729_065107", "step_number": 4, "timestamp": "2025-07-29T06:51:09.153108", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1280.1332473754883}, {"session_id": "20250729_065107", "step_number": 5, "timestamp": "2025-07-29T06:51:09.183779", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 24.62148666381836}, {"session_id": "20250729_065107", "step_number": 6, "timestamp": "2025-07-29T06:51:09.190318", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.160831451416016}, {"session_id": "20250729_065107", "step_number": 7, "timestamp": "2025-07-29T06:52:09.837321", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "666fab67-6741-467b-9089-f85f7ab6f6ad", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "666fab67-6741-467b-9089-f85f7ab6f6ad", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 12}}, "duration_ms": null}, {"session_id": "20250729_065107", "step_number": 8, "timestamp": "2025-07-29T06:52:09.845093", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "666fab67-6741-467b-9089-f85f7ab6f6ad", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "666fab67-6741-467b-9089-f85f7ab6f6ad", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_065107", "step_number": 9, "timestamp": "2025-07-29T06:52:11.538194", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1684.6530437469482}, {"session_id": "20250729_065107", "step_number": 10, "timestamp": "2025-07-29T06:52:11.544189", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1690.6483173370361}, {"session_id": "20250729_065107", "step_number": 11, "timestamp": "2025-07-29T06:52:11.580291", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.391382217407227}, {"session_id": "20250729_065107", "step_number": 12, "timestamp": "2025-07-29T06:52:11.587087", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.187503814697266}, {"session_id": "20250729_065622", "step_number": 1, "timestamp": "2025-07-29T06:56:22.635216", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bd284ebc-aa31-4e0b-a3fa-47430cd9186d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bd284ebc-aa31-4e0b-a3fa-47430cd9186d", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_065622", "step_number": 2, "timestamp": "2025-07-29T06:56:22.645203", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bd284ebc-aa31-4e0b-a3fa-47430cd9186d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bd284ebc-aa31-4e0b-a3fa-47430cd9186d", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250729_065622", "step_number": 3, "timestamp": "2025-07-29T06:56:28.109044", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 5449.4969844818115}, {"session_id": "20250729_065622", "step_number": 4, "timestamp": "2025-07-29T06:56:28.116766", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 5457.219123840332}, {"session_id": "20250729_065622", "step_number": 5, "timestamp": "2025-07-29T06:56:28.172480", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.748897552490234}, {"session_id": "20250729_065622", "step_number": 6, "timestamp": "2025-07-29T06:56:28.178922", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 56.19049072265625}, {"session_id": "20250729_152646", "step_number": 1, "timestamp": "2025-07-29T15:26:46.051416", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0703ed22-3bf9-4510-a22e-8d2020666f85", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0703ed22-3bf9-4510-a22e-8d2020666f85", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 103}}, "duration_ms": null}, {"session_id": "20250729_152646", "step_number": 2, "timestamp": "2025-07-29T15:26:46.079721", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0703ed22-3bf9-4510-a22e-8d2020666f85", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0703ed22-3bf9-4510-a22e-8d2020666f85", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_152646", "step_number": 3, "timestamp": "2025-07-29T15:26:49.039411", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2941.3223266601562}, {"session_id": "20250729_152646", "step_number": 4, "timestamp": "2025-07-29T15:26:49.045518", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2947.429418563843}, {"session_id": "20250729_152646", "step_number": 5, "timestamp": "2025-07-29T15:26:49.092709", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.61801528930664}, {"session_id": "20250729_152646", "step_number": 6, "timestamp": "2025-07-29T15:26:49.098371", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.82977294921875}, {"session_id": "20250729_154511", "step_number": 1, "timestamp": "2025-07-29T15:45:11.428235", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ccd29605-fc26-40ce-a835-53a986345164", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ccd29605-fc26-40ce-a835-53a986345164", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 89}}, "duration_ms": null}, {"session_id": "20250729_154511", "step_number": 2, "timestamp": "2025-07-29T15:45:11.440609", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ccd29605-fc26-40ce-a835-53a986345164", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ccd29605-fc26-40ce-a835-53a986345164", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_154511", "step_number": 3, "timestamp": "2025-07-29T15:45:14.222889", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2771.2972164154053}, {"session_id": "20250729_154511", "step_number": 4, "timestamp": "2025-07-29T15:45:14.229747", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2778.1550884246826}, {"session_id": "20250729_154511", "step_number": 5, "timestamp": "2025-07-29T15:45:14.275418", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.04438018798828}, {"session_id": "20250729_154511", "step_number": 6, "timestamp": "2025-07-29T15:45:14.280946", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 44.57259178161621}, {"session_id": "20250729_154602", "step_number": 1, "timestamp": "2025-07-29T15:46:02.546114", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1591d943-43d1-425e-a8ee-afebafebcb83", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1591d943-43d1-425e-a8ee-afebafebcb83", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 97}}, "duration_ms": null}, {"session_id": "20250729_154602", "step_number": 2, "timestamp": "2025-07-29T15:46:02.558671", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1591d943-43d1-425e-a8ee-afebafebcb83", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1591d943-43d1-425e-a8ee-afebafebcb83", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_154602", "step_number": 3, "timestamp": "2025-07-29T15:46:05.577271", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3002.476453781128}, {"session_id": "20250729_154602", "step_number": 4, "timestamp": "2025-07-29T15:46:05.584631", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3009.8373889923096}, {"session_id": "20250729_154602", "step_number": 5, "timestamp": "2025-07-29T15:46:05.632587", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.136741638183594}, {"session_id": "20250729_154602", "step_number": 6, "timestamp": "2025-07-29T15:46:05.640273", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 48.82359504699707}, {"session_id": "20250729_154602", "step_number": 7, "timestamp": "2025-07-29T15:48:03.026529", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "df87b32f-57d9-45f3-9c77-e69beeae1a4d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "df87b32f-57d9-45f3-9c77-e69beeae1a4d", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 74}}, "duration_ms": null}, {"session_id": "20250729_154602", "step_number": 8, "timestamp": "2025-07-29T15:48:03.042312", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "df87b32f-57d9-45f3-9c77-e69beeae1a4d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "df87b32f-57d9-45f3-9c77-e69beeae1a4d", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_154602", "step_number": 9, "timestamp": "2025-07-29T15:48:05.774164", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2718.7232971191406}, {"session_id": "20250729_154602", "step_number": 10, "timestamp": "2025-07-29T15:48:05.781653", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2726.2122631073}, {"session_id": "20250729_154602", "step_number": 11, "timestamp": "2025-07-29T15:48:05.831212", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.95880889892578}, {"session_id": "20250729_154602", "step_number": 12, "timestamp": "2025-07-29T15:48:05.838726", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.47328567504883}, {"session_id": "20250729_155517", "step_number": 1, "timestamp": "2025-07-29T15:55:17.732889", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "aedfaed5-643e-4df9-a9e3-4d9ed0c81628", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "aedfaed5-643e-4df9-a9e3-4d9ed0c81628", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 103}}, "duration_ms": null}, {"session_id": "20250729_155517", "step_number": 2, "timestamp": "2025-07-29T15:55:17.759315", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "aedfaed5-643e-4df9-a9e3-4d9ed0c81628", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "aedfaed5-643e-4df9-a9e3-4d9ed0c81628", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_155517", "step_number": 3, "timestamp": "2025-07-29T15:55:20.234553", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2459.3586921691895}, {"session_id": "20250729_155517", "step_number": 4, "timestamp": "2025-07-29T15:55:20.241632", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2466.438055038452}, {"session_id": "20250729_155517", "step_number": 5, "timestamp": "2025-07-29T15:55:20.286861", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.97745704650879}, {"session_id": "20250729_155517", "step_number": 6, "timestamp": "2025-07-29T15:55:20.293418", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 44.53396797180176}, {"session_id": "20250729_160913", "step_number": 1, "timestamp": "2025-07-29T16:09:13.768742", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "89cfae74-2b85-46a2-aeea-a5e8a8adcc58", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "89cfae74-2b85-46a2-aeea-a5e8a8adcc58", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 65}}, "duration_ms": null}, {"session_id": "20250729_160913", "step_number": 2, "timestamp": "2025-07-29T16:09:13.790535", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "89cfae74-2b85-46a2-aeea-a5e8a8adcc58", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "89cfae74-2b85-46a2-aeea-a5e8a8adcc58", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_160913", "step_number": 3, "timestamp": "2025-07-29T16:09:16.307235", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2490.8812046051025}, {"session_id": "20250729_160913", "step_number": 4, "timestamp": "2025-07-29T16:09:16.313566", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2497.2128868103027}, {"session_id": "20250729_160913", "step_number": 5, "timestamp": "2025-07-29T16:09:16.357202", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.46738624572754}, {"session_id": "20250729_160913", "step_number": 6, "timestamp": "2025-07-29T16:09:16.365228", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 43.49231719970703}, {"session_id": "20250729_162206", "step_number": 1, "timestamp": "2025-07-29T16:22:06.957876", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7781938d-a7f6-4ccd-bb2a-af23d7e97349", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7781938d-a7f6-4ccd-bb2a-af23d7e97349", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 63}}, "duration_ms": null}, {"session_id": "20250729_162206", "step_number": 2, "timestamp": "2025-07-29T16:22:06.973536", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7781938d-a7f6-4ccd-bb2a-af23d7e97349", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7781938d-a7f6-4ccd-bb2a-af23d7e97349", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_162206", "step_number": 3, "timestamp": "2025-07-29T16:22:09.476621", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2489.600419998169}, {"session_id": "20250729_162206", "step_number": 4, "timestamp": "2025-07-29T16:22:09.484039", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2498.0156421661377}, {"session_id": "20250729_162206", "step_number": 5, "timestamp": "2025-07-29T16:22:09.524043", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.886505126953125}, {"session_id": "20250729_162206", "step_number": 6, "timestamp": "2025-07-29T16:22:09.532264", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.108131408691406}, {"session_id": "20250729_162206", "step_number": 7, "timestamp": "2025-07-29T16:24:01.751052", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d2bf2c48-75ca-4876-86db-f0ecff66f887", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d2bf2c48-75ca-4876-86db-f0ecff66f887", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_162206", "step_number": 8, "timestamp": "2025-07-29T16:24:01.761052", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d2bf2c48-75ca-4876-86db-f0ecff66f887", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d2bf2c48-75ca-4876-86db-f0ecff66f887", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_162206", "step_number": 9, "timestamp": "2025-07-29T16:24:04.266370", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2495.6130981445312}, {"session_id": "20250729_162206", "step_number": 10, "timestamp": "2025-07-29T16:24:04.273206", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2502.448558807373}, {"session_id": "20250729_162206", "step_number": 11, "timestamp": "2025-07-29T16:24:04.317822", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.63937950134277}, {"session_id": "20250729_162206", "step_number": 12, "timestamp": "2025-07-29T16:24:04.324308", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 44.12579536437988}, {"session_id": "20250729_164519", "step_number": 1, "timestamp": "2025-07-29T16:45:19.142016", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "97fb1a3c-183e-43a5-902e-858358c31e3a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "97fb1a3c-183e-43a5-902e-858358c31e3a", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_164519", "step_number": 2, "timestamp": "2025-07-29T16:45:19.152116", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "97fb1a3c-183e-43a5-902e-858358c31e3a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "97fb1a3c-183e-43a5-902e-858358c31e3a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_164519", "step_number": 3, "timestamp": "2025-07-29T16:45:22.054322", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2892.260789871216}, {"session_id": "20250729_164519", "step_number": 4, "timestamp": "2025-07-29T16:45:22.062067", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2900.0062942504883}, {"session_id": "20250729_164519", "step_number": 5, "timestamp": "2025-07-29T16:45:22.116284", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.31296348571777}, {"session_id": "20250729_164519", "step_number": 6, "timestamp": "2025-07-29T16:45:22.122849", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 40.8780574798584}, {"session_id": "20250729_164519", "step_number": 7, "timestamp": "2025-07-29T16:51:12.681493", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f335f2b8-d943-47b1-be5c-e667ccae62d6", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f335f2b8-d943-47b1-be5c-e667ccae62d6", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 91}}, "duration_ms": null}, {"session_id": "20250729_164519", "step_number": 8, "timestamp": "2025-07-29T16:51:12.695413", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f335f2b8-d943-47b1-be5c-e667ccae62d6", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f335f2b8-d943-47b1-be5c-e667ccae62d6", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_164519", "step_number": 9, "timestamp": "2025-07-29T16:51:22.137145", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 9429.071426391602}, {"session_id": "20250729_164519", "step_number": 10, "timestamp": "2025-07-29T16:51:22.144365", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 9436.292171478271}, {"session_id": "20250729_164519", "step_number": 11, "timestamp": "2025-07-29T16:51:22.247865", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 96.11725807189941}, {"session_id": "20250729_164519", "step_number": 12, "timestamp": "2025-07-29T16:51:22.255432", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 103.6839485168457}, {"session_id": "20250729_165844", "step_number": 1, "timestamp": "2025-07-29T16:58:44.427426", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ea2f8d3a-87b6-49db-8980-ff29e115b7e7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ea2f8d3a-87b6-49db-8980-ff29e115b7e7", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 66}}, "duration_ms": null}, {"session_id": "20250729_165844", "step_number": 2, "timestamp": "2025-07-29T16:58:44.441139", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ea2f8d3a-87b6-49db-8980-ff29e115b7e7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ea2f8d3a-87b6-49db-8980-ff29e115b7e7", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_165844", "step_number": 3, "timestamp": "2025-07-29T16:58:55.008877", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 10555.173397064209}, {"session_id": "20250729_165844", "step_number": 4, "timestamp": "2025-07-29T16:58:55.016857", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 10563.153266906738}, {"session_id": "20250729_165844", "step_number": 5, "timestamp": "2025-07-29T16:58:55.112837", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 88.53459358215332}, {"session_id": "20250729_165844", "step_number": 6, "timestamp": "2025-07-29T16:58:55.118945", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 94.64287757873535}, {"session_id": "20250729_170621", "step_number": 1, "timestamp": "2025-07-29T17:06:21.392964", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fc28c9e6-663c-46cb-bd07-1143a9c4e83c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fc28c9e6-663c-46cb-bd07-1143a9c4e83c", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_170621", "step_number": 2, "timestamp": "2025-07-29T17:06:21.402970", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fc28c9e6-663c-46cb-bd07-1143a9c4e83c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fc28c9e6-663c-46cb-bd07-1143a9c4e83c", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_170621", "step_number": 3, "timestamp": "2025-07-29T17:06:23.494126", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2081.422805786133}, {"session_id": "20250729_170621", "step_number": 4, "timestamp": "2025-07-29T17:06:23.501953", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2089.250326156616}, {"session_id": "20250729_170621", "step_number": 5, "timestamp": "2025-07-29T17:06:23.540995", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.708478927612305}, {"session_id": "20250729_170621", "step_number": 6, "timestamp": "2025-07-29T17:06:23.549167", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.88075256347656}, {"session_id": "20250729_172443", "step_number": 1, "timestamp": "2025-07-29T17:24:43.713794", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0bde1a85-e87f-433b-96a9-07b4f9f9a03c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0bde1a85-e87f-433b-96a9-07b4f9f9a03c", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_172443", "step_number": 2, "timestamp": "2025-07-29T17:24:43.725170", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0bde1a85-e87f-433b-96a9-07b4f9f9a03c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0bde1a85-e87f-433b-96a9-07b4f9f9a03c", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_172443", "step_number": 3, "timestamp": "2025-07-29T17:24:46.831568", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3096.397638320923}, {"session_id": "20250729_172443", "step_number": 4, "timestamp": "2025-07-29T17:24:46.839987", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3104.816436767578}, {"session_id": "20250729_172443", "step_number": 5, "timestamp": "2025-07-29T17:24:46.893851", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.43009948730469}, {"session_id": "20250729_172443", "step_number": 6, "timestamp": "2025-07-29T17:24:46.902342", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.92018508911133}, {"session_id": "20250729_173400", "step_number": 1, "timestamp": "2025-07-29T17:34:00.620571", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7ab4ccb5-558c-4162-ad5d-4d322e9a1f42", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7ab4ccb5-558c-4162-ad5d-4d322e9a1f42", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 72}}, "duration_ms": null}, {"session_id": "20250729_173400", "step_number": 2, "timestamp": "2025-07-29T17:34:00.648825", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7ab4ccb5-558c-4162-ad5d-4d322e9a1f42", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7ab4ccb5-558c-4162-ad5d-4d322e9a1f42", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_173400", "step_number": 3, "timestamp": "2025-07-29T17:34:04.149923", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3480.901002883911}, {"session_id": "20250729_173400", "step_number": 4, "timestamp": "2025-07-29T17:34:04.158607", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3489.5851612091064}, {"session_id": "20250729_173400", "step_number": 5, "timestamp": "2025-07-29T17:34:04.200449", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.91265869140625}, {"session_id": "20250729_173400", "step_number": 6, "timestamp": "2025-07-29T17:34:04.208621", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 42.08540916442871}, {"session_id": "20250729_173400", "step_number": 7, "timestamp": "2025-07-29T17:36:15.124385", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "77830507-742f-4f61-ac78-5af85278ae9a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "77830507-742f-4f61-ac78-5af85278ae9a", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 73}}, "duration_ms": null}, {"session_id": "20250729_173400", "step_number": 8, "timestamp": "2025-07-29T17:36:15.145694", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "77830507-742f-4f61-ac78-5af85278ae9a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "77830507-742f-4f61-ac78-5af85278ae9a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_173400", "step_number": 9, "timestamp": "2025-07-29T17:36:18.140184", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2977.9303073883057}, {"session_id": "20250729_173400", "step_number": 10, "timestamp": "2025-07-29T17:36:18.148109", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2985.8548641204834}, {"session_id": "20250729_173400", "step_number": 11, "timestamp": "2025-07-29T17:36:18.213890", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 56.42294883728027}, {"session_id": "20250729_173400", "step_number": 12, "timestamp": "2025-07-29T17:36:18.222444", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 64.97716903686523}, {"session_id": "20250729_174811", "step_number": 1, "timestamp": "2025-07-29T17:48:11.123161", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "74e01a88-26b5-49ba-b632-b51eeab94b60", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "74e01a88-26b5-49ba-b632-b51eeab94b60", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 100}}, "duration_ms": null}, {"session_id": "20250729_174811", "step_number": 2, "timestamp": "2025-07-29T17:48:11.149447", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "74e01a88-26b5-49ba-b632-b51eeab94b60", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "74e01a88-26b5-49ba-b632-b51eeab94b60", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_174811", "step_number": 3, "timestamp": "2025-07-29T17:48:14.720454", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3550.764560699463}, {"session_id": "20250729_174811", "step_number": 4, "timestamp": "2025-07-29T17:48:14.728643", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3558.9540004730225}, {"session_id": "20250729_174811", "step_number": 5, "timestamp": "2025-07-29T17:48:14.770882", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.70015525817871}, {"session_id": "20250729_174811", "step_number": 6, "timestamp": "2025-07-29T17:48:14.779443", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 43.261051177978516}, {"session_id": "20250729_174913", "step_number": 1, "timestamp": "2025-07-29T17:49:13.068238", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "33d73fc9-0e41-41a3-b3dd-5df7c8afe289", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "33d73fc9-0e41-41a3-b3dd-5df7c8afe289", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 69}}, "duration_ms": null}, {"session_id": "20250729_174913", "step_number": 2, "timestamp": "2025-07-29T17:49:13.084819", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "33d73fc9-0e41-41a3-b3dd-5df7c8afe289", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "33d73fc9-0e41-41a3-b3dd-5df7c8afe289", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_174913", "step_number": 3, "timestamp": "2025-07-29T17:49:16.304719", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3202.745199203491}, {"session_id": "20250729_174913", "step_number": 4, "timestamp": "2025-07-29T17:49:16.313403", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3211.428642272949}, {"session_id": "20250729_174913", "step_number": 5, "timestamp": "2025-07-29T17:49:16.357074", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.91783142089844}, {"session_id": "20250729_174913", "step_number": 6, "timestamp": "2025-07-29T17:49:16.366641", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 44.484615325927734}, {"session_id": "20250729_175022", "step_number": 1, "timestamp": "2025-07-29T17:50:22.661596", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "14327896-af99-42ff-8b79-37e16cfd05dd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "14327896-af99-42ff-8b79-37e16cfd05dd", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 95}}, "duration_ms": null}, {"session_id": "20250729_175022", "step_number": 2, "timestamp": "2025-07-29T17:50:22.686648", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "14327896-af99-42ff-8b79-37e16cfd05dd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "14327896-af99-42ff-8b79-37e16cfd05dd", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_175022", "step_number": 3, "timestamp": "2025-07-29T17:50:30.779070", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 8076.040267944336}, {"session_id": "20250729_175022", "step_number": 4, "timestamp": "2025-07-29T17:50:30.787622", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 8084.592580795288}, {"session_id": "20250729_175022", "step_number": 5, "timestamp": "2025-07-29T17:50:30.879584", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 84.09523963928223}, {"session_id": "20250729_175022", "step_number": 6, "timestamp": "2025-07-29T17:50:30.888719", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 93.2307243347168}, {"session_id": "20250729_175242", "step_number": 1, "timestamp": "2025-07-29T17:52:42.328298", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6d5d46fc-e0a9-4ac4-a062-ddb0b31a9088", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6d5d46fc-e0a9-4ac4-a062-ddb0b31a9088", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 65}}, "duration_ms": null}, {"session_id": "20250729_175242", "step_number": 2, "timestamp": "2025-07-29T17:52:42.353244", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6d5d46fc-e0a9-4ac4-a062-ddb0b31a9088", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6d5d46fc-e0a9-4ac4-a062-ddb0b31a9088", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_175242", "step_number": 3, "timestamp": "2025-07-29T17:52:47.875736", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 5506.11138343811}, {"session_id": "20250729_175242", "step_number": 4, "timestamp": "2025-07-29T17:52:47.883529", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 5513.904333114624}, {"session_id": "20250729_175242", "step_number": 5, "timestamp": "2025-07-29T17:52:47.946074", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 53.52425575256348}, {"session_id": "20250729_175242", "step_number": 6, "timestamp": "2025-07-29T17:52:47.956639", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 64.08905982971191}, {"session_id": "20250729_175755", "step_number": 1, "timestamp": "2025-07-29T17:57:55.767517", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f2292a00-73f7-4a54-b4cc-62687f8d710a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f2292a00-73f7-4a54-b4cc-62687f8d710a", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 87}}, "duration_ms": null}, {"session_id": "20250729_175755", "step_number": 2, "timestamp": "2025-07-29T17:57:55.787502", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f2292a00-73f7-4a54-b4cc-62687f8d710a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f2292a00-73f7-4a54-b4cc-62687f8d710a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_175755", "step_number": 3, "timestamp": "2025-07-29T17:58:01.158455", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 5342.338085174561}, {"session_id": "20250729_175755", "step_number": 4, "timestamp": "2025-07-29T17:58:01.167046", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 5350.929021835327}, {"session_id": "20250729_175755", "step_number": 5, "timestamp": "2025-07-29T17:58:01.229849", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 55.028438568115234}, {"session_id": "20250729_175755", "step_number": 6, "timestamp": "2025-07-29T17:58:01.238613", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 63.79222869873047}, {"session_id": "20250729_182202", "step_number": 1, "timestamp": "2025-07-29T18:22:02.591631", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "eed805f4-8c15-4aff-a756-a1895c597fda", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "eed805f4-8c15-4aff-a756-a1895c597fda", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 66}}, "duration_ms": null}, {"session_id": "20250729_182202", "step_number": 2, "timestamp": "2025-07-29T18:22:02.610124", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "eed805f4-8c15-4aff-a756-a1895c597fda", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "eed805f4-8c15-4aff-a756-a1895c597fda", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_182202", "step_number": 3, "timestamp": "2025-07-29T18:22:05.446940", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2813.74192237854}, {"session_id": "20250729_182202", "step_number": 4, "timestamp": "2025-07-29T18:22:05.455360", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2823.160409927368}, {"session_id": "20250729_182202", "step_number": 5, "timestamp": "2025-07-29T18:22:05.498838", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.722328186035156}, {"session_id": "20250729_182202", "step_number": 6, "timestamp": "2025-07-29T18:22:05.508461", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 44.34514045715332}, {"session_id": "20250729_183134", "step_number": 1, "timestamp": "2025-07-29T18:31:34.841351", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9ced210f-2676-4940-90b3-55bf2cdc0093", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9ced210f-2676-4940-90b3-55bf2cdc0093", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 67}}, "duration_ms": null}, {"session_id": "20250729_183134", "step_number": 2, "timestamp": "2025-07-29T18:31:34.866240", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9ced210f-2676-4940-90b3-55bf2cdc0093", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9ced210f-2676-4940-90b3-55bf2cdc0093", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_183134", "step_number": 3, "timestamp": "2025-07-29T18:31:38.045470", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3154.3188095092773}, {"session_id": "20250729_183134", "step_number": 4, "timestamp": "2025-07-29T18:31:38.054958", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3163.806676864624}, {"session_id": "20250729_183134", "step_number": 5, "timestamp": "2025-07-29T18:31:38.102033", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.33253479003906}, {"session_id": "20250729_183134", "step_number": 6, "timestamp": "2025-07-29T18:31:38.111520", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 46.81968688964844}, {"session_id": "20250729_184558", "step_number": 1, "timestamp": "2025-07-29T18:45:58.977215", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0d7518d4-0f28-4e3b-b04a-c8a542f9fc8e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0d7518d4-0f28-4e3b-b04a-c8a542f9fc8e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_184558", "step_number": 2, "timestamp": "2025-07-29T18:45:58.988367", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0d7518d4-0f28-4e3b-b04a-c8a542f9fc8e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0d7518d4-0f28-4e3b-b04a-c8a542f9fc8e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_184558", "step_number": 3, "timestamp": "2025-07-29T18:46:01.974486", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2975.0382900238037}, {"session_id": "20250729_184558", "step_number": 4, "timestamp": "2025-07-29T18:46:01.985174", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2985.727071762085}, {"session_id": "20250729_184558", "step_number": 5, "timestamp": "2025-07-29T18:46:02.037480", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 44.04807090759277}, {"session_id": "20250729_184558", "step_number": 6, "timestamp": "2025-07-29T18:46:02.055345", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 60.91570854187012}, {"session_id": "20250729_185600", "step_number": 1, "timestamp": "2025-07-29T18:56:00.670332", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "26aa9039-9c03-4d2e-bb8f-2bdfe3e19fee", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "26aa9039-9c03-4d2e-bb8f-2bdfe3e19fee", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30, "buildings_count": 13}}, "duration_ms": null}, {"session_id": "20250729_185600", "step_number": 2, "timestamp": "2025-07-29T18:56:00.689560", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "26aa9039-9c03-4d2e-bb8f-2bdfe3e19fee", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "26aa9039-9c03-4d2e-bb8f-2bdfe3e19fee", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_185600", "step_number": 3, "timestamp": "2025-07-29T18:56:05.373518", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 4672.395706176758}, {"session_id": "20250729_185600", "step_number": 4, "timestamp": "2025-07-29T18:56:05.382571", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 4680.460691452026}, {"session_id": "20250729_185600", "step_number": 5, "timestamp": "2025-07-29T18:56:05.440764", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 48.68340492248535}, {"session_id": "20250729_185600", "step_number": 6, "timestamp": "2025-07-29T18:56:05.449752", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 57.671546936035156}, {"session_id": "20250729_185600", "step_number": 7, "timestamp": "2025-07-29T18:58:37.604405", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b15478a1-d82f-472c-993f-f1d9e350a199", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b15478a1-d82f-472c-993f-f1d9e350a199", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30, "buildings_count": 90}}, "duration_ms": null}, {"session_id": "20250729_185600", "step_number": 8, "timestamp": "2025-07-29T18:58:37.620194", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b15478a1-d82f-472c-993f-f1d9e350a199", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b15478a1-d82f-472c-993f-f1d9e350a199", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_185600", "step_number": 9, "timestamp": "2025-07-29T18:58:44.048231", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 6412.581920623779}, {"session_id": "20250729_185600", "step_number": 10, "timestamp": "2025-07-29T18:58:44.057953", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 6422.304630279541}, {"session_id": "20250729_185600", "step_number": 11, "timestamp": "2025-07-29T18:58:44.157169", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 88.81902694702148}, {"session_id": "20250729_185600", "step_number": 12, "timestamp": "2025-07-29T18:58:44.169413", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 101.06301307678223}, {"session_id": "20250729_185600", "step_number": 13, "timestamp": "2025-07-29T18:58:59.298660", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0b040fca-d033-473c-b08c-c31ebc109900", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0b040fca-d033-473c-b08c-c31ebc109900", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 90}}, "duration_ms": null}, {"session_id": "20250729_185600", "step_number": 14, "timestamp": "2025-07-29T18:58:59.313655", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0b040fca-d033-473c-b08c-c31ebc109900", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0b040fca-d033-473c-b08c-c31ebc109900", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_185600", "step_number": 15, "timestamp": "2025-07-29T18:59:05.538190", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 6206.562519073486}, {"session_id": "20250729_185600", "step_number": 16, "timestamp": "2025-07-29T18:59:05.548292", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 6216.665267944336}, {"session_id": "20250729_185600", "step_number": 17, "timestamp": "2025-07-29T18:59:05.617232", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 57.906150817871094}, {"session_id": "20250729_185600", "step_number": 18, "timestamp": "2025-07-29T18:59:05.627384", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 68.05825233459473}, {"session_id": "20250729_185600", "step_number": 19, "timestamp": "2025-07-29T19:02:50.307722", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6dd3f85b-c275-4aed-8fd9-11b910107bb2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6dd3f85b-c275-4aed-8fd9-11b910107bb2", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_185600", "step_number": 20, "timestamp": "2025-07-29T19:02:50.324696", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6dd3f85b-c275-4aed-8fd9-11b910107bb2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6dd3f85b-c275-4aed-8fd9-11b910107bb2", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_185600", "step_number": 21, "timestamp": "2025-07-29T19:02:50.847716", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 510.55002212524414}, {"session_id": "20250729_185600", "step_number": 22, "timestamp": "2025-07-29T19:02:50.863468", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 526.3028144836426}, {"session_id": "20250729_185600", "step_number": 23, "timestamp": "2025-07-29T19:02:50.911116", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.51724624633789}, {"session_id": "20250729_185600", "step_number": 24, "timestamp": "2025-07-29T19:02:50.927650", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.050859451293945}, {"session_id": "20250729_190641", "step_number": 1, "timestamp": "2025-07-29T19:06:41.086162", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "58bc6b1d-7750-473f-a28c-d054ebf0269a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "58bc6b1d-7750-473f-a28c-d054ebf0269a", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_190641", "step_number": 2, "timestamp": "2025-07-29T19:06:41.099626", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "58bc6b1d-7750-473f-a28c-d054ebf0269a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "58bc6b1d-7750-473f-a28c-d054ebf0269a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_190641", "step_number": 3, "timestamp": "2025-07-29T19:06:41.519678", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 406.46815299987793}, {"session_id": "20250729_190641", "step_number": 4, "timestamp": "2025-07-29T19:06:41.530642", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 417.4323081970215}, {"session_id": "20250729_190641", "step_number": 5, "timestamp": "2025-07-29T19:06:41.560743", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.8530216217041}, {"session_id": "20250729_190641", "step_number": 6, "timestamp": "2025-07-29T19:06:41.570529", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 27.639150619506836}, {"session_id": "20250729_190747", "step_number": 1, "timestamp": "2025-07-29T19:07:47.365757", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c84b6c76-f144-4423-9948-80cc71b8d1d7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c84b6c76-f144-4423-9948-80cc71b8d1d7", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_190747", "step_number": 2, "timestamp": "2025-07-29T19:07:47.375876", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c84b6c76-f144-4423-9948-80cc71b8d1d7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c84b6c76-f144-4423-9948-80cc71b8d1d7", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_190747", "step_number": 3, "timestamp": "2025-07-29T19:07:47.826422", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 441.0290718078613}, {"session_id": "20250729_190747", "step_number": 4, "timestamp": "2025-07-29T19:07:47.842554", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 457.16142654418945}, {"session_id": "20250729_190747", "step_number": 5, "timestamp": "2025-07-29T19:07:47.868766", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.733407974243164}, {"session_id": "20250729_190747", "step_number": 6, "timestamp": "2025-07-29T19:07:47.878777", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.744365692138672}, {"session_id": "20250729_190953", "step_number": 1, "timestamp": "2025-07-29T19:09:53.984180", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "938477b2-d83b-458b-87da-4ee4aab34d79", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "938477b2-d83b-458b-87da-4ee4aab34d79", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_190953", "step_number": 2, "timestamp": "2025-07-29T19:09:53.995730", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "938477b2-d83b-458b-87da-4ee4aab34d79", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "938477b2-d83b-458b-87da-4ee4aab34d79", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_190953", "step_number": 3, "timestamp": "2025-07-29T19:09:54.413692", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 397.90821075439453}, {"session_id": "20250729_190953", "step_number": 4, "timestamp": "2025-07-29T19:09:54.425200", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 409.41572189331055}, {"session_id": "20250729_190953", "step_number": 5, "timestamp": "2025-07-29T19:09:54.452925", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.193078994750977}, {"session_id": "20250729_190953", "step_number": 6, "timestamp": "2025-07-29T19:09:54.462372", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.639699935913086}, {"session_id": "20250729_191047", "step_number": 1, "timestamp": "2025-07-29T19:10:47.534678", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e8de3c49-6bb5-452a-871a-a5445530dbdd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e8de3c49-6bb5-452a-871a-a5445530dbdd", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_191047", "step_number": 2, "timestamp": "2025-07-29T19:10:47.547471", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e8de3c49-6bb5-452a-871a-a5445530dbdd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e8de3c49-6bb5-452a-871a-a5445530dbdd", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_191047", "step_number": 3, "timestamp": "2025-07-29T19:10:47.972543", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 411.0236167907715}, {"session_id": "20250729_191047", "step_number": 4, "timestamp": "2025-07-29T19:10:47.984003", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 422.4832057952881}, {"session_id": "20250729_191047", "step_number": 5, "timestamp": "2025-07-29T19:10:48.017939", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.982192993164062}, {"session_id": "20250729_191047", "step_number": 6, "timestamp": "2025-07-29T19:10:48.028690", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.732248306274414}, {"session_id": "20250729_191154", "step_number": 1, "timestamp": "2025-07-29T19:11:54.901911", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b181a958-58e5-47cc-8b08-804966659df2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b181a958-58e5-47cc-8b08-804966659df2", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_191154", "step_number": 2, "timestamp": "2025-07-29T19:11:54.915073", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b181a958-58e5-47cc-8b08-804966659df2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b181a958-58e5-47cc-8b08-804966659df2", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_191154", "step_number": 3, "timestamp": "2025-07-29T19:11:55.326016", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 397.63712882995605}, {"session_id": "20250729_191154", "step_number": 4, "timestamp": "2025-07-29T19:11:55.338790", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 410.4115962982178}, {"session_id": "20250729_191154", "step_number": 5, "timestamp": "2025-07-29T19:11:55.374905", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 23.406982421875}, {"session_id": "20250729_191154", "step_number": 6, "timestamp": "2025-07-29T19:11:55.387017", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.51888465881348}, {"session_id": "20250729_191617", "step_number": 1, "timestamp": "2025-07-29T19:16:17.914535", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "15d121ab-2dca-4863-b444-e88c82166737", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "15d121ab-2dca-4863-b444-e88c82166737", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_191617", "step_number": 2, "timestamp": "2025-07-29T19:16:17.932198", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "15d121ab-2dca-4863-b444-e88c82166737", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "15d121ab-2dca-4863-b444-e88c82166737", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_191617", "step_number": 3, "timestamp": "2025-07-29T19:16:18.437191", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 491.0435676574707}, {"session_id": "20250729_191617", "step_number": 4, "timestamp": "2025-07-29T19:16:18.449374", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 503.22604179382324}, {"session_id": "20250729_191617", "step_number": 5, "timestamp": "2025-07-29T19:16:18.482249", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.34282684326172}, {"session_id": "20250729_191617", "step_number": 6, "timestamp": "2025-07-29T19:16:18.495776", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.86998176574707}, {"session_id": "20250729_191757", "step_number": 1, "timestamp": "2025-07-29T19:17:57.170803", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f4121a57-2574-48e9-8242-3c6443464dc2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f4121a57-2574-48e9-8242-3c6443464dc2", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_191757", "step_number": 2, "timestamp": "2025-07-29T19:17:57.185430", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f4121a57-2574-48e9-8242-3c6443464dc2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f4121a57-2574-48e9-8242-3c6443464dc2", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_191757", "step_number": 3, "timestamp": "2025-07-29T19:18:06.204372", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 9004.681825637817}, {"session_id": "20250729_191757", "step_number": 4, "timestamp": "2025-07-29T19:18:06.216995", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 9017.30465888977}, {"session_id": "20250729_191757", "step_number": 5, "timestamp": "2025-07-29T19:18:06.309797", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 79.53524589538574}, {"session_id": "20250729_191757", "step_number": 6, "timestamp": "2025-07-29T19:18:06.321568", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 91.30668640136719}, {"session_id": "20250729_191954", "step_number": 1, "timestamp": "2025-07-29T19:19:54.939822", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ae77d29a-a0dc-426b-86fb-c5829bb9a347", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ae77d29a-a0dc-426b-86fb-c5829bb9a347", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_191954", "step_number": 2, "timestamp": "2025-07-29T19:19:54.954525", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ae77d29a-a0dc-426b-86fb-c5829bb9a347", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ae77d29a-a0dc-426b-86fb-c5829bb9a347", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_191954", "step_number": 3, "timestamp": "2025-07-29T19:20:05.521163", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 10550.559520721436}, {"session_id": "20250729_191954", "step_number": 4, "timestamp": "2025-07-29T19:20:05.533293", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 10562.690019607544}, {"session_id": "20250729_191954", "step_number": 5, "timestamp": "2025-07-29T19:20:05.626617", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 81.74872398376465}, {"session_id": "20250729_191954", "step_number": 6, "timestamp": "2025-07-29T19:20:05.637871", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 93.0027961730957}, {"session_id": "20250729_191954", "step_number": 7, "timestamp": "2025-07-29T19:23:12.072756", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "648c71b3-73db-4b0f-bda4-a3ebeb7ab9b4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "648c71b3-73db-4b0f-bda4-a3ebeb7ab9b4", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 83}}, "duration_ms": null}, {"session_id": "20250729_191954", "step_number": 8, "timestamp": "2025-07-29T19:23:12.096287", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "648c71b3-73db-4b0f-bda4-a3ebeb7ab9b4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "648c71b3-73db-4b0f-bda4-a3ebeb7ab9b4", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_191954", "step_number": 9, "timestamp": "2025-07-29T19:23:18.393413", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 6276.1218547821045}, {"session_id": "20250729_191954", "step_number": 10, "timestamp": "2025-07-29T19:23:18.405960", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 6288.668632507324}, {"session_id": "20250729_191954", "step_number": 11, "timestamp": "2025-07-29T19:23:18.484941", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 66.47872924804688}, {"session_id": "20250729_191954", "step_number": 12, "timestamp": "2025-07-29T19:23:18.500156", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 81.69364929199219}, {"session_id": "20250729_192856", "step_number": 1, "timestamp": "2025-07-29T19:28:56.289873", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "874b94ed-3a57-44c3-8b0e-171aff34e0e9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "874b94ed-3a57-44c3-8b0e-171aff34e0e9", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_192856", "step_number": 2, "timestamp": "2025-07-29T19:28:56.307398", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "874b94ed-3a57-44c3-8b0e-171aff34e0e9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "874b94ed-3a57-44c3-8b0e-171aff34e0e9", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_192856", "step_number": 3, "timestamp": "2025-07-29T19:29:06.559650", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 10232.863426208496}, {"session_id": "20250729_192856", "step_number": 4, "timestamp": "2025-07-29T19:29:06.573765", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 10246.978282928467}, {"session_id": "20250729_192856", "step_number": 5, "timestamp": "2025-07-29T19:29:06.692841", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 104.3236255645752}, {"session_id": "20250729_192856", "step_number": 6, "timestamp": "2025-07-29T19:29:06.705826", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 117.3086166381836}, {"session_id": "20250729_192856", "step_number": 7, "timestamp": "2025-07-29T19:30:41.703009", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bfeea19c-8c8c-4353-8d85-5113b3e9b30d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bfeea19c-8c8c-4353-8d85-5113b3e9b30d", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_192856", "step_number": 8, "timestamp": "2025-07-29T19:30:41.748620", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bfeea19c-8c8c-4353-8d85-5113b3e9b30d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bfeea19c-8c8c-4353-8d85-5113b3e9b30d", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_192856", "step_number": 9, "timestamp": "2025-07-29T19:30:44.848249", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3080.470561981201}, {"session_id": "20250729_192856", "step_number": 10, "timestamp": "2025-07-29T19:30:44.860636", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3092.857837677002}, {"session_id": "20250729_192856", "step_number": 11, "timestamp": "2025-07-29T19:30:44.913098", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.72370719909668}, {"session_id": "20250729_192856", "step_number": 12, "timestamp": "2025-07-29T19:30:44.924750", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 50.37569999694824}, {"session_id": "20250729_193645", "step_number": 1, "timestamp": "2025-07-29T19:36:45.073570", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "60416550-7508-40cf-8a9c-bb49d6138cb9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "60416550-7508-40cf-8a9c-bb49d6138cb9", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_193645", "step_number": 2, "timestamp": "2025-07-29T19:36:45.089678", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "60416550-7508-40cf-8a9c-bb49d6138cb9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "60416550-7508-40cf-8a9c-bb49d6138cb9", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_193645", "step_number": 3, "timestamp": "2025-07-29T19:36:54.241383", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 9134.626626968384}, {"session_id": "20250729_193645", "step_number": 4, "timestamp": "2025-07-29T19:36:54.255804", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 9149.047374725342}, {"session_id": "20250729_193645", "step_number": 5, "timestamp": "2025-07-29T19:36:54.361129", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 91.75848960876465}, {"session_id": "20250729_193645", "step_number": 6, "timestamp": "2025-07-29T19:36:54.373959", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 104.58874702453613}, {"session_id": "20250729_193645", "step_number": 7, "timestamp": "2025-07-29T19:38:24.965776", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a1599d08-c67b-4a5b-95b3-fde7888c0720", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a1599d08-c67b-4a5b-95b3-fde7888c0720", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_193645", "step_number": 8, "timestamp": "2025-07-29T19:38:24.985567", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a1599d08-c67b-4a5b-95b3-fde7888c0720", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a1599d08-c67b-4a5b-95b3-fde7888c0720", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_193645", "step_number": 9, "timestamp": "2025-07-29T19:38:28.919302", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3914.4532680511475}, {"session_id": "20250729_193645", "step_number": 10, "timestamp": "2025-07-29T19:38:28.932365", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3927.515983581543}, {"session_id": "20250729_193645", "step_number": 11, "timestamp": "2025-07-29T19:38:28.999819", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 52.932024002075195}, {"session_id": "20250729_193645", "step_number": 12, "timestamp": "2025-07-29T19:38:29.020573", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 73.68588447570801}, {"session_id": "20250729_194557", "step_number": 1, "timestamp": "2025-07-29T19:45:57.135794", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ef386684-0df7-4a43-be61-8ea3c4903474", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ef386684-0df7-4a43-be61-8ea3c4903474", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_194557", "step_number": 2, "timestamp": "2025-07-29T19:45:57.153266", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ef386684-0df7-4a43-be61-8ea3c4903474", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ef386684-0df7-4a43-be61-8ea3c4903474", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_194557", "step_number": 3, "timestamp": "2025-07-29T19:46:05.933649", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 8764.362335205078}, {"session_id": "20250729_194557", "step_number": 4, "timestamp": "2025-07-29T19:46:05.946848", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 8777.562379837036}, {"session_id": "20250729_194557", "step_number": 5, "timestamp": "2025-07-29T19:46:06.048767", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 88.23943138122559}, {"session_id": "20250729_194557", "step_number": 6, "timestamp": "2025-07-29T19:46:06.061343", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 100.8145809173584}, {"session_id": "20250729_194557", "step_number": 7, "timestamp": "2025-07-29T19:50:42.476312", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "851857fd-c53b-4090-9272-9c61604d5e6f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "851857fd-c53b-4090-9272-9c61604d5e6f", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_194557", "step_number": 8, "timestamp": "2025-07-29T19:50:42.498774", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "851857fd-c53b-4090-9272-9c61604d5e6f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "851857fd-c53b-4090-9272-9c61604d5e6f", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_194557", "step_number": 9, "timestamp": "2025-07-29T19:50:46.528042", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 4008.2406997680664}, {"session_id": "20250729_194557", "step_number": 10, "timestamp": "2025-07-29T19:50:46.542437", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 4022.634983062744}, {"session_id": "20250729_194557", "step_number": 11, "timestamp": "2025-07-29T19:50:46.599648", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 43.862342834472656}, {"session_id": "20250729_194557", "step_number": 12, "timestamp": "2025-07-29T19:50:46.615832", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 60.04619598388672}]