#!/usr/bin/env python3
"""
更新函数参数以符合标准规范
按照变量管理规范更新所有核心函数的参数名
"""

import re
from typing import Dict, List, Tuple

# 需要更新的函数参数映射
FUNCTION_PARAMETER_UPDATES = {
    # CostCalculator类的函数
    "calculate_final_cost": {
        "old_params": [
            "path_length", "turning_cost", "risk_value", "collision_cost",
            "risk_reference", "collision_reference", "turning_reference", "length_manhattan"
        ],
        "new_params": [
            "path_length", "turning_cost", "risk_value", "collision_cost",
            "risk_reference", "collision_reference", "turning_reference"
        ],
        "description": "计算路径最终代价（公式14-15）"
    },
    
    "calculate_path_length": {
        "old_params": ["waypoints"],
        "new_params": ["waypoints"],
        "description": "计算路径长度（公式1-2）"
    },
    
    "calculate_turning_cost": {
        "old_params": ["waypoints"],
        "new_params": ["waypoints"],
        "description": "计算转向成本（公式3-4）"
    },
    
    "calculate_risk_value": {
        "old_params": ["waypoints", "buildings", "path_length"],
        "new_params": ["waypoints", "buildings", "path_length"],
        "description": "计算风险值（公式5-9）"
    },
    
    "calculate_collision_cost": {
        "old_params": ["waypoints", "protection_zones"],
        "new_params": ["waypoints", "protection_zones"],
        "description": "计算碰撞代价（公式10-13）"
    },
    
    # ImprovedClusterBasedPathPlanning类的主函数
    "calculate_path": {
        "old_params": ["request"],
        "new_params": ["start_point", "end_point", "flight_height", "safety_distance", 
                      "max_turn_angle", "buildings", "protection_zones", "k_value", 
                      "enable_path_switching"],
        "description": "改进分簇路径规划主算法"
    },
    
    # InitialPathSetGenerator类的函数
    "generate_initial_path_set": {
        "old_params": ["start_point", "end_point", "buildings"],
        "new_params": ["start_point", "end_point", "buildings", "flight_height"],
        "description": "生成81条初始路径集"
    },
    
    # ClusterManager类的函数
    "assign_paths_to_clusters": {
        "old_params": ["initial_paths"],
        "new_params": ["initial_paths"],
        "description": "将路径分配到13个固定空间分簇"
    },
    
    # GradientFieldManager类的函数
    "calculate_gradient_field": {
        "old_params": ["waypoint"],
        "new_params": ["waypoint", "detected_objects"],
        "description": "计算航点处的碰撞代价梯度场"
    },
    
    # PathSwitchingStrategy类的函数
    "should_switch_path": {
        "old_params": ["current_path", "current_waypoint_index"],
        "new_params": ["current_path", "current_waypoint_index", "anomaly_threshold", "consecutive_check_count"],
        "description": "判断是否需要执行动态换路"
    },
    
    # PathSmoother类的函数
    "smooth_path": {
        "old_params": ["waypoints"],
        "new_params": ["waypoints", "smoothing_factor", "interpolation_points", "max_curvature"],
        "description": "三次样条路径平滑处理"
    },
    
    # ObjectDetectionSimulator类的函数
    "simulate_detection_at_waypoint": {
        "old_params": ["waypoint", "real_objects"],
        "new_params": ["waypoint", "real_objects", "weather_condition", "time_of_day", "visibility"],
        "description": "模拟航点处的物体检测"
    }
}

def generate_parameter_validation_code(function_name: str, params: List[str]) -> str:
    """生成参数验证代码"""
    validation_code = []
    validation_code.append("    # 参数验证")
    validation_code.append("    from algorithm_input_parameters import AlgorithmInputParameters, ALGORITHM_INPUT_PARAMETER_NAMES")
    validation_code.append("")
    
    # 为每个参数生成验证
    for param in params:
        if param in ["self", "cls"]:
            continue
            
        validation_code.append(f"    if {param} is not None:")
        validation_code.append(f"        # 验证 {param} 参数")
        validation_code.append(f"        try:")
        validation_code.append(f"            # 这里可以添加具体的参数验证逻辑")
        validation_code.append(f"            pass")
        validation_code.append(f"        except Exception as e:")
        validation_code.append(f"            raise ValueError(f'参数 {param} 验证失败: {{e}}')")
        validation_code.append("")
    
    return "\n".join(validation_code)

def generate_standardized_function_signature(function_name: str, params: List[str], 
                                           is_async: bool = False, is_method: bool = True) -> str:
    """生成标准化的函数签名"""
    # 处理可选参数
    required_params = []
    optional_params = []
    
    # 根据函数类型确定必需和可选参数
    if function_name == "calculate_path":
        required_params = ["start_point", "end_point", "flight_height"]
        optional_params = [p for p in params if p not in required_params and p != "self"]
    elif function_name == "calculate_final_cost":
        required_params = ["path_length", "turning_cost", "risk_value", "collision_cost"]
        optional_params = [p for p in params if p not in required_params and p != "self"]
    else:
        # 默认前几个为必需参数
        all_params = [p for p in params if p != "self"]
        if len(all_params) <= 2:
            required_params = all_params
            optional_params = []
        else:
            required_params = all_params[:2]
            optional_params = all_params[2:]
    
    # 构建参数字符串
    param_strs = []
    if is_method:
        param_strs.append("self")
    
    param_strs.extend(required_params)
    param_strs.extend([f"{p}=None" for p in optional_params])
    
    # 生成函数签名
    async_prefix = "async " if is_async else ""
    signature = f"{async_prefix}def {function_name}({', '.join(param_strs)})"
    
    return signature

def create_function_update_template(function_name: str) -> str:
    """创建函数更新模板"""
    if function_name not in FUNCTION_PARAMETER_UPDATES:
        return f"# 未找到函数 {function_name} 的更新配置"
    
    config = FUNCTION_PARAMETER_UPDATES[function_name]
    new_params = config["new_params"]
    description = config["description"]
    
    # 确定函数类型
    is_async = function_name in ["calculate_path", "generate_initial_path_set"]
    is_method = True  # 大部分都是类方法
    
    # 生成标准化签名
    signature = generate_standardized_function_signature(function_name, new_params, is_async, is_method)
    
    # 生成参数验证代码
    validation_code = generate_parameter_validation_code(function_name, new_params)
    
    # 生成函数模板
    template = f'''
{signature}:
    """
    {description}
    
    Args:
{chr(10).join(f"        {param}: {param} 参数" for param in new_params if param != "self")}
    
    Returns:
        函数执行结果
    """
{validation_code}
    
    # TODO: 实现函数逻辑
    # 原有的函数实现代码需要在这里更新
    
    pass
'''
    
    return template

def generate_update_instructions() -> str:
    """生成更新指导说明"""
    instructions = """
# 函数参数标准化更新指导

## 更新原则
1. 所有函数参数名必须与 algorithm_input_parameters.py 中定义的标准名称一致
2. 保持函数功能不变，只更新参数名和添加验证
3. 添加参数验证逻辑，确保输入数据的正确性
4. 保持向后兼容性，可以通过包装器实现

## 需要更新的核心函数

"""
    
    for func_name, config in FUNCTION_PARAMETER_UPDATES.items():
        instructions += f"### {func_name}\n"
        instructions += f"- 描述: {config['description']}\n"
        instructions += f"- 旧参数: {config['old_params']}\n"
        instructions += f"- 新参数: {config['new_params']}\n"
        instructions += f"- 更新模板:\n```python\n{create_function_update_template(func_name)}\n```\n\n"
    
    instructions += """
## 更新步骤
1. 备份原始文件
2. 更新函数签名
3. 添加参数验证代码
4. 测试函数功能
5. 更新调用代码

## 验证方法
- 运行现有的测试脚本
- 检查参数验证是否正常工作
- 确保算法结果与更新前一致
"""
    
    return instructions

def main():
    """主函数"""
    print("=== 函数参数标准化更新工具 ===")
    
    print(f"需要更新的函数数量: {len(FUNCTION_PARAMETER_UPDATES)}")
    
    print(f"\n核心函数列表:")
    for func_name, config in FUNCTION_PARAMETER_UPDATES.items():
        print(f"  ✓ {func_name}: {config['description']}")
    
    # 生成更新指导文档
    instructions = generate_update_instructions()
    
    # 保存到文件
    with open("function_update_instructions.md", "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print(f"\n📝 更新指导文档已生成: function_update_instructions.md")
    
    # 生成示例更新
    print(f"\n📋 示例函数更新模板:")
    example_template = create_function_update_template("calculate_final_cost")
    print(example_template)
    
    print(f"\n🔧 下一步操作建议:")
    print(f"  1. 查看生成的更新指导文档")
    print(f"  2. 按照模板逐个更新核心函数")
    print(f"  3. 运行测试验证更新结果")
    print(f"  4. 更新函数调用代码以使用新的参数名")

if __name__ == "__main__":
    main()
