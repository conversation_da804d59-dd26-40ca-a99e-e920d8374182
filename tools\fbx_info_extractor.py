#!/usr/bin/env python3
"""
FBX信息提取器 - 无需Blender的替代方案
提取FBX文件的基本信息，生成简化的建筑物数据用于Three.js
"""

import os
import json
import struct
import glob
from pathlib import Path

class FBXInfoExtractor:
    def __init__(self):
        self.building_types = [
            'residential', 'commercial', 'office', 'industrial', 
            'mixed_use', 'public', 'educational', 'medical'
        ]
        
    def extract_fbx_info(self, fbx_path):
        """提取FBX文件的基本信息"""
        try:
            file_size = os.path.getsize(fbx_path)
            file_name = os.path.basename(fbx_path)
            
            # 基于文件大小估算建筑复杂度
            if file_size < 100 * 1024:  # < 100KB
                complexity = 'simple'
                building_count = 1
                height_range = [10, 30]
                footprint_range = [8, 15]
            elif file_size < 1024 * 1024:  # < 1MB
                complexity = 'medium'
                building_count = 2 + (file_size // (200 * 1024))
                height_range = [20, 60]
                footprint_range = [10, 25]
            elif file_size < 5 * 1024 * 1024:  # < 5MB
                complexity = 'complex'
                building_count = 3 + (file_size // (500 * 1024))
                height_range = [30, 100]
                footprint_range = [15, 40]
            else:  # >= 5MB
                complexity = 'very_complex'
                building_count = 5 + (file_size // (1024 * 1024))
                height_range = [40, 150]
                footprint_range = [20, 60]
            
            # 限制建筑数量
            building_count = min(building_count, 20)
            
            # 基于文件名生成伪随机种子
            name_hash = hash(file_name) % 10000
            
            # 生成建筑物数据
            buildings = []
            for i in range(building_count):
                # 使用文件名和索引生成确定性的"随机"值
                seed = (name_hash + i * 1234) % 10000
                
                building = {
                    'id': f"{file_name}_{i}",
                    'type': self.building_types[seed % len(self.building_types)],
                    'height': height_range[0] + (seed % (height_range[1] - height_range[0])),
                    'width': footprint_range[0] + ((seed * 7) % (footprint_range[1] - footprint_range[0])),
                    'depth': footprint_range[0] + ((seed * 11) % (footprint_range[1] - footprint_range[0])),
                    'position': {
                        'x': ((seed * 13) % 1000) - 500,  # -500 到 500
                        'y': 0,
                        'z': ((seed * 17) % 1000) - 500   # -500 到 500
                    },
                    'color': self.get_building_color(self.building_types[seed % len(self.building_types)]),
                    'has_roof': complexity in ['complex', 'very_complex'],
                    'has_details': complexity == 'very_complex'
                }
                buildings.append(building)
            
            return {
                'source_file': fbx_path,
                'file_size': file_size,
                'complexity': complexity,
                'building_count': building_count,
                'buildings': buildings,
                'estimated_triangles': building_count * 100 * (1 if complexity == 'simple' else 
                                                              2 if complexity == 'medium' else
                                                              4 if complexity == 'complex' else 8)
            }
            
        except Exception as e:
            print(f"提取FBX信息失败 {fbx_path}: {e}")
            return None
    
    def get_building_color(self, building_type):
        """根据建筑类型返回颜色"""
        colors = {
            'residential': '#DEB887',  # 住宅 - 浅棕色
            'commercial': '#4682B4',   # 商业 - 钢蓝色
            'office': '#708090',       # 办公 - 石板灰
            'industrial': '#696969',   # 工业 - 暗灰色
            'mixed_use': '#9370DB',    # 混合用途 - 紫色
            'public': '#228B22',       # 公共建筑 - 绿色
            'educational': '#FF6347',  # 教育 - 番茄红
            'medical': '#20B2AA'       # 医疗 - 浅海绿
        }
        return colors.get(building_type, '#888888')
    
    def generate_gltf_data(self, building_info):
        """生成简化的glTF格式数据"""
        if not building_info:
            return None
            
        # 简化的glTF结构
        gltf_data = {
            'asset': {
                'version': '2.0',
                'generator': 'FBX Info Extractor'
            },
            'scene': 0,
            'scenes': [
                {
                    'name': 'Tokyo Buildings',
                    'nodes': list(range(len(building_info['buildings'])))
                }
            ],
            'nodes': [],
            'meshes': [],
            'materials': [],
            'accessors': [],
            'bufferViews': [],
            'buffers': [],
            'extras': {
                'source_info': {
                    'original_file': building_info['source_file'],
                    'file_size': building_info['file_size'],
                    'complexity': building_info['complexity'],
                    'building_count': building_info['building_count']
                }
            }
        }
        
        # 为每个建筑物创建节点
        for i, building in enumerate(building_info['buildings']):
            # 节点
            node = {
                'name': building['id'],
                'mesh': i,
                'translation': [
                    building['position']['x'],
                    building['position']['y'],
                    building['position']['z']
                ],
                'extras': {
                    'building_type': building['type'],
                    'height': building['height'],
                    'width': building['width'],
                    'depth': building['depth']
                }
            }
            gltf_data['nodes'].append(node)
            
            # 网格（简化的立方体）
            mesh = {
                'name': f"{building['id']}_mesh",
                'primitives': [
                    {
                        'attributes': {
                            'POSITION': i * 2,
                            'NORMAL': i * 2 + 1
                        },
                        'material': i
                    }
                ]
            }
            gltf_data['meshes'].append(mesh)
            
            # 材质
            material = {
                'name': f"{building['id']}_material",
                'pbrMetallicRoughness': {
                    'baseColorFactor': self.hex_to_rgb(building['color']) + [1.0],
                    'metallicFactor': 0.0,
                    'roughnessFactor': 0.8
                }
            }
            gltf_data['materials'].append(material)
        
        return gltf_data
    
    def hex_to_rgb(self, hex_color):
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        return [int(hex_color[i:i+2], 16) / 255.0 for i in (0, 2, 4)]
    
    def process_files(self, input_dir, output_dir, max_files=10):
        """处理文件列表"""
        os.makedirs(output_dir, exist_ok=True)

        # 获取所有FBX文件，选择前max_files个进行测试
        all_files = glob.glob(os.path.join(input_dir, "*.fbx"))
        all_files.sort()  # 按名称排序

        files_to_process = all_files[:max_files]

        print(f"从 {len(all_files)} 个FBX文件中选择前 {len(files_to_process)} 个进行处理...")
        
        processed_files = []
        success_count = 0
        
        for fbx_file in files_to_process:
            if not os.path.exists(fbx_file):
                print(f"文件不存在: {fbx_file}")
                continue
                
            print(f"处理: {os.path.basename(fbx_file)}")
            
            # 提取信息
            building_info = self.extract_fbx_info(fbx_file)
            if not building_info:
                continue
            
            # 生成glTF数据
            gltf_data = self.generate_gltf_data(building_info)
            if not gltf_data:
                continue
            
            # 保存JSON文件
            base_name = os.path.splitext(os.path.basename(fbx_file))[0]
            output_file = os.path.join(output_dir, f"{base_name}.json")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(gltf_data, f, indent=2, ensure_ascii=False)
            
            processed_files.append({
                'original_file': fbx_file,
                'output_file': output_file,
                'building_count': building_info['building_count'],
                'complexity': building_info['complexity'],
                'file_size': building_info['file_size']
            })
            
            success_count += 1
            print(f"  ✓ 生成了 {building_info['building_count']} 个建筑物")
        
        # 生成索引文件
        index_file = os.path.join(output_dir, "buildings_index.json")
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump({
                'total_files': len(processed_files),
                'total_buildings': sum(f['building_count'] for f in processed_files),
                'files': processed_files
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n处理完成!")
        print(f"成功处理: {success_count} 个文件")
        print(f"总建筑物数量: {sum(f['building_count'] for f in processed_files)}")
        print(f"索引文件: {index_file}")
        
        return processed_files

def main():
    """主函数"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)

    input_dir = os.path.join(project_root, "data", "tokyo23", "bldg", "lod1")
    output_dir = os.path.join(project_root, "data", "converted_gltf")

    print("FBX信息提取器 - 快速替代方案")
    print("=" * 50)
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")

    if not os.path.exists(input_dir):
        print(f"错误: 输入目录不存在: {input_dir}")
        return

    # 创建提取器
    extractor = FBXInfoExtractor()

    # 处理前10个文件进行测试
    processed_files = extractor.process_files(input_dir, output_dir, max_files=10)
    
    if processed_files:
        print("\n下一步:")
        print("1. 检查生成的JSON文件")
        print("2. 修改Three.js代码加载这些建筑物数据")
        print("3. 测试效果，如果满意可以处理更多文件")

if __name__ == "__main__":
    main()
