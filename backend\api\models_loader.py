"""
FBX模型加载API模块
提供3D模型文件的索引、加载和管理服务
"""
from flask import Blueprint, request, jsonify, send_file, current_app
import os
from utils.data_loader import get_data_loader

# 创建蓝图
models_loader_bp = Blueprint('models_loader', __name__)

@models_loader_bp.route('/index', methods=['GET'])
def get_model_index():
    """
    获取所有FBX模型文件的索引
    """
    try:
        data_loader = get_data_loader()
        index = data_loader.get_file_index()
        metadata = data_loader.get_metadata()
        
        return jsonify({
            'success': True,
            'index': index,
            'metadata': metadata,
            'message': '模型索引获取成功'
        })
        
    except Exception as e:
        return jsonify({'error': f'获取模型索引失败: {str(e)}'}), 500

@models_loader_bp.route('/recommended', methods=['GET'])
def get_recommended_models():
    """
    获取推荐加载的模型文件列表
    """
    try:
        # 获取查询参数
        max_files = request.args.get('max_files', 20, type=int)
        prefer_lod = request.args.get('prefer_lod', 'lod1')
        
        data_loader = get_data_loader()
        recommended_files = data_loader.get_recommended_files(max_files, prefer_lod)
        
        # 转换为前端可用的URL
        for file_info in recommended_files:
            file_info['url'] = data_loader.get_file_url(file_info['relative_path'])
        
        return jsonify({
            'success': True,
            'files': recommended_files,
            'count': len(recommended_files),
            'message': f'推荐{len(recommended_files)}个模型文件'
        })
        
    except Exception as e:
        return jsonify({'error': f'获取推荐模型失败: {str(e)}'}), 500

@models_loader_bp.route('/by_area', methods=['POST'])
def get_models_by_area():
    """
    根据区域获取模型文件
    """
    try:
        data = request.get_json()
        mesh_ids = data.get('mesh_ids', [])
        
        if not mesh_ids:
            return jsonify({'error': '缺少mesh_ids参数'}), 400
        
        data_loader = get_data_loader()
        area_files = data_loader.get_files_by_area(mesh_ids)
        
        # 转换为前端可用的URL
        for file_info in area_files:
            file_info['url'] = data_loader.get_file_url(file_info['relative_path'])
        
        return jsonify({
            'success': True,
            'files': area_files,
            'count': len(area_files),
            'mesh_ids': mesh_ids,
            'message': f'找到{len(area_files)}个区域模型文件'
        })
        
    except Exception as e:
        return jsonify({'error': f'获取区域模型失败: {str(e)}'}), 500

@models_loader_bp.route('/by_type', methods=['GET'])
def get_models_by_type():
    """
    根据类型获取模型文件
    """
    try:
        # 获取查询参数
        data_type = request.args.get('type', 'buildings')  # buildings, bridges, terrain, transportation
        lod_level = request.args.get('lod', 'lod1')  # 仅对buildings有效
        limit = request.args.get('limit', 50, type=int)
        
        data_loader = get_data_loader()
        index = data_loader.get_file_index()
        
        files = []
        if data_type == 'buildings':
            if lod_level in index['buildings']:
                files = index['buildings'][lod_level][:limit]
        elif data_type in index:
            files = index[data_type][:limit]
        
        # 转换为前端可用的URL
        for file_info in files:
            file_info['url'] = data_loader.get_file_url(file_info['relative_path'])
        
        return jsonify({
            'success': True,
            'files': files,
            'count': len(files),
            'type': data_type,
            'lod_level': lod_level if data_type == 'buildings' else None,
            'message': f'获取{data_type}类型模型{len(files)}个'
        })
        
    except Exception as e:
        return jsonify({'error': f'获取类型模型失败: {str(e)}'}), 500

@models_loader_bp.route('/file/<path:file_path>')
def serve_model_file(file_path):
    """
    提供FBX模型文件下载服务
    """
    try:
        # 安全检查：确保文件路径在数据目录内
        data_path = current_app.config.get('DATA_PATH')
        full_path = os.path.join(data_path, file_path)
        full_path = os.path.abspath(full_path)
        data_path = os.path.abspath(data_path)
        
        if not full_path.startswith(data_path):
            return jsonify({'error': '非法文件路径'}), 403
        
        if not os.path.exists(full_path):
            return jsonify({'error': '文件不存在'}), 404
        
        # 检查文件扩展名
        if not file_path.lower().endswith('.fbx'):
            return jsonify({'error': '不支持的文件类型'}), 400
        
        return send_file(
            full_path,
            as_attachment=False,
            mimetype='application/octet-stream',
            conditional=True  # 支持断点续传
        )
        
    except Exception as e:
        return jsonify({'error': f'文件服务失败: {str(e)}'}), 500

@models_loader_bp.route('/validate', methods=['GET'])
def validate_data():
    """
    验证数据完整性
    """
    try:
        data_loader = get_data_loader()
        validation_result = data_loader.validate_data_integrity()
        
        return jsonify({
            'success': True,
            'validation': validation_result,
            'message': '数据验证完成'
        })
        
    except Exception as e:
        return jsonify({'error': f'数据验证失败: {str(e)}'}), 500

@models_loader_bp.route('/stats', methods=['GET'])
def get_model_stats():
    """
    获取模型统计信息
    """
    try:
        data_loader = get_data_loader()
        index = data_loader.get_file_index()
        
        stats = {
            'summary': index['summary'],
            'details': {
                'buildings_lod1': len(index['buildings']['lod1']),
                'buildings_lod2': len(index['buildings']['lod2']),
                'bridges': len(index['bridges']),
                'terrain': len(index['terrain']),
                'transportation': len(index['transportation'])
            },
            'recommendations': {
                'suggested_lod': 'lod1' if len(index['buildings']['lod1']) > 0 else 'lod2',
                'estimated_load_time': '根据文件大小估算加载时间',
                'memory_usage': '预估内存使用量'
            }
        }
        
        return jsonify({
            'success': True,
            'stats': stats,
            'message': '统计信息获取成功'
        })
        
    except Exception as e:
        return jsonify({'error': f'获取统计信息失败: {str(e)}'}), 500

@models_loader_bp.route('/batch_info', methods=['POST'])
def get_batch_info():
    """
    获取批量文件信息
    """
    try:
        data = request.get_json()
        file_paths = data.get('file_paths', [])
        
        if not file_paths:
            return jsonify({'error': '缺少file_paths参数'}), 400
        
        data_loader = get_data_loader()
        index = data_loader.get_file_index()
        
        # 查找文件信息
        batch_info = []
        for file_path in file_paths:
            file_found = False
            
            # 在所有类别中搜索
            for category in ['buildings', 'bridges', 'terrain', 'transportation']:
                if category == 'buildings':
                    for lod in ['lod1', 'lod2']:
                        for file_info in index[category][lod]:
                            if file_info['relative_path'] == file_path:
                                file_info['url'] = data_loader.get_file_url(file_path)
                                batch_info.append(file_info)
                                file_found = True
                                break
                        if file_found:
                            break
                else:
                    for file_info in index[category]:
                        if file_info['relative_path'] == file_path:
                            file_info['url'] = data_loader.get_file_url(file_path)
                            batch_info.append(file_info)
                            file_found = True
                            break
                
                if file_found:
                    break
            
            if not file_found:
                batch_info.append({
                    'relative_path': file_path,
                    'error': '文件未找到'
                })
        
        return jsonify({
            'success': True,
            'files': batch_info,
            'requested_count': len(file_paths),
            'found_count': len([f for f in batch_info if 'error' not in f]),
            'message': f'批量查询完成，找到{len([f for f in batch_info if "error" not in f])}个文件'
        })
        
    except Exception as e:
        return jsonify({'error': f'批量查询失败: {str(e)}'}), 500
