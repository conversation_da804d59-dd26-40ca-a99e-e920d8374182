#!/usr/bin/env python3
"""
测试导出API端点
"""

import requests
import json

def test_export_api():
    """测试导出API端点"""
    print("🧪 测试导出API端点...")
    
    try:
        # 测试导出API
        url = "http://127.0.0.1:5000/api/export_calculated_paths"
        data = {
            "timestamp": "2025-07-31T12:30:00",
            "request_type": "complete_csv_export"
        }
        
        print(f"📡 发送请求到: {url}")
        print(f"📦 请求数据: {data}")
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 导出成功!")
            print(f"   文件名: {result.get('filename', '未知')}")
            print(f"   路径数量: {result.get('total_paths', '未知')}")
            print(f"   源JSON: {result.get('source_json', '未知')}")
            print(f"   导出时间: {result.get('export_time', '未知')}")
            return True
        else:
            print(f"❌ 导出失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('error', '未知错误')}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_export_api()
    exit(0 if success else 1)
