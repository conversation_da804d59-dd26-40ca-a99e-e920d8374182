"""
路径规划算法基类
提供所有路径规划算法的统一接口和基础功能
"""

import time
import uuid
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class AlgorithmParameter:
    """算法参数定义"""
    name: str
    type: str  # 'number', 'boolean', 'string', 'array'
    description: str
    default_value: Any = None
    required: bool = False
    validation: Optional[callable] = None
    min_value: Optional[float] = None
    max_value: Optional[float] = None


@dataclass
class AlgorithmInfo:
    """算法信息"""
    name: str
    version: str
    description: str
    author: str = "System"
    category: str = "basic"  # basic, advanced, ai, custom
    supported_optimizations: List[str] = field(default_factory=lambda: ['distance', 'time'])
    required_parameters: List[AlgorithmParameter] = field(default_factory=list)
    optional_parameters: List[AlgorithmParameter] = field(default_factory=list)


@dataclass
class AlgorithmStats:
    """算法性能统计"""
    total_executions: int = 0
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    success_count: int = 0
    error_count: int = 0
    success_rate: float = 0.0
    last_execution: Optional[datetime] = None
    max_execution_time: float = 0.0
    min_execution_time: float = float('inf')


class PathPlanningAlgorithm(ABC):
    """
    路径规划算法基类
    所有路径规划算法都应该继承此类
    """
    
    def __init__(self):
        # 算法基本信息
        self.info = AlgorithmInfo(
            name="BaseAlgorithm",
            version="1.0.0",
            description="基础算法类"
        )
        
        # 执行状态
        self.is_running = False
        self.is_cancelled = False
        self.progress = 0.0
        self.start_time = 0.0
        
        # 性能统计
        self.stats = AlgorithmStats()
        
        # 算法配置
        self.config = {
            'timeout': 30.0,  # 超时时间(秒)
            'max_memory': 512,  # 最大内存使用(MB)
            'enable_cache': True,  # 是否启用缓存
            'debug_mode': False  # 调试模式
        }
    
    def validate_input(self, request) -> Tuple[bool, List[str]]:
        """
        验证输入参数
        
        Args:
            request: 路径规划请求对象
            
        Returns:
            (is_valid, errors): 验证结果和错误信息列表
        """
        errors = []
        
        # 验证基本参数
        if not hasattr(request, 'start_point') or not request.start_point:
            errors.append("缺少起点坐标")
        
        if not hasattr(request, 'end_point') or not request.end_point:
            errors.append("缺少终点坐标")
        
        if hasattr(request, 'flight_height') and request.flight_height <= 0:
            errors.append("飞行高度必须大于0")
        
        # 验证算法特定参数
        for param in self.info.required_parameters:
            if param.name not in request.parameters:
                errors.append(f"缺少必需参数: {param.name}")
            else:
                value = request.parameters[param.name]
                if param.validation and not param.validation(value):
                    errors.append(f"参数 {param.name} 验证失败")
        
        # 验证可选参数
        for param in self.info.optional_parameters:
            if param.name in request.parameters:
                value = request.parameters[param.name]
                if param.validation and not param.validation(value):
                    errors.append(f"参数 {param.name} 验证失败")
        
        return len(errors) == 0, errors
    
    def estimate_execution_time(self, request) -> float:
        """
        估算执行时间
        
        Args:
            request: 路径规划请求对象
            
        Returns:
            estimated_time: 估算的执行时间(秒)
        """
        # 基于历史统计估算
        if self.stats.average_execution_time > 0:
            return self.stats.average_execution_time
        
        # 默认估算
        complexity = self._estimate_complexity(request)
        base_times = {
            'low': 1.0,
            'medium': 5.0,
            'high': 15.0
        }
        return base_times.get(complexity, 5.0)
    
    def _estimate_complexity(self, request) -> str:
        """估算问题复杂度"""
        # 基于距离和障碍物数量估算
        if hasattr(request, 'start_point') and hasattr(request, 'end_point'):
            distance = self._calculate_distance(request.start_point, request.end_point)
            obstacle_count = len(getattr(request, 'obstacles', []))
            
            if distance < 1000 and obstacle_count < 10:
                return 'low'
            elif distance < 5000 and obstacle_count < 50:
                return 'medium'
            else:
                return 'high'
        
        return 'medium'
    
    def _calculate_distance(self, point1: Dict, point2: Dict) -> float:
        """计算两点间距离"""
        if 'lng' in point1 and 'lat' in point1:
            # 地理坐标，使用Haversine公式
            return self._haversine_distance(
                point1['lat'], point1['lng'],
                point2['lat'], point2['lng']
            )
        else:
            # 笛卡尔坐标
            return np.sqrt(
                (point2['x'] - point1['x'])**2 +
                (point2['y'] - point1['y'])**2 +
                (point2.get('z', 0) - point1.get('z', 0))**2
            )
    
    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """使用Haversine公式计算地理距离"""
        R = 6371000  # 地球半径(米)
        
        lat1_rad = np.radians(lat1)
        lat2_rad = np.radians(lat2)
        delta_lat = np.radians(lat2 - lat1)
        delta_lon = np.radians(lon2 - lon1)
        
        a = (np.sin(delta_lat / 2)**2 +
             np.cos(lat1_rad) * np.cos(lat2_rad) *
             np.sin(delta_lon / 2)**2)
        c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))
        
        return R * c
    
    @abstractmethod
    async def calculate_path(self, request):
        """
        计算路径的主要方法 - 子类必须实现
        
        Args:
            request: PathPlanningRequest对象
            
        Returns:
            PathPlanningResponse对象
        """
        raise NotImplementedError("calculate_path方法必须在子类中实现")
    
    async def execute(self, request):
        """
        执行算法的包装方法
        
        Args:
            request: PathPlanningRequest对象
            
        Returns:
            PathPlanningResponse对象
        """
        from .data_structures import PathPlanningResponse
        
        # 验证输入
        print(f"🔧 BASE: 开始验证输入")
        is_valid, errors = self.validate_input(request)
        print(f"🔧 BASE: 验证结果 - is_valid: {is_valid}, errors: {errors}")
        if not is_valid:
            print(f"❌ BASE: 输入验证失败: {errors}")
            response = PathPlanningResponse()
            response.success = False
            response.error = "; ".join(errors)
            return response
        print(f"✅ BASE: 输入验证通过")
        
        # 初始化执行状态
        self.is_running = True
        self.is_cancelled = False
        self.progress = 0.0
        self.start_time = time.time()
        
        try:
            # 执行算法
            print(f"🔧 BASE: 准备调用self.calculate_path")
            print(f"🔧 BASE: self类型: {type(self)}")
            print(f"🔧 BASE: 是否有calculate_path方法: {hasattr(self, 'calculate_path')}")
            response = await self.calculate_path(request)
            print(f"🔧 BASE: calculate_path调用完成，响应类型: {type(response)}")
            print(f"🔧 BASE: response.success: {getattr(response, 'success', 'N/A')}")
            
            # 更新统计信息
            execution_time = time.time() - self.start_time
            self._update_stats(True, execution_time)
            
            # 设置响应元数据（保留算法设置的详细数据）
            response.execution_time = execution_time

            # 安全地更新metadata，不覆盖已有的详细数据
            if not hasattr(response, 'metadata') or response.metadata is None:
                response.metadata = {}

            # 只添加基本元数据，不覆盖算法的详细计算数据
            response.metadata.update({
                'algorithm_used': self.info.name if hasattr(self, 'info') and self.info else '',
                'algorithm_version': self.info.version if hasattr(self, 'info') and self.info else '',
                'parameters_used': request.parameters.copy()
            })
            
            return response
            
        except Exception as e:
            # 更新统计信息
            execution_time = time.time() - self.start_time
            self._update_stats(False, execution_time)
            
            # 创建错误响应
            response = PathPlanningResponse()
            response.success = False
            response.error = f"算法执行失败: {str(e)}"
            response.execution_time = execution_time
            
            return response
            
        finally:
            # 清理执行状态
            self.is_running = False
            self.progress = 100.0
    
    def _update_stats(self, success: bool, execution_time: float):
        """更新算法统计信息"""
        self.stats.total_executions += 1
        self.stats.total_execution_time += execution_time
        self.stats.last_execution = datetime.now()
        
        if success:
            self.stats.success_count += 1
        else:
            self.stats.error_count += 1
        
        # 更新平均执行时间
        self.stats.average_execution_time = (
            self.stats.total_execution_time / self.stats.total_executions
        )
        
        # 更新成功率
        self.stats.success_rate = self.stats.success_count / self.stats.total_executions
        
        # 更新最大/最小执行时间
        self.stats.max_execution_time = max(self.stats.max_execution_time, execution_time)
        self.stats.min_execution_time = min(self.stats.min_execution_time, execution_time)
    
    def cancel(self):
        """取消算法执行"""
        self.is_cancelled = True
    
    def get_info(self) -> Dict[str, Any]:
        """获取算法信息"""
        return {
            'name': self.info.name,
            'version': self.info.version,
            'description': self.info.description,
            'author': self.info.author,
            'category': self.info.category,
            'supported_optimizations': self.info.supported_optimizations,
            'required_parameters': [
                {
                    'name': p.name,
                    'type': p.type,
                    'description': p.description,
                    'required': p.required,
                    'default_value': p.default_value
                }
                for p in self.info.required_parameters
            ],
            'optional_parameters': [
                {
                    'name': p.name,
                    'type': p.type,
                    'description': p.description,
                    'required': p.required,
                    'default_value': p.default_value
                }
                for p in self.info.optional_parameters
            ],
            'stats': {
                'total_executions': self.stats.total_executions,
                'average_execution_time': self.stats.average_execution_time,
                'success_rate': self.stats.success_rate,
                'last_execution': self.stats.last_execution.isoformat() if self.stats.last_execution else None
            }
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = AlgorithmStats()
