#!/usr/bin/env python3
"""
测试math导入修复
"""

def test_math_import():
    """测试math模块导入"""
    try:
        from algorithms.astar import AStarAlgorithm
        print("✅ A*算法导入成功")
        
        # 创建实例
        astar = AStarAlgorithm()
        print("✅ A*算法实例创建成功")
        
        # 测试math相关方法
        from algorithms.astar import GridNode
        
        # 创建测试节点
        p1 = GridNode(0, 0, 0)
        p2 = GridNode(1, 1, 0)
        p3 = GridNode(2, 0, 0)
        
        # 测试方向变化计算（这个方法使用了math）
        angle = astar._calculate_direction_change(p1, p2, p3)
        print(f"✅ 方向变化计算成功: {angle:.3f}弧度")
        
        print("🎉 math导入修复成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 测试math导入修复")
    print("=" * 30)
    test_math_import()
