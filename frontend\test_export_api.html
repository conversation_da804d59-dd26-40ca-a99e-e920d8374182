<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试导出API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 测试导出API</h1>
    
    <button id="testExportBtn" class="button">📊 测试导出81条路径数据</button>
    <button id="clearLogBtn" class="button">🗑️ 清空日志</button>
    
    <div id="log" class="log">点击按钮开始测试...</div>

    <script>
        const logElement = document.getElementById('log');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        async function testExportAPI() {
            const button = document.getElementById('testExportBtn');
            const originalText = button.textContent;
            
            try {
                button.disabled = true;
                button.textContent = '⏳ 导出中...';
                
                log('🚀 开始测试导出API...');
                
                const requestData = {
                    timestamp: new Date().toISOString(),
                    request_type: 'complete_csv_export'
                };
                
                log(`📦 请求数据: ${JSON.stringify(requestData, null, 2)}`);
                
                const response = await fetch('/api/export_calculated_paths', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                log(`📊 HTTP响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`❌ HTTP错误响应内容: ${errorText}`);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`✅ 解析后的响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (!data.success) {
                    log(`❌ API返回失败: ${data.error}`);
                    throw new Error(data.error || '导出失败');
                }
                
                log(`🔍 filename字段值: "${data.filename}"`);
                log(`🔍 source_json字段值: "${data.source_json}"`);
                log(`🔍 total_paths字段值: ${data.total_paths}`);
                
                const filename = data.filename || '路径数据文件';
                const sourceJson = data.source_json || '未知';
                
                log(`🔍 处理后的filename: "${filename}"`);
                log(`🔍 处理后的sourceJson: "${sourceJson}"`);
                
                log(`✅ 导出成功！`);
                log(`   文件名: ${filename}`);
                log(`   路径数量: ${data.total_paths}`);
                log(`   源JSON: ${sourceJson}`);
                log(`   文件已保存到 csv/ 目录`);
                
                alert(`✅ 路径数据导出成功！\n\n文件名: ${filename}\n路径数量: ${data.total_paths}\n源JSON: ${sourceJson}\n\n文件已保存到 csv/ 目录`);
                
            } catch (error) {
                log(`❌ 导出失败: ${error.message}`);
                log(`🔍 错误详情: ${error.stack}`);
                alert(`❌ 路径数据导出失败！\n\n错误信息: ${error.message}\n\n请检查控制台获取详细信息`);
            } finally {
                button.disabled = false;
                button.textContent = originalText;
            }
        }
        
        // 绑定事件
        document.getElementById('testExportBtn').addEventListener('click', testExportAPI);
        document.getElementById('clearLogBtn').addEventListener('click', clearLog);
    </script>
</body>
</html>
