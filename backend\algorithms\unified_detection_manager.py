"""
统一检测管理器
用于统一改进算法和A*算法的保护区、建筑物、车辆、行人检测逻辑
以改进算法的检测方法为准，确保算法对比的公平性
"""

import math
from typing import List, Dict, Tuple, Any
from dataclasses import dataclass
from .data_structures import Point3D


@dataclass
class UnifiedDetectionResult:
    """统一检测结果"""
    buildings: List[Dict] = None
    protection_zones: List[Dict] = None
    traffic_data: List[Dict] = None
    collision_cost: float = 0.0
    risk_value: float = 0.0
    turning_cost: float = 0.0

    def __post_init__(self):
        """初始化后处理，确保列表不为None"""
        if self.buildings is None:
            self.buildings = []
        if self.protection_zones is None:
            self.protection_zones = []
        if self.traffic_data is None:
            self.traffic_data = []


class UnifiedDetectionManager:
    """
    统一检测管理器
    
    核心原则：
    1. 以改进算法的检测逻辑为准
    2. 确保A*和改进算法使用相同的检测方法
    3. 统一建筑物、保护区、交通数据的生成和处理
    4. 提供一致的成本计算接口
    """
    
    def __init__(self):
        """初始化统一检测管理器"""
        self.detection_radius = 30.0  # 检测半径（米）
        self.max_buildings = 20  # 最大建筑物数量
        self.safety_margin = 20.0  # 安全余量
        
        # 导入改进算法的检测器
        self._import_improved_detectors()
        
    def _import_improved_detectors(self):
        """导入改进算法的检测器"""
        try:
            from .improved_cluster_pathfinding import (
                PathBasedBuildingDetector,
                CostCalculator,
                TrafficPoint,
                ImprovedPathPoint,
                LegacyProtectionZone
            )
            from .data_structures import Point3D

            # 将类设置为类属性，供其他方法使用
            self.ImprovedPathPoint = ImprovedPathPoint
            self.LegacyProtectionZone = LegacyProtectionZone
            self.Point3D = Point3D
            
            # 创建改进算法的检测器实例
            self.building_detector = PathBasedBuildingDetector(
                detection_radius=self.detection_radius,
                max_buildings=self.max_buildings
            )
            
            self.cost_calculator = CostCalculator({
                'flightHeight': 120.0,
                'safetyDistance': 30.0,
                'gridSize': 10.0,
                'maxTurnAngle': 90.0,
                'riskEdgeDistance': 50.0,
                'kValue': 5
            })
            
            print("✅ 统一检测管理器：成功导入改进算法检测器")
            
        except ImportError as e:
            print(f"❌ 统一检测管理器：导入改进算法检测器失败: {e}")
            self.building_detector = None
            self.cost_calculator = None
    
    def detect_for_path(self, waypoints: List[Any], flight_height: float = 120.0) -> UnifiedDetectionResult:
        """
        为路径进行统一检测
        
        Args:
            waypoints: 路径航点列表（支持多种格式）
            flight_height: 飞行高度
            
        Returns:
            统一检测结果
        """
        print(f"🔍 统一检测管理器：开始为路径进行统一检测")
        print(f"🔍 航点数量: {len(waypoints)}, 飞行高度: {flight_height}米")

        # 🔧 保存当前航点用于保护区生成
        self._current_waypoints = waypoints

        # 转换航点格式为ImprovedPathPoint
        improved_waypoints = self._convert_waypoints_to_improved_format(waypoints)
        
        result = UnifiedDetectionResult()
        
        if not self.building_detector or not self.cost_calculator:
            print("⚠️ 统一检测管理器：检测器未初始化，返回空结果")
            # 确保返回的结果有有效的空列表
            result.buildings = []
            result.protection_zones = []
            result.traffic_data = []
            return result
        
        try:
            # 1. 建筑物检测（使用改进算法的方法）
            print("🏗️ 统一检测：建筑物检测...")
            detected_buildings = self.building_detector.detect_buildings_for_path(
                improved_waypoints, flight_height
            )
            # 确保buildings不为None
            result.buildings = detected_buildings if detected_buildings is not None else []
            print(f"🏗️ 检测到 {len(result.buildings)} 个建筑物")
            
            # 2. 交通数据生成（使用改进算法的方法）
            print("🚗 统一检测：交通数据生成...")
            if hasattr(self.building_detector, 'traffic_data') and self.building_detector.traffic_data:
                result.traffic_data = self.building_detector.traffic_data
            else:
                result.traffic_data = []
            print(f"🚗 生成 {len(result.traffic_data)} 个交通点")
            
            # 3. 保护区生成（基于交通数据）
            result.protection_zones = self._generate_protection_zones_from_traffic(
                result.traffic_data
            )
            
            # 4. 成本计算（使用改进算法的方法）
            print("💰 统一检测：成本计算...")

            try:
                # 设置建筑物数据到成本计算器
                print("💰 设置建筑物数据到成本计算器...")
                self.cost_calculator.set_buildings_data(result.buildings)

                # 计算风险值
                print("💰 计算风险值...")
                path_length = self._calculate_path_length(improved_waypoints)
                print(f"💰 路径长度: {path_length:.2f}米")

                result.risk_value, _ = self.cost_calculator.calculate_risk_value(
                    improved_waypoints, result.buildings, path_length
                )
                print(f"💰 风险值计算完成: {result.risk_value:.4f}")

                # 计算碰撞代价
                print("💰 计算碰撞代价...")
                result.collision_cost = self.cost_calculator.calculate_collision_cost(
                    improved_waypoints, result.protection_zones
                )
                print(f"💰 碰撞代价计算完成: {result.collision_cost:.4f}")

                # 计算转向成本
                print("💰 计算转向成本...")
                result.turning_cost = self.cost_calculator.calculate_turning_cost(
                    improved_waypoints
                )
                print(f"💰 转向成本计算完成: {result.turning_cost:.4f}")

            except Exception as cost_error:
                print(f"❌ 统一检测成本计算失败: {cost_error}")
                import traceback
                traceback.print_exc()
                # 设置默认值
                result.risk_value = 0.0
                result.collision_cost = 0.0
                result.turning_cost = 0.0
            
            print(f"💰 成本计算完成:")
            print(f"   - 风险值: {result.risk_value:.4f}")
            print(f"   - 碰撞代价: {result.collision_cost:.4f}")
            print(f"   - 转向成本: {result.turning_cost:.4f}")
            
        except Exception as e:
            print(f"❌ 统一检测过程中出错: {e}")
            import traceback
            traceback.print_exc()
            # 确保即使出错也返回有效的空结果
            result.buildings = []
            result.protection_zones = []
            result.traffic_data = []
            result.collision_cost = 0.0
            result.risk_value = 0.0
            result.turning_cost = 0.0

        # 最终安全检查，确保所有列表都不为None
        if result.buildings is None:
            result.buildings = []
        if result.protection_zones is None:
            result.protection_zones = []
        if result.traffic_data is None:
            result.traffic_data = []

        return result
    
    def _convert_waypoints_to_improved_format(self, waypoints: List[Any]) -> List[Any]:
        """将各种格式的航点转换为ImprovedPathPoint格式"""
        improved_waypoints = []
        
        for i, wp in enumerate(waypoints):
            try:
                # 如果已经是ImprovedPathPoint格式
                if hasattr(self, 'ImprovedPathPoint') and isinstance(wp, self.ImprovedPathPoint):
                    improved_waypoints.append(wp)
                    continue
                
                # 如果是Point3D格式
                if hasattr(wp, 'lng') and hasattr(wp, 'lat'):
                    if hasattr(self, 'ImprovedPathPoint'):
                        improved_wp = self.ImprovedPathPoint(
                            x=wp.lng,
                            y=wp.lat,
                            z=getattr(wp, 'alt', 120.0),
                            lng=wp.lng,
                            lat=wp.lat,
                            alt=getattr(wp, 'alt', 120.0),
                            waypoint_index=i
                        )
                        improved_waypoints.append(improved_wp)
                        continue
                
                # 如果是字典格式
                if isinstance(wp, dict):
                    lng = wp.get('lng', wp.get('longitude', 0))
                    lat = wp.get('lat', wp.get('latitude', 0))
                    alt = wp.get('alt', wp.get('altitude', 120.0))

                    if hasattr(self, 'ImprovedPathPoint'):
                        improved_wp = self.ImprovedPathPoint(
                            x=lng,
                            y=lat,
                            z=alt,
                            lng=lng,
                            lat=lat,
                            alt=alt,
                            waypoint_index=i
                        )
                        improved_waypoints.append(improved_wp)
                        continue
                
                print(f"⚠️ 无法转换航点格式: {type(wp)}")
                
            except Exception as e:
                print(f"⚠️ 转换航点 {i} 时出错: {e}")
                continue
        
        print(f"🔄 航点格式转换: {len(waypoints)} → {len(improved_waypoints)}")
        return improved_waypoints
    
    def _calculate_path_length(self, waypoints: List[Any]) -> float:
        """计算路径长度"""
        if len(waypoints) < 2:
            return 0.0
        
        total_length = 0.0
        for i in range(len(waypoints) - 1):
            wp1 = waypoints[i]
            wp2 = waypoints[i + 1]
            
            # 使用地理距离计算
            distance = self._calculate_geo_distance(
                wp1.lat, wp1.lng, wp2.lat, wp2.lng
            )
            total_length += distance
        
        return total_length
    
    def _calculate_geo_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """计算地理距离（米）"""
        # 简化的地理距离计算
        lat_diff = (lat2 - lat1) * 110540  # 纬度差转米
        lng_diff = (lng2 - lng1) * 111320 * math.cos(math.radians((lat1 + lat2) / 2))  # 经度差转米
        return math.sqrt(lat_diff**2 + lng_diff**2)
    
    def _generate_protection_zones_from_traffic(self, traffic_data: List[Dict]) -> List[Any]:
        """
        基于交通数据生成保护区对象
        🔧 修复：增加保护区密度，确保碰撞代价合理
        """
        import random
        protection_zones = []

        # 检查是否有必要的类
        if not hasattr(self, 'LegacyProtectionZone') or not hasattr(self, 'Point3D'):
            print("⚠️ 统一检测：缺少必要的保护区类，返回空列表")
            return protection_zones

        # 1. 基于实际交通数据生成保护区
        for i, traffic_point in enumerate(traffic_data):
            try:
                x = traffic_point.get('x', traffic_point.get('lng', 0))
                y = traffic_point.get('y', traffic_point.get('lat', 0))
                vehicles = traffic_point.get('vehicles', 0)
                pedestrians = traffic_point.get('pedestrians', 0)

                # 车辆保护区
                if vehicles > 0:
                    # 创建圆形保护区的多边形近似（8边形）
                    radius = 25.0  # 25米半径
                    vehicle_polygon = self._create_circular_polygon(x, y, radius, 8)

                    vehicle_zone = self.LegacyProtectionZone(
                        zone_id=f'unified_vehicle_zone_{i}',
                        zone_type='vehicle',
                        polygon_points=vehicle_polygon,
                        collision_cost_density=8.0 * vehicles
                        # 🔧 移除手动设置area，让__post_init__自动计算
                    )
                    protection_zones.append(vehicle_zone)
                    # 车辆保护区创建完成

                # 行人保护区
                if pedestrians > 0:
                    # 创建圆形保护区的多边形近似（8边形）
                    radius = 15.0  # 15米半径
                    pedestrian_polygon = self._create_circular_polygon(x, y, radius, 8)

                    pedestrian_zone = self.LegacyProtectionZone(
                        zone_id=f'unified_pedestrian_zone_{i}',
                        zone_type='pedestrian',
                        polygon_points=pedestrian_polygon,
                        collision_cost_density=10.0 * pedestrians
                        # 🔧 移除手动设置area，让__post_init__自动计算
                    )
                    protection_zones.append(pedestrian_zone)
                    # 行人保护区创建完成

            except Exception as e:
                print(f"⚠️ 生成保护区 {i} 时出错: {e}")
                import traceback
                traceback.print_exc()
                continue

        # 🔧 2. 补充额外保护区，确保路径覆盖充分
        # 目标：确保有足够的保护区来产生合理的碰撞代价
        current_zone_count = len(protection_zones)
        min_zones_needed = 15  # 最少需要15个保护区

        if current_zone_count < min_zones_needed:
            additional_zones_needed = min_zones_needed - current_zone_count

            for j in range(additional_zones_needed):
                # 随机生成补充保护区
                zone_type = random.choice(["vehicle", "pedestrian", "mixed"])

                # 🔧 修复：在路径附近生成保护区，而不是随机位置
                # 计算路径的边界框
                if hasattr(self, '_current_waypoints') and self._current_waypoints:
                    min_x = min(getattr(wp, 'x', wp.lng) for wp in self._current_waypoints)
                    max_x = max(getattr(wp, 'x', wp.lng) for wp in self._current_waypoints)
                    min_y = min(getattr(wp, 'y', wp.lat) for wp in self._current_waypoints)
                    max_y = max(getattr(wp, 'y', wp.lat) for wp in self._current_waypoints)

                    # 在路径附近生成保护区
                    base_x = random.uniform(min_x - 0.001, max_x + 0.001)
                    base_y = random.uniform(min_y - 0.001, max_y + 0.001)
                else:
                    # 回退到默认位置
                    base_x = random.uniform(-500, 500)
                    base_y = random.uniform(-500, 500)

                if zone_type == "vehicle":
                    # 车辆密集区
                    radius = 30.0
                    polygon = self._create_circular_polygon(base_x, base_y, radius, 8)
                    additional_zone = self.LegacyProtectionZone(
                        zone_id=f"additional_vehicle_zone_{j}",
                        zone_type="vehicle",
                        polygon_points=polygon,
                        collision_cost_density=random.uniform(25.0, 45.0)  # 中高密度
                        # 🔧 移除手动设置area，让__post_init__自动计算
                    )
                elif zone_type == "pedestrian":
                    # 行人密集区
                    radius = 20.0
                    polygon = self._create_circular_polygon(base_x, base_y, radius, 8)
                    additional_zone = self.LegacyProtectionZone(
                        zone_id=f"additional_pedestrian_zone_{j}",
                        zone_type="pedestrian",
                        polygon_points=polygon,
                        collision_cost_density=random.uniform(30.0, 60.0)  # 高密度
                        # 🔧 移除手动设置area，让__post_init__自动计算
                    )
                else:
                    # 混合区域
                    radius = 25.0
                    polygon = self._create_circular_polygon(base_x, base_y, radius, 8)
                    additional_zone = self.LegacyProtectionZone(
                        zone_id=f"additional_mixed_zone_{j}",
                        zone_type="mixed",
                        polygon_points=polygon,
                        collision_cost_density=random.uniform(35.0, 70.0)  # 高密度
                        # 🔧 移除手动设置area，让__post_init__自动计算
                    )

                protection_zones.append(additional_zone)
        return protection_zones

    def _create_circular_polygon(self, center_x: float, center_y: float, radius: float, sides: int = 8) -> List[Any]:
        """创建圆形的多边形近似"""
        import math

        polygon_points = []

        for i in range(sides):
            angle = 2 * math.pi * i / sides
            x = center_x + radius * math.cos(angle) / 111320  # 转换为经度差
            y = center_y + radius * math.sin(angle) / 110540  # 转换为纬度差

            point = self.Point3D(lng=x, lat=y, alt=0, x=x, y=y, z=0)
            polygon_points.append(point)

        return polygon_points


# 全局统一检测管理器实例
_unified_detection_manager = None

def get_unified_detection_manager() -> UnifiedDetectionManager:
    """获取全局统一检测管理器实例"""
    global _unified_detection_manager
    if _unified_detection_manager is None:
        _unified_detection_manager = UnifiedDetectionManager()
    return _unified_detection_manager
