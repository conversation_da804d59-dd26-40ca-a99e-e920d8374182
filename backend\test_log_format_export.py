#!/usr/bin/env python3
"""
测试日志格式导出功能
"""

import os
import sys
import json

def test_log_format_export():
    """测试日志格式导出功能"""
    
    print("🧪 开始测试日志格式导出功能...")
    
    try:
        # 导入导出模块
        from export_log_format import export_log_format_data, generate_log_format_summary
        
        print("✅ 成功导入导出模块")
        
        # 执行导出
        result = export_log_format_data()
        
        if result and result.get('success'):
            print(f"✅ 导出成功!")
            print(f"   文件路径: {result['filepath']}")
            print(f"   文件名: {result['filename']}")
            print(f"   总路径数: {result['stats']['total_paths']}")
            print(f"   导出时间: {result['export_time']}")
            
            # 检查文件是否存在
            if os.path.exists(result['filepath']):
                print(f"✅ 文件已成功创建: {result['filepath']}")
                
                # 读取并显示前几行
                with open(result['filepath'], 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    print(f"\n📋 文件内容预览 (前5行):")
                    for i, line in enumerate(lines[:5]):
                        print(f"   {i+1}: {line.strip()}")
                    
                    print(f"\n📊 文件统计:")
                    print(f"   总行数: {len(lines)}")
                    print(f"   数据行数: {len(lines) - 1}")  # 减去表头
                
                return True
            else:
                print(f"❌ 文件未创建: {result['filepath']}")
                return False
        else:
            print("❌ 导出失败")
            if result:
                print(f"   错误信息: {result.get('error', '未知错误')}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("   请确保已运行算法对比生成路径数据")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint():
    """测试API端点"""
    
    print("\n🌐 测试API端点...")
    
    try:
        import requests
        
        # 测试API
        response = requests.post('http://localhost:5000/api/export_log_format_paths', 
                               json={'test': True})
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ API测试成功")
                print(f"   文件名: {data.get('filename')}")
                print(f"   路径数: {data.get('total_paths')}")
                return True
            else:
                print(f"❌ API返回失败: {data.get('error')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except ImportError:
        print("⚠️ requests模块未安装，跳过API测试")
        return True
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def check_data_availability():
    """检查数据可用性"""
    
    print("\n🔍 检查数据可用性...")
    
    try:
        from export_all_paths_data import export_all_paths_data
        
        result = export_all_paths_data()
        
        if result and result.get('all_paths'):
            print(f"✅ 找到路径数据: {len(result['all_paths'])}条路径")
            
            # 检查基准路径
            if result.get('baseline_path'):
                print("✅ 找到基准路径数据")
            else:
                print("⚠️ 未找到基准路径数据")
            
            # 检查选中路径
            if result.get('selected_path'):
                print(f"✅ 找到选中路径: {result['selected_path'].get('selected_path_id')}")
            else:
                print("⚠️ 未找到选中路径信息")
            
            return True
        else:
            print("❌ 未找到路径数据")
            print("   请先运行算法对比生成路径数据")
            return False
            
    except Exception as e:
        print(f"❌ 数据检查失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🚀 日志格式导出功能测试")
    print("=" * 50)
    
    # 检查数据可用性
    data_ok = check_data_availability()
    
    if not data_ok:
        print("\n❌ 测试终止：没有可用的路径数据")
        print("   请先运行以下步骤:")
        print("   1. 启动系统: python backend/app.py")
        print("   2. 打开前端页面")
        print("   3. 执行算法对比")
        print("   4. 再次运行此测试")
        return False
    
    # 测试导出功能
    export_ok = test_log_format_export()
    
    # 测试API端点
    api_ok = test_api_endpoint()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   数据可用性: {'✅ 通过' if data_ok else '❌ 失败'}")
    print(f"   导出功能:   {'✅ 通过' if export_ok else '❌ 失败'}")
    print(f"   API端点:    {'✅ 通过' if api_ok else '❌ 失败'}")
    
    if data_ok and export_ok:
        print("\n🎉 所有测试通过！日志格式导出功能正常工作")
        return True
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
