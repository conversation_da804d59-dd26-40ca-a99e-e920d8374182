#!/usr/bin/env python3
"""
测试算法对比API的碰撞代价修复
验证API是否使用了正确的保护区碰撞代价
"""

import requests
import json

def test_api_collision_cost():
    """测试算法对比API的碰撞代价"""
    
    print("🔍 测试算法对比API碰撞代价修复")
    print("=" * 80)
    
    # 发送算法对比请求
    request_data = {
        "start": {"lng": 139.767, "lat": 35.681, "alt": 120},
        "end": {"lng": 139.7016, "lat": 35.6598, "alt": 120}
    }
    
    try:
        print(f"📤 发送算法对比请求...")
        response = requests.post('http://localhost:5000/api/algorithm_comparison', 
                               json=request_data, 
                               timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 算法对比API成功")
            
            # 检查改进算法的碰撞代价
            if 'improved' in result:
                improved = result['improved']
                collision_cost = improved.get('collision_cost', 0)
                print(f"\n📊 改进算法:")
                print(f"   API显示碰撞代价: {collision_cost}")
                
                # 检查保护区信息
                if 'protectionZonesInfo' in improved:
                    protection_info = improved['protectionZonesInfo']
                    if 'total_estimated_collision_cost' in protection_info:
                        total_cost = protection_info['total_estimated_collision_cost']
                        print(f"   保护区系统总代价: {total_cost}")
                        
                        if abs(collision_cost - total_cost) < 0.01:
                            print(f"   ✅ 改进算法碰撞代价已修复！")
                        else:
                            print(f"   ❌ 改进算法碰撞代价未修复")
                    
                    if 'collision_cost_breakdown' in protection_info:
                        breakdown = protection_info['collision_cost_breakdown']
                        breakdown_total = sum(info.get('total_cost', 0) for info in breakdown.values())
                        print(f"   从breakdown计算总代价: {breakdown_total}")
                        
                        print(f"   活跃保护区:")
                        for zone_id, info in breakdown.items():
                            print(f"     - {zone_id}: {info.get('zone_name', 'Unknown')} = {info.get('total_cost', 0)}")
            
            # 检查基准算法的碰撞代价
            if 'baseline' in result:
                baseline = result['baseline']
                collision_cost = baseline.get('collision_cost', 0)
                print(f"\n📊 基准算法:")
                print(f"   API显示碰撞代价: {collision_cost}")
                
                # 检查保护区信息
                if 'metadata' in baseline and baseline['metadata']:
                    metadata = baseline['metadata']
                    if 'protection_zones' in metadata:
                        protection_zones = metadata['protection_zones']
                        if 'collision_cost_breakdown' in protection_zones:
                            breakdown = protection_zones['collision_cost_breakdown']
                            breakdown_total = sum(info.get('total_cost', 0) for info in breakdown.values())
                            print(f"   保护区系统总代价: {breakdown_total}")
                            
                            if abs(collision_cost - breakdown_total) < 0.01:
                                print(f"   ✅ 基准算法碰撞代价已修复！")
                            else:
                                print(f"   ❌ 基准算法碰撞代价未修复")
                            
                            print(f"   活跃保护区:")
                            for zone_id, info in breakdown.items():
                                print(f"     - {zone_id}: {info.get('zone_name', 'Unknown')} = {info.get('total_cost', 0)}")
            
            # 检查算法对比结果
            if 'comparison' in result:
                comparison = result['comparison']
                print(f"\n📈 算法对比结果:")
                
                improved_collision = 0
                baseline_collision = 0
                
                if 'improved_metrics' in comparison:
                    improved_metrics = comparison['improved_metrics']
                    improved_collision = improved_metrics.get('collision_cost', 0)
                    print(f"   改进算法碰撞代价: {improved_collision}")
                
                if 'baseline_metrics' in comparison:
                    baseline_metrics = comparison['baseline_metrics']
                    baseline_collision = baseline_metrics.get('collision_cost', 0)
                    print(f"   基准算法碰撞代价: {baseline_collision}")
                
                if 'improvement_percentages' in comparison:
                    improvements = comparison['improvement_percentages']
                    collision_improvement = improvements.get('collision_cost', 0)
                    print(f"   碰撞代价改进率: {collision_improvement:.1f}%")
                
                # 验证碰撞代价是否在合理范围内
                if improved_collision > 1000 and baseline_collision > 1000:
                    print(f"   ✅ 碰撞代价数值合理（几千到几万级别）")
                else:
                    print(f"   ❌ 碰撞代价数值异常（应该是几千到几万级别）")
            
            return True
            
        else:
            print(f"❌ 算法对比API失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 启动算法对比API碰撞代价测试")
    print("请确保后端服务器正在运行 (http://localhost:5000)")
    
    success = test_api_collision_cost()
    
    if success:
        print(f"\n🎉 算法对比API碰撞代价测试完成")
        print(f"💡 如果显示'已修复'，说明API现在使用了正确的保护区碰撞代价")
        print(f"💡 如果显示'未修复'，说明还需要进一步调试")
    else:
        print(f"\n❌ 算法对比API碰撞代价测试失败")
