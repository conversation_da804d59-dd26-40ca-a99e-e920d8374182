#!/usr/bin/env python3
"""
测试保护区状态更新
验证前端能否正确接收和显示保护区参与运算的状态
"""

def test_protection_zone_status_update():
    """测试保护区状态更新"""
    
    print("🔍 测试保护区状态更新")
    print("=" * 60)
    
    try:
        # 1. 模拟改进算法的保护区检测
        print("📋 1. 模拟改进算法的保护区检测")
        
        from protection_zones import ProtectionZoneManager
        protection_manager = ProtectionZoneManager()
        
        # 模拟一条经过东京站和涩谷的路径
        test_path_points = [
            (139.7673, 35.6812),  # 东京站附近
            (139.7016, 35.6598),  # 涩谷附近
        ]
        
        print(f"   测试路径: 东京站 → 涩谷")
        
        # 检测相关保护区
        relevant_zones = protection_manager.get_zones_for_path(test_path_points, buffer_distance=500)
        
        print(f"   检测到相关保护区: {len(relevant_zones)} 个")
        for zone in relevant_zones:
            print(f"     - {zone.name} (ID: {zone.id}, 类型: {zone.zone_type.value})")
        
        # 2. 模拟碰撞代价计算
        print(f"\n💰 2. 模拟碰撞代价计算")
        
        collision_cost_breakdown = {}
        total_cost = 0.0
        
        for zone in relevant_zones:
            zone_total_cost = 0.0
            waypoints_affected = []
            
            for i, (lng, lat) in enumerate(test_path_points):
                cost = zone.get_collision_cost(lng, lat)
                if cost > 0:
                    zone_total_cost += cost
                    waypoints_affected.append(i)
            
            if zone_total_cost > 0:
                collision_cost_breakdown[zone.id] = {
                    'zone_name': zone.name,
                    'zone_type': zone.zone_type.value,
                    'total_cost': zone_total_cost,
                    'waypoints_affected': waypoints_affected,
                    'average_crash_cost': zone.average_crash_cost
                }
                total_cost += zone_total_cost
                
                print(f"   {zone.name} (ID: {zone.id}): 代价 {zone_total_cost:.4f}")
        
        print(f"   总碰撞代价: {total_cost:.4f}")
        
        # 3. 模拟改进算法响应格式
        print(f"\n📤 3. 模拟改进算法响应格式")
        
        protection_zones_info = {
            'involved_zones': [],
            'total_zones': len(relevant_zones),
            'collision_cost_breakdown': collision_cost_breakdown,
            'total_estimated_cost': total_cost
        }
        
        # 构建涉及的航点信息
        for i, (lng, lat) in enumerate(test_path_points):
            waypoint_zones = []
            for zone in relevant_zones:
                cost = zone.get_collision_cost(lng, lat)
                if cost > 0:
                    waypoint_zones.append({
                        'zone_id': zone.id,
                        'zone_name': zone.name,
                        'zone_type': zone.zone_type.value,
                        'collision_cost': cost,
                        'waypoint_index': i
                    })
            
            if waypoint_zones:
                protection_zones_info['involved_zones'].append({
                    'waypoint_index': i,
                    'waypoint_coords': {'lng': lng, 'lat': lat},
                    'zones': waypoint_zones
                })
        
        print(f"   响应格式构建完成")
        print(f"   - 涉及的航点: {len(protection_zones_info['involved_zones'])} 个")
        print(f"   - 碰撞代价分解: {len(collision_cost_breakdown)} 个保护区")
        
        # 4. 提取活跃保护区ID（前端需要的格式）
        print(f"\n🎯 4. 提取活跃保护区ID")
        
        active_zone_ids = list(collision_cost_breakdown.keys())
        print(f"   活跃保护区ID列表: {active_zone_ids}")
        
        # 5. 模拟前端接收和处理
        print(f"\n🖥️ 5. 模拟前端接收和处理")
        
        # 模拟前端的updateProtectionZoneStatus方法
        def simulate_frontend_update(active_zone_ids):
            print(f"   前端接收到活跃保护区ID: {active_zone_ids}")
            
            # 模拟前端保护区数据
            frontend_zones = [
                {'id': 'tokyo_station', 'name': '东京站'},
                {'id': 'shibuya_crossing', 'name': '涩谷十字路口'},
                {'id': 'ginza_district', 'name': '银座商业区'},
                {'id': 'shinjuku_station', 'name': '新宿站'},
                {'id': 'metro_exit', 'name': '地铁出口'}
            ]
            
            # 处理不同的ID格式（移除frontend_前缀）
            normalized_active_ids = [id.replace('frontend_', '') for id in active_zone_ids]
            print(f"   标准化后的活跃ID: {normalized_active_ids}")
            
            # 更新保护区状态
            updated_zones = []
            for zone in frontend_zones:
                is_active = zone['id'] in normalized_active_ids or zone['id'] in active_zone_ids
                zone['active'] = is_active
                updated_zones.append(zone)
                
                if is_active:
                    print(f"   ✅ {zone['name']} ({zone['id']}) 标记为参与运算")
                else:
                    print(f"   ○ {zone['name']} ({zone['id']}) 未参与运算")
            
            return updated_zones
        
        updated_zones = simulate_frontend_update(active_zone_ids)
        
        # 6. 验证结果
        print(f"\n✅ 6. 验证结果")
        
        active_count = sum(1 for zone in updated_zones if zone['active'])
        print(f"   参与运算的保护区数量: {active_count}")
        print(f"   预期的活跃保护区数量: {len(active_zone_ids)}")
        
        if active_count == len(active_zone_ids):
            print(f"   ✅ 保护区状态更新正确")
        else:
            print(f"   ❌ 保护区状态更新有问题")
        
        # 7. 测试基准算法的ID格式
        print(f"\n⭐ 7. 测试基准算法的ID格式")
        
        # 模拟基准算法返回的ID格式（带frontend_前缀）
        baseline_active_ids = [f"frontend_{zone_id}" for zone_id in active_zone_ids]
        print(f"   基准算法活跃保护区ID: {baseline_active_ids}")
        
        # 测试前端是否能正确处理
        baseline_updated_zones = simulate_frontend_update(baseline_active_ids)
        baseline_active_count = sum(1 for zone in baseline_updated_zones if zone['active'])
        
        print(f"   基准算法参与运算的保护区数量: {baseline_active_count}")
        
        if baseline_active_count == len(active_zone_ids):
            print(f"   ✅ 基准算法ID格式处理正确")
        else:
            print(f"   ❌ 基准算法ID格式处理有问题")
        
        # 8. 生成测试报告
        print(f"\n📊 8. 测试报告")
        
        test_results = {
            'relevant_zones_count': len(relevant_zones),
            'active_zones_count': len(active_zone_ids),
            'total_collision_cost': total_cost,
            'frontend_update_success': active_count == len(active_zone_ids),
            'baseline_format_success': baseline_active_count == len(active_zone_ids),
            'active_zone_names': [collision_cost_breakdown[zone_id]['zone_name'] for zone_id in active_zone_ids]
        }
        
        print(f"   检测到相关保护区: {test_results['relevant_zones_count']} 个")
        print(f"   参与运算的保护区: {test_results['active_zones_count']} 个")
        print(f"   总碰撞代价: {test_results['total_collision_cost']:.4f}")
        print(f"   前端状态更新: {'✅ 成功' if test_results['frontend_update_success'] else '❌ 失败'}")
        print(f"   基准算法格式处理: {'✅ 成功' if test_results['baseline_format_success'] else '❌ 失败'}")
        print(f"   参与运算的保护区: {', '.join(test_results['active_zone_names'])}")
        
        return test_results
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_protection_zone_status_update()
    if result:
        print(f"\n🎉 保护区状态更新测试完成")
        if result['frontend_update_success'] and result['baseline_format_success']:
            print(f"✅ 所有测试通过！前端应该能正确显示保护区参与运算状态")
            print(f"✅ 改进算法和基准算法的ID格式都能正确处理")
        else:
            print(f"⚠️ 部分测试失败，需要进一步调试")
    else:
        print(f"\n❌ 保护区状态更新测试失败")
