#!/usr/bin/env python3
"""
测试A*算法的调试信息
验证路径是否符合常识
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

from algorithms.astar import AStarAlgorithm
from algorithms.data_structures import PathPlanningRequest, Point3D

async def test_astar_with_debug():
    """测试A*算法并查看详细调试信息"""
    
    print("🧪 ===== A*算法调试信息测试 =====")
    print("目标：验证A*算法路径是否符合常识\n")
    
    # 创建A*算法实例
    astar = AStarAlgorithm()
    
    # 创建测试请求
    request = PathPlanningRequest(
        start_point=Point3D(
            lng=139.7673,  # 东京站
            lat=35.6812,
            alt=100.0,
            x=0, y=0, z=100
        ),
        end_point=Point3D(
            lng=139.7734,  # 上野公园
            lat=35.7153,
            alt=100.0,
            x=1000, y=1000, z=100
        ),
        flight_height=100.0,
        safety_distance=30.0,
        max_speed=15.0,
        buildings=[],  # 简化测试，不添加建筑物
        parameters={
            'gridSize': 10,
            'heuristicWeight': 1.0,
            'maxIterations': 10000,
            'allowDiagonal': True,
            'smoothPath': True
        }
    )
    
    print(f"📍 测试场景:")
    print(f"   起点: 东京站 ({request.start_point.lng:.6f}, {request.start_point.lat:.6f})")
    print(f"   终点: 上野公园 ({request.end_point.lng:.6f}, {request.end_point.lat:.6f})")
    print(f"   直线距离: 约4.2公里")
    print(f"   飞行高度: {request.flight_height}米")
    print(f"   安全距离: {request.safety_distance}米")
    print(f"   网格大小: {request.parameters['gridSize']}米")
    print()
    
    try:
        # 执行A*算法
        print("🚀 开始执行A*算法...")
        response = await astar.calculate_path(request)
        
        if response.success:
            print(f"✅ A*算法执行成功!")
            print(f"📊 最终结果:")
            print(f"   路径点数量: {len(response.path)}")
            print(f"   路径长度: {response.path_length:.2f}米")
            print(f"   执行时间: {response.execution_time:.3f}秒")
            
            if hasattr(response, 'collision_cost'):
                print(f"   碰撞代价: {response.collision_cost}")
            
            # 简单的常识性检查
            print(f"\n🧠 常识性检查:")
            
            # 检查路径点数量是否合理
            if 5 <= len(response.path) <= 50:
                print(f"   ✅ 路径点数量合理: {len(response.path)}个")
            else:
                print(f"   ❌ 路径点数量异常: {len(response.path)}个")
            
            # 检查路径长度是否合理（不应该超过直线距离的2倍）
            straight_distance = 4200  # 约4.2公里
            if response.path_length <= straight_distance * 2:
                ratio = response.path_length / straight_distance
                print(f"   ✅ 路径长度合理: {ratio:.2f}倍直线距离")
            else:
                ratio = response.path_length / straight_distance
                print(f"   ❌ 路径过长: {ratio:.2f}倍直线距离")
            
            # 检查起终点是否正确
            if response.path:
                start_path = response.path[0]
                end_path = response.path[-1]
                
                start_diff = abs(start_path.lng - request.start_point.lng) + abs(start_path.lat - request.start_point.lat)
                end_diff = abs(end_path.lng - request.end_point.lng) + abs(end_path.lat - request.end_point.lat)
                
                if start_diff < 0.001 and end_diff < 0.001:
                    print(f"   ✅ 起终点正确")
                else:
                    print(f"   ❌ 起终点偏差过大")
            
        else:
            print(f"❌ A*算法执行失败!")
            print(f"   错误信息: {response.error}")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🧪 ===== 测试完成 =====")

def main():
    """主函数"""
    print("🔍 A*算法调试信息测试")
    print("=" * 50)
    
    # 运行异步测试
    asyncio.run(test_astar_with_debug())

if __name__ == "__main__":
    main()
