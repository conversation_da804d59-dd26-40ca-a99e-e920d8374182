#!/usr/bin/env python3
"""
调试最终代价计算问题
分析为什么各项指标都改进了，但最终代价却退化了77.9%
"""

def debug_final_cost_calculation():
    """调试最终代价计算"""
    
    print("🔍 调试最终代价计算问题")
    print("=" * 60)
    
    # 从用户截图中的数据（假设的具体数值）
    print("📊 从用户截图推测的数据：")
    
    # 假设基准算法的数据
    baseline_data = {
        "path_length": 8247.56,  # 基准算法路径长度
        "turning_cost": 0.14,    # 基准算法转向成本
        "risk_value": 82.67,     # 基准算法风险值
        "collision_cost": 56.13  # 基准算法碰撞代价
    }
    
    # 根据改进率计算改进算法的数据
    improvements = {
        "path_length": -27.4,    # 改进27.4%
        "turning_cost": 91.5,    # 增加91.5%（可能为了避开障碍）
        "risk_value": -27.2,     # 改进27.2%
        "collision_cost": 1.3    # 增加1.3%
    }
    
    # 计算改进算法的数据
    improved_data = {}
    for metric, baseline_value in baseline_data.items():
        improvement_percent = improvements[metric]
        if improvement_percent < 0:  # 改进（减少）
            improved_value = baseline_value * (1 + improvement_percent / 100)
        else:  # 退化（增加）
            improved_value = baseline_value * (1 + improvement_percent / 100)
        improved_data[metric] = improved_value
    
    print(f"基准算法数据：")
    for metric, value in baseline_data.items():
        print(f"  {metric}: {value}")
    
    print(f"\n改进算法数据：")
    for metric, value in improved_data.items():
        improvement = improvements[metric]
        print(f"  {metric}: {value:.2f} (改进率: {improvement:+.1f}%)")
    
    # 使用系统的权重设置
    weights = {
        "alpha": 0.5,   # 风险权重 50%
        "beta": 0.4,    # 碰撞权重 40%
        "gamma": 0.05,  # 长度权重 5%
        "delta": 0.05   # 转向权重 5%
    }
    
    print(f"\n⚖️ 权重设置：")
    for name, weight in weights.items():
        print(f"  {name}: {weight} ({weight*100:.0f}%)")
    
    # 计算参考值（使用系统的动态计算方法）
    references = {
        "risk_reference": max(100.0, baseline_data["risk_value"] * 2.0),
        "collision_reference": max(50.0, baseline_data["collision_cost"] * 2.0),
        "turning_reference": max(30.0, baseline_data["turning_cost"] * 1.5),
        "manhattan_length": baseline_data["path_length"] * 1.5  # 简化估算
    }
    
    print(f"\n📐 参考值：")
    for name, ref_value in references.items():
        print(f"  {name}: {ref_value:.2f}")
    
    # 计算基准算法的最终代价
    def calculate_final_cost(data, weights, references):
        risk_term = weights["alpha"] * (data["risk_value"] / references["risk_reference"])
        collision_term = weights["beta"] * (data["collision_cost"] / references["collision_reference"])
        length_term = weights["gamma"] * (data["path_length"] / references["manhattan_length"])
        turning_term = weights["delta"] * (data["turning_cost"] / references["turning_reference"])
        
        final_cost = risk_term + collision_term + length_term + turning_term
        
        return {
            "risk_term": risk_term,
            "collision_term": collision_term,
            "length_term": length_term,
            "turning_term": turning_term,
            "final_cost": final_cost
        }
    
    print(f"\n🧮 基准算法最终代价计算：")
    baseline_final = calculate_final_cost(baseline_data, weights, references)
    for term, value in baseline_final.items():
        print(f"  {term}: {value:.6f}")
    
    print(f"\n🧮 改进算法最终代价计算：")
    improved_final = calculate_final_cost(improved_data, weights, references)
    for term, value in improved_final.items():
        print(f"  {term}: {value:.6f}")
    
    # 计算各项的改进率
    print(f"\n📈 各项代价改进率：")
    term_improvements = {}
    for term in ["risk_term", "collision_term", "length_term", "turning_term"]:
        baseline_term = baseline_final[term]
        improved_term = improved_final[term]
        if baseline_term > 0:
            improvement = ((baseline_term - improved_term) / baseline_term) * 100
        else:
            improvement = 0
        term_improvements[term] = improvement
        print(f"  {term}: {improvement:+.2f}%")
    
    # 计算最终代价改进率
    final_cost_improvement = ((baseline_final["final_cost"] - improved_final["final_cost"]) / baseline_final["final_cost"]) * 100
    print(f"\n🎯 最终代价改进率: {final_cost_improvement:+.2f}%")
    
    # 分析问题
    print(f"\n🔍 问题分析：")
    print(f"1. 转向成本增加了91.5%，虽然权重只有5%，但绝对影响较大")
    print(f"2. 风险权重高达50%，如果风险值计算有问题，会严重影响最终代价")
    print(f"3. 路径长度改进27.4%，但权重只有5%，影响很小")
    
    # 权重敏感性分析
    print(f"\n⚖️ 权重敏感性分析：")
    print(f"如果使用更平衡的权重：")
    
    balanced_weights = {
        "alpha": 0.25,  # 风险权重 25%
        "beta": 0.25,   # 碰撞权重 25%
        "gamma": 0.25,  # 长度权重 25%
        "delta": 0.25   # 转向权重 25%
    }
    
    baseline_balanced = calculate_final_cost(baseline_data, balanced_weights, references)
    improved_balanced = calculate_final_cost(improved_data, balanced_weights, references)
    balanced_improvement = ((baseline_balanced["final_cost"] - improved_balanced["final_cost"]) / baseline_balanced["final_cost"]) * 100
    
    print(f"  平衡权重下的最终代价改进率: {balanced_improvement:+.2f}%")
    
    return {
        "current_improvement": final_cost_improvement,
        "balanced_improvement": balanced_improvement,
        "weights": weights,
        "baseline_final_cost": baseline_final["final_cost"],
        "improved_final_cost": improved_final["final_cost"]
    }

if __name__ == "__main__":
    try:
        result = debug_final_cost_calculation()
        print(f"\n✅ 调试完成")
        print(f"   当前权重下改进率: {result['current_improvement']:+.2f}%")
        print(f"   平衡权重下改进率: {result['balanced_improvement']:+.2f}%")
        print(f"\n💡 建议：")
        print(f"   1. 检查转向成本计算是否合理（增加91.5%太多）")
        print(f"   2. 考虑调整权重设置，给长度更高的权重")
        print(f"   3. 检查风险值计算是否准确")
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
