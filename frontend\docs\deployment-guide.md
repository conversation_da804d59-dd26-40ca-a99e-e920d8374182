# 🚀 无人机路径规划系统 - 详细部署指南

## 📋 系统架构

### 前端架构
```
┌─────────────────────────────────────────────────────────────┐
│                    前端系统架构                              │
├─────────────────────────────────────────────────────────────┤
│  modern-city.html (主页面)                                  │
│  ├── ModernCityManager (核心管理器)                         │
│  ├── AlgorithmIntegrator (算法集成器)                       │
│  ├── OSMDataLoader (数据加载器)                             │
│  └── Mapbox GL JS (地图引擎)                                │
├─────────────────────────────────────────────────────────────┤
│  算法系统                                                    │
│  ├── PathPlanningAlgorithm (算法基类)                       │
│  ├── StraightLineAlgorithm (直线算法)                       │
│  ├── AStarAlgorithm (A*算法)                                │
│  └── RRTAlgorithm (RRT算法)                                 │
├─────────────────────────────────────────────────────────────┤
│  数据层                                                      │
│  ├── Mapbox API (地图数据)                                  │
│  ├── OpenStreetMap API (建筑数据)                           │
│  └── 本地配置文件 (城市配置)                                │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈
- **前端框架**: 原生JavaScript (ES6+)
- **地图引擎**: Mapbox GL JS v2.15+
- **3D渲染**: WebGL
- **数据源**: OpenStreetMap Overpass API
- **HTTP服务器**: Python http.server / Node.js / Nginx

## 🔧 详细安装步骤

### 步骤1: 环境准备

**检查Node.js版本**
```bash
node --version  # 应该 >= 16.0
npm --version   # 应该 >= 8.0
```

**检查Python版本**
```bash
python --version  # 应该 >= 3.8
# 或者
python3 --version
```

**检查Git**
```bash
git --version
```

### 步骤2: 获取项目代码

**方式A: Git克隆**
```bash
git clone <项目仓库地址>
cd 无人机建模python版
```

**方式B: 下载压缩包**
```bash
# 下载并解压项目文件
unzip 无人机建模python版.zip
cd 无人机建模python版
```

### 步骤3: 配置Mapbox

**3.1 获取Mapbox Access Token**
1. 访问 https://account.mapbox.com/
2. 注册或登录账户
3. 进入 "Access tokens" 页面
4. 点击 "Create a token"
5. 设置Token名称和权限
6. 复制生成的Token

**3.2 配置Token**
编辑 `frontend/config/mapbox-config.js`:
```javascript
const MAPBOX_CONFIG = {
    // 替换为您的Mapbox Access Token
    accessToken: 'pk.eyJ1IjoieW91ci11c2VybmFtZSIsImEiOiJjbGV4YW1wbGUifQ.example_token_string',
    
    // 地图样式 (可选择不同样式)
    style: 'mapbox://styles/mapbox/dark-v10',  // 深色主题
    // style: 'mapbox://styles/mapbox/light-v10',  // 浅色主题
    // style: 'mapbox://styles/mapbox/satellite-v9',  // 卫星图
    
    // 默认视图设置
    center: [139.7670, 35.6814], // 东京皇居坐标
    zoom: 16,
    pitch: 60,
    bearing: -17.6,
    
    // 性能设置
    antialias: true,
    optimizeForTerrain: true
};
```

### 步骤4: 启动服务器

**方式A: Python HTTP服务器 (推荐)**
```bash
# 进入前端目录
cd frontend

# Python 3
python -m http.server 5000

# Python 2 (不推荐)
python -m SimpleHTTPServer 5000

# 指定绑定地址
python -m http.server 5000 --bind 127.0.0.1
```

**方式B: Node.js HTTP服务器**
```bash
# 全局安装http-server
npm install -g http-server

# 进入前端目录
cd frontend

# 启动服务器
http-server -p 5000 -c-1 --cors

# 或使用npx (无需全局安装)
npx http-server -p 5000 -c-1 --cors
```

**方式C: 使用Live Server (VS Code)**
```bash
# 安装VS Code扩展: Live Server
# 右键点击 modern-city.html
# 选择 "Open with Live Server"
```

### 步骤5: 验证部署

**5.1 打开浏览器**
```
http://localhost:5000/modern-city.html
```

**5.2 检查系统状态**
- 等待页面加载完成
- 检查控制台是否有错误信息
- 确认地图正常显示
- 验证3D建筑是否可见

**5.3 运行部署测试**
```
http://localhost:5000/test-deployment.html
```
点击 "🚀 运行全部测试" 进行完整测试。

## 🌐 生产环境部署

### Nginx配置

**创建Nginx配置文件** `/etc/nginx/sites-available/drone-system`:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 网站根目录
    root /var/www/drone-system/frontend;
    index modern-city.html;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';" always;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 主页面
    location / {
        try_files $uri $uri/ =404;
        
        # 禁用缓存HTML文件
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # JSON配置文件
    location ~* \.json$ {
        expires 1h;
        add_header Cache-Control "public";
    }
    
    # 日志配置
    access_log /var/log/nginx/drone-system.access.log;
    error_log /var/log/nginx/drone-system.error.log;
}
```

**启用站点**
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/drone-system /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重新加载Nginx
sudo systemctl reload nginx
```

### Apache配置

**创建虚拟主机配置** `/etc/apache2/sites-available/drone-system.conf`:
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    ServerAlias www.your-domain.com
    Redirect permanent / https://your-domain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName your-domain.com
    ServerAlias www.your-domain.com
    
    DocumentRoot /var/www/drone-system/frontend
    DirectoryIndex modern-city.html
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key
    
    # 安全头
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    
    # 压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
    
    # 缓存配置
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </FilesMatch>
    
    <FilesMatch "\.html$">
        ExpiresActive On
        ExpiresDefault "access plus 0 seconds"
    </FilesMatch>
    
    # 日志
    CustomLog /var/log/apache2/drone-system.access.log combined
    ErrorLog /var/log/apache2/drone-system.error.log
</VirtualHost>
```

### Docker部署

**创建Dockerfile**:
```dockerfile
FROM nginx:alpine

# 复制网站文件
COPY frontend/ /usr/share/nginx/html/

# 复制Nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
```

**创建docker-compose.yml**:
```yaml
version: '3.8'

services:
  drone-system:
    build: .
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
      - ./ssl:/etc/nginx/ssl:ro
    environment:
      - NGINX_HOST=your-domain.com
      - NGINX_PORT=80
    restart: unless-stopped

  # 可选: 添加SSL证书自动更新
  certbot:
    image: certbot/certbot
    volumes:
      - ./ssl:/etc/letsencrypt
      - ./frontend:/var/www/html
    command: certonly --webroot --webroot-path=/var/www/html --email <EMAIL> --agree-tos --no-eff-email -d your-domain.com
```

**部署命令**:
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 🔍 性能优化

### 前端优化

**1. 资源压缩**
```bash
# 安装压缩工具
npm install -g uglify-js clean-css-cli html-minifier

# 压缩JavaScript
uglifyjs js/modern-city-manager.js -o js/modern-city-manager.min.js -c -m

# 压缩CSS
cleancss -o styles.min.css styles.css

# 压缩HTML
html-minifier --collapse-whitespace --remove-comments modern-city.html -o modern-city.min.html
```

**2. 图片优化**
```bash
# 安装图片优化工具
npm install -g imagemin-cli

# 优化图片
imagemin images/*.png --out-dir=images/optimized --plugin=pngquant
```

**3. 启用Service Worker缓存**
```javascript
// 在modern-city.html中添加
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js')
        .then(registration => console.log('SW registered'))
        .catch(error => console.log('SW registration failed'));
}
```

### 服务器优化

**1. 启用HTTP/2**
```nginx
listen 443 ssl http2;
```

**2. 配置CDN**
```html
<!-- 使用CDN加载Mapbox -->
<script src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js"></script>
<link href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css" rel="stylesheet">
```

**3. 数据库缓存**
```javascript
// 实现本地存储缓存
const cacheKey = 'osm-data-' + bounds.toString();
const cachedData = localStorage.getItem(cacheKey);
if (cachedData) {
    return JSON.parse(cachedData);
}
```

## 🛡️ 安全配置

### API密钥保护

**1. 环境变量**
```bash
# 设置环境变量
export MAPBOX_ACCESS_TOKEN="your-token-here"
```

**2. 服务器端代理**
```javascript
// 通过后端API代理Mapbox请求
fetch('/api/mapbox-proxy', {
    method: 'POST',
    body: JSON.stringify({ endpoint: 'styles/v1/mapbox/dark-v10' })
});
```

### 访问控制

**1. IP白名单**
```nginx
# 限制访问IP
allow ***********/24;
allow 10.0.0.0/8;
deny all;
```

**2. 速率限制**
```nginx
# 限制请求频率
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req zone=api burst=20 nodelay;
```

## 📊 监控和日志

### 系统监控

**1. Nginx状态监控**
```nginx
location /nginx_status {
    stub_status on;
    access_log off;
    allow 127.0.0.1;
    deny all;
}
```

**2. 应用性能监控**
```javascript
// 添加性能监控
const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
        console.log(`${entry.name}: ${entry.duration}ms`);
    }
});
observer.observe({entryTypes: ['measure', 'navigation']});
```

### 日志分析

**1. 访问日志分析**
```bash
# 分析访问日志
tail -f /var/log/nginx/drone-system.access.log | grep "modern-city.html"

# 统计访问量
awk '{print $1}' /var/log/nginx/drone-system.access.log | sort | uniq -c | sort -nr
```

**2. 错误日志监控**
```bash
# 监控错误日志
tail -f /var/log/nginx/drone-system.error.log

# 设置日志轮转
logrotate -d /etc/logrotate.d/nginx
```

## 🚨 故障排除

### 常见部署问题

**1. 端口占用**
```bash
# 检查端口占用
netstat -tlnp | grep :5000
lsof -i :5000

# 杀死占用进程
kill -9 <PID>
```

**2. 权限问题**
```bash
# 设置正确权限
sudo chown -R www-data:www-data /var/www/drone-system
sudo chmod -R 755 /var/www/drone-system
```

**3. 防火墙配置**
```bash
# Ubuntu/Debian
sudo ufw allow 80
sudo ufw allow 443

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 性能问题诊断

**1. 网络延迟测试**
```bash
# 测试Mapbox API延迟
curl -w "@curl-format.txt" -o /dev/null -s "https://api.mapbox.com/styles/v1/mapbox/dark-v10"

# 测试OSM API延迟
curl -w "@curl-format.txt" -o /dev/null -s "https://overpass-api.de/api/interpreter"
```

**2. 浏览器性能分析**
```javascript
// 在浏览器控制台运行
console.time('page-load');
window.addEventListener('load', () => {
    console.timeEnd('page-load');
});
```

---

**🎉 按照本指南完成部署后，您将拥有一个高性能、安全可靠的3D无人机路径规划系统！**
