"""
无人机路径规划系统配置文件
"""
import os

class Config:
    """基础配置类"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-for-drone-pathfinding'
    DEBUG = True
    
    # 静态文件配置
    STATIC_FOLDER = '../frontend'
    STATIC_URL_PATH = ''
    
    # 数据文件路径
    DATA_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
    TOKYO23_DATA_PATH = os.path.join(DATA_PATH, 'tokyo23')
    
    # 算法参数配置
    ALGORITHM_CONFIG = {
        'astar': {
            'grid_size': 10,  # 网格大小（米）
            'max_iterations': 10000,
            'heuristic_weight': 1.0
        },
        'rrt_star': {
            'max_iterations': 5000,
            'step_size': 20,
            'goal_radius': 30
        },
        'path_optimization': {
            'max_turn_angle': 90,  # 最大转向角度
            'risk_edge_distance': 50,  # 风险边缘距离
            'collision_check_radius': 30,  # 碰撞检测半径
            'k_value': 5  # 指数变化速率控制参数
        }
    }
    
    # 数学模型参数
    MATH_MODEL_PARAMS = {
        'weights': {
            'alpha_base': 0.6,  # 风险权重基础值
            'beta_base': 0.3,   # 碰撞代价权重基础值
            'gamma_base': 0.7,  # 路径长度权重基础值
            'delta_base': 0.2   # 转向成本权重基础值
        },
        'risk_params': {
            'building_risk': 1.0,
            'risk_decay_factor': 0.05,
            'point_check_radius': 30
        }
    }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SECRET_KEY = os.environ.get('SECRET_KEY')

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
