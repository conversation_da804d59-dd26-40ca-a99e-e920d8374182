#!/usr/bin/env python3
"""
测试总体评估逻辑是否正确
验证当最终代价增加时，评估文本应该显示"退化"而不是"改进"
"""

def test_evaluation_logic():
    """测试评估逻辑"""
    
    print("🧪 测试总体评估逻辑")
    print("=" * 50)
    
    # 模拟测试数据
    test_cases = [
        {
            "name": "改进算法表现更好",
            "baseline_final_cost": 100.0,
            "improved_final_cost": 80.0,
            "expected_improvement": 20.0,  # (100-80)/100*100 = 20%
            "expected_status": "改进"
        },
        {
            "name": "改进算法表现更差",
            "baseline_final_cost": 100.0,
            "improved_final_cost": 177.9,  # 增加77.9%
            "expected_improvement": -77.9,  # (100-177.9)/100*100 = -77.9%
            "expected_status": "退化"
        },
        {
            "name": "两算法表现相近",
            "baseline_final_cost": 100.0,
            "improved_final_cost": 102.0,
            "expected_improvement": -2.0,  # (100-102)/100*100 = -2%
            "expected_status": "退化"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试案例 {i}: {case['name']}")
        print(f"   基准算法最终代价: {case['baseline_final_cost']}")
        print(f"   改进算法最终代价: {case['improved_final_cost']}")
        
        # 计算改进率（使用与系统相同的公式）
        baseline_value = case['baseline_final_cost']
        improved_value = case['improved_final_cost']
        
        if baseline_value > 0:
            improvement_percent = ((baseline_value - improved_value) / baseline_value) * 100
        else:
            improvement_percent = 0.0
        
        print(f"   计算的改进率: {improvement_percent:.1f}%")
        print(f"   期望的改进率: {case['expected_improvement']:.1f}%")
        
        # 判断总体表现
        is_overall_improved = improvement_percent > 0
        overall_status = "改进" if is_overall_improved else "退化"
        
        print(f"   计算的状态: {overall_status}")
        print(f"   期望的状态: {case['expected_status']}")
        
        # 生成评估文本
        if is_overall_improved:
            evaluation_text = f"改进分簇算法相比基准A*算法，最终代价降低{improvement_percent:.1f}%"
        else:
            evaluation_text = f"改进分簇算法相比基准A*算法，最终代价增加{abs(improvement_percent):.1f}%"
        
        print(f"   生成的评估文本: {evaluation_text}")
        
        # 验证结果
        improvement_correct = abs(improvement_percent - case['expected_improvement']) < 0.1
        status_correct = overall_status == case['expected_status']
        
        if improvement_correct and status_correct:
            print(f"   ✅ 测试通过")
        else:
            print(f"   ❌ 测试失败")
            if not improvement_correct:
                print(f"      改进率计算错误: 期望{case['expected_improvement']:.1f}%, 实际{improvement_percent:.1f}%")
            if not status_correct:
                print(f"      状态判断错误: 期望{case['expected_status']}, 实际{overall_status}")
    
    print(f"\n🎯 关键测试：用户反馈的案例")
    print(f"   如果最终代价增加77.9%，应该显示：")
    print(f"   ✅ 正确: '改进分簇算法相比基准A*算法，最终代价增加77.9%'")
    print(f"   ❌ 错误: '改进算法能低于基准算法，最终代价增加77.9%'")
    
    return True

def test_backend_summary_logic():
    """测试后端summary生成逻辑"""
    
    print(f"\n🔧 测试后端summary生成逻辑")
    print("=" * 30)
    
    # 模拟后端的summary生成逻辑
    def generate_summary(overall_improvement):
        if overall_improvement > 5:
            return f"改进算法显著优于基准算法，最终代价降低{overall_improvement:.1f}%"
        elif overall_improvement > 0:
            return f"改进算法略优于基准算法，最终代价降低{overall_improvement:.1f}%"
        else:
            return f"改进算法性能需要优化，最终代价增加{abs(overall_improvement):.1f}%"
    
    test_cases = [
        {"improvement": 15.5, "expected": "显著优于"},
        {"improvement": 2.3, "expected": "略优于"},
        {"improvement": -77.9, "expected": "需要优化"},
        {"improvement": -5.2, "expected": "需要优化"}
    ]
    
    for case in test_cases:
        summary = generate_summary(case["improvement"])
        print(f"   改进率: {case['improvement']:6.1f}% -> {summary}")
        
        if case["expected"] in summary:
            print(f"   ✅ 正确")
        else:
            print(f"   ❌ 错误")

if __name__ == "__main__":
    try:
        test_evaluation_logic()
        test_backend_summary_logic()
        print(f"\n✅ 所有测试完成")
        print(f"\n📝 修复总结:")
        print(f"   1. 前端评估文本已修复：使用'最终代价降低/增加'而不是'平均性能改进/退化'")
        print(f"   2. 后端summary逻辑已修复：使用'最终代价降低/增加'而不是'平均改进/退化'")
        print(f"   3. 逻辑一致性：负值表示退化，正值表示改进")
        print(f"   4. 消除了自相矛盾的文本：不再出现'能低于基准算法，但代价增加'的错误表述")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
