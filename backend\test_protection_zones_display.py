#!/usr/bin/env python3
"""
测试保护区信息展示功能
验证前端能够正确展示路径规划中涉及的保护区信息
"""

import sys
import os
import json
import asyncio
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

from algorithms.data_structures import PathPlanningRequest, Point3D
from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedAlgorithm

async def test_protection_zones_display():
    """测试保护区信息展示功能"""
    
    print("=" * 60)
    print("保护区信息展示功能测试")
    print("=" * 60)
    
    # 1. 创建测试请求
    print("\n1. 创建测试路径规划请求")
    print("-" * 40)
    
    # 设置起点和终点（东京地区，会涉及多个保护区）
    start_point = Point3D(lng=139.7500, lat=35.6800, alt=120)  # 商业区附近
    end_point = Point3D(lng=139.7625, lat=35.7126, alt=120)    # 东京大学附近
    
    print(f"起点: ({start_point.lng}, {start_point.lat}, {start_point.alt}m)")
    print(f"终点: ({end_point.lng}, {end_point.lat}, {end_point.alt}m)")
    
    # 创建请求
    request = PathPlanningRequest()
    request.start_point = start_point
    request.end_point = end_point
    request.algorithm = "ImprovedClusterBased"
    request.parameters = {
        'flightHeight': 120,
        'safetyDistance': 30,
        'maxTurnAngle': 90,
        'riskEdgeDistance': 50,
        'kValue': 5,
        'enablePathSwitching': True,
        'collisionThreshold': 20,
        'smoothingMethod': 'cubic_spline',
        'smoothingFactor': 0.5,
        'minSegmentLength': 5.0
    }
    request.buildings = []  # 简化测试，不添加建筑物
    
    # 2. 执行路径规划
    print("\n2. 执行改进分簇算法")
    print("-" * 40)
    
    algorithm = ImprovedClusterBasedAlgorithm()
    
    try:
        response = await algorithm.calculate_path(request)
        
        if not response.success:
            print(f"❌ 路径规划失败: {response.error}")
            return
        
        print(f"✅ 路径规划成功")
        print(f"📍 路径点数: {len(response.path)}")
        print(f"📏 路径长度: {response.path_length:.2f}米")
        
        # 3. 检查保护区信息
        print("\n3. 检查保护区信息")
        print("-" * 40)
        
        if hasattr(response, 'protection_zones_info') and response.protection_zones_info:
            zones_info = response.protection_zones_info
            print(f"✅ 保护区信息已收集")
            
            # 显示基本统计
            summary = zones_info.get('summary', {})
            print(f"涉及保护区数量: {summary.get('zones_count', 0)}")
            print(f"影响航点数量: {summary.get('waypoints_affected', 0)}")
            print(f"总航点数量: {summary.get('total_waypoints', 0)}")
            print(f"覆盖率: {summary.get('coverage_percentage', 0)}%")
            print(f"总估计碰撞代价: {zones_info.get('total_estimated_collision_cost', 0)}")
            
            # 显示参考值
            reference_values = zones_info.get('reference_values', {})
            if reference_values:
                print(f"\n参考值信息:")
                for key, value in reference_values.items():
                    print(f"  {key}: {value}")
            
            # 显示保护区详细信息
            breakdown = zones_info.get('collision_cost_breakdown', {})
            if breakdown:
                print(f"\n涉及的保护区详情:")
                for zone_id, zone_data in breakdown.items():
                    print(f"  {zone_data['zone_name']} ({zone_data['zone_type']}):")
                    print(f"    碰撞代价贡献: {zone_data['total_cost']:.4f}")
                    print(f"    平均碰撞代价: {zone_data['average_crash_cost']:.6f}/m²")
                    print(f"    影响航点: {len(zone_data['waypoints_affected'])} 个")
            
            # 4. 生成前端展示用的JSON
            print("\n4. 生成前端展示用的JSON数据")
            print("-" * 40)
            
            # 模拟前端需要的完整响应数据
            frontend_response = {
                'success': response.success,
                'path': [
                    {
                        'lat': point.lat,
                        'lng': point.lng,
                        'alt': point.alt
                    } for point in response.path
                ],
                'pathLength': response.path_length,
                'executionTime': response.execution_time,
                'metadata': response.metadata,
                'protectionZonesInfo': zones_info
            }
            
            # 保存到文件供前端测试使用
            output_file = backend_dir / 'test_protection_zones_response.json'
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(frontend_response, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 前端测试数据已保存到: {output_file}")
            
            # 5. 验证数据完整性
            print("\n5. 验证数据完整性")
            print("-" * 40)
            
            required_fields = [
                'involved_zones', 'total_zones', 'collision_cost_breakdown',
                'reference_values', 'total_estimated_collision_cost', 'summary'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in zones_info:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"⚠️ 缺少字段: {missing_fields}")
            else:
                print(f"✅ 所有必需字段都存在")
            
            # 检查数据类型
            type_checks = [
                ('involved_zones', list),
                ('total_zones', int),
                ('collision_cost_breakdown', dict),
                ('reference_values', dict),
                ('summary', dict)
            ]
            
            type_errors = []
            for field, expected_type in type_checks:
                if field in zones_info and not isinstance(zones_info[field], expected_type):
                    type_errors.append(f"{field} 应该是 {expected_type.__name__}")
            
            if type_errors:
                print(f"⚠️ 类型错误: {type_errors}")
            else:
                print(f"✅ 所有字段类型正确")
            
            print(f"\n✅ 保护区信息展示功能测试完成")
            print(f"📊 数据已准备好供前端展示")
            
        else:
            print(f"❌ 响应中没有保护区信息")
            print(f"响应属性: {dir(response)}")
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

def main():
    """主函数"""
    asyncio.run(test_protection_zones_display())

if __name__ == "__main__":
    main()
