#!/usr/bin/env python3
"""
测试保护区API
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_protection_zones_import():
    """测试保护区模块导入"""
    print("🔍 测试保护区模块导入")
    
    try:
        from protection_zones import ProtectionZoneManager
        print("✅ ProtectionZoneManager 导入成功")
        
        # 测试初始化
        manager = ProtectionZoneManager()
        print(f"✅ 保护区管理器初始化成功，保护区数量: {len(manager.zones)}")
        
        # 测试to_dict方法
        zone_data = manager.to_dict()
        print(f"✅ to_dict方法正常，返回数据包含: {list(zone_data.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 保护区模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_protection_zones_api():
    """测试保护区API"""
    print("\n🔍 测试保护区API")
    
    try:
        # 修复导入路径
        sys.path.insert(0, os.path.join(current_dir, 'api'))
        
        from api.protection_zones import protection_zones_bp, protection_zone_manager
        print("✅ 保护区API蓝图导入成功")
        
        # 测试保护区管理器
        print(f"✅ API中的保护区管理器，保护区数量: {len(protection_zone_manager.zones)}")
        
        # 测试to_dict方法
        zone_data = protection_zone_manager.to_dict()
        print(f"✅ API中的to_dict方法正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 保护区API导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_protection_zones_api():
    """创建简化的保护区API响应"""
    print("\n🔧 创建简化的保护区API响应")
    
    try:
        from protection_zones import ProtectionZoneManager
        manager = ProtectionZoneManager()
        
        # 生成API响应格式
        zones_data = []
        for zone in manager.zones:
            zones_data.append({
                'id': zone.id,
                'name': zone.name,
                'type': zone.zone_type.value,
                'center': zone.center,
                'radius': zone.radius,
                'average_crash_cost': zone.average_crash_cost,
                'collision_cost_factor': zone.collision_cost_factor,
                'description': zone.description
            })
        
        api_response = {
            'success': True,
            'zones': zones_data,
            'statistics': {
                'total_zones': len(zones_data),
                'by_type': {},
                'high_risk_zones': [],
                'coverage_area': 0.0
            },
            'message': '保护区信息获取成功'
        }
        
        print(f"✅ 生成API响应成功，包含 {len(zones_data)} 个保护区")
        return api_response
        
    except Exception as e:
        print(f"❌ 生成API响应失败: {e}")
        return None

def test_flask_app():
    """测试Flask应用中的保护区API"""
    print("\n🔍 测试Flask应用中的保护区API")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            response = client.get('/api/protection-zones/info')
            print(f"✅ API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"✅ API响应成功，包含 {len(data.get('zones', []))} 个保护区")
                return True
            else:
                print(f"❌ API响应失败: {response.status_code}")
                print(f"响应内容: {response.get_data(as_text=True)}")
                return False
                
    except Exception as e:
        print(f"❌ Flask应用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 保护区API测试")
    print("=" * 50)
    
    # 1. 测试保护区模块导入
    import_success = test_protection_zones_import()
    
    # 2. 测试保护区API导入
    api_success = test_protection_zones_api()
    
    # 3. 创建简化响应
    simple_response = create_simple_protection_zones_api()
    
    # 4. 测试Flask应用
    flask_success = test_flask_app()
    
    print("\n📊 测试结果总结")
    print("=" * 50)
    print(f"保护区模块导入: {'✅ 成功' if import_success else '❌ 失败'}")
    print(f"保护区API导入: {'✅ 成功' if api_success else '❌ 失败'}")
    print(f"简化响应生成: {'✅ 成功' if simple_response else '❌ 失败'}")
    print(f"Flask应用测试: {'✅ 成功' if flask_success else '❌ 失败'}")
    
    if not flask_success and simple_response:
        print("\n💡 建议：如果Flask API失败，可以使用简化响应数据")
        print("简化响应示例:")
        print(f"  保护区数量: {len(simple_response['zones'])}")
        print(f"  响应格式: {list(simple_response.keys())}")
