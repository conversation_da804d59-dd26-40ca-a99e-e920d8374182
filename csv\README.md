# 路径数据导出文件夹

## 📊 自动导出说明

这个文件夹用于存放**路径规划完成后自动生成的学术验证数据**。

### 🚀 如何触发导出

1. 在前端页面进行**算法对比**
2. 等待路径规划完成
3. 系统会自动在此文件夹生成以下文件：

```
csv/
├── path_metrics_comparison_YYYYMMDD_HHMMSS.csv     # 路径指标对比
├── path_coordinates_YYYYMMDD_HHMMSS.csv            # 路径坐标数据
├── algorithm_metadata_YYYYMMDD_HHMMSS.json         # 算法元数据
├── protection_zones_analysis_YYYYMMDD_HHMMSS.csv   # 保护区分析
├── statistical_analysis_YYYYMMDD_HHMMSS.csv        # 统计分析
└── export_report_YYYYMMDD_HHMMSS.txt               # 导出报告
```

### 📋 数据内容

#### 1. 路径指标对比 (path_metrics_comparison_*.csv)
- **82条路径**：1条基准路径 + 81条改进路径
- **四个核心指标**：风险值、碰撞代价、路径长度、转向成本
- **参考值**：每个指标的标准化参考值
- **标准化指标**：归一化后的指标值
- **最终代价**：公式14计算的综合代价

#### 2. 路径坐标数据 (path_coordinates_*.csv)
- **详细航点**：每条路径的所有航点坐标
- **航点属性**：碰撞代价、风险值、转向角度等

#### 3. 算法元数据 (algorithm_metadata_*.json)
- **实验配置**：算法参数、权重设置
- **环境信息**：起终点、建筑物、保护区数量
- **性能指标**：执行时间、内存使用

#### 4. 保护区分析 (protection_zones_analysis_*.csv)
- **保护区影响**：每个保护区对各路径的影响
- **碰撞代价贡献**：保护区的具体贡献值

#### 5. 统计分析 (statistical_analysis_*.csv)
- **基准vs改进**：两种算法的统计对比
- **最优路径**：最佳改进路径的详细信息
- **分布统计**：均值、标准差、最值等

### 📈 数据分析示例

#### 使用Excel分析
1. 打开 `path_metrics_comparison_*.csv`
2. 筛选 `path_type` 列：
   - `baseline`：基准路径
   - `improved`：改进路径
3. 对比 `final_cost` 列查看性能提升

#### 使用Python分析
```python
import pandas as pd

# 读取数据
df = pd.read_csv('path_metrics_comparison_*.csv')

# 基准vs改进对比
baseline = df[df['path_type'] == 'baseline']
improved = df[df['path_type'] == 'improved']

print(f"基准算法最终代价: {baseline['final_cost'].iloc[0]}")
print(f"改进算法平均代价: {improved['final_cost'].mean():.2f}")
print(f"改进算法最优代价: {improved['final_cost'].min():.2f}")
print(f"性能提升: {((baseline['final_cost'].iloc[0] - improved['final_cost'].min()) / baseline['final_cost'].iloc[0] * 100):.1f}%")
```

### 🎯 学术应用

这些数据可用于：
- **论文写作**：提供定量分析数据
- **算法验证**：证明改进算法的有效性
- **实验重现**：完整的参数和环境记录
- **性能对比**：多维度的算法性能评估

### 📝 注意事项

1. **文件命名**：使用时间戳确保唯一性
2. **数据完整性**：包含所有实验配置和结果
3. **格式标准**：CSV和JSON标准格式，便于分析
4. **自动生成**：无需手动操作，完全自动化

---

**每次进行算法对比后，都会在此文件夹自动生成完整的学术验证数据！** 📊✨
