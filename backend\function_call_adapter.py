#!/usr/bin/env python3
"""
函数调用适配器
提供新旧接口之间的兼容性，确保平滑过渡到标准化参数
"""

import asyncio
from typing import Dict, Any, List, Optional
from algorithms.data_structures import PathPlanningRequest, PathPlanningResponse, Point3D
from variable_mapper import map_frontend_to_backend


class FunctionCallAdapter:
    """函数调用适配器类"""
    
    def __init__(self, algorithm_instance):
        """
        初始化适配器
        
        Args:
            algorithm_instance: 算法实例
        """
        self.algorithm = algorithm_instance
    
    async def calculate_path_with_request(self, request: PathPlanningRequest) -> PathPlanningResponse:
        """
        使用PathPlanningRequest调用标准化的calculate_path函数
        
        Args:
            request: 路径规划请求对象
            
        Returns:
            PathPlanningResponse: 路径规划响应
        """
        try:
            print(f"🔧 ADAPTER: calculate_path_with_request被调用")
            print(f"🔧 ADAPTER: algorithm类型: {type(self.algorithm)}")
            print(f"🔧 ADAPTER: 是否有_original_calculate_path: {hasattr(self.algorithm, '_original_calculate_path')}")

            # 将PathPlanningRequest转换为标准化参数
            standard_params = self._convert_request_to_standard_params(request)
            print(f"🔧 ADAPTER: 标准化参数转换完成，参数数量: {len(standard_params)}")

            # 调用标准化的calculate_path函数
            if hasattr(self.algorithm, '_original_calculate_path'):
                # 如果有原始方法，直接用request调用（不转换参数）
                print(f"🔧 ADAPTER: 调用原始方法 _original_calculate_path (使用request)")
                result = await self.algorithm._original_calculate_path(request)
            else:
                # 否则直接调用当前的calculate_path方法
                print(f"🔧 ADAPTER: 调用当前的 calculate_path 方法")
                result = await self.algorithm.calculate_path(**standard_params)

            print(f"🔧 ADAPTER: 算法调用完成，结果类型: {type(result)}")

            # 检查结果类型，如果已经是PathPlanningResponse，直接返回
            if isinstance(result, PathPlanningResponse):
                print(f"🔧 ADAPTER: 结果已经是PathPlanningResponse对象，直接返回")
                return result

            # 如果是字典，则转换为PathPlanningResponse
            response = self._convert_standard_result_to_response(result, request)
            print(f"🔧 ADAPTER: 响应转换完成，success: {getattr(response, 'success', 'N/A')}")

            return response
            
        except Exception as e:
            # 返回错误响应
            response = PathPlanningResponse()
            response.success = False
            response.error = f"路径规划失败: {str(e)}"
            return response
    
    def _convert_request_to_standard_params(self, request: PathPlanningRequest) -> Dict[str, Any]:
        """
        将PathPlanningRequest转换为标准化参数
        
        Args:
            request: 路径规划请求
            
        Returns:
            标准化参数字典
        """
        # 提取基本参数
        params = {
            'start_point': {
                'x': request.start_point.x,
                'y': request.start_point.y,
                'z': request.start_point.z,
                'lng': request.start_point.lng,
                'lat': request.start_point.lat,
                'alt': request.start_point.alt
            },
            'end_point': {
                'x': request.end_point.x,
                'y': request.end_point.y,
                'z': request.end_point.z,
                'lng': request.end_point.lng,
                'lat': request.end_point.lat,
                'alt': request.end_point.alt
            },
            'flight_height': request.flight_height
        }
        
        # 添加可选参数
        if hasattr(request, 'safety_distance') and request.safety_distance is not None:
            params['safety_distance'] = request.safety_distance
        
        if hasattr(request, 'max_turn_angle') and request.max_turn_angle is not None:
            params['max_turn_angle'] = request.max_turn_angle
        
        if hasattr(request, 'buildings') and request.buildings:
            params['buildings'] = request.buildings
        
        if hasattr(request, 'protection_zones') and request.protection_zones:
            params['protection_zones'] = request.protection_zones
        
        # 从parameters中提取算法参数
        if hasattr(request, 'parameters') and request.parameters:
            if 'kValue' in request.parameters:
                params['k_value'] = request.parameters['kValue']
            
            if 'enablePathSwitching' in request.parameters:
                params['enable_path_switching'] = request.parameters['enablePathSwitching']
        
        return params
    
    def _convert_standard_result_to_response(self, result: Dict[str, Any], 
                                           original_request: PathPlanningRequest) -> PathPlanningResponse:
        """
        将标准化结果转换为PathPlanningResponse
        
        Args:
            result: 标准化结果
            original_request: 原始请求
            
        Returns:
            PathPlanningResponse对象
        """
        response = PathPlanningResponse()
        
        # 基本状态
        response.success = result.get('success', False)
        response.error = result.get('error')
        
        # 路径数据
        if result.get('path'):
            response.path = []
            for waypoint_data in result['path']:
                point = Point3D(
                    lng=waypoint_data.get('lng', 0),
                    lat=waypoint_data.get('lat', 0),
                    alt=waypoint_data.get('alt', 0),
                    x=waypoint_data.get('x', 0),
                    y=waypoint_data.get('y', 0),
                    z=waypoint_data.get('z', 0)
                )
                response.path.append(point)
        
        # 路径长度和执行时间
        response.path_length = result.get('pathLength', result.get('path_length', 0))
        response.execution_time = result.get('executionTime', result.get('execution_time', 0))
        response.estimated_flight_time = result.get('estimatedFlightTime', result.get('estimated_flight_time', 0))

        # 元数据
        response.metadata = {
            'final_cost': result.get('total_cost', 0),
            'execution_time': result.get('executionTime', result.get('execution_time', 0)),
            'path_length': result.get('pathLength', result.get('path_length', 0)),
            'turning_cost': result.get('turning_cost', 0),
            'risk_value': result.get('risk_value', 0),
            'collision_cost': result.get('collision_cost', 0),
            'algorithm_type': 'improved_cluster_based'
        }
        
        # 算法元数据
        if result.get('algorithm_metadata'):
            response.metadata.update(result['algorithm_metadata'])
        
        return response


class LegacyFunctionWrapper:
    """遗留函数包装器，提供向后兼容性"""
    
    def __init__(self, algorithm_instance):
        """
        初始化包装器
        
        Args:
            algorithm_instance: 算法实例
        """
        self.algorithm = algorithm_instance
        self.adapter = FunctionCallAdapter(algorithm_instance)
    
    async def calculate_path(self, request: PathPlanningRequest) -> PathPlanningResponse:
        """
        兼容旧版本的calculate_path调用
        
        Args:
            request: 路径规划请求
            
        Returns:
            PathPlanningResponse: 路径规划响应
        """
        return await self.adapter.calculate_path_with_request(request)
    
    def calculate_final_cost_legacy(self, path_length: float, turning_cost: float,
                                  risk_value: float, collision_cost: float,
                                  risk_reference: float = None, collision_reference: float = None,
                                  turning_reference: float = None, **kwargs) -> float:
        """
        兼容旧版本的calculate_final_cost调用
        
        Args:
            path_length: 路径长度
            turning_cost: 转向成本
            risk_value: 风险值
            collision_cost: 碰撞代价
            risk_reference: 风险参考值
            collision_reference: 碰撞代价参考值
            turning_reference: 转向成本参考值
            **kwargs: 其他参数（忽略）
            
        Returns:
            最终代价
        """
        # 调用标准化的函数，忽略不支持的参数
        return self.algorithm.cost_calculator.calculate_final_cost(
            path_length=path_length,
            turning_cost=turning_cost,
            risk_value=risk_value,
            collision_cost=collision_cost,
            risk_reference=risk_reference,
            collision_reference=collision_reference,
            turning_reference=turning_reference
        )


def create_adapter_for_algorithm(algorithm_instance):
    """
    为算法实例创建适配器
    
    Args:
        algorithm_instance: 算法实例
        
    Returns:
        包装后的算法实例
    """
    # 如果算法已经有标准化的calculate_path方法，使用适配器
    if hasattr(algorithm_instance, 'calculate_path'):
        # 保存原始方法
        if not hasattr(algorithm_instance, '_original_calculate_path'):
            algorithm_instance._original_calculate_path = algorithm_instance.calculate_path
        
        # 创建适配器
        adapter = FunctionCallAdapter(algorithm_instance)
        
        # 替换方法
        algorithm_instance.calculate_path = adapter.calculate_path_with_request
    
    return algorithm_instance


def apply_legacy_compatibility(algorithm_instance):
    """
    为算法实例应用遗留兼容性
    
    Args:
        algorithm_instance: 算法实例
        
    Returns:
        应用兼容性后的算法实例
    """
    wrapper = LegacyFunctionWrapper(algorithm_instance)
    
    # 添加兼容性方法
    algorithm_instance.calculate_path_legacy = wrapper.calculate_path

    if hasattr(algorithm_instance, 'cost_calculator') and algorithm_instance.cost_calculator:
        algorithm_instance.cost_calculator.calculate_final_cost_legacy = wrapper.calculate_final_cost_legacy
    
    return algorithm_instance


# 便捷函数
def wrap_algorithm_for_compatibility(algorithm_instance):
    """
    为算法实例添加完整的兼容性支持
    
    Args:
        algorithm_instance: 算法实例
        
    Returns:
        包装后的算法实例
    """
    # 应用适配器
    algorithm_instance = create_adapter_for_algorithm(algorithm_instance)
    
    # 应用遗留兼容性
    algorithm_instance = apply_legacy_compatibility(algorithm_instance)
    
    return algorithm_instance


if __name__ == "__main__":
    # 测试适配器
    print("=== 函数调用适配器测试 ===")
    
    # 这里可以添加测试代码
    print("适配器模块加载成功")
    print("可以使用以下函数:")
    print("  - create_adapter_for_algorithm()")
    print("  - apply_legacy_compatibility()")
    print("  - wrap_algorithm_for_compatibility()")
