"""
A*路径规划算法
基于网格的最优路径搜索算法，适用于复杂环境的路径规划
"""

import asyncio
import heapq
import math
import time
import numpy as np
from typing import List, Tuple, Dict, Set, Optional
from dataclasses import dataclass

from .base import PathPlanningAlgorithm, AlgorithmInfo, AlgorithmParameter
from .data_structures import PathPlanningRequest, PathPlanningResponse, PathPoint

# 修复导入问题 - 使用绝对导入
try:
    from protection_zones import ProtectionZoneManager
except ImportError:
    # 如果绝对导入失败，尝试相对导入
    try:
        from ..protection_zones import ProtectionZoneManager
    except ImportError:
        # 如果都失败，创建一个简单的替代类
        class ProtectionZoneManager:
            def __init__(self):
                print("⚠️ 保护区管理器导入失败，使用简化版本")

            def calculate_path_collision_cost(self, path_points):
                return 0.0


@dataclass
class GridNode:
    """网格节点"""
    x: int
    y: int
    z: int
    g_cost: float = float('inf')  # 从起点到当前节点的实际代价
    h_cost: float = 0.0           # 从当前节点到终点的启发式代价
    f_cost: float = float('inf')  # 总代价 f = g + h
    parent: Optional['GridNode'] = None
    
    def __lt__(self, other):
        return self.f_cost < other.f_cost
    
    def __eq__(self, other):
        return self.x == other.x and self.y == other.y and self.z == other.z
    
    def __hash__(self):
        return hash((self.x, self.y, self.z))


class AStarAlgorithm(PathPlanningAlgorithm):
    """A*路径规划算法实现"""
    
    def __init__(self):
        super().__init__()

        # 初始化保护区管理器（暂时禁用）
        self.protection_zone_manager = None
        try:
            self.protection_zone_manager = ProtectionZoneManager()
            print("✅ A*算法: 保护区管理器初始化成功")
        except Exception as e:
            print(f"⚠️ A*算法: 保护区管理器初始化失败: {e}")
            self.protection_zone_manager = None

        # 设置算法信息
        self.info = AlgorithmInfo(
            name="AStar",
            version="1.0.0",
            description="A*算法，适用于复杂环境的最优路径规划",
            author="System",
            category="advanced",
            supported_optimizations=["distance", "time", "safety"],
            required_parameters=[
                AlgorithmParameter(
                    name="gridSize",
                    type="number",
                    description="网格大小(米)",
                    default_value=10,
                    required=True,
                    validation=lambda x: isinstance(x, (int, float)) and 1 <= x <= 100
                )
            ],
            optional_parameters=[
                AlgorithmParameter(
                    name="heuristicWeight",
                    type="number",
                    description="启发式权重",
                    default_value=1.0,
                    validation=lambda x: isinstance(x, (int, float)) and 0.1 <= x <= 10.0
                ),
                AlgorithmParameter(
                    name="maxIterations",
                    type="number",
                    description="最大迭代次数",
                    default_value=10000,
                    validation=lambda x: isinstance(x, (int, float)) and 100 <= x <= 100000
                ),
                AlgorithmParameter(
                    name="allowDiagonal",
                    type="boolean",
                    description="是否允许对角线移动",
                    default_value=True
                ),
                AlgorithmParameter(
                    name="smoothPath",
                    type="boolean",
                    description="是否平滑路径",
                    default_value=True
                )
            ]
        )
    
    async def calculate_path(self, request: PathPlanningRequest) -> PathPlanningResponse:
        """
        使用A*算法计算路径
        
        Args:
            request: 路径规划请求
            
        Returns:
            PathPlanningResponse: 路径规划响应
        """
        response = PathPlanningResponse()

        # 初始化调试信息
        self._debug_info = {}

        try:
            # 保存当前请求信息供后续使用
            self._current_request = request

            # 获取参数
            grid_size = float(request.parameters.get('gridSize', 10))
            heuristic_weight = float(request.parameters.get('heuristicWeight', 1.0))
            max_iterations = int(request.parameters.get('maxIterations', 10000))
            allow_diagonal = request.parameters.get('allowDiagonal', True)
            smooth_path = request.parameters.get('smoothPath', True)
            
            # 创建网格
            grid_info = self._create_grid(request, grid_size)
            
            # 转换起点和终点到网格坐标
            start_grid = self._world_to_grid(request.start_point, grid_info)
            end_grid = self._world_to_grid(request.end_point, grid_info)
            
            # 执行A*搜索
            path_nodes = await self._astar_search(
                start_grid, end_grid, grid_info, 
                heuristic_weight, max_iterations, allow_diagonal, request
            )
            
            if not path_nodes:
                response.success = False
                response.error = "无法找到可行路径"
                return response
            
            # 转换网格路径到世界坐标
            world_path = self._grid_path_to_world(path_nodes, grid_info, request.flight_height)
            
            # 路径平滑
            if smooth_path:
                world_path = self._smooth_path(world_path)
            
            # 设置速度和航向
            world_path = self._set_speed_and_heading(world_path, request.max_speed)
            
            # 设置时间戳
            world_path = self._set_timestamps(world_path)
            
            # 设置响应数据
            response.success = True
            response.path = world_path

            # 🔍 详细的路径质量分析
            path_quality = self._analyze_path_quality(world_path, request)

            # 🔧 添加路径质量调试信息
            print(f"🔍 A*算法路径质量:")
            print(f"   网格路径点数: {len(path_nodes)}")
            print(f"   世界坐标路径点数: {len(world_path)}")
            print(f"   网格大小: {grid_size}米")
            print(f"   路径平滑: {'是' if smooth_path else '否'}")
            if len(world_path) >= 2:
                start = world_path[0]
                end = world_path[-1]
                print(f"   起点: ({start.lng:.6f}, {start.lat:.6f})")
                print(f"   终点: ({end.lng:.6f}, {end.lat:.6f})")

            # 🔧 重要修复：先计算保护区碰撞代价，再计算统计信息
            print(f"🔍 A*算法: 开始计算保护区碰撞代价...")
            if self.protection_zone_manager:
                try:
                    path_points = [(point.lng, point.lat) for point in world_path]
                    print(f"🔍 A*算法: 开始计算保护区碰撞代价，路径点数: {len(path_points)}")
                    print(f"🔍 A*算法: 前3个路径点: {path_points[:3] if len(path_points) >= 3 else path_points}")

                    response.estimated_collision_cost = self.protection_zone_manager.calculate_path_collision_cost(path_points)
                    print(f"🔍 A*算法: 保护区管理器返回碰撞代价: {response.estimated_collision_cost}")

                    # 获取参与运算的保护区信息
                    relevant_zones = self.protection_zone_manager.get_zones_for_path(path_points)
                    active_zone_ids = [zone.id for zone in relevant_zones]
                    print(f"🔍 A*算法: 相关保护区数量: {len(relevant_zones)}")

                    # 对于基准算法，预估碰撞代价就是主要的碰撞代价
                    response.collision_cost = response.estimated_collision_cost
                    print(f"🔍 A*算法: 设置response.collision_cost = {response.collision_cost}")

                    # 添加保护区信息到metadata
                    if not hasattr(response, 'metadata'):
                        response.metadata = {}
                    response.metadata['protection_zones'] = {
                        'active_zone_ids': active_zone_ids,
                        'total_zones': len(self.protection_zone_manager.zones),
                        'active_zones': len(relevant_zones),
                        'zone_details': [
                            {
                                'id': zone.id,
                                'name': zone.name,
                                'type': zone.zone_type.value,
                                'average_crash_cost': zone.average_crash_cost,
                                'collision_cost_factor': zone.collision_cost_factor
                            }
                            for zone in relevant_zones
                        ]
                    }

                    print(f"🛡️ A*算法预估碰撞代价: {response.estimated_collision_cost:.4f}")
                    print(f"🛡️ A*算法最终碰撞代价: {response.collision_cost:.4f}")
                    print(f"🛡️ A*算法参与运算的保护区: {len(relevant_zones)} 个")
                    for zone in relevant_zones:
                        print(f"   - {zone.name} ({zone.zone_type.value}): 碰撞代价={zone.average_crash_cost:.4f}/m²")

                except Exception as e:
                    print(f"⚠️ A*算法: 保护区碰撞代价计算失败: {e}")
                    response.estimated_collision_cost = 0.0
                    response.actual_collision_cost = 0.0
            else:
                print("⚠️ A*算法: 保护区管理器不可用，使用默认碰撞代价")
                response.estimated_collision_cost = 0.0
                response.actual_collision_cost = 0.0

            # 🔧 重要修复：在保护区碰撞代价计算完成后，再计算统计信息
            print(f"🔍 A*算法: 保护区碰撞代价计算完成，开始计算统计信息...")
            print(f"🔍 A*算法: response.estimated_collision_cost = {getattr(response, 'estimated_collision_cost', 'None')}")
            response.calculate_statistics()
            print(f"🔍 A*算法: 统计计算完成，最终collision_cost = {response.collision_cost}")

            # 质量评估
            response.quality = {
                'risk_score': self._calculate_risk_score(world_path, request),
                'smoothness': 85 if smooth_path else 60,
                'efficiency': self._calculate_efficiency_score(world_path, request),
                'safety_margin': request.safety_distance,
                'complexity': 'medium'
            }
            
            # 调试信息
            response.debug_info = {
                'algorithm_version': self.info.version,
                'iteration_count': len(path_nodes),
                'convergence_time': response.execution_time,
                'optimization_steps': ['网格创建', 'A*搜索', '路径平滑' if smooth_path else '无平滑'],
                'warnings': [],
                'suggestions': [],
                # 添加建筑物处理的调试信息
                **self._debug_info
            }
            
            return response
            
        except Exception as e:
            response.success = False
            response.error = f"A*算法执行失败: {str(e)}"
            return response
    
    def _create_grid(self, request: PathPlanningRequest, grid_size: float) -> Dict:
        """创建搜索网格（改进版，更适合地理坐标）"""
        # 计算网格边界
        start_point = request.start_point
        end_point = request.end_point

        # 计算路径长度，动态调整边界扩展
        path_distance = self._calculate_distance_between_points(start_point, end_point)

        # 根据路径长度动态调整边界扩展
        if path_distance < 1000:  # 短距离
            boundary_expansion = 0.005  # 约500米
        elif path_distance < 5000:  # 中距离
            boundary_expansion = 0.01   # 约1公里
        else:  # 长距离
            boundary_expansion = 0.02   # 约2公里

        # 扩展边界以包含起点和终点
        min_lng = min(start_point.lng, end_point.lng) - boundary_expansion
        max_lng = max(start_point.lng, end_point.lng) + boundary_expansion
        min_lat = min(start_point.lat, end_point.lat) - boundary_expansion
        max_lat = max(start_point.lat, end_point.lat) + boundary_expansion

        # 转换为米（使用更精确的计算）
        center_lat = (min_lat + max_lat) / 2
        lng_to_meters = 111320 * np.cos(np.radians(center_lat))
        lat_to_meters = 110540

        width_meters = (max_lng - min_lng) * lng_to_meters
        height_meters = (max_lat - min_lat) * lat_to_meters

        # 计算网格尺寸（限制最大网格大小以提高性能）
        max_grid_size = 200  # 最大网格尺寸
        grid_width = max(10, min(max_grid_size, int(np.ceil(width_meters / grid_size))))
        grid_height = max(10, min(max_grid_size, int(np.ceil(height_meters / grid_size))))

        # 如果网格太大，增加网格大小
        if grid_width > max_grid_size or grid_height > max_grid_size:
            new_grid_size = max(width_meters / max_grid_size, height_meters / max_grid_size)
            grid_width = int(np.ceil(width_meters / new_grid_size))
            grid_height = int(np.ceil(height_meters / new_grid_size))
            grid_size = new_grid_size
            print(f"A*算法: 调整网格大小为 {grid_size:.1f}m 以限制网格尺寸")

        # 根据飞行高度动态调整高度层数（限制最大层数）
        max_flight_height = max(start_point.alt, end_point.alt, request.flight_height)
        grid_depth = max(3, min(10, int(np.ceil(max_flight_height / 30))))  # 每30米一层，最多10层
        
        grid_info = {
            'min_lng': min_lng,
            'max_lng': max_lng,
            'min_lat': min_lat,
            'max_lat': max_lat,
            'grid_size': grid_size,
            'grid_width': grid_width,
            'grid_height': grid_height,
            'grid_depth': grid_depth,
            'lng_to_meters': lng_to_meters,
            'lat_to_meters': lat_to_meters
        }

        # 创建障碍物网格，传递完整的网格信息
        grid_info['obstacles'] = self._create_obstacle_grid(request, grid_info)

        # 🔍 网格创建调试信息
        print(f"\n🔍 ===== A*算法网格创建信息 =====")
        print(f"📐 网格参数:")
        print(f"   网格大小: {grid_size}米")
        print(f"   网格尺寸: {grid_width} x {grid_height} x {grid_depth}")
        print(f"   总网格节点: {grid_width * grid_height * grid_depth:,}")

        print(f"🌍 地理范围:")
        print(f"   经度范围: {min_lng:.6f} ~ {max_lng:.6f}")
        print(f"   纬度范围: {min_lat:.6f} ~ {max_lat:.6f}")
        print(f"   高度范围: 0 ~ {grid_depth * 30:.1f}米")

        print(f"📍 起终点信息:")
        print(f"   起点世界坐标: ({start_point.lng:.6f}, {start_point.lat:.6f}, {start_point.alt:.1f})")
        print(f"   终点世界坐标: ({end_point.lng:.6f}, {end_point.lat:.6f}, {end_point.alt:.1f})")

        print(f"🚧 障碍物信息:")
        print(f"   障碍物总数: {len(grid_info['obstacles']):,}")
        if grid_info['obstacles']:
            obstacle_density = len(grid_info['obstacles']) / (grid_width * grid_height * grid_depth) * 100
            print(f"   障碍物密度: {obstacle_density:.2f}%")

        print(f"🔍 ===== 网格创建完成 =====\n")

        return grid_info
    
    def _create_obstacle_grid(self, request: PathPlanningRequest, grid_info: Dict) -> Set[Tuple[int, int, int]]:
        """创建障碍物网格（改进版，更好地处理建筑物）"""
        obstacles = set()

        # 从网格信息中获取参数，确保类型正确
        width = int(grid_info['grid_width'])
        height = int(grid_info['grid_height'])
        depth = int(grid_info['grid_depth'])
        min_lng = float(grid_info['min_lng'])
        max_lng = float(grid_info['max_lng'])
        min_lat = float(grid_info['min_lat'])
        max_lat = float(grid_info['max_lat'])
        grid_size = float(grid_info['grid_size'])

        # 调试信息
        print(f"A*算法障碍物创建调试:")
        print(f"  建筑物总数: {len(request.buildings)}")
        print(f"  网格尺寸: {width} x {height} x {depth}")
        print(f"  网格边界: lng[{min_lng:.6f}, {max_lng:.6f}], lat[{min_lat:.6f}, {max_lat:.6f}]")
        print(f"  网格大小: {grid_size}m")
        print(f"  安全距离: {request.safety_distance}m")

        # 处理建筑物障碍（移除数量限制，处理所有建筑物）
        processed_buildings = 0
        total_buildings = len(request.buildings)

        # 根据建筑物数量调整处理策略
        if total_buildings > 1000:
            # 大量建筑物时，采样处理
            step = max(1, total_buildings // 500)  # 最多处理500个建筑物
            buildings_to_process = request.buildings[::step]
            print(f"  大量建筑物检测到({total_buildings}个)，采样处理{len(buildings_to_process)}个")
        else:
            # 少量建筑物时，全部处理
            buildings_to_process = request.buildings
            print(f"  处理所有{total_buildings}个建筑物")

        for building in buildings_to_process:
            try:
                # 获取建筑物信息（支持GeoJSON格式）
                center = None
                height = 20  # 默认高度

                if isinstance(building, dict):
                    # 检查是否是GeoJSON格式（前端实际发送的格式）
                    if 'geometry' in building and 'properties' in building:
                        # GeoJSON格式：{geometry: {type: "Polygon", coordinates: [...]}, properties: {height: ...}}
                        geometry = building.get('geometry', {})
                        properties = building.get('properties', {})

                        # 从properties获取高度
                        height = properties.get('height', 20)

                        # 从geometry计算中心点
                        geom_type = geometry.get('type', 'Unknown')

                        if geom_type == 'Polygon' and 'coordinates' in geometry:
                            coordinates = geometry['coordinates']
                            if coordinates and len(coordinates) > 0 and len(coordinates[0]) > 0:
                                # 计算多边形的中心点（简单平均）
                                coords = coordinates[0]  # 外环
                                if len(coords) > 0:
                                    lng_sum = sum(coord[0] for coord in coords)
                                    lat_sum = sum(coord[1] for coord in coords)
                                    center = {
                                        'lng': lng_sum / len(coords),
                                        'lat': lat_sum / len(coords)
                                    }
                            else:
                                if processed_buildings < 3:
                                    print(f"    跳过建筑物: Polygon坐标为空 coordinates={coordinates}")

                        elif geom_type == 'Point' and 'coordinates' in geometry:
                            coords = geometry['coordinates']
                            if len(coords) >= 2:
                                center = {'lng': coords[0], 'lat': coords[1]}
                                if processed_buildings < 3:
                                    print(f"    使用Point几何: center={center}")
                            else:
                                if processed_buildings < 3:
                                    print(f"    跳过建筑物: Point坐标无效 coordinates={coords}")

                        elif geom_type == 'MultiPolygon' and 'coordinates' in geometry:
                            coords = geometry['coordinates']
                            if coords and len(coords) > 0 and len(coords[0]) > 0 and len(coords[0][0]) > 0:
                                # 使用第一个多边形的第一个环
                                first_ring = coords[0][0]
                                lng_sum = sum(coord[0] for coord in first_ring)
                                lat_sum = sum(coord[1] for coord in first_ring)
                                center = {
                                    'lng': lng_sum / len(first_ring),
                                    'lat': lat_sum / len(first_ring)
                                }
                                if processed_buildings < 3:
                                    print(f"    使用MultiPolygon几何: center={center}")
                            else:
                                if processed_buildings < 3:
                                    print(f"    跳过建筑物: MultiPolygon坐标无效 coordinates={coords}")

                        else:
                            if processed_buildings < 3:
                                has_coords = 'coordinates' in geometry
                                print(f"    跳过建筑物: 几何类型不支持 type={geom_type}, has_coordinates={has_coords}")

                    # 检查旧格式：{center: {lng, lat}, height: ...}
                    elif 'center' in building:
                        center = building.get('center')
                        height = building.get('height', 20)

                    # 检查直接坐标格式：{lng: ..., lat: ..., height: ...}
                    elif 'lng' in building and 'lat' in building:
                        center = {'lng': building['lng'], 'lat': building['lat']}
                        height = building.get('height', 20)

                elif hasattr(building, 'center'):
                    # 对象格式
                    center = building.center
                    height = getattr(building, 'height', 20)
                else:
                    print(f"    跳过未知建筑物格式: {type(building)}")
                    continue

                # 验证center格式
                if not center or not isinstance(center, dict):
                    print(f"    跳过建筑物: center格式不正确 {center}")
                    continue

                if 'lng' not in center or 'lat' not in center:
                    print(f"    跳过建筑物: center缺少坐标 {center}")
                    continue

                # 验证坐标值
                try:
                    building_lng = float(center['lng'])
                    building_lat = float(center['lat'])
                    building_height = float(height)
                except (ValueError, TypeError) as e:
                    print(f"    跳过建筑物: 坐标转换失败 {e}")
                    continue

                # 简化的坐标转换逻辑
                # 确保所有变量都是数字类型
                try:
                    building_lng = float(building_lng)
                    building_lat = float(building_lat)
                    building_height = float(building_height)
                    min_lng = float(min_lng)
                    max_lng = float(max_lng)
                    min_lat = float(min_lat)
                    max_lat = float(max_lat)
                    width = int(width)
                    height = int(height)
                except (ValueError, TypeError) as e:
                    print(f"    跳过建筑物: 类型转换失败 {e}")
                    continue

                # 计算建筑物在网格中的相对位置（0-1之间）
                lng_diff = max_lng - min_lng
                lat_diff = max_lat - min_lat

                if lng_diff <= 0 or lat_diff <= 0:
                    print(f"    跳过建筑物: 网格范围无效")
                    continue

                lng_ratio = (building_lng - min_lng) / lng_diff
                lat_ratio = (building_lat - min_lat) / lat_diff

                # 转换到网格坐标
                grid_x = int(lng_ratio * width)
                grid_y = int(lat_ratio * height)

                # 确保坐标在有效范围内
                grid_x = max(0, min(grid_x, width - 1))
                grid_y = max(0, min(grid_y, height - 1))

                # 调试信息（简化版）
                if processed_buildings < 3:
                    print(f"    建筑物 {processed_buildings}: 坐标({building_lng:.4f}, {building_lat:.4f}) → 网格({grid_x}, {grid_y}), 高度{building_height}m")

                # 检查坐标是否在网格范围内
                if 0 <= grid_x < width and 0 <= grid_y < height:
                    # 根据建筑高度标记障碍层
                    max_obstacle_height = min(int(building_height / 30), depth - 1)  # 每30米一层

                    # 根据建筑高度标记障碍层
                    max_obstacle_height = min(int(building_height / 30), depth - 1)
                    if max_obstacle_height < 0:
                        max_obstacle_height = 0

                    # 计算安全距离对应的网格数量
                    safety_grid_radius = max(1, int(request.safety_distance / grid_size))

                    # 标记建筑物占用的网格（包含安全距离）
                    for z in range(max_obstacle_height + 1):
                        if 0 <= z < depth:
                            # 在安全距离范围内标记障碍物
                            for dx in range(-safety_grid_radius, safety_grid_radius + 1):
                                for dy in range(-safety_grid_radius, safety_grid_radius + 1):
                                    obs_x = grid_x + dx
                                    obs_y = grid_y + dy
                                    if 0 <= obs_x < width and 0 <= obs_y < height:
                                        # 计算距离，只在圆形安全区域内标记
                                        distance = np.sqrt(dx*dx + dy*dy)
                                        if distance <= safety_grid_radius:
                                            obstacles.add((obs_x, obs_y, z))
                else:
                    if processed_buildings < 3:
                        print(f"      建筑物超出网格范围，跳过")

                processed_buildings += 1

            except Exception as e:
                # 忽略单个建筑物的处理错误
                print(f"    处理建筑物时出错: {e}")
                continue

        print(f"A*算法处理了 {processed_buildings} 个建筑物，创建了 {len(obstacles)} 个障碍物网格点")

        # 确保起点和终点不被障碍物阻塞
        start_grid = self._convert_to_grid_coords(request.start_point, grid_info)
        end_grid = self._convert_to_grid_coords(request.end_point, grid_info)

        # 清除起点和终点周围的障碍物
        for point in [start_grid, end_grid]:
            if point:
                for dx in range(-2, 3):  # 5x5区域
                    for dy in range(-2, 3):
                        for dz in range(-1, 2):  # 3层高度
                            clear_x = point[0] + dx
                            clear_y = point[1] + dy
                            clear_z = point[2] + dz
                            if (0 <= clear_x < grid_info['grid_width'] and
                                0 <= clear_y < grid_info['grid_height'] and
                                0 <= clear_z < grid_info['grid_depth']):
                                obstacles.discard((clear_x, clear_y, clear_z))

        print(f"A*算法: 清除起点终点障碍物后，剩余 {len(obstacles)} 个障碍物网格点")

        # 将调试信息添加到响应中
        if hasattr(self, '_debug_info'):
            self._debug_info['buildings_processed'] = processed_buildings
            self._debug_info['obstacles_created'] = len(obstacles)
            self._debug_info['total_buildings'] = len(request.buildings)

        return obstacles

    def _convert_to_grid_coords(self, point, grid_info):
        """将地理坐标转换为网格坐标"""
        try:
            width = grid_info['grid_width']
            height = grid_info['grid_height']
            depth = grid_info['grid_depth']
            min_lng = grid_info['min_lng']
            max_lng = grid_info['max_lng']
            min_lat = grid_info['min_lat']
            max_lat = grid_info['max_lat']

            lng_diff = max_lng - min_lng
            lat_diff = max_lat - min_lat

            if lng_diff <= 0 or lat_diff <= 0:
                return None

            lng_ratio = (point.lng - min_lng) / lng_diff
            lat_ratio = (point.lat - min_lat) / lat_diff

            grid_x = int(lng_ratio * width)
            grid_y = int(lat_ratio * height)
            grid_z = int(point.alt / 30)  # 每30米一层

            # 确保坐标在有效范围内
            grid_x = max(0, min(grid_x, width - 1))
            grid_y = max(0, min(grid_y, height - 1))
            grid_z = max(0, min(grid_z, depth - 1))

            return (grid_x, grid_y, grid_z)

        except Exception as e:
            return None
    
    def _world_to_grid(self, point, grid_info) -> GridNode:
        """世界坐标转网格坐标"""
        # 转换经纬度到网格坐标
        x = int((point.lng - grid_info['min_lng']) * grid_info['lng_to_meters'] / grid_info['grid_size'])
        y = int((point.lat - grid_info['min_lat']) * grid_info['lat_to_meters'] / grid_info['grid_size'])
        z = int(point.alt / (grid_info['grid_size'] * 2))  # 高度层
        
        # 确保坐标在网格范围内
        x = max(0, min(x, grid_info['grid_width'] - 1))
        y = max(0, min(y, grid_info['grid_height'] - 1))
        z = max(0, min(z, grid_info['grid_depth'] - 1))
        
        return GridNode(x, y, z)
    
    def _grid_to_world(self, node: GridNode, grid_info, altitude: float) -> PathPoint:
        """网格坐标转世界坐标 - 🔧 修复：正确处理高度计算"""
        lng = grid_info['min_lng'] + (node.x * grid_info['grid_size']) / grid_info['lng_to_meters']
        lat = grid_info['min_lat'] + (node.y * grid_info['grid_size']) / grid_info['lat_to_meters']

        # 🔧 修复：使用网格层次计算高度，而不是简单的altitude + z
        # 每层30米，z=0对应地面高度1米
        alt = 1.0 + node.z * 30.0  # 地面1米 + 网格层次 * 30米/层
        
        return PathPoint(lng=lng, lat=lat, alt=alt)
    
    async def _astar_search(
        self,
        start: GridNode,
        goal: GridNode,
        grid_info: Dict,
        heuristic_weight: float,
        max_iterations: int,
        allow_diagonal: bool,
        request: PathPlanningRequest
    ) -> List[GridNode]:
        """A*搜索算法（改进版，符合论文要求）"""

        # 存储网格信息供移动代价计算使用
        self._grid_info = grid_info

        open_set = []
        closed_set = set()

        start.g_cost = 0
        start.h_cost = self._heuristic(start, goal) * heuristic_weight
        start.f_cost = start.g_cost + start.h_cost

        heapq.heappush(open_set, start)

        # 🔍 A*搜索开始调试信息
        print(f"🔍 ===== A*算法搜索开始 =====")
        print(f"🎯 搜索参数:")
        print(f"   启发式权重: {heuristic_weight}")
        print(f"   最大迭代次数: {max_iterations}")
        print(f"   允许对角移动: {allow_diagonal}")
        print(f"   起点网格坐标: ({start.x}, {start.y}, {start.z})")
        print(f"   终点网格坐标: ({goal.x}, {goal.y}, {goal.z})")

        iterations = 0

        while open_set and iterations < max_iterations:
            iterations += 1
            self.progress = min(90, (iterations / max_iterations) * 90)
            
            # 每100次迭代检查一次取消状态
            if iterations % 100 == 0:
                if self.is_cancelled:
                    return []
                # 模拟异步处理
                await asyncio.sleep(0.001)
            
            current = heapq.heappop(open_set)

            # 检查是否到达目标 - 使用更宽松的条件
            if (current == goal or
                (abs(current.x - goal.x) <= 1 and
                 abs(current.y - goal.y) <= 1 and
                 abs(current.z - goal.z) <= 1)):

                # 找到路径，重构并优化路径
                raw_path = []
                while current:
                    raw_path.append(current)
                    current = current.parent
                raw_path = raw_path[::-1]  # 反转路径

                # 确保路径包含真正的终点
                if raw_path and raw_path[-1] != goal:
                    raw_path.append(goal)

                # 🔍 A*搜索完成调试信息
                print(f"✅ A*搜索成功完成!")
                print(f"   搜索迭代次数: {iterations}")
                print(f"   原始路径节点数: {len(raw_path)}")
                print(f"   搜索效率: {iterations}/{max_iterations} ({iterations/max_iterations*100:.1f}%)")

                # 路径优化：移除不必要的中间点
                optimized_path = self._optimize_path(raw_path, grid_info)

                print(f"   优化后路径节点数: {len(optimized_path)}")
                print(f"   优化效果: 减少了 {len(raw_path) - len(optimized_path)} 个节点")
                print(f"🔍 ===== A*搜索阶段完成 =====\n")

                return optimized_path
            
            closed_set.add(current)
            
            # 获取邻居节点
            neighbors = self._get_neighbors(current, grid_info, allow_diagonal)
            
            for neighbor in neighbors:
                if neighbor in closed_set:
                    continue
                
                # 检查是否为障碍物
                if (neighbor.x, neighbor.y, neighbor.z) in grid_info['obstacles']:
                    continue
                
                # 计算移动代价
                move_cost = self._calculate_move_cost(current, neighbor)
                tentative_g_cost = current.g_cost + move_cost
                
                # 检查是否找到更好的路径
                if tentative_g_cost < neighbor.g_cost:
                    neighbor.parent = current
                    neighbor.g_cost = tentative_g_cost
                    neighbor.h_cost = self._heuristic(neighbor, goal) * heuristic_weight
                    neighbor.f_cost = neighbor.g_cost + neighbor.h_cost
                    
                    if neighbor not in open_set:
                        heapq.heappush(open_set, neighbor)

        # 🔍 A*搜索失败调试信息
        print(f"❌ A*搜索失败!")
        print(f"   搜索迭代次数: {iterations}")
        print(f"   是否达到最大迭代: {'是' if iterations >= max_iterations else '否'}")
        print(f"   开放集是否为空: {'是' if not open_set else '否'}")
        print(f"   可能原因: {'迭代次数不足' if iterations >= max_iterations else '无可达路径'}")
        print(f"🔍 ===== A*搜索失败 =====\n")

        return []  # 未找到路径
    
    def _get_neighbors(self, node: GridNode, grid_info: Dict, allow_diagonal: bool) -> List[GridNode]:
        """获取邻居节点"""
        neighbors = []
        
        # 定义移动方向
        if allow_diagonal:
            directions = [
                (-1, -1, 0), (-1, 0, 0), (-1, 1, 0),
                (0, -1, 0),              (0, 1, 0),
                (1, -1, 0),  (1, 0, 0),  (1, 1, 0),
                (0, 0, -1),  (0, 0, 1)   # 垂直移动
            ]
        else:
            directions = [
                (-1, 0, 0), (1, 0, 0), (0, -1, 0), (0, 1, 0),
                (0, 0, -1), (0, 0, 1)
            ]
        
        for dx, dy, dz in directions:
            new_x = node.x + dx
            new_y = node.y + dy
            new_z = node.z + dz
            
            # 检查边界
            if (0 <= new_x < grid_info['grid_width'] and
                0 <= new_y < grid_info['grid_height'] and
                0 <= new_z < grid_info['grid_depth']):
                
                neighbors.append(GridNode(new_x, new_y, new_z))
        
        return neighbors
    
    def _heuristic(self, node1: GridNode, node2: GridNode) -> float:
        """启发式函数（欧几里得距离，更适合地理坐标）"""
        # 使用欧几里得距离作为启发式函数，更适合实际地理距离
        dx = node2.x - node1.x
        dy = node2.y - node1.y
        dz = node2.z - node1.z
        return np.sqrt(dx*dx + dy*dy + dz*dz)
    
    def _calculate_move_cost(self, from_node: GridNode, to_node: GridNode) -> float:
        """计算移动代价（考虑实际地理距离和风险因素）"""
        dx = abs(to_node.x - from_node.x)
        dy = abs(to_node.y - from_node.y)
        dz = abs(to_node.z - from_node.z)

        # 基础移动代价（基于实际距离）
        if dx + dy + dz == 3:  # 三维对角线移动
            base_cost = 1.732  # sqrt(3)
        elif dx + dy + dz == 2:  # 二维对角线移动
            base_cost = 1.414  # sqrt(2)
        elif dz > 0:  # 垂直移动
            base_cost = 1.2  # 垂直移动稍微困难
        else:  # 直线移动
            base_cost = 1.0

        # 添加高度惩罚（高度越高风险越大）
        height_penalty = 1.0 + (to_node.z * 0.01)  # 每层增加1%代价

        # 添加边界惩罚（靠近边界的节点代价更高）
        boundary_penalty = 1.0
        if hasattr(self, '_grid_info'):
            grid_info = self._grid_info
            margin = 2  # 边界缓冲区
            if (to_node.x < margin or to_node.x >= grid_info['grid_width'] - margin or
                to_node.y < margin or to_node.y >= grid_info['grid_height'] - margin):
                boundary_penalty = 1.1

        return base_cost * height_penalty * boundary_penalty

    def _calculate_distance_between_points(self, point1, point2) -> float:
        """计算两点间的地理距离（米）"""
        # 使用Haversine公式计算地理距离
        R = 6371000  # 地球半径（米）

        lat1_rad = np.radians(point1.lat)
        lat2_rad = np.radians(point2.lat)
        delta_lat = np.radians(point2.lat - point1.lat)
        delta_lng = np.radians(point2.lng - point1.lng)

        a = (np.sin(delta_lat/2) * np.sin(delta_lat/2) +
             np.cos(lat1_rad) * np.cos(lat2_rad) *
             np.sin(delta_lng/2) * np.sin(delta_lng/2))
        c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1-a))

        return R * c

    def _optimize_path(self, path: List[GridNode], grid_info: Dict) -> List[GridNode]:
        """优化路径，移除不必要的中间点（保留更多路径点以确保公平对比）"""
        if len(path) <= 2:
            return path

        # 🔍 路径优化开始调试信息
        print(f"🔍 ===== A*路径优化开始 =====")
        print(f"📊 优化前信息:")
        print(f"   原始路径点数: {len(path)}")

        optimization_stats = {
            'jumps_made': 0,
            'points_skipped': 0,
            'direction_changes_preserved': 0
        }

        # 🔧 修复：保持A*算法特性，使用适度的路径优化
        # 目标：减少明显的锯齿状路径，但保持A*算法的基本特征

        optimized = [path[0]]  # 起点

        # 使用适度的优化策略，保持A*算法的网格特性
        i = 0
        while i < len(path) - 1:
            # 🔧 修复：适度跳跃，最多跳过5个点，保持路径安全性
            max_jump = min(i + 6, len(path) - 1)  # 最多跳过5个点
            j = max_jump
            found_jump = False

            while j > i + 1:
                if self._is_line_clear(path[i], path[j], grid_info):
                    # 检查是否有显著的方向变化，如果有，保留中间点
                    if j - i > 3:  # 如果跳跃距离大于3，检查方向变化
                        mid_point = path[i + (j - i) // 2]
                        # 计算方向变化角度
                        angle_change = self._calculate_direction_change(path[i], mid_point, path[j])
                        if angle_change > 0.3:  # 如果方向变化超过0.3弧度（约17度），保留中间点
                            optimized.append(mid_point)

                    optimized.append(path[j])
                    i = j
                    found_jump = True
                    break
                j -= 1

            if not found_jump:
                # 无法跳过，添加下一个点
                i += 1
                if i < len(path):
                    optimized.append(path[i])

        # 🔍 路径优化完成调试信息
        print(f"📊 优化后信息:")
        print(f"   优化后路径点数: {len(optimized)}")
        print(f"   减少点数: {len(path) - len(optimized)}")
        print(f"   优化率: {(len(path) - len(optimized))/len(path)*100:.1f}%")
        print(f"   跳跃次数: {optimization_stats['jumps_made']}")
        print(f"   跳过点数: {optimization_stats['points_skipped']}")
        print(f"🔍 ===== A*路径优化完成 =====\n")

        return optimized

    def _calculate_direction_change(self, p1: GridNode, p2: GridNode, p3: GridNode) -> float:
        """计算三点间的方向变化角度"""

        # 计算两个向量
        v1 = (p2.x - p1.x, p2.y - p1.y)
        v2 = (p3.x - p2.x, p3.y - p2.y)

        # 计算向量长度
        len1 = math.sqrt(v1[0]**2 + v1[1]**2)
        len2 = math.sqrt(v2[0]**2 + v2[1]**2)

        if len1 == 0 or len2 == 0:
            return 0

        # 计算夹角
        dot_product = v1[0] * v2[0] + v1[1] * v2[1]
        cos_angle = dot_product / (len1 * len2)
        cos_angle = max(-1, min(1, cos_angle))  # 限制在[-1, 1]范围内

        angle = math.acos(cos_angle)
        return angle

    def _is_line_clear(self, start: GridNode, end: GridNode, grid_info: Dict) -> bool:
        """检查两点间的直线是否无障碍物"""
        obstacles = grid_info['obstacles']

        # 使用Bresenham 3D算法检查直线路径
        dx = abs(end.x - start.x)
        dy = abs(end.y - start.y)
        dz = abs(end.z - start.z)

        x_step = 1 if end.x > start.x else -1
        y_step = 1 if end.y > start.y else -1
        z_step = 1 if end.z > start.z else -1

        # 简化的3D直线检查
        steps = max(dx, dy, dz)
        if steps == 0:
            return True

        for i in range(steps + 1):
            t = i / steps
            x = int(start.x + t * (end.x - start.x))
            y = int(start.y + t * (end.y - start.y))
            z = int(start.z + t * (end.z - start.z))

            if (x, y, z) in obstacles:
                return False

        return True
    
    def _grid_path_to_world(self, path_nodes: List[GridNode], grid_info: Dict, altitude: float) -> List[PathPoint]:
        """将网格路径转换为世界坐标路径"""
        world_path = []

        for i, node in enumerate(path_nodes):
            point = self._grid_to_world(node, grid_info, altitude)

            # 设置动作
            if i == 0:
                point.action = 'takeoff'
            elif i == len(path_nodes) - 1:
                point.action = 'land'
            else:
                point.action = 'fly'

            world_path.append(point)

        # 🔧 修复：确保起点和终点精确匹配，特别是高度设置
        if hasattr(self, '_current_request') and self._current_request:
            if world_path:
                # 精确设置起点（地面起飞）
                world_path[0].lng = self._current_request.start_point.lng
                world_path[0].lat = self._current_request.start_point.lat
                world_path[0].alt = self._current_request.start_point.alt  # 应该是1.0米

                # 精确设置终点（地面降落）
                world_path[-1].lng = self._current_request.end_point.lng
                world_path[-1].lat = self._current_request.end_point.lat
                world_path[-1].alt = self._current_request.end_point.alt  # 应该是1.0米

                print(f"🎯 A*算法: 路径生成成功，起点({self._current_request.start_point.lng:.6f}, {self._current_request.start_point.lat:.6f}, {self._current_request.start_point.alt:.1f}m) -> 终点({self._current_request.end_point.lng:.6f}, {self._current_request.end_point.lat:.6f}, {self._current_request.end_point.alt:.1f}m)")
                print(f"🎯 A*算法: 路径点数={len(world_path)}, 起点高度={world_path[0].alt:.1f}m, 终点高度={world_path[-1].alt:.1f}m")

        return world_path
    
    def _smooth_path(self, path: List[PathPoint]) -> List[PathPoint]:
        """
        路径平滑处理 - 保持A*算法特性
        🔧 修复：适度平滑，保持A*算法的网格特征
        """
        if len(path) < 3:
            return path

        # 🔧 修复：适度平滑处理，保持A*算法特性
        smoothed_path = [path[0]]  # 保持起点

        # 进行1次适度平滑处理
        for i in range(1, len(path) - 1):
            prev_point = path[i - 1]
            curr_point = path[i]
            next_point = path[i + 1]

            # 🔧 修复：使用适度的加权平均，保持原路径特征
            smoothed_point = PathPoint(
                lng=(prev_point.lng * 0.25 + curr_point.lng * 0.5 + next_point.lng * 0.25),
                lat=(prev_point.lat * 0.25 + curr_point.lat * 0.5 + next_point.lat * 0.25),
                alt=(prev_point.alt * 0.25 + curr_point.alt * 0.5 + next_point.alt * 0.25),
                action=curr_point.action
            )

            smoothed_path.append(smoothed_point)

        smoothed_path.append(path[-1])  # 保持终点

        print(f"🔧 A*路径平滑: 原始{len(path)}个点 -> 平滑后{len(smoothed_path)}个点")
        return smoothed_path
    
    def _set_speed_and_heading(self, path: List[PathPoint], max_speed: float) -> List[PathPoint]:
        """设置速度和航向"""
        if len(path) < 2:
            return path
        
        for i in range(len(path)):
            # 设置速度（简化处理）
            path[i].speed = max_speed * 0.8  # 稍微降低速度以提高安全性
            
            # 设置航向
            if i < len(path) - 1:
                next_point = path[i + 1]
                curr_point = path[i]
                
                delta_lng = next_point.lng - curr_point.lng
                delta_lat = next_point.lat - curr_point.lat
                
                if delta_lng != 0 or delta_lat != 0:
                    heading = np.degrees(np.arctan2(delta_lng, delta_lat))
                    if heading < 0:
                        heading += 360
                    path[i].heading = heading
            else:
                if i > 0:
                    path[i].heading = path[i - 1].heading
        
        return path
    
    def _set_timestamps(self, path: List[PathPoint]) -> List[PathPoint]:
        """设置时间戳"""
        if not path:
            return path
        
        current_time = int(time.time() * 1000)
        path[0].timestamp = current_time
        
        for i in range(1, len(path)):
            prev_point = path[i - 1]
            curr_point = path[i]
            
            # 计算飞行时间
            distance = self._calculate_distance(prev_point, curr_point)
            flight_time = distance / curr_point.speed if curr_point.speed > 0 else 1.0
            
            curr_point.timestamp = prev_point.timestamp + int(flight_time * 1000)
        
        return path
    
    def _calculate_distance(self, point1: PathPoint, point2: PathPoint) -> float:
        """计算两点间距离"""
        R = 6371000  # 地球半径
        
        lat1_rad = np.radians(point1.lat)
        lat2_rad = np.radians(point2.lat)
        delta_lat = np.radians(point2.lat - point1.lat)
        delta_lon = np.radians(point2.lng - point1.lng)
        
        a = (np.sin(delta_lat / 2)**2 +
             np.cos(lat1_rad) * np.cos(lat2_rad) *
             np.sin(delta_lon / 2)**2)
        c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))
        
        horizontal_distance = R * c
        vertical_distance = abs(point2.alt - point1.alt)
        
        return np.sqrt(horizontal_distance**2 + vertical_distance**2)
    
    def _calculate_risk_score(self, path: List[PathPoint], request: PathPlanningRequest) -> int:
        """计算风险评分"""
        # 简化的风险评估
        base_risk = 30
        
        # 根据路径长度调整
        if len(path) > 50:
            base_risk += 10
        
        # 根据高度调整
        avg_altitude = sum(p.alt for p in path) / len(path) if path else 0
        if avg_altitude < 50:
            base_risk += 15
        elif avg_altitude > 300:
            base_risk += 10
        
        return min(100, base_risk)
    
    def _calculate_efficiency_score(self, path: List[PathPoint], request: PathPlanningRequest) -> int:
        """计算效率评分"""
        if not path:
            return 0

        # 计算实际路径长度
        actual_distance = sum(
            self._calculate_distance(path[i], path[i + 1])
            for i in range(len(path) - 1)
        )

        # 计算直线距离
        straight_distance = self._calculate_distance(path[0], path[-1])

        if straight_distance == 0:
            return 100

        # 效率 = 直线距离 / 实际距离
        efficiency_ratio = straight_distance / actual_distance
        return int(efficiency_ratio * 100)

    def _analyze_path_quality(self, path: List[PathPoint], request: PathPlanningRequest) -> dict:
        """
        🔍 详细分析路径质量和合理性
        """
        if not path or len(path) < 2:
            return {"error": "路径为空或点数不足"}

        print(f"\n🔍 ===== A*算法路径质量分析 =====")

        # 基本信息
        path_points_count = len(path)
        print(f"📊 基本信息:")
        print(f"   路径点数量: {path_points_count}")

        # 计算距离信息
        actual_distance = sum(
            self._calculate_distance(path[i], path[i + 1])
            for i in range(len(path) - 1)
        )
        straight_distance = self._calculate_distance(path[0], path[-1])

        print(f"   实际路径长度: {actual_distance:.2f}米")
        print(f"   直线距离: {straight_distance:.2f}米")

        # 路径效率分析
        if straight_distance > 0:
            efficiency_ratio = actual_distance / straight_distance
            print(f"   路径效率比: {efficiency_ratio:.3f} (实际/直线)")

            if efficiency_ratio <= 1.2:
                efficiency_status = "✅ 优秀"
            elif efficiency_ratio <= 1.5:
                efficiency_status = "⚠️ 良好"
            elif efficiency_ratio <= 2.0:
                efficiency_status = "❌ 一般（轻微绕路）"
            else:
                efficiency_status = "❌ 较差（明显绕路）"
            print(f"   效率评估: {efficiency_status}")

        # 路径点密度分析
        if path_points_count > 1:
            avg_segment_length = actual_distance / (path_points_count - 1)
            print(f"   平均段长度: {avg_segment_length:.2f}米")

            if 50 <= avg_segment_length <= 200:
                density_status = "✅ 合理"
            elif avg_segment_length < 50:
                density_status = "⚠️ 过密（路径点过多）"
            else:
                density_status = "⚠️ 过疏（路径点过少）"
            print(f"   点密度评估: {density_status}")

        # 路径方向分析
        start_point = path[0]
        end_point = path[-1]
        print(f"   起点: ({start_point.lng:.6f}, {start_point.lat:.6f})")
        print(f"   终点: ({end_point.lng:.6f}, {end_point.lat:.6f})")

        # 路径样本点
        print(f"📍 路径样本点:")
        sample_count = min(5, len(path))
        for i in range(sample_count):
            point = path[i]
            print(f"   点{i+1}: ({point.lng:.6f}, {point.lat:.6f}, {point.alt:.1f}m)")

        if len(path) > 10:
            print(f"   ... (中间省略)")
            for i in range(max(sample_count, len(path)-3), len(path)):
                point = path[i]
                print(f"   点{i+1}: ({point.lng:.6f}, {point.lat:.6f}, {point.alt:.1f}m)")

        # 转向分析
        if len(path) >= 3:
            total_turn_angle = 0
            max_turn_angle = 0
            sharp_turns = 0

            for i in range(1, len(path) - 1):
                p1 = path[i-1]
                p2 = path[i]
                p3 = path[i+1]

                # 计算转向角度
                v1 = (p2.lng - p1.lng, p2.lat - p1.lat)
                v2 = (p3.lng - p2.lng, p3.lat - p2.lat)

                # 计算角度
                dot_product = v1[0] * v2[0] + v1[1] * v2[1]
                mag1 = (v1[0]**2 + v1[1]**2)**0.5
                mag2 = (v2[0]**2 + v2[1]**2)**0.5

                if mag1 > 0 and mag2 > 0:
                    cos_angle = dot_product / (mag1 * mag2)
                    cos_angle = max(-1, min(1, cos_angle))
                    angle = math.acos(cos_angle)
                    angle_degrees = math.degrees(angle)

                    total_turn_angle += angle
                    max_turn_angle = max(max_turn_angle, angle_degrees)

                    if angle_degrees > 90:
                        sharp_turns += 1

            avg_turn_angle = math.degrees(total_turn_angle / (len(path) - 2)) if len(path) > 2 else 0
            print(f"🔄 转向分析:")
            print(f"   平均转向角度: {avg_turn_angle:.1f}度")
            print(f"   最大转向角度: {max_turn_angle:.1f}度")
            print(f"   急转弯次数: {sharp_turns} (>90度)")

            if sharp_turns == 0 and avg_turn_angle < 15:
                turn_status = "✅ 平滑"
            elif sharp_turns <= 2 and avg_turn_angle < 30:
                turn_status = "⚠️ 一般"
            else:
                turn_status = "❌ 弯折较多"
            print(f"   平滑度评估: {turn_status}")

        # 常识性判断
        print(f"🧠 常识性判断:")
        issues = []

        if 'efficiency_ratio' in locals() and efficiency_ratio > 2.0:
            issues.append("路径绕路严重")

        if 'avg_segment_length' in locals() and avg_segment_length < 20:
            issues.append("路径点过于密集")

        if 'sharp_turns' in locals() and sharp_turns > 5:
            issues.append("急转弯过多")

        if not issues:
            print(f"   ✅ 路径符合常识，质量良好")
        else:
            print(f"   ⚠️ 发现问题: {', '.join(issues)}")

        print(f"🔍 ===== 路径质量分析完成 =====\n")

        return {
            "path_points_count": path_points_count,
            "actual_distance": actual_distance,
            "straight_distance": straight_distance,
            "efficiency_ratio": efficiency_ratio if 'efficiency_ratio' in locals() else 0,
            "avg_segment_length": avg_segment_length if 'avg_segment_length' in locals() else 0,
            "avg_turn_angle": avg_turn_angle if 'avg_turn_angle' in locals() else 0,
            "sharp_turns": sharp_turns if 'sharp_turns' in locals() else 0,
            "issues": issues
        }
