#!/usr/bin/env python3
"""
测试碰撞代价修复效果
验证统一检测管理器的碰撞代价是否正确传递到最终结果
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from algorithms.data_structures import PathPlanningRequest
from algorithms.manager import AlgorithmManager

async def test_collision_cost_fix():
    """测试碰撞代价修复效果"""
    
    print("🔍 测试碰撞代价修复效果")
    print("=" * 60)
    
    # 创建算法管理器
    manager = AlgorithmManager()

    # 检查可用的算法
    print(f"🔍 可用算法: {list(manager.algorithms.keys())}")
    
    # 创建测试请求（修复参数格式）
    request_data = {
        'startPoint': {'lng': 139.767300, 'lat': 35.681200, 'alt': 100.0},
        'endPoint': {'lng': 139.773400, 'lat': 35.715300, 'alt': 100.0},
        'flightHeight': 100.0,
        'safetyDistance': 30.0,
        'algorithm': 'ImprovedClusterBased',
        'buildings': [],  # 添加建筑物列表
        'weather': {     # 添加天气条件
            'windSpeed': 5.0,
            'windDirection': 0.0,
            'visibility': 10000.0,
            'precipitation': 0.0,
            'temperature': 20.0
        }
    }
    request = PathPlanningRequest(request_data)
    
    print(f"📍 测试路径：")
    print(f"   起点: ({request.start_point.lng:.6f}, {request.start_point.lat:.6f})")
    print(f"   终点: ({request.end_point.lng:.6f}, {request.end_point.lat:.6f})")
    
    # 1. 测试改进算法
    print(f"\n🔧 1. 测试改进算法碰撞代价")

    try:
        result = await manager.execute_algorithm('ImprovedCluster', request)
        
        print(f"   算法执行成功: {result.success}")
        if not result.success:
            print(f"   执行失败原因: {result.error}")
        print(f"   路径长度: {result.path_length:.2f}米")
        print(f"   转向成本: {result.turning_cost:.4f}")
        print(f"   风险值: {result.risk_value:.4f}")
        print(f"   碰撞代价: {result.collision_cost:.4f}")
        print(f"   最终代价: {result.final_cost:.4f}")
        
        # 检查各种碰撞代价属性
        print(f"\n   碰撞代价属性检查:")
        if hasattr(result, 'estimated_collision_cost'):
            print(f"   - estimated_collision_cost: {result.estimated_collision_cost:.4f}")
        if hasattr(result, 'unified_collision_cost'):
            print(f"   - unified_collision_cost: {result.unified_collision_cost:.4f}")
        if hasattr(result, 'actual_collision_cost'):
            print(f"   - actual_collision_cost: {result.actual_collision_cost:.4f}")
        
        # 分析碰撞代价合理性
        print(f"\n   碰撞代价合理性分析:")
        if result.collision_cost > 1000:
            print(f"   ✅ 碰撞代价 {result.collision_cost:.0f} 在合理范围内（>1000）")
        elif result.collision_cost > 100:
            print(f"   ⚠️ 碰撞代价 {result.collision_cost:.0f} 偏低但可接受（100-1000）")
        else:
            print(f"   ❌ 碰撞代价 {result.collision_cost:.0f} 过低（<100），可能仍有问题")
        
        improved_result = result
        
    except Exception as e:
        print(f"   ❌ 改进算法执行失败: {e}")
        improved_result = None
    
    # 2. 测试A*算法
    print(f"\n🔧 2. 测试A*算法碰撞代价")
    
    try:
        result = await manager.execute_algorithm('A*', request)
        
        print(f"   算法执行成功: {result.success}")
        if not result.success:
            print(f"   执行失败原因: {result.error}")
        print(f"   路径长度: {result.path_length:.2f}米")
        print(f"   转向成本: {result.turning_cost:.4f}")
        print(f"   风险值: {result.risk_value:.4f}")
        print(f"   碰撞代价: {result.collision_cost:.4f}")
        print(f"   最终代价: {result.final_cost:.4f}")
        
        # 检查各种碰撞代价属性
        print(f"\n   碰撞代价属性检查:")
        if hasattr(result, 'estimated_collision_cost'):
            print(f"   - estimated_collision_cost: {result.estimated_collision_cost:.4f}")
        if hasattr(result, 'unified_collision_cost'):
            print(f"   - unified_collision_cost: {result.unified_collision_cost:.4f}")
        if hasattr(result, 'actual_collision_cost'):
            print(f"   - actual_collision_cost: {result.actual_collision_cost:.4f}")
        
        # 分析碰撞代价合理性
        print(f"\n   碰撞代价合理性分析:")
        if result.collision_cost > 1000:
            print(f"   ✅ 碰撞代价 {result.collision_cost:.0f} 在合理范围内（>1000）")
        elif result.collision_cost > 100:
            print(f"   ⚠️ 碰撞代价 {result.collision_cost:.0f} 偏低但可接受（100-1000）")
        else:
            print(f"   ❌ 碰撞代价 {result.collision_cost:.0f} 过低（<100），可能仍有问题")
        
        astar_result = result
        
    except Exception as e:
        print(f"   ❌ A*算法执行失败: {e}")
        astar_result = None
    
    # 3. 对比分析
    print(f"\n📊 3. 对比分析")
    
    if improved_result and astar_result:
        print(f"   改进算法 vs A*算法:")
        print(f"   - 路径长度: {improved_result.path_length:.2f} vs {astar_result.path_length:.2f}")
        print(f"   - 碰撞代价: {improved_result.collision_cost:.2f} vs {astar_result.collision_cost:.2f}")
        print(f"   - 风险值: {improved_result.risk_value:.4f} vs {astar_result.risk_value:.4f}")
        print(f"   - 最终代价: {improved_result.final_cost:.4f} vs {astar_result.final_cost:.4f}")
        
        # 检查碰撞代价是否在同一数量级
        collision_ratio = max(improved_result.collision_cost, astar_result.collision_cost) / max(min(improved_result.collision_cost, astar_result.collision_cost), 1)
        
        if collision_ratio < 10:
            print(f"   ✅ 两个算法的碰撞代价在同一数量级（比例: {collision_ratio:.1f}）")
        else:
            print(f"   ⚠️ 两个算法的碰撞代价差异较大（比例: {collision_ratio:.1f}）")
    
    # 4. 修复效果总结
    print(f"\n✅ 4. 修复效果总结")
    
    success_count = 0
    total_tests = 0
    
    if improved_result:
        total_tests += 1
        if improved_result.collision_cost > 100:
            success_count += 1
            print(f"   ✅ 改进算法碰撞代价修复成功")
        else:
            print(f"   ❌ 改进算法碰撞代价仍有问题")
    
    if astar_result:
        total_tests += 1
        if astar_result.collision_cost > 100:
            success_count += 1
            print(f"   ✅ A*算法碰撞代价修复成功")
        else:
            print(f"   ❌ A*算法碰撞代价仍有问题")
    
    success_rate = (success_count / total_tests * 100) if total_tests > 0 else 0
    print(f"   修复成功率: {success_rate:.0f}% ({success_count}/{total_tests})")
    
    if success_rate >= 50:
        print(f"   🎉 碰撞代价修复基本成功！")
    else:
        print(f"   ⚠️ 碰撞代价修复仍需进一步调整")
    
    return {
        'improved_collision_cost': improved_result.collision_cost if improved_result else 0,
        'astar_collision_cost': astar_result.collision_cost if astar_result else 0,
        'success_rate': success_rate
    }

if __name__ == "__main__":
    try:
        result = asyncio.run(test_collision_cost_fix())
        print(f"\n✅ 测试完成")
        print(f"   改进算法碰撞代价: {result['improved_collision_cost']:.2f}")
        print(f"   A*算法碰撞代价: {result['astar_collision_cost']:.2f}")
        print(f"   修复成功率: {result['success_rate']:.0f}%")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
