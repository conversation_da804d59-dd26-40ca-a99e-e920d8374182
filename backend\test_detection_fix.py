#!/usr/bin/env python3
"""
测试保护区检测修复效果
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

def test_protection_zone_detection():
    """测试保护区检测修复"""
    
    print("🔍 测试保护区检测修复效果")
    print("=" * 50)
    
    try:
        from protection_zones import ProtectionZoneManager
        
        # 创建保护区管理器
        manager = ProtectionZoneManager()
        print(f"✅ 保护区管理器创建成功，共 {len(manager.zones)} 个保护区")
        
        # 找到东京站
        tokyo_station = None
        for zone in manager.zones:
            if zone.id == "tokyo_station":
                tokyo_station = zone
                break
        
        if not tokyo_station:
            print("❌ 未找到东京站保护区")
            return False
        
        print(f"✅ 找到东京站保护区:")
        print(f"   中心: {tokyo_station.center}")
        print(f"   半径: {tokyo_station.radius}米")
        print(f"   碰撞代价: {tokyo_station.average_crash_cost}/m²")
        
        # 测试东京站内的路径
        tokyo_path = [
            (139.7673, 35.6812),  # 东京站中心
            (139.7670, 35.6810),  # 东京站西南
            (139.7676, 35.6814),  # 东京站东北
        ]
        
        print(f"\n📍 测试路径（东京站内）:")
        for i, (lng, lat) in enumerate(tokyo_path):
            distance = tokyo_station.get_distance_to_point(lng, lat)
            print(f"   点{i+1}: ({lng}, {lat}) - 距离中心 {distance:.1f}米")
        
        # 测试保护区检测
        print(f"\n🛡️ 保护区检测测试:")
        relevant_zones = manager.get_zones_for_path(tokyo_path)
        
        tokyo_detected = any(zone.id == "tokyo_station" for zone in relevant_zones)
        
        if tokyo_detected:
            print(f"✅ 成功检测到东京站保护区！")
            print(f"   检测到的保护区:")
            for zone in relevant_zones:
                print(f"   - {zone.name} (半径: {zone.radius}m)")
        else:
            print(f"❌ 未检测到东京站保护区")
            print(f"   检测到的保护区:")
            for zone in relevant_zones:
                print(f"   - {zone.name} (半径: {zone.radius}m)")
        
        # 测试碰撞代价计算
        print(f"\n💰 碰撞代价计算测试:")
        total_cost = manager.calculate_path_collision_cost(tokyo_path)
        print(f"   总碰撞代价: {total_cost:.4f}")
        
        if total_cost > 0:
            print(f"✅ 碰撞代价计算正常")
        else:
            print(f"❌ 碰撞代价为0，可能检测有问题")
        
        return tokyo_detected and total_cost > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试"""
    print("🚀 开始测试保护区检测修复...")
    
    success = test_protection_zone_detection()
    
    print(f"\n" + "=" * 50)
    if success:
        print("🎉 测试通过！保护区检测修复成功！")
        print("💡 现在在东京站范围内应该能检测到东京站参与运算")
    else:
        print("❌ 测试失败，需要进一步检查")
    
    return success

if __name__ == "__main__":
    main()
