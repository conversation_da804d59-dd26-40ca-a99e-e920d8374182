#!/usr/bin/env python3
"""
测试手动路径规划vs自动算法计算的差异
检查缓存系统和数据一致性问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from algorithms.data_structures import Point3D, PathPlanningRequest
from algorithms.unified_detection_manager import UnifiedDetectionManager
from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathPlanning, CostCalculator

def test_manual_vs_auto_calculation():
    """测试手动路径vs自动计算的差异"""
    
    print("🔍 测试手动路径规划vs自动算法计算的差异")
    print("=" * 60)
    
    # 1. 创建相同的测试路径（修复坐标系统）
    manual_waypoints = [
        Point3D(lng=139.767300, lat=35.681200, alt=100.0, x=139.767300, y=35.681200, z=100),
        Point3D(lng=139.768000, lat=35.685000, alt=100.0, x=139.768000, y=35.685000, z=100),
        Point3D(lng=139.770000, lat=35.690000, alt=100.0, x=139.770000, y=35.690000, z=100),
        Point3D(lng=139.772000, lat=35.700000, alt=100.0, x=139.772000, y=35.700000, z=100),
        Point3D(lng=139.773400, lat=35.715300, alt=100.0, x=139.773400, y=35.715300, z=100)
    ]
    
    print(f"📍 手动路径航点数量: {len(manual_waypoints)}")
    print(f"📍 起点: ({manual_waypoints[0].lng:.6f}, {manual_waypoints[0].lat:.6f})")
    print(f"📍 终点: ({manual_waypoints[-1].lng:.6f}, {manual_waypoints[-1].lat:.6f})")
    
    # 2. 手动计算（不使用缓存）
    print("\n🔧 手动计算（清除所有缓存）:")
    
    # 创建新的代价计算器（不使用缓存）
    manual_calculator = CostCalculator({
        'flightHeight': 100.0,
        'safetyDistance': 30.0,
        'gridSize': 10.0,
        'maxTurnAngle': 90.0,
        'riskEdgeDistance': 50.0,
        'kValue': 5
    })
    
    # 手动生成建筑物数据（不使用缓存）
    manual_buildings = []
    for i in range(10):
        building = {
            'id': f'manual_building_{i}',
            'x': 100 + i * 200,
            'y': 100 + i * 300,
            'lng': 139.767 + i * 0.001,
            'lat': 35.681 + i * 0.002,
            'height': 30 + i * 10,
            'width': 20,
            'length': 25,
            'type': 'residential',
            'area': 500,
            'source': 'manual_generated'
        }
        manual_buildings.append(building)
    
    manual_calculator.set_buildings_data(manual_buildings)
    
    # 手动计算各项指标
    manual_length = manual_calculator.calculate_path_length(manual_waypoints)
    manual_turning = manual_calculator.calculate_turning_cost(manual_waypoints)
    manual_risk, _ = manual_calculator.calculate_risk_value(manual_waypoints, manual_buildings, manual_length)
    
    print(f"   路径长度: {manual_length:.2f}m")
    print(f"   转向成本: {manual_turning:.4f}")
    print(f"   风险值: {manual_risk:.4f}")
    print(f"   建筑物数量: {len(manual_buildings)}")
    
    # 3. 自动算法计算（使用缓存系统）
    print("\n🤖 自动算法计算（使用缓存系统）:")
    
    # 创建算法请求
    request_data = {
        'startPoint': {'lng': 139.767300, 'lat': 35.681200, 'alt': 100.0},
        'endPoint': {'lng': 139.773400, 'lat': 35.715300, 'alt': 100.0},
        'flightHeight': 100.0,
        'safetyDistance': 30.0,
        'algorithm': 'ImprovedClusterBased'
    }
    request = PathPlanningRequest(request_data)
    
    # 创建算法实例
    algorithm = ImprovedClusterBasedPathPlanning()
    
    # 使用统一检测管理器
    unified_manager = UnifiedDetectionManager()
    result = unified_manager.detect_for_path(manual_waypoints, 100.0)
    
    print(f"   统一检测建筑物数量: {len(result.buildings)}")
    print(f"   统一检测保护区数量: {len(result.protection_zones)}")
    print(f"   统一检测风险值: {result.risk_value:.4f}")
    print(f"   统一检测碰撞代价: {result.collision_cost:.4f}")
    
    # 4. 对比分析
    print("\n📊 对比分析:")
    print("-" * 40)
    
    length_diff = abs(manual_length - 3800) / 3800 * 100  # 估算自动算法长度

    # 修复除零错误
    max_risk = max(manual_risk, result.risk_value)
    if max_risk > 0:
        risk_diff = abs(manual_risk - result.risk_value) / max_risk * 100
    else:
        risk_diff = 0.0  # 两个都是0，差异为0
    
    print(f"路径长度差异: {length_diff:.1f}%")
    print(f"风险值差异: {risk_diff:.1f}%")
    print(f"建筑物数据源: 手动={len(manual_buildings)}, 自动={len(result.buildings)}")
    
    # 5. 检查缓存状态
    print("\n🗄️ 缓存状态检查:")
    
    # 检查建筑物检测器缓存
    if hasattr(unified_manager.building_detector, '_cache'):
        cache_size = len(unified_manager.building_detector._cache)
        print(f"   建筑物检测器缓存条目: {cache_size}")
    
    # 检查全局缓存
    if hasattr(unified_manager.building_detector, '_global_buildings_cache_ref'):
        global_cache = unified_manager.building_detector._global_buildings_cache_ref
        if global_cache:
            print(f"   全局建筑物缓存: 存在")
        else:
            print(f"   全局建筑物缓存: 不存在")
    
    # 6. 强制数据替换检查
    print("\n⚠️ 强制数据替换检查:")
    
    # 检查是否有强制替换逻辑
    original_buildings = result.buildings.copy()
    
    # 模拟再次调用
    result2 = unified_manager.detect_for_path(manual_waypoints, 100.0)
    
    if len(result2.buildings) != len(original_buildings):
        print(f"   ❌ 检测到数据不一致！第一次:{len(original_buildings)}, 第二次:{len(result2.buildings)}")
    else:
        print(f"   ✅ 数据一致性检查通过")
    
    # 7. 建议修复方案
    print("\n💡 建议修复方案:")
    print("1. 禁用强制数据替换逻辑")
    print("2. 统一手动和自动的建筑物数据源")
    print("3. 清理多层缓存冲突")
    print("4. 确保缓存键的一致性")
    
    return {
        'manual_length': manual_length,
        'manual_risk': manual_risk,
        'auto_risk': result.risk_value,
        'manual_buildings': len(manual_buildings),
        'auto_buildings': len(result.buildings),
        'risk_diff_percent': risk_diff
    }

if __name__ == "__main__":
    try:
        result = test_manual_vs_auto_calculation()
        print(f"\n✅ 测试完成，风险值差异: {result['risk_diff_percent']:.1f}%")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
