"""
数据加载工具模块
负责FBX模型文件的索引、管理和元数据提取
"""
import os
import json
import glob
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import xml.etree.ElementTree as ET

class Tokyo23DataLoader:
    """东京23区数据加载器"""
    
    def __init__(self, data_path: str):
        self.data_path = Path(data_path)
        self.tokyo23_path = self.data_path / 'tokyo23'
        
        # 数据类型映射
        self.data_types = {
            'bldg': '建筑物',
            'brid': '桥梁', 
            'dem': '地形',
            'tran': '交通道路'
        }
        
        # LOD级别映射
        self.lod_levels = {
            'lod1': 'LOD1 - 基础立体模型',
            'lod2': 'LOD2 - 详细立体模型'
        }
        
        # 缓存
        self._file_index = None
        self._metadata = None
    
    def get_file_index(self) -> Dict:
        """获取所有FBX文件的索引"""
        if self._file_index is None:
            self._build_file_index()
        return self._file_index
    
    def _build_file_index(self):
        """构建文件索引"""
        self._file_index = {
            'buildings': {
                'lod1': [],
                'lod2': []
            },
            'bridges': [],
            'terrain': [],
            'transportation': [],
            'summary': {
                'total_files': 0,
                'total_size_mb': 0,
                'by_type': {}
            }
        }
        
        try:
            # 索引建筑物文件
            self._index_buildings()
            
            # 索引其他类型文件
            self._index_bridges()
            self._index_terrain()
            self._index_transportation()
            
            # 计算统计信息
            self._calculate_summary()
            
        except Exception as e:
            print(f"构建文件索引时出错: {e}")
    
    def _index_buildings(self):
        """索引建筑物文件"""
        bldg_path = self.tokyo23_path / 'bldg'
        
        # LOD1建筑物
        lod1_path = bldg_path / 'lod1'
        if lod1_path.exists():
            for fbx_file in lod1_path.glob('*.fbx'):
                file_info = self._get_file_info(fbx_file, 'building', 'lod1')
                self._file_index['buildings']['lod1'].append(file_info)
        
        # LOD2建筑物
        lod2_path = bldg_path / 'lod2'
        if lod2_path.exists():
            for fbx_file in lod2_path.glob('*.fbx'):
                file_info = self._get_file_info(fbx_file, 'building', 'lod2')
                self._file_index['buildings']['lod2'].append(file_info)
    
    def _index_bridges(self):
        """索引桥梁文件"""
        brid_path = self.tokyo23_path / 'brid'
        if brid_path.exists():
            for fbx_file in brid_path.glob('*.fbx'):
                file_info = self._get_file_info(fbx_file, 'bridge')
                self._file_index['bridges'].append(file_info)
    
    def _index_terrain(self):
        """索引地形文件"""
        dem_path = self.tokyo23_path / 'dem'
        if dem_path.exists():
            for fbx_file in dem_path.glob('*.fbx'):
                file_info = self._get_file_info(fbx_file, 'terrain')
                self._file_index['terrain'].append(file_info)
    
    def _index_transportation(self):
        """索引交通道路文件"""
        tran_path = self.tokyo23_path / 'tran'
        if tran_path.exists():
            for fbx_file in tran_path.glob('*.fbx'):
                file_info = self._get_file_info(fbx_file, 'transportation')
                self._file_index['transportation'].append(file_info)
    
    def _get_file_info(self, file_path: Path, data_type: str, lod_level: str = None) -> Dict:
        """获取文件信息"""
        try:
            stat = file_path.stat()
            
            # 解析文件名获取网格编号
            filename = file_path.stem
            parts = filename.split('_')
            mesh_id = parts[0] if parts else filename
            
            file_info = {
                'filename': file_path.name,
                'path': str(file_path),
                'relative_path': str(file_path.relative_to(self.data_path)),
                'size_bytes': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'data_type': data_type,
                'mesh_id': mesh_id,
                'modified_time': stat.st_mtime
            }
            
            if lod_level:
                file_info['lod_level'] = lod_level
            
            return file_info
            
        except Exception as e:
            print(f"获取文件信息失败 {file_path}: {e}")
            return {}
    
    def _calculate_summary(self):
        """计算统计摘要"""
        summary = self._file_index['summary']
        
        # 统计各类型文件数量和大小
        type_stats = {}
        
        # 建筑物统计
        lod1_count = len(self._file_index['buildings']['lod1'])
        lod2_count = len(self._file_index['buildings']['lod2'])
        lod1_size = sum(f.get('size_mb', 0) for f in self._file_index['buildings']['lod1'])
        lod2_size = sum(f.get('size_mb', 0) for f in self._file_index['buildings']['lod2'])
        
        type_stats['buildings'] = {
            'lod1': {'count': lod1_count, 'size_mb': round(lod1_size, 2)},
            'lod2': {'count': lod2_count, 'size_mb': round(lod2_size, 2)},
            'total': {'count': lod1_count + lod2_count, 'size_mb': round(lod1_size + lod2_size, 2)}
        }
        
        # 其他类型统计
        for data_type in ['bridges', 'terrain', 'transportation']:
            files = self._file_index[data_type]
            count = len(files)
            size = sum(f.get('size_mb', 0) for f in files)
            type_stats[data_type] = {
                'count': count,
                'size_mb': round(size, 2)
            }
        
        # 总计
        total_files = sum(
            type_stats['buildings']['total']['count'] +
            sum(type_stats[t]['count'] for t in ['bridges', 'terrain', 'transportation'])
        )
        total_size = sum(
            type_stats['buildings']['total']['size_mb'] +
            sum(type_stats[t]['size_mb'] for t in ['bridges', 'terrain', 'transportation'])
        )
        
        summary['total_files'] = total_files
        summary['total_size_mb'] = round(total_size, 2)
        summary['by_type'] = type_stats
    
    def get_metadata(self) -> Dict:
        """获取元数据信息"""
        if self._metadata is None:
            self._load_metadata()
        return self._metadata
    
    def _load_metadata(self):
        """加载元数据"""
        metadata_file = self.tokyo23_path / 'metadata' / 'fbx_13100_2020_metadata_op.xml'
        
        self._metadata = {
            'title': '3D都市モデル_13100_2020',
            'description': '东京23区3D城市模型',
            'coordinate_system': 'JGD2011',
            'coverage_area': '东京都区部全域',
            'data_source': '国土交通省都市局',
            'creation_date': '2022-03-01',
            'lod_info': {
                'lod1': '基础立体模型，统一高度',
                'lod2': '详细立体模型，真实形状'
            }
        }
        
        if metadata_file.exists():
            try:
                tree = ET.parse(metadata_file)
                root = tree.getroot()
                
                # 提取更多元数据信息
                # 这里可以根据需要解析XML内容
                
            except Exception as e:
                print(f"解析元数据文件失败: {e}")
    
    def get_recommended_files(self, max_files: int = 20, prefer_lod: str = 'lod1') -> List[Dict]:
        """获取推荐加载的文件列表"""
        index = self.get_file_index()
        recommended = []
        
        # 优先选择建筑物文件
        buildings = index['buildings'][prefer_lod]
        if buildings:
            # 按文件大小排序，选择中等大小的文件
            sorted_buildings = sorted(buildings, key=lambda x: x.get('size_mb', 0))
            mid_start = len(sorted_buildings) // 4
            mid_end = mid_start + min(max_files // 2, len(sorted_buildings) // 2)
            recommended.extend(sorted_buildings[mid_start:mid_end])
        
        # 添加一些地形文件
        terrain_count = min(3, len(index['terrain']))
        recommended.extend(index['terrain'][:terrain_count])
        
        # 添加一些桥梁文件
        bridge_count = min(2, len(index['bridges']))
        recommended.extend(index['bridges'][:bridge_count])
        
        # 添加一些道路文件
        road_count = min(3, len(index['transportation']))
        recommended.extend(index['transportation'][:road_count])
        
        return recommended[:max_files]
    
    def get_files_by_area(self, mesh_ids: List[str]) -> List[Dict]:
        """根据网格ID获取特定区域的文件"""
        index = self.get_file_index()
        result = []
        
        for category in ['buildings', 'bridges', 'terrain', 'transportation']:
            if category == 'buildings':
                for lod in ['lod1', 'lod2']:
                    for file_info in index[category][lod]:
                        if file_info.get('mesh_id') in mesh_ids:
                            result.append(file_info)
            else:
                for file_info in index[category]:
                    if file_info.get('mesh_id') in mesh_ids:
                        result.append(file_info)
        
        return result
    
    def get_file_url(self, relative_path: str) -> str:
        """获取文件的URL路径"""
        # 将Windows路径分隔符转换为URL路径分隔符
        url_path = relative_path.replace('\\', '/')
        return f"/data/{url_path}"
    
    def validate_data_integrity(self) -> Dict:
        """验证数据完整性"""
        index = self.get_file_index()
        
        validation_result = {
            'is_valid': True,
            'missing_files': [],
            'corrupted_files': [],
            'warnings': []
        }
        
        # 检查关键目录是否存在
        required_dirs = ['bldg/lod1', 'bldg/lod2', 'dem', 'tran', 'brid']
        for dir_path in required_dirs:
            full_path = self.tokyo23_path / dir_path
            if not full_path.exists():
                validation_result['warnings'].append(f"目录不存在: {dir_path}")
        
        # 检查是否有足够的文件
        if len(index['buildings']['lod1']) < 10:
            validation_result['warnings'].append("LOD1建筑物文件数量较少")
        
        if len(index['terrain']) == 0:
            validation_result['warnings'].append("缺少地形文件")
        
        return validation_result

# 全局数据加载器实例
_data_loader = None

def get_data_loader() -> Tokyo23DataLoader:
    """获取数据加载器单例"""
    global _data_loader
    if _data_loader is None:
        try:
            from flask import current_app
            data_path = current_app.config.get('DATA_PATH')  # 使用DATA_PATH而不是TOKYO23_DATA_PATH
            _data_loader = Tokyo23DataLoader(data_path)
        except Exception as e:
            print(f"创建数据加载器失败: {e}")
            # 降级到默认路径
            import os
            default_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '..', 'data')
            _data_loader = Tokyo23DataLoader(default_path)
    return _data_loader
