#!/usr/bin/env python3
"""
测试改进算法的保护区检测
直接调用改进算法，检查保护区检测逻辑
"""

import asyncio
from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathPlanning
from algorithms.data_structures import PathPlanningRequest

async def test_improved_algorithm_protection_zones():
    """测试改进算法的保护区检测"""
    
    print("🔍 测试改进算法的保护区检测")
    print("=" * 80)
    
    # 创建改进算法实例
    algorithm = ImprovedClusterBasedPathPlanning()

    # 设置A*算法实例
    from algorithms.astar import AStarAlgorithm
    astar = AStarAlgorithm()
    algorithm.set_astar_algorithm(astar)

    # 创建测试请求（使用正确的字段名）
    request_data = {
        "startPoint": {"lng": 139.7670, "lat": 35.6810, "alt": 120, "x": 0, "y": 0, "z": 120},
        "endPoint": {"lng": 139.7016, "lat": 35.6598, "alt": 120, "x": 6000, "y": -2000, "z": 120}
    }

    request = PathPlanningRequest(request_data)
    
    print(f"📋 测试请求:")
    print(f"   起点: ({request.start_point.lng:.6f}, {request.start_point.lat:.6f})")
    print(f"   终点: ({request.end_point.lng:.6f}, {request.end_point.lat:.6f})")
    
    try:
        # 调用改进算法
        print(f"\n🚀 调用改进算法...")
        response = await algorithm.calculate_path(request)
        
        print(f"✅ 算法执行完成")
        print(f"   成功: {response.success}")
        print(f"   路径点数: {len(response.path) if response.path else 0}")
        
        # 检查保护区信息
        if hasattr(response, 'protectionZonesInfo'):
            protection_info = response.protectionZonesInfo
            print(f"\n🛡️ 保护区信息:")
            print(f"   protectionZonesInfo存在: {protection_info is not None}")
            
            if protection_info:
                print(f"   字段: {list(protection_info.keys())}")
                
                if 'collision_cost_breakdown' in protection_info:
                    breakdown = protection_info['collision_cost_breakdown']
                    print(f"   collision_cost_breakdown类型: {type(breakdown)}")
                    print(f"   活跃保护区数: {len(breakdown) if breakdown else 0}")
                    
                    if breakdown:
                        print(f"   活跃保护区ID: {list(breakdown.keys())}")
                        for zone_id, info in breakdown.items():
                            print(f"     {zone_id}: {info.get('zone_name', 'Unknown')} (代价: {info.get('total_cost', 0):.4f})")
                    else:
                        print(f"   ❌ collision_cost_breakdown为空")
                        
                        # 检查其他字段
                        if 'total_zones' in protection_info:
                            print(f"   总保护区数: {protection_info['total_zones']}")
                        if 'involved_zones' in protection_info:
                            print(f"   涉及的航点数: {len(protection_info['involved_zones'])}")
                        if 'summary' in protection_info:
                            summary = protection_info['summary']
                            print(f"   摘要: {summary}")
                else:
                    print(f"   ❌ 没有collision_cost_breakdown字段")
            else:
                print(f"   ❌ protectionZonesInfo为None")
        else:
            print(f"\n❌ 响应中没有protectionZonesInfo属性")
        
        # 检查路径点格式
        if response.path and len(response.path) > 0:
            print(f"\n📍 路径点格式检查:")
            print(f"   路径点数: {len(response.path)}")
            
            # 检查前3个路径点
            for i, point in enumerate(response.path[:3]):
                print(f"   点{i+1}: lng={getattr(point, 'lng', 'N/A'):.6f}, lat={getattr(point, 'lat', 'N/A'):.6f}")
                
                # 检查点的属性
                attrs = [attr for attr in dir(point) if not attr.startswith('_')]
                print(f"     属性: {attrs}")
        
        return response
        
    except Exception as e:
        print(f"❌ 改进算法执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_protection_zone_manager():
    """测试保护区管理器"""
    
    print(f"\n🛡️ 测试保护区管理器")
    print("=" * 40)
    
    try:
        from protection_zones import ProtectionZoneManager
        manager = ProtectionZoneManager()
        
        print(f"   保护区总数: {len(manager.zones)}")
        
        # 测试路径点
        test_points = [
            (139.7670, 35.6810),  # 东京站附近
            (139.7673, 35.6812),  # 东京站中心
            (139.7016, 35.6598),  # 涩谷十字路口
        ]
        
        print(f"   测试路径点: {test_points}")
        
        # 获取相关保护区
        relevant_zones = manager.get_zones_for_path(test_points, buffer_distance=500)
        print(f"   相关保护区数: {len(relevant_zones)}")
        
        for zone in relevant_zones:
            print(f"     - {zone.name}: 半径{zone.radius}m")
        
        # 测试碰撞代价计算
        total_cost = 0.0
        for lng, lat in test_points:
            point_cost = 0.0
            for zone in relevant_zones:
                cost = zone.get_collision_cost(lng, lat)
                if cost > 0:
                    point_cost += cost
                    print(f"     点({lng:.6f}, {lat:.6f}) 在 {zone.name}: {cost:.4f}")
            
            if point_cost > 0:
                total_cost += point_cost
                print(f"   点({lng:.6f}, {lat:.6f}) 总代价: {point_cost:.4f}")
        
        print(f"   路径总代价: {total_cost:.4f}")
        
        return len(relevant_zones) > 0 and total_cost > 0
        
    except Exception as e:
        print(f"❌ 保护区管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    
    print("🚀 开始改进算法保护区检测测试")
    
    # 1. 测试保护区管理器
    protection_manager_ok = await test_protection_zone_manager()
    
    # 2. 测试改进算法
    response = await test_improved_algorithm_protection_zones()
    
    # 3. 分析结果
    print(f"\n📊 测试结果分析:")
    print(f"   保护区管理器正常: {'✅' if protection_manager_ok else '❌'}")
    print(f"   改进算法执行成功: {'✅' if response and response.success else '❌'}")
    
    if response and hasattr(response, 'protectionZonesInfo') and response.protectionZonesInfo:
        breakdown = response.protectionZonesInfo.get('collision_cost_breakdown', {})
        has_active_zones = len(breakdown) > 0
        print(f"   改进算法检测到保护区: {'✅' if has_active_zones else '❌'}")
        
        if not has_active_zones:
            print(f"\n💡 问题诊断:")
            print(f"   1. 保护区管理器工作正常，但改进算法没有检测到保护区")
            print(f"   2. 可能的原因:")
            print(f"      - 路径点格式不正确")
            print(f"      - _collect_protection_zones_info方法有bug")
            print(f"      - 保护区检测逻辑有问题")
            print(f"      - 距离计算有误")
    else:
        print(f"   改进算法检测到保护区: ❌")
        print(f"\n💡 问题诊断:")
        print(f"   1. 改进算法没有生成protectionZonesInfo")
        print(f"   2. 可能的原因:")
        print(f"      - _collect_protection_zones_info方法没有被调用")
        print(f"      - _collect_protection_zones_info方法执行失败")
        print(f"      - 响应对象设置有问题")

if __name__ == "__main__":
    asyncio.run(main())
