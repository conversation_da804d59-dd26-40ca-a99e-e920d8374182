# 📋 无人机后端日志系统

## 🎯 系统概述

本日志系统为无人机路径规划后端提供完整的日志记录、查看和分析功能。系统严格按步骤记录后端输出，支持终端和文件双重输出。

## 📁 文件结构

```
logs/
├── README.md                           # 本文档
├── drone_backend_YYYYMMDD.log         # 主日志文件（按日期分文件）
├── drone_errors_YYYYMMDD.log          # 错误日志文件
├── drone_steps_YYYYMMDD.json          # 步骤日志文件（JSON格式）
└── .gitkeep                           # 保持目录存在
```

## 🔧 日志系统组件

### 1. 核心日志器 (`backend/utils/logger.py`)
- **DroneLogger**: 主要日志器类
- **StepType**: 步骤类型枚举
- **LogLevel**: 日志级别枚举

### 2. 日志查看器 (`backend/utils/log_viewer.py`)
- **LogViewer**: 日志分析和查看工具
- 支持按日期、会话、算法筛选
- 提供性能分析功能

### 3. Web界面 (`frontend/src/components/LogViewer.vue`)
- 可视化日志查看界面
- 实时日志监控
- 统计信息展示

### 4. API接口 (`backend/api/logs.py`)
- RESTful API接口
- 支持日志查询、导出、清理

## 📊 日志类型

### 步骤类型 (StepType)
- **算法开始/结束**: 算法执行的开始和结束
- **路径生成**: 初始路径集生成过程
- **分簇处理**: 固定空间分簇操作
- **代价计算**: 各种代价函数计算
- **路径切换**: 动态换路策略执行
- **错误处理**: 异常和错误处理
- **API请求/响应**: HTTP接口调用
- **数据库操作**: 数据存储操作
- **文件操作**: 文件读写操作
- **系统信息**: 系统状态信息

### 日志级别 (LogLevel)
- **DEBUG**: 调试信息
- **INFO**: 一般信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

## 🚀 使用方法

### 1. 在代码中记录日志

```python
from utils.logger import get_logger, StepType, LogLevel

# 获取日志器
logger = get_logger()

# 开始算法执行
logger.start_algorithm(
    algorithm_name="ImprovedClusterBased",
    request_id="req_001",
    params={"start_point": "(0,0,100)", "end_point": "(1000,1000,100)"}
)

# 记录路径生成
logger.log_path_generation(
    path_count=81,
    generation_time=150.5,
    details={"method": "优化版本"}
)

# 记录分簇
logger.log_clustering(
    cluster_count=13,
    path_count=81,
    clustering_time=75.2
)

# 记录错误
try:
    # 一些操作
    pass
except Exception as e:
    logger.log_error(e, "操作上下文")

# 结束算法执行
logger.end_algorithm(
    success=True,
    result={"path_count": 15, "total_time": 620.3}
)
```

### 2. 命令行查看日志

```bash
# 查看最近24小时活动
python backend/utils/log_viewer.py recent

# 查看最近48小时活动
python backend/utils/log_viewer.py recent 48

# 查看特定会话详情
python backend/utils/log_viewer.py session 20241226_143022

# 查看错误日志
python backend/utils/log_viewer.py errors

# 查看今天的错误
python backend/utils/log_viewer.py errors 20241226

# 查看可用日期
python backend/utils/log_viewer.py dates
```

### 3. Web界面查看

访问前端应用中的日志查看器组件：
- 选择日期查看特定日期的日志
- 按级别、步骤类型筛选
- 点击日志行查看详细信息
- 查看统计信息和性能指标

### 4. API接口使用

```bash
# 获取可用日期
GET /api/logs/dates

# 获取特定日期的步骤日志
GET /api/logs/steps/20241226

# 获取会话步骤
GET /api/logs/session/20241226_143022

# 获取算法步骤
GET /api/logs/algorithm/ImprovedClusterBased

# 获取错误日志
GET /api/logs/errors

# 获取最近会话
GET /api/logs/recent-sessions?hours=24

# 分析会话性能
GET /api/logs/session/20241226_143022/analysis

# 获取统计信息
GET /api/logs/statistics

# 导出日志
GET /api/logs/export/20241226?format=json
GET /api/logs/export/20241226?format=csv

# 清理旧日志
DELETE /api/logs/clean?days=30

# 检查系统健康状态
GET /api/logs/health
```

## 📈 日志分析功能

### 1. 会话性能分析
- 总执行时间
- 步骤数量统计
- 错误数量和详情
- 算法执行情况
- 平均步骤耗时

### 2. 统计信息
- 步骤类型分布
- 日志级别分布
- 算法执行次数
- 会话数量
- 错误趋势

### 3. 性能指标
- 平均步骤耗时
- 最长步骤耗时
- 总测量耗时
- 算法性能对比

## 🔧 配置选项

### 日志文件配置
- 日志文件按日期自动分割
- 错误日志单独存储
- JSON格式步骤日志便于分析

### 清理策略
- 默认保留30天日志
- 支持手动清理旧日志
- 自动压缩历史日志（可选）

## 🚨 注意事项

1. **磁盘空间**: 定期清理旧日志文件，避免占用过多磁盘空间
2. **性能影响**: 日志记录对性能影响很小，但大量并发时需注意
3. **敏感信息**: 避免在日志中记录敏感信息（密码、密钥等）
4. **文件权限**: 确保日志目录有适当的读写权限

## 🔍 故障排除

### 常见问题

1. **日志文件不存在**
   - 检查logs目录是否存在
   - 检查文件权限
   - 确认日志器已正确初始化

2. **日志记录失败**
   - 检查磁盘空间
   - 检查文件权限
   - 查看控制台错误信息

3. **Web界面无法加载日志**
   - 检查API接口是否正常
   - 确认日志文件格式正确
   - 检查网络连接

### 调试方法

```python
# 测试日志系统
python backend/test_logger.py

# 检查日志系统健康状态
curl http://localhost:8000/api/logs/health
```

## 📝 更新日志

- **v1.0.0**: 初始版本，基础日志功能
- 支持步骤记录、错误追踪、性能分析
- 提供Web界面和API接口
- 支持日志导出和清理功能
