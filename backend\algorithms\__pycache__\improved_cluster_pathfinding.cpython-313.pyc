�

    ˾�hd� �                   ��  � S r SSKrSSKrSSKrSSKrSSKJrJrJ	r	J
r
JrJr  SSK
JrJr  SSKJr  SSKrSSKJr  SSKJrJrJr  SS	KJrJrJrJr  SS
KJr   " S S\5      r \ " S
 S5      5       r!\ " S S5      5       r"\ " S S5      5       r#\ " S S5      5       r$\ " S S5      5       r% " S S5      r& " S S5      r' " S S5      r( " S S5      r) " S S 5      r* " S! S"5      r+ " S# S$5      r, " S% S&\5      r-g)'u�   
改进的基于分簇的路径规划算法
基于初始路径集生成、固定空间分簇、动态换路策略的创新算法
�    N)�List�Tuple�Dict�Set�Optional�Any)�	dataclass�field)�Enum)�deepcopy�   )�PathPlanningAlgorithm�
AlgorithmInfo�AlgorithmParameter)�PathPlanningRequest�PathPlanningResponse�	PathPoint�Point3D)�AStarAlgorithmc                   �$   � \ rS rSrSrSrSrSrSrg)�
ObjectType�   u   物体类型枚举�bicycle�
pedestrian�vehicle� N)	�__name__�
__module__�__qualname__�__firstlineno__�__doc__�BICYCLE�
PEDESTRIAN�VEHICLE�__static_attributes__r   �    �U   E:\挣钱\无人机建模python版\backend\algorithms\improved_cluster_pathfinding.pyr   r      s   � ���G��J��Gr&   r   c                   ��   � \ rS rSr% SrSr\\S'   Sr\\S'   Sr	\\S'   Sr
\\S'   Sr\\S	'   Sr
\\S
'   Sr\\S'   Sr\\S'   Sr\\S
'   Sr\\S'   \" \S9r\\   \S'   S\\\4   4S jrS\4S jrSrg)�ImprovedPathPoint�   u   扩展的航点类�        �x�y�zr   �waypoint_index�
path_index�position_order�deviation_angle�
risk_value�estimated_collision_cost�actual_collision_cost��default_factory�detected_objects�returnc                 ��   � U R                   U R                  U R                  U R                  U R                  U R
                  U R                  U R                  U R                  U R                  U R                  S.$ )�   转换为字典格式)r,   r-   r.   �
waypointIndex�	pathIndex�
positionOrder�deviationAngle�	riskValue�estimatedCollisionCost�actualCollisionCost�detectedObjects)r,   r-   r.   r/   r0   r1   r2   r3   r4   r5   r8   ��selfs    r'   �to_dict�ImprovedPathPoint.to_dict+   sg   � � ���������!�0�0����!�0�0�"�2�2����&*�&C�&C�#'�#=�#=�#�4�4�
� 	
r&   c                 �T   � [        U R                  U R                  U R                  S9$ )u   转换为标准PathPoint)�lng�lat�alt)r   r,   r-   r.   rD   s    r'   �
to_path_point�ImprovedPathPoint.to_path_point;   s   � ��T�V�V����T�V�V�<�<r&   r   N)r   r   r   r    r!   r,   �float�__annotations__r-   r.   r/   �intr0   r1   r2   r3   r4   r5   r
   �listr8   r   r   �strr   rF   r   rL   r%   r   r&   r'   r)   r)      s�   � ���A�u�N��A�u�N��A�u�N��N�C���J����N�C�� �O�U� ��J���&)��e�)�#&��5�&�#(��#>��d�4�j�>�
��c�3�h�� 
� =�y� =r&   r)   c                   �   � \ rS rSr% Sr\\S'   \\S'   \\   \S'   \	\S'   Sr
\	\S'   S	 rS
\	4S jrSS\S
\	S
\
4S jjrS\S
\	S
\	4S jrSrg)�ProtectionZone�@   u�   
保护区类
碰撞代价保护区分为车辆保护区和行人保护区，是一块封闭的平面区域
具有独立的碰撞代价均值AverageCrashCost，例如15/㎡
�zone_id�	zone_type�polygon_points�collision_cost_densityr+   �areac                 �p   � [        U R                  5      S:�  a  U R                  5       U l        gSU l        g)u   计算面积�   r+   N)�lenrX   �_calculate_polygon_arearZ   rD   s    r'   �
__post_init__�ProtectionZone.__post_init__M   s,   � ��t�"�"�#�q�(��4�4�6�D�I��D�Ir&   r9   c                 �X  � [        U R                  5      S:  a  gSn[        U R                  5      n[        U5       H�  nUS-   U-  n[        U R                  U   SU R                  U   R                  5      n[        U R                  U   SU R                  U   R
                  5      n[        U R                  U   SU R                  U   R                  5      n[        U R                  U   SU R                  U   R
                  5      nXU-  -
  nXU-  -  nM�     [
        U5      S-  $ )u-   计算多边形面积（使用鞋带公式）r\   r+   r
   r,   r-   g       @)r]   rX   �range�getattrrI   rJ   �abs)	rE   rZ   �n�i�j�x1�y1�x2�y2s	            r'   r^   �&ProtectionZone._calculate_polygon_areaT   s  � ��t�"�"�#�a�'������#�#�$���q��A��Q��!��A���,�,�Q�/��d�6I�6I�!�6L�6P�6P�Q�B���,�,�Q�/��d�6I�6I�!�6L�6P�6P�Q�B���,�,�Q�/��d�6I�6I�!�6L�6P�6P�Q�B���,�,�Q�/��d�6I�6I�!�6L�6P�6P�Q�B���G�O�D���G�O�D� � �4�y�3��r&   �point�radiusc                 �  � [        S U R                   5       5      U-
  n[        S U R                   5       5      U-   n[        S U R                   5       5      U-
  n[        S U R                   5       5      U-   nX1R                  s=:*  =(       a    U:*  Os  =(       a    XQR                  s=:*  =(       a    U:*  $ s  $ )u<   判断点是否在保护区内（或指定半径范围内）c              3   �8   #   � U  H  oR                   v �  M     g 7f�N�r,   ��.0�ps     r'   �	<genexpr>�0ProtectionZone.contains_point.<locals>.<genexpr>j   �   � � �5�!4�A�C�C�!4��   �c              3   �8   #   � U  H  oR                   v �  M     g 7frq   rr   rs   s     r'   rv   rw   k   rx   ry   c              3   �8   #   � U  H  oR                   v �  M     g 7frq   �r-   rs   s     r'   rv   rw   l   rx   ry   c              3   �8   #   � U  H  oR                   v �  M     g 7frq   r|   rs   s     r'   rv   rw   m   rx   ry   )�minrX   �maxr,   r-   )rE   rm   rn   �min_x�max_x�min_y�max_ys          r'   �contains_point�ProtectionZone.contains_pointg   s�   � � �5��!4�!4�5�5��>���5��!4�!4�5�5��>���5��!4�!4�5�5��>���5��!4�!4�5�5��>�����(�(�5�(�F�U�g�g�-F�-F��-F�F�-F�Fr&   �centerc                 ��  � U R                   (       d  g[        S U R                    5       5      [        U R                   5      -  n[        S U R                    5       5      [        U R                   5      -  n[        R                  " X1R
                  -
  S-  XAR                  -
  S-  -   5      nXR::  a,  [        R                  U-  U-  n[        U R                  U5      $ g)u�   
计算与圆形区域的交集面积
简化实现：如果保护区中心在圆形范围内，返回保护区面积与圆形面积的较小值
r+   c              3   �P   #   � U  H  n[        US UR                  5      v �  M     g7f)r,   N)rc   rI   rs   s     r'   rv   �7ProtectionZone.get_intersection_area.<locals>.<genexpr>z   �"   � � �P�<O�q�G�A�s�A�E�E�2�2�<O��   �$&c              3   �P   #   � U  H  n[        US UR                  5      v �  M     g7f)r-   N)rc   rJ   rs   s     r'   rv   r�   {   r�   r�   �   )
rX   �sumr]   �math�sqrtr,   r-   �pir~   rZ   )rE   r�   rn   �
zone_center_x�
zone_center_y�distance�circle_areas          r'   �get_intersection_area�$ProtectionZone.get_intersection_areaq   s�   � � �"�"���P�D�<O�<O�P�P�SV�W[�Wj�Wj�Sk�k�
��P�D�<O�<O�P�P�SV�W[�Wj�Wj�Sk�k�
� �9�9�m�h�h�6��:�m�h�h�>V�YZ�=Z�Z�[�� ���'�'�F�*�V�3�K��t�y�y�+�.�.�r&   )rZ   N)r+   )r   r   r   r    r!   rR   rO   r   r   rN   rZ   r_   r^   �boolr�   r�   r%   r   r&   r'   rT   rT   @   sv   � ��
 �L��N���M�!�!�!��D�%����� �&G�G� G�U� G�T� G��G� �U� �u� r&   rT   c                   �|   � \ rS rSr% Sr\\S'   \\S'   \\S'   \" \	S9r
\\\\\4      \S'   S\S	\S
\4S jr
S rS
rg)�
GradientField�   u   梯度场类r/   �gradient_direction�gradient_magnituder6   �object_vectors�angler�   �costc                 �^   � U R                   R                  XU45        U R                  5         g)u   添加物体向量N)r�   �append�_recalculate_gradient)rE   r�   r�   r�   s       r'   �add_object_vector�GradientField.add_object_vector�   s'   � ����"�"�E�T�#:�;��"�"�$r&   c                 �  � U R                   (       d  SU l        SU l        gSnSnU R                    H@  u  p4nU[        R                  " U5      -  nU[        R
                  " U5      -  nX-
  nX'-
  nMB     [        R                  " X-  X"-  -   5      U l        U R                  S:�  a  [        R                  " X!5      U l        gSU l        g)u   重新计算梯度向量r+   Nr   )r�   r�   r�   r�   �cos�sinr�   �atan2)rE   �total_x�total_yr�   r�   r�   �vector_x�vector_ys           r'   r�   �#GradientField._recalculate_gradient�   s�   � ��"�"�&)�D�#�&)�D�#�� ����%)�%8�%8�!�E�T��d�h�h�u�o�-�H��d�h�h�u�o�-�H���G���G� &9� #'�)�)�G�,=��@Q�,Q�"R����"�"�Q�&�&*�j�j��&B�D�#�&)�D�#r&   )r�   r�   N)r   r   r   r    r!   rP   rO   rN   r
   rQ   r�   r   r   r�   r�   r%   r   r&   r'   r�   r�   �   sV   � ��������7<�T�7R�N�D��u�e�U�2�3�4�R�%�u� %�� %�U� %�
*r&   r�   c                   �   � \ rS rSr% Sr\\S'   \\S'   \\\4   \S'   \\\4   \S'   \	" \
S9r\\   \S'   S	r
\\S
'   Sr\\S'   S
\S\S\4S jrS\\\4   4S jrSrg)�PathCluster�   u   路径簇类�
cluster_id�cluster_type�x_range�y_ranger6   �pathsr+   �average_costr   �rankr,   r-   r9   c                 ��   � U R                   S   Us=:*  =(       a    U R                   S   :*  Os  =(       a/    U R                  S   Us=:*  =(       a    U R                  S   :*  $ s  $ )u   判断位置是否在簇内r   r
   )r�   r�   )rE   r,   r-   s      r'   �contains_position�PathCluster.contains_position�   sT   � ����Q��1�7�7����Q��7� 8����Q��1�7�7����Q��7�	9�7�	9r&   �
path_costsc                 ��   ^� U R                   (       d  [        S5      U l        g[        U4S jU R                    5       5      n[	        U R                   5      nX#-  U l        g)u�   
计算簇的平均代价（保密公式）
ClusterFinalCost = (1/n) * Σ PathFinalCost

其中：
- n: 簇内路径数量（3×3 簇为 9 条，4×4 簇为 16 条）
- Σ PathFinalCost: 簇内所有路径的最终代价总和
�infNc              3   �Z   >#   � U  H   nTR                  U[        S 5      5      v �  M"     g7f)r�   N)�getrN   )rt   �path_idr�   s     �r'   rv   �5PathCluster.calculate_average_cost.<locals>.<genexpr>�   s#   �� � �Y�j�7������u��>�>�j�s   �(+)r�   rN   r�   r�   r]   )rE   r�   �
total_costre   s    `  r'   �calculate_average_cost�"PathCluster.calculate_average_cost�   sL   �� � �z�z� %�e��D��� �Y�d�j�j�Y�Y�
� 
��
�
�O�� '�N��r&   �r�   N)r   r   r   r    r!   rR   rO   r   rP   r
   rQ   r�   r   r�   rN   r�   r�   r�   r   r�   r%   r   r&   r'   r�   r�   �   s�   � ���O���
�3��8�_��
�3��8�_���T�2�E�4��9�2��L�%���D�#�M�9�3� 9�3� 9�4� 9�
+��c�5�j�1A� +r&   r�   c                   ��   � \ rS rSr% Sr\\S'   \\S'   \\S'   \" \S9r	\
\   \S'   Sr\
\S	'   S
r\\S'   Sr\
\S'   Sr\
\S
'   Sr\
\S'   Sr\
\S'   S\\\4   4S jrSrg)�PathInfo��   u   路径信息类r�   �flight_direction�height_layerr6   �	waypointsr+   �
final_costr   r�   �path_length�turning_costr3   �collision_costr9   c                 �*  � U R                   U R                  U R                  U R                   Vs/ s H  oR	                  5       PM     snU R
                  U R                  U R                  U R                  U R                  U R                  S.
$ s  snf )r;   )
�pathId�flightDirection�heightLayerr�   �	finalCostr�   �
pathLength�turningCostr@   �
collisionCost)r�   r�   r�   r�   rF   r�   r�   r�   r�   r3   r�   )rE   �wps     r'   rF   �PathInfo.to_dict�   sw   � � �l�l�#�4�4��,�,�15���@��2�*�*�,��@�����I�I��*�*��,�,����!�0�0�
� 	
�� As   �B
r   N)r   r   r   r    r!   rP   rO   r
   rQ   r�   r   r)   r�   rN   r�   r�   r�   r3   r�   r   rR   r   rF   r%   r   r&   r'   r�   r�   �   s|   � ��
�L�����).�t�)D�I�t�%�&�D��J����D�#�M� �K����L�%���J����N�E��

��c�3�h�� 

r&   r�   c                   �L  � \ rS rSrSrS\\\4   4S jrS\	\
   S\4S jrS\
S	\
S\4S
 jr
S\	\
   S\4S jrS\S\4S
 jrS\
S\
S\
S\4S jrS\	\
   S\	\   S\S\\\4   4S jrS\	\   S\4S jrS\
S\	\   S\4S jrS\
S\S\4S jrS\S\4S jrS\	\
   S\	\   S\4S jrS\
S\	\   S\4S jrS\
S\4S  jrS\
S\	\   4S! jr S1S\S#\S$\S%\S&\S'\S(\S)\\\4   S\4S* jjrS\	\   S\S\4S+ jrS\	\
   S\4S, jrS\	\
   S\4S- jr S\	\   4S. jr!S\	\
   S\4S/ jr"S0r#g")2�CostCalculator��   u   四个核心指标计算器�configc                 �  � Xl         UR                  SS5      U l        UR                  SS5      U l        UR                  SS5      U l        [
        R                  S[
        R                  S[
        R                  S	0U l	        g )
N�maxTurnAngle�Z   �riskEdgeDistance�2   �kValue�   �      .@�      $@�       @)
r�   r�   �max_turn_angle�risk_edge_distance�k_valuer   r"   r#   r$   �object_collision_costs�rE   r�   s     r'   �__init__�CostCalculator.__init__�   sm   � ���$�j�j���<���"(�*�*�-?��"D����z�z�(�A�.��� 
�����!�!�4�����'
��#r&   r�   r9   c                 �^  � [        U5      S:  a  gSn[        [        U5      S-
  5       H  nX   nXS-      nUR                  UR                  -
  nUR                  UR                  -
  nUR                  UR                  -
  n[
        R                  " Xf-  Xw-  -   X�-  -   5      n	X)-
  nM�     U$ )uW   
计算路径长度
公式(1): Length = Σ√[(xi+1-xi)² + (yi+1-yi)² + (zi+1-zi)²]
r�   r+   r
   )r]   rb   r,   r-   r.   r�   r�   )
rE   r�   �total_lengthrf   �p1�p2�dx�dy�dz�segment_lengths
             r'   �calculate_path_length�$CostCalculator.calculate_path_length  s�   � �
 �y�>�A������s�9�~��)�*�A���B��q�5�!�B��������B��������B��������B�!�Y�Y�r�u�r�u�}�r�u�'<�=�N��*�L� +� �r&   �start_point�	end_pointc                 ��   � [        UR                  UR                  -
  5      n[        UR                  UR                  -
  5      n[        UR                  UR                  -
  5      nSX4-   U-   -  $ )u{   
计算曼哈顿距离长度
公式(2): Length_manhattan = 3 * (|x_end - x_start| + |y_end - y_start| + |z_end - z_start|)
�      @)rd   r,   r-   r.   )rE   r�   r�   r�   r�   r�   s         r'   �calculate_manhattan_length�)CostCalculator.calculate_manhattan_length  sZ   � �
 ����{�}�}�,�
-��
����{�}�}�,�
-��
����{�}�}�,�
-���b�g��l�#�#r&   c                 ��   � [        U5      S:  a  gSn[        S[        U5      S-
  5       HH  nXS-
     nX   nXS-      nU R                  XEU5      n[        R                  " U5      X   l        X'-
  nMJ     U$ )u�   
计算转向成本
公式(3): OrientAdjustCost = Σ Aθ
其中Aθ是目标航点的前一个航点与目标航点连线的延长线与目标航点与后一个航点的连线的夹角
r\   r+   r
   )r]   rb   �_calculate_deviation_angler�   �degreesr2   )rE   r�   �total_orient_adjust_costrf   �
prev_point�
curr_point�
next_pointr2   s           r'   �calculate_turning_cost�%CostCalculator.calculate_turning_cost(  s�   � � �y�>�A���#&� � �q�#�i�.�1�,�-�A�"�q�5�)�J�"��J�"�q�5�)�J� #�=�=�j�V`�a�O�+/�<�<��+H�I�L�(� 
%�7�$� .� (�'r&   �n_waypointsc                 �f   � US:  a  g[         R                  " U R                  5      nSUS-
  -  U-  $ )u�   
计算转向成本参考值
公式(4): OrientAdjustCost_reference = 0.3 * (n - 2) * Aθ_max
其中Aθ_max为设定的无人机航点间路径最大偏移角限制，取90度；n为总航点数量
r\   r+   �333333�?r�   )r�   �radiansr�   )rE   r	  �   aθ_maxs      r'   � calculate_turning_cost_reference�/CostCalculator.calculate_turning_cost_referenceB  s6   � � ��?���,�,�t�2�2�3���k�A�o�&��0�0r&   r  r  r  c                 ��  � UR                   UR                   -
  nUR                  UR                  -
  nUR                   UR                   -
  nUR                  UR                  -
  n[        R                  " XD-  XU-  -   5      n[        R                  " Xf-  Xw-  -   5      n	US:X  d  U	S:X  a  gXF-  XW-  -   n
X�U	-  -  n[	        S[        SU5      5      n[        R                  " U5      n[        R                  U-
  $ )u   计算偏离角（弧度）r   r+   �      ��      �?)r,   r-   r�   r�   r   r~   �acosr�   )
rE   r  r  r  �v1_x�v1_y�v2_x�v2_y�len1�len2�dot_product�	cos_angler�   s
                r'   r  �)CostCalculator._calculate_deviation_angleN  s�   � �
 �|�|�j�l�l�*���|�|�j�l�l�*�� �|�|�j�l�l�*���|�|�j�l�l�*�� �y�y���T�Y�.�/���y�y���T�Y�.�/���1�9���	�� �i�$�)�+���$�;�/�	� ��c�#�y�1�2�	� �	�	�)�$���w�w���r&   �	buildingsr�   c                 �l   � SnU H  nU R                  XR5      nXel        XF-
  nM      US:�  a  XC-  OSnXG4$ )u�   
计算风险值和风险密度

Args:
    waypoints: 航点列表
    buildings: 建筑物列表
    path_length: 路径长度

Returns:
    Tuple[总风险值, 风险密度]
r+   r   ��_calculate_waypoint_riskr3   )rE   r�   r  r�   �
total_risk�waypoint�
waypoint_risk�risk_densitys           r'   �calculate_risk_value�#CostCalculator.calculate_risk_valuel  sL   � � �
�!�H� �9�9�(�N�M�"/���'�J�	 "� 4?��?�z�/����'�'r&   �all_path_risksc                 �@   � U(       d  g[        U5      [        U5      -  $ )u�   
计算风险参考值
公式(9): RiskSum_reference = (Σ RiskSum) / n
根据无人机使用A*算法生成的初始路径集的风险的平均数得出

Args:
    all_path_risks: 所有路径的风险值列表

Returns:
    风险参考值
r  �r�   r]   )rE   r'  s     r'   �calculate_risk_reference_value�-CostCalculator.calculate_risk_reference_value�  s   � � ���>�"�S��%8�8�8r&   r"  c                 �z   � SnSnU H0  nU R                  X5      nXd::  d  M  U R                  U5      nX7-
  nM2     U$ )u�   
计算单个航点的风险值
公式(6): PointRisk(x_i, y_i, z_i) = Σ BuildingRisk
该航点所在高度层的周围30米范围内的建筑物的风险区的风险值
r+   �      >@)�_calculate_distance_to_building�_calculate_building_risk)rE   r"  r  r!  �
search_radius�buildingr�   �
building_risks           r'   r   �'CostCalculator._calculate_waypoint_risk�  sN   � � �
��
�!�H��;�;�H�O�H��(� $� =� =�h� G�
��+�
� "� �r&   r1  c                 �  � UR                  SS5      nUR                  SS5      nUR                  SS5      nUR                  U-
  nUR                  U-
  n[        R                  " Xf-  Xw-  -   5      n[        SX�-
  5      n	U	$ )u-   计算航点到建筑物边缘的最短距离r,   r   r-   rn   �
   )r�   r,   r-   r�   r�   r   )
rE   r"  r1  �
building_x�
building_y�building_radiusr�   r�   �center_distance�
edge_distances
             r'   r.  �.CostCalculator._calculate_distance_to_building�  s}   � � �\�\�#�q�)�
��\�\�#�q�)�
�"�,�,�x��4��
�Z�Z�*�
$��
�Z�Z�*�
$���)�)�B�E�B�E�M�2�� �A��@�A�
��r&   r�   c                 �   � US::  a  gXR                   :�  a  g[        R                  " S5      nX!-  U R                   -  n[        R                  " U5      nU$ )u�   
计算建筑物的风险值
公式(5): ZoneRisk = e^(ln(0.05) * d / d_max)
其中d是距离建筑物边缘的最短距离，d_max是设定的风险边缘距离
r   r  g�������?)r�   r�   �log�exp)rE   r�   �ln_005�exponent�risks        r'   r/  �'CostCalculator._calculate_building_risk�  sR   � � �q�=���.�.�.�� ���$����$�t�'>�'>�>���x�x��!���r&   �protection_zonesc                 �   � SnU H5  nU R                  XB5      nXTl        U R                  U5      nXdl        X5-
  nM7     U$ )u�   
计算碰撞代价
公式(12): RoadCrashCost = Σ PointEstimateCrashCost(x_i, y_i, z_i)

Args:
    waypoints: 航点列表
    protection_zones: 保护区列表

Returns:
    总碰撞代价 (RoadCrashCost)
r+   )�#_calculate_estimated_collision_costr4   �_simulate_actual_collision_costr5   )rE   r�   rC  �total_collision_costr"  �estimated_cost�actual_costs          r'   �calculate_collision_cost�'CostCalculator.calculate_collision_cost�  sU   � �  #��!�H�!�E�E�h�a�N�0>�-� �>�>�x�H�K�-8�*� 
!�2� � "� $�#r&   c           	      �  � SnSn[        UR                  UR                  UR                  UR                  UR                  UR                  S9nU H/  nUR	                  XT5      nUS:�  d  M  UR
                  U-  nX8-
  nM1     U$ )u�   
计算航点的估计碰撞代价
公式(11): PointEstimateCrashCost(x_i, y_i, z_i) = Σ AverageCrashCost * S
其中S为以航点为中心，半径30米的圆范围内包含的保护区面积
r+   r-  �rI   rJ   rK   r,   r-   r.   r   )r   r,   r-   r.   r�   rY   )	rE   r"  rC  r�   r0  �waypoint_pos�zone�intersection_area�	zone_costs	            r'   rE  �2CostCalculator._calculate_estimated_collision_cost�  s�   � � �
��
��8�:�:�8�:�:�8�:�:�!)���x�z�z�X�Z�Z�I�� %�D� $� :� :�<� W�� �1�$� �7�7�:K�K�	��'�
� %� �r&   c                 �   � U R                  U5      nX!l        SnU H9  n[        US   5      nUS   nU R                  R	                  US5      nX6U-  -
  nM;     U$ )u�   
模拟实际碰撞代价（通过物体识别）
公式(10): PointActualCrashCost(x_i, y_i, z_i) = Σ ObstacleCrashCost
无人机在航点处拍摄机身正下方区域的高清照片后利用图像识别技术得到照片中各类物体的数量
r+   �type�count)�_simulate_object_detectionr8   r   r�   r�   )rE   r"  r8   r�   �obj�obj_type�	obj_count�obj_costs           r'   rF  �.CostCalculator._simulate_actual_collision_cost  sn   � �  �:�:�8�D��$4�!��
�#�C�!�#�f�+�.�H��G��I��2�2�6�6�x��E�H��h�.�.�J� $� �r&   c                 �f  � [        US5      (       a  UR                  (       a  UR                  $ SSKn/ nUR                  5       S:  ah  [        [        5      nUR                  U5      nUR
                  SS5      nUR                  UR                  UUR                  UR                  S.S.5        U$ )	uH   模拟物体检测（实际实现中会调用真实的物体识别API）r8   r   Nr  r
   r\   �r,   r-   )rT  rU  �position)�hasattrr8   �randomrQ   r   �choice�randintr�   �valuer,   r-   )rE   r"  r`  �objects�	obj_typesrX  rY  s          r'   rV  �)CostCalculator._simulate_object_detection  s�   � � �8�/�0�0�X�5N�5N��,�,�,� 	����=�=�?�S� ��Z�(�I��}�}�Y�/�H����q�!�,�I��N�N� ���"�"*�*�*�8�:�:�>�� 
� �r&   Nr�   r3   r�   �risk_reference�collision_reference�turning_reference�weightsc	                 �  � U[        US5      -  n	Sn
SSKnSSUR                  " U
* U	-  5      -
  -  nSSUR                  " U
* U	-  5      -
  -  n
SUR                  " U
* U	-  5      -  S	-   nSX�-   U-   -
  n[        S
U5      nX�-  X�-  -   [        X�-  X�-  -   S5      -  nUS-  nX�-  [        US5      -  nX�-  [        US5      -  nUU-   U-   nU$ )u�  
计算路径最终代价 - 使用动态权重模型（保密公式）

PathFinalCost = (α·RiskSum + β·RoadCrashCost)/(α·RiskSum_ref + β·RoadCrashCost_ref)
               + γ·Length/Length_manhattan + δ·OrientAdjustCost/OrientAdjustCost_ref

动态权重：
α = 0.6 * (1 - e^(-k * RiskDensity))
β = 0.3 * (1 - e^(-k * RiskDensity))
γ = 0.7 * e^(-k * RiskDensity) + 0.1
δ = 1 - (α + β + γ)
r  �      @r   N�333333�?r
   r  �ffffff�?皙�����?r+   g      �?)r   r�   r>  )rE   r�   r�   r3   r�   rg  rh  ri  rj  r$  �kr�   �alpha�beta�gamma�delta�risk_crash_term�length_manhattan�length_term�orient_term�path_final_costs                        r'   �calculate_final_cost�#CostCalculator.calculate_final_cost/  s!  � �" "�C��S�$9�9�� 
��� �q�4�8�8�Q�B��$5�6�6�7�� �a�$�(�(�A�2��#4�5�5�6�� �d�h�h��r�L�0�1�1�C�7�� �u�|�e�+�,�� �C���� !�-��0E�E���"�T�%?�?��J
� 
�� '��,���)�C�0@�#�,F�F�� �*�S�1B�C�-H�H�� *�K�7�+�E���r&   c                 �h   � U(       d  g[        S U 5       5      n[        U5      nSnX4-  U-  U-  nU$ )u�   
计算碰撞代价参考值
公式(13): RoadCrashCost_reference = (Σ AverageCrashCost / ProtectzoneNum) * 300 * n

Args:
    protection_zones: 保护区列表
    n_waypoints: 目标路径的航点总数

Returns:
    碰撞代价参考值
r  c              3   �8   #   � U  H  oR                   v �  M     g 7frq   )rY   )rt   rO  s     r'   rv   �DCostCalculator.calculate_collision_cost_reference.<locals>.<genexpr>y  s   � � �&`�O_�t�'B�'B�O_�ry   g     �r@r)  )rE   rC  r	  �total_average_crash_cost�protection_zone_num�
standard_area�reference_values          r'   �"calculate_collision_cost_reference�1CostCalculator.calculate_collision_cost_referenceh  sN   � �  �� $'�&`�O_�&`�#`� � "�"2�3�� �
� 4�I�]�Z�]h�h���r&   c                 �T   � SnU H  nU R                  U/ 5      nXCl        X$-
  nM!     U$ )uK   
计算路径总风险值
公式(7): RiskSum = Σ PointRisk(x_i, y_i, z_i)
r+   r  )rE   r�   r!  r"  �
point_risks        r'   �calculate_risk_sum�!CostCalculator.calculate_risk_sum�  s;   � �
 �
�!�H��6�6�x��D�J�",���$�J�	 "� �r&   c                 �r   � SnU R                  5       nU H  nU R                  XC5      nXTl        X%-
  nM      U$ )ue   
计算路径估计碰撞代价
公式(12): RoadCrashCost = Σ PointEstimateCrashCost(x_i, y_i, z_i)
r+   )�_create_test_protection_zonesrE  r4   )rE   r�   r�   rC  r"  rH  s         r'   �"calculate_estimated_collision_cost�1CostCalculator.calculate_estimated_collision_cost�  sJ   � �
 �
�  �=�=�?��!�H�!�E�E�h�a�N�0>�-��(�J� "�
 �r&   c                 �   � SSK Jn  / n[        SSU" SSSSSSS9U" SSSSSSS9U" SSSSSSS9U" SSSSSSS9/SS	9nUR                  U5        U$ )
u   创建测试用的保护区r
   )r   �test_vehicler   r   rM  �d   r�   �rV   rW   rX   rY   )�data_structuresr   rT   r�   )rE   r   rC  �vehicle_zones       r'   r�  �,CostCalculator._create_test_protection_zones�  s�   � �,� �� &�"���A�1�!�q�A��;��C�Q�A���Q�?��C�S�a�3�#��C��A�3�A��c�Q�?�	� $(�

�� 	����-��r&   c                 �  � [        U5      S:  a  [        S5      $  U R                  U5      nU R                  U5      nU R	                  U5      nU R                  U5      nUS   nUS   nU R
                  Xg5      nU[        US5      -  n	Sn
SSKnSS	UR                  " U
* U	-  5      -
  -  nS
S	UR                  " U
* U	-  5      -
  -  n
SUR                  " U
* U	-  5      -  S-   nSX�-   U-   -
  n[        S
U5      n[        U SS5      n[        U SS5      n[        U SS5      nX�-  X�-  -   [        UU-  U
U-  -   S5      -  nX�-  [        US5      -  nX�-  [        US5      -  nUU-   U-   nU$ ! [         a  n[        S5      s SnA$ SnAff = f)u�   
使用航点列表计算路径最终代价 - 动态权重模型（保密公式）

这是完整版本的目标函数实现，包含正确的曼哈顿距离计算
r�   r�   r   �����r  rl  Nrm  r
   r  rn  ro  r+   �risk_sum_reference�      Y@�road_crash_cost_referenceg      I@�orient_adjust_cost_referencer-  )r]   rN   r�   r  r�  r�  r�   r   r�   r>  rc   �	Exception)rE   r�   r�   r�   r3   r�   r�   r�   rv  r$  rp  r�   rq  rr  rs  rt  rg  rh  ri  ru  rw  rx  ry  �es                           r'   �(calculate_path_final_cost_with_waypoints�7CostCalculator.calculate_path_final_cost_with_waypoints�  s�  � � �y�>�A����<��:	 ��4�4�Y�?�K��6�6�y�A�L��0�0��;�J�!�D�D�Y�O�N� $�A�,�K�!�"�
�I�#�>�>�{�V�� &��K��(=�=�L� �A�� �1�t�x�x���\�(9�:�:�;�E� �!�d�h�h��r�L�'8�9�9�:�D� �$�(�(�A�2��#4�5�5��;�E� �5�<�%�/�0�E� ��U�O�E� %�T�+?��G�N�")�$�0K�T�"R�� '��.L�d� S��  %�1�D�4I�I�S���&��0C�)C�C�S�N� �O�
  �-��4D�c�0J�J�K�  �.��5F��1L�L�K� .��;�k�I�O�"�"��� 	 ���<���	 �s   �EE, �,
F�6
F� F�F)r�   r�   r�   r�   r�   rq   )$r   r   r   r    r!   r   rR   r   r�   r   r)   rN   r�   r�   r  rP   r  r  r   r%  r*  r   r.  r/  rT   rJ  rE  rF  rV  rz  r�  r�  r�  r�  r�  r%   r   r&   r'   r�   r�   �   s�  � �%�
�t�C��H�~� 
��t�4E�/F� �5� �,	$�6G� 	$�Te� 	$�jo� 	$�(��5F�0G� (�E� (�4
1�C� 
1�E� 
1��5F� �->��->��CH��<(�d�3D�.E� (�&*�4�j�(�?D�(�IN�u�V[�|�I\�(�69�T�%�[� 9�U� 9�"�1B� �t�TX�z� �^c� �(
�8I� 
�UY� 
�^c� 
��� �5� �($�$�7H�2I� $�15�n�1E�$�JO�$�:�<M� �<@��<P��UZ��0�8I� �e� �(�3D� ��d�� �4 RV�7�� 7�U� 7�',�7�>C�7�+0�7�GL�7� /4�7� ?C�3��:�>N�7� [`�7�r�4��CW� �69��>C��<�D�1B�,C� �� ��D�AR�<S� �X]� �" �t�N�/C�  �.C �$�GX�BY� C �^c� C r&   r�   c                   �  � \ rS rSrSrS\S\\\4   4S jr	S\
S\
S\\   S	\\   4S
 jr
S\
S\
S	\4S jrS\S	\\   4S
 jrS\
S\
S	\4S jrS\
S\
S\S\S\S\\   S	\
4S jrS\
S\\   S	\
4S jrS\
S\
S\
S\\   S	\\   4
S jrSrg)�InitialPathSetGeneratori  u   初始路径集生成器�astar_algorithmr�   c                 �x   � Xl         X l        UR                  SS5      U l        UR                  SS5      U l        g )N�flightHeightr�  �safetyDistance�   )�astarr�   r�   �
flight_height�safety_distance)rE   r�  r�   s      r'   r�   � InitialPathSetGenerator.__init__  s3   � �$�
���#�Z�Z���<���%�z�z�*:�B�?��r&   r�   r�   r  r9   c              �   �
  #   � / nSnU R                  X5      nU R                  U5      nU R                  X5      n[        US5       Hq  u  p�[	        SS5       H\  n U R                  XX�X�5      nU R
                  XX#5      I Sh  v�N n
U
(       a$  [        UU	UU
S9nUR                  U5        US-
  nM\  M^     Ms     [        S	[        U5       S
35        U$  NQ! [         a  n[        SU	 SU SU 35         SnAM�  SnAff = f7f)u�   
生成81条初始路径

Args:
    start_point: 起点
    end_point: 终点
    buildings: 建筑物列表

Returns:
    初始路径集
r   r
   r5  N�r�   r�   r�   r�   u   生成路径失败 (方向�   , 高度�): u
   成功生成 u    条初始路径)�_calculate_base_direction�_generate_flight_directions�_calculate_transfer_regions�	enumeraterb   �_generate_transfer_point�_generate_path_with_astarr�   r�   r�  �printr]   )rE   r�   r�   r  �
initial_pathsr�   �base_direction�flight_directions�transfer_regions�
direction_idx�direction_angler�   �transfer_point�path_waypoints�	path_infor�  s                   r'   �generate_initial_path_set�1InitialPathSetGenerator.generate_initial_path_set  s9  � � � �
��� �7�7��O�� �<�<�^�L��  �;�;�K�S�� /8�8I�1�.M�*�M� %�a�����%)�%B�%B�#�
�(�&�N� ,0�+I�+I�#�Y�,� &�N� &�$,�$+�-:�)5�&4�	%�	� &�,�,�Y�7��1��� &� !-� /N�< 	�
�c�-�0�1�1A�B�C���+&��  !� ��6�}�o�X�l�^�[^�_`�^a�b�c����sB   �AD�(C�C�-C�1$D�C�
D �!C;�5D�;D � Dc                 �   � UR                   UR                   -
  nUR                  UR                  -
  n[        R                  " XC5      $ )u3   计算起点到终点的基准方向角（弧度）)r,   r-   r�   r�   )rE   r�   r�   r�   r�   s        r'   r�  �1InitialPathSetGenerator._calculate_base_directionD  s5   � �
�[�[�;�=�=�
(��
�[�[�;�=�=�
(���z�z�"�!�!r&   r�  c                 �   � / nU[         R                  " S5      -
  n[        S5       H0  nU[         R                  " SU-  5      -   nUR                  U5        M2     U$ )u�   
生成9个起飞方向（90度扇形，每10度一个方向）

Args:
    base_direction: 基准方向角（弧度）

Returns:
    9个飞行方向角度列表（弧度）
�-   �	   r5  )r�   r  rb   r�   )rE   r�  �
directions�start_anglerf   r�   s         r'   r�  �3InitialPathSetGenerator._generate_flight_directionsJ  sW   � � �
�$�t�|�|�B�'7�7���q��A��$�,�,�r�A�v�"6�6�E����e�$� � �r&   c                 �:  � UR                   UR                   -   S-  nUR                  UR                  -   S-  nUR                  UR                  -   S-  n[        R                  " UR                   UR                   -
  S-  UR                  UR                  -
  S-  -   5      nUS-  nUR                   UR                   -
  nUR                  UR                  -
  n	[        R                  " X�-  X�-  -   5      n
U
S:�  a  U	* U
-  nX�-  nOSu  p�[        X4U5      UX�4SS.$ )uP   
计算中转点生成区域

Returns:
    包含中转点区域信息的字典
r�   r  r   )r   r
   g      9@)r�   �reference_length�perpendicular_unit�extension_width)r,   r-   r.   r�   r�   r   )
rE   r�   r�   �mid_x�mid_y�mid_zr�   r�  r�   r�   �length�perp_x�perp_ys
                r'   r�  �3InitialPathSetGenerator._calculate_transfer_regions]  s  � � ������,��1��������,��1��������,��1�� �9�9�
�[�[�;�=�=�
(�1�,�
�[�[�;�=�=�
(�1�,�
-�
�� $�c�>�� �[�[�;�=�=�
(��
�[�[�;�=�=�
(�����2�5�2�5�=�)���A�:��S�6�\�F��[�F�!�N�F� �e�E�2� 0�#)�"2�#�	
� 	
r&   r�  r�   r�  c                 �4  � US   nUS   nUS   n	US   n
US-
  S-  nX�-  S-  nUS-
  U
S-  -  n
UR                   X�S	   -  -   X�S
   * -  -   nUR                  X�S
   -  -   X�S	   -  -   nU R                  US-  -   n[        X�U5      nU R	                  UU5      nU$ )u  
生成中转点

Args:
    start_point: 起点
    end_point: 终点
    direction_idx: 飞行方向编号(1-9)
    height_layer: 高度层编号(1-9)
    transfer_regions: 中转点区域信息
    buildings: 建筑物列表

Returns:
    中转点坐标
r�   r�  r�  r�  r�   g      @r�   �   r   r
   r5  )r,   r-   r�  r   �_adjust_for_buildings)rE   r�   r�   r�  r�   r�  r  r�   �
ref_length�	perp_unitr�  �position_ratio�
ref_offset�perp_offset�
transfer_x�
transfer_y�
transfer_zr�  s                     r'   r�  �0InitialPathSetGenerator._generate_transfer_point�  s�   � �" "�(�+��%�&8�9�
�$�%9�:�	�*�+<�=�� (�!�+�s�2��#�0�1�4�
� $�a�'�O�a�,?�@�� �h�h���l�!:�:� �q�\�M�2�3�
��h�h���l�!:�:� �Q�<�/�0�
��'�'�,��*;�;�
� !���D���3�3�N�I�N���r&   rm   c                 �p  � [        UR                  UR                  UR                  5      nU GH  nUR	                  SS5      nUR	                  SS5      nUR	                  SS5      nUR	                  SS5      nUR                  U-
  n	UR                  U-
  n
[
        R                  " X�-  X�-  -   5      nX�R                  -   nX�:  a8  US:�  a  X�-  n
XYU
-  -   Ul        XjU
-  -   Ul        OU=R                  U-
  sl        UR                  XpR                  -   ::  d  M�  XpR                  -   Ul        GM     U$ )u!   调整点位置以避开建筑物r,   r   r-   �heightrn   r5  )r   r,   r-   r.   r�   r�   r�   r�  )rE   rm   r  �adjusted_pointr1  r6  r7  �building_heightr8  r�   r�   �horizontal_distance�min_distance�scales                 r'   r�  �-InitialPathSetGenerator._adjust_for_buildings�  s(  � � ����%�'�'�5�7�7�;��!�H�!���c�1�-�J�!���c�1�-�J�&�l�l�8�Q�7�O�&�l�l�8�R�8�O�  �!�!�J�.�B��!�!�J�.�B�"&�)�)�B�E�B�E�M�":�� +�-A�-A�A�L�"�1�&��*�(�>�E�'1��J�'>�N�$�'1��J�'>�N�$� #�$�$��4�$� ���?�5I�5I�#I�I�#2�5I�5I�#I�� �1 "�4 �r&   r�  c           	   �   �  #   �  SSK JnJn  UR                  5       UR                  5       USSS.S.nU" U5      nU R                  R                  U5      I Sh  v�N n	UR                  5       UR                  5       USSS.S.n
U" U
5      nU R                  R                  U5      I Sh  v�N nU	R                  (       a�  UR                  (       a�  / n
Sn[        U	R                  SS 5       HG  u  nn[        UR                  UR                  UR                  UUS	9nU
R                  U5        US-
  nMI     [        UR                  5       HG  u  nn[        UR                  UR                  UR                  UUS	9nU
R                  U5        US-
  nMI     U
$ / $  GNC N�! [         a  n[        S
U 35         SnA/ $ SnAff = f7f)u�   
使用A*算法生成经过中转点的路径

Args:
    start_point: 起点
    transfer_point: 中转点
    end_point: 终点
    buildings: 建筑物列表

Returns:
    航点列表
r
   �r   �
DroneSpecsr5  i�  ��gridSize�
maxIterations��
startPoint�endPoint�	obstacles�
parametersNr�  �r,   r-   r.   r/   r1   u   A*路径生成失败: )r�  r   r�  rF   r�  �calculate_path�successr�  �pathr)   r,   r-   r.   r�   r�  r�  )rE   r�   r�  r�   r  r   r�  �
request1_data�request1�	response1�
request2_data�request2�	response2r�   r/   rf   rm   r�   r�  s                      r'   r�  �1InitialPathSetGenerator._generate_path_with_astar�  s�  � � �6	0�H� *�1�1�3�*�2�2�4�&�+-��E�	�M� +�=�9�H�"�j�j�7�7��A�A�I� -�4�4�6�%�-�-�/�&�+-��E�	�M� +�=�9�H�"�j�j�7�7��A�A�I� � � �Y�%6�%6��	�!"�� !*�)�.�.��"�*=� >�H�A�u�*��'�'�U�W�W����'5�'5��B�
 �$�$�R�(�"�a�'�N� !?� !*�)�.�.� 9�H�A�u�*��'�'�U�W�W����'5�'5��B�
 �$�$�R�(�"�a�'�N� !:� !� �
 �	�W B� B��: � 	0��*�1�#�.�/�/��	��	0�sU   �G�AF  �F�AF  �)F�*C.F  �G�F  �F  � 
G�*F>�8G�>G�G)r�  r�   r�  r�  N)r   r   r   r    r!   r   r   rR   r   r�   r   r   r�   r�  rN   r�  r�  r�  rP   r�  r�  r)   r�  r%   r   r&   r'   r�  r�    s8  � �"�@�� @��S�#�X�� @�7�7� 7�w� 7�15�d��7�@D�X��7�r"�W� "�� "�UZ� "��%� �D��K� �&&
�w� &
�7� &
�W[� &
�P(�G� (�� (�.1�(�AD�(�15�(�BF�t�*�(�QX�(�T�7� �t�D�z� �g� �@F�7� F�T[� F�18�F�EI�$�Z�F�TX�Yj�Tk�Fr&   r�  c                   �  � \ rS rSrSrS rS rS\\   4S jr	S\
\\4   4S jr
S	\\   4S
 jrS	\\   4S jrS	\\   4S jrS
\S	\\   4S jrS\S\S	\\   4S jrS\S\\   S	\\   4S jrS	\
\\4   4S jrS\\   S	\4S jrS\S\S\S\\   S	\\\      4
S jr S$S\S\S	\\   4S jjrS\S\\   S	\\   4S  jrS!\S\S	\\\      4S" jr S#r!g)%�ClusterManageri  u   固定空间分簇管理器c                 �2   � / U l         U R                  5         g rq   )�clusters�_initialize_clustersrD   s    r'   r�   �ClusterManager.__init__  s   � ���
��!�!�#r&   c                 �v  � / U l         / SQn[        U5       H5  u  nu  p4[        SUS-    3SUUS9nU R                   R                  U5        M7     / SQn[        U5       H5  u  nu  p4[        SUS-    3SUUS9nU R                   R                  U5        M7     [	        S	[        U R                   5       S
35        g)u    初始化13个固定空间分簇)	)�r
   r\   r  )�r�  �   r  )��   r�  r  )r  r  )r  r  )r  r  )r  r  )r  r  )r  r  �3x3_r
   �3x3)r�   r�   r�   r�   ))�r�   r�   r
  )�r�   �   r
  )r
  r  )r  r  �4x4_�4x4u   初始化完成，共 u
    个分簇N)r�  r�  r�   r�   r�  r]   )rE   �cluster_3x3_configsrf   r�   r�   �cluster�cluster_4x4_configss          r'   r   �#ClusterManager._initialize_clusters  s�   � ���
�

�� &/�/B�%C�!�A�!��!�!�!�A�#��<�"���	�G� 
�M�M� � ��)� &D�
�� &/�/B�%C�!�A�!��!�!�!�A�#��<�"���	�G� 
�M�M� � ��)� &D� 	�&�s�4�=�=�'9�&:�*�E�Fr&   r�   c           	      ��  � U R                    H  nUR                  R                  5         M     U Hk  nUR                  nUR                  nU R                    H@  nUR                  XE5      (       d  M  UR                  R
                  UR                  5        MB     Mm     [        S5        U R                    H2  n[        SUR                   S[        UR                  5       S35        M4     g)u!   将路径分配到对应的簇中u   路径分配完成:z  z: �
    条路径N)r�  r�   �clearr�   r�   r�   r�   r�   r�  r�   r]   )rE   r�   r  r�  �x_pos�y_poss         r'   �assign_paths_to_clusters�'ClusterManager.assign_paths_to_clustersL  s�   � � �}�}�G��M�M���!� %� �D��)�)�E��%�%�E�  �=�=���,�,�U�:�:��M�M�(�(����6� )� � 	�#�$��}�}�G��B�w�)�)�*�"�S����-?�,@�
�K�L� %r&   r�   c                 �L   � U R                    H  nUR                  U5        M     g)u   计算每个簇的平均代价N)r�  r�   )rE   r�   r  s      r'   �calculate_cluster_costs�&ClusterManager.calculate_cluster_costs`  s   � ��}�}�G��*�*�:�6� %r&   r9   c           
      �  � [        U R                  S S9n[        U5       H  u  p#US-   Ul        M     [	        S5        USS  H7  n[	        SUR                   SUR
                   S	UR                  S
 S35        M9     U$ )u   对簇按平均代价排序c                 �   � U R                   $ rq   r�   ��cs    r'   �<lambda>�.ClusterManager.rank_clusters.<locals>.<lambda>g  s   � �a�n�nr&   ��keyr
   u   簇排序结果:Nr�   u     第u   名: u    (平均代价: �.2f�))�sortedr�  r�  r�   r�  r�   r�   )rE   �sorted_clustersrf   r  s       r'   �
rank_clusters�ClusterManager.rank_clusterse  s�   � � ����4L�M�� $�O�4�J�A��q�5�G�L� 5� 	� �!�&�r��*�G��E�'�,�,��u�W�-?�-?�,@� A$�$+�$8�$8��#=�Q�@� 
A� +� �r&   c                 �T   � U R                   (       d  g[        U R                   S S9nU$ )u�   
获取领导者种群（保密策略）
领导者种群 PathHeader: 代价平均值最低的簇
按 ClusterFinalCost 升序排序，第一个就是领导者种群
Nc                 �   � U R                   $ rq   r�   r  s    r'   r!  �3ClusterManager.get_leader_cluster.<locals>.<lambda>}  s   � �!�.�.r&   r#  )r�  r~   )rE   �leaders     r'   �get_leader_cluster�!ClusterManager.get_leader_clustert  s$   � � �}�}���T�]�]�(@�A���
r&   c                 �   � U R                  5       nUc  / $ U R                   Vs/ s H   o"R                  UR                  :w  d  M  UPM"     nnU$ s  snf )ur   
获取跟随者种群（保密策略）
跟随者种群 PathFollower: 其余簇（除领导者外的所有簇）
)r/  r�  r�   )rE   r.  r   �	followerss       r'   �get_follower_clusters�$ClusterManager.get_follower_clusters�  sO   � �
 �(�(�*���>��I� $�
�
�S�
�1����AR�AR�1R�Q�
�	�S���� Ts   �A�A�current_clusterc                 ��  � / nUR                   S   UR                   S   -   S-  nUR                  S   UR                  S   -   S-  nU R                   H�  nUR                  UR                  :X  a  M  UR                   S   UR                   S   -   S-  nUR                  S   UR                  S   -   S-  n[        R
                  " Xc-
  S-  Xt-
  S-  -   5      nUS::  d  M�  UR
                  U5        M�     U$ )u   找到与当前簇相邻的簇r   r
   r�   r�   )r�   r�   r�  r�   r�   r�   r�   )	rE   r5  �adjacent_clusters�current_x_center�current_y_centerr  �cluster_x_center�cluster_y_centerr�   s	            r'   �find_adjacent_clusters�%ClusterManager.find_adjacent_clusters�  s  � ���+�3�3�A�6��9P�9P�QR�9S�S�WX�X��+�3�3�A�6��9P�9P�QR�9S�S�WX�X���}�}�G��!�!�_�%?�%?�?�� '���� 2�W�_�_�Q�5G� G�1�L�� '���� 2�W�_�_�Q�5G� G�1�L�� �y�y�!�4�q�8�!�4�q�8�9��H� �3��!�(�(��1� %�" !� r&   r,   r-   c                 �   � / nU R                    H,  nUR                  X5      (       d  M  UR                  U5        M.     U$ )u-   根据位置获取包含该位置的所有簇)r�  r�   r�   )rE   r,   r-   �containing_clustersr  s        r'   �get_cluster_by_position�&ClusterManager.get_cluster_by_position�  s=   � � ���}�}�G��(�(��.�.�#�*�*�7�3� %� #�"r&   r  c                 �   � UR                   (       d  gU Vs/ s H   o3R                  UR                   ;   d  M  UPM"     nnU(       d  g[        US S9nU$ s  snf )u3   从簇中获取最优路径（最终代价最低）Nc                 �   � U R                   $ rq   �r�   �ru   s    r'   r!  �>ClusterManager.get_optimal_path_from_cluster.<locals>.<lambda>�  s   � ���r&   r#  )r�   r�   r~   )rE   r  r�   ru   �
cluster_paths�optimal_paths         r'   �get_optimal_path_from_cluster�,ClusterManager.get_optimal_path_from_cluster�  sN   � � �}�}��$)�H�E�q�Y�Y�'�-�-�-G��E�
�H����=�.D�E����� Is
   �A�Ac           	      �"  � [        U R                  5      [        U R                   Vs/ s H  oR                  S:X  d  M  UPM     sn5      [        U R                   Vs/ s H  oR                  S:X  d  M  UPM     sn5      / S.nU R                   Hp  nUR                  UR                  UR                  UR
                  [        UR                  5      UR                  UR                  S.nUS   R                  U5        Mr     U$ s  snf s  snf )u   获取分簇统计信息r	  r  )�total_clusters�cluster_3x3_count�cluster_4x4_countr�  )�idrT  r�   r�   �
path_countr�   r�   r�  )
r]   r�  r�   r�   r�   r�   r�   r�   r�   r�   )rE   r   �statsr  �cluster_infos        r'   �get_cluster_statistics�%ClusterManager.get_cluster_statistics�  s�   � � "�$�-�-�0�!$����%Z��A�.�.�TY�BY�a��%Z�![�!$����%Z��A�.�.�TY�BY�a��%Z�![��	
�� �}�}�G��(�(��,�,�"�?�?�"�?�?�!�'�-�-�0� '� 4� 4�����L� 
�*��$�$�\�2� %� ���# &[��%Zs   �D
�D
�D�5D�waypoints_historyc                 �   � [        U5      S:  a  gUSS nU H0  n[        USS5      n[        USS5      nUS::  a  M%  US	U-  :�  d  M0    g
   g)u)  
检查换路触发条件（保密策略）

触发条件：当无人机连续飞越 5 个航点 且满足：
实际碰撞代价 > 1.2 × 预设保护区模型估算碰撞代价

Args:
    waypoints_history: 最近飞越的航点历史（至少5个）

Returns:
    bool: 是否需要触发换路
r�   F�����Nr5   r+   r4   r   g333333�?T)r]   rc   )rE   rU  �recent_waypointsr"  rI  rH  s         r'   �check_path_switching_condition�-ClusterManager.check_path_switching_condition�  sp   � � � �!�A�%�� -�R�S�1��(�H�!�(�,C�S�I�K�$�X�/I�3�O�N� ��"�� �S�>�1�1�� )� r&   �current_position�current_cluster_id�target_waypoint�	all_pathsc                 �$  �  [        SUR                   SUR                   S35        U R                  X5      nU(       d  [        S5        gU R	                  XT5      nU(       a  UR
                  (       d  [        S5        gU R
                  XR
                  5      nU(       d  [        S5        gU R                  X5      nU(       a  [        S[        U5       S	35        U$ [        S
5        g! [         a  n	[        SU	 35         Sn	A	gSn	A	ff = f)u  
执行换路策略（保密策略）

换路流程：
1. 立即悬停
2. 梯度场引导 - 选择最近航点梯度降低方向的邻近簇
3. 路径重规划 - 使用当前算法生成当前位置到目标路径最近航点的新路径
4. 沿新路径飞行
u5   [ClusterManager] 触发换路策略，当前位置: (�, r&  u,   [ClusterManager] 未找到合适的目标簇Nu,   [ClusterManager] 目标簇中无可用路径u&   [ClusterManager] 未找到最近航点u0   [ClusterManager] 换路成功，新路径包含 �
    个航点u&   [ClusterManager] 路径重规划失败u+   [ClusterManager] 换路策略执行失败: )
r�  r,   r-   � find_nearest_cluster_by_gradientrI  r�   �_find_nearest_waypoint�_generate_switching_pathr]   r�  )
rE   r[  r\  r]  r^  �target_cluster�target_path�nearest_waypoint�new_pathr�  s
             r'   �execute_path_switching_strategy�.ClusterManager.execute_path_switching_strategy�  s  � �$	��I�JZ�J\�J\�I]�]_�`p�`r�`r�_s�st�u�v� "�B�B�CS�h�N�!��D�E�� �<�<�^�W�K��k�&;�&;��D�E��  $�:�:�;K�Mb�Mb�c��#��>�?�� �4�4�5E�X�H���H��X���Wa�b�c����>�?���� 	��?��s�C�D���	�s0   �A	C- �4C- �-C- �/1C- �!C- �-
D�7D
�
DN�exclude_cluster_idc                 ��  � U R                   (       d  gSn[        S5      nU R                    H�  nU(       a  UR                  U:X  a  M  UR                  S   UR                  S   -   S-  nUR                  S   UR                  S   -   S-  n[
        R                  " UR                  US-  -
  S-  UR                  US-  -
  S-  -   5      nUS-  UR                  S-  -   n	X�:  d  M�  U	nUnM�     U$ )	u�   
基于梯度场找到最近的可行簇（保密换路策略）
选择最近航点梯度降低方向的邻近簇
优先切换至 ClusterFinalCost 最小的簇
Nr�   r   r
   r�   r�  rn  r  )
r�  rN   r�   r�   r�   r�   r�   r,   r-   r�   )
rE   r[  rk  �best_cluster�
best_scorer  �cluster_center_x�cluster_center_yr�   �scores
             r'   rb  �/ClusterManager.find_nearest_cluster_by_gradient)  s�   � � �}�}�����5�\�
��}�}�G�!�g�&8�&8�<N�&N�� !(���� 2�W�_�_�Q�5G� G�1�L�� '���� 2�W�_�_�Q�5G� G�1�L�� �y�y�!�#�#�&6��&<�<�q�@�!�#�#�&6��&<�<�q�@�A��H� �s�N�W�%9�%9�C�%?�?�E��!�"�
�&��) %�, �r&   r�   c                 �,  � U(       d  gSn[        S5      nU Hx  n[        R                  " UR                  UR                  -
  S-  UR                  UR                  -
  S-  -   UR
                  UR
                  -
  S-  -   5      nXd:  d  Mt  UnUnMz     U$ )�   找到最近的航点Nr�   r�   )rN   r�   r�   r,   r-   r.   )rE   r[  r�   rg  r�  r"  r�   s          r'   rc  �%ClusterManager._find_nearest_waypointN  s�   � � �����U�|��!�H��y�y�!�#�#�h�j�j�0�1�4�!�#�#�h�j�j�0�1�4�5�!�#�#�h�j�j�0�1�4�5��H� �&�'��#+� � "�  �r&   �	start_posc                 �   �  [        UR                  UR                  UR                  SSS9n[        UR                  UR                  UR                  SSS9nX4/$ ! [         a  n[        SU 35         SnAgSnAff = f)u$   生成换路路径（简化版本）r
   r�  r�   u+   [ClusterManager] 生成换路路径失败: N)r)   r,   r-   r.   r�  r�  )rE   rv  r]  r�   r�   r�  s         r'   rd  �'ClusterManager._generate_switching_pathd  s�   � �	�+��+�+����	��� ���K� *�!�#�#��'8�'8�O�<M�<M� ���I�  �+�+��� 	��?��s�C�D���	�s   �AA �
A=�%A8�8A=)r�  rq   )"r   r   r   r    r!   r�   r   r   r�   r  r   rP   rN   r  r�   r)  r   r/  r3  r<  r@  rI  rR   r   rS  r)   r�   rY  r   ri  rb  rc  rd  r%   r   r&   r'   r�  r�    s�  � �%�$�+G�ZM�d�8�n� M�(7�$�s�E�z�2B� 7�

�t�K�0� 
�
�H�[�$9� 
�
�t�K�'8� 
�!�k� !�d�;�FW� !�4#�� #�� #��k�9J� #��[� �+/��>��>F�x�>P����S�#�X�� �. ��EV�@W�  �\`�  �D1�� 1�:=�1�7H�1� 26�h��1� EM�T�Rc�Md�De�1�h BF�#�� #�;>�#�JR�S^�J_�#�J �w�  �(,�->�(?� �DL�M^�D_� �,�'� �0A��FN�t�Te�Of�Fg�r&   r�  c                   �  � \ rS rSrSrS\\\4   4S jrS\	S\
4S jrS\
S\	4S	 jrS
\
S\\
   4S jrS\	4S jrS
\
S\4S
 jrS
\
S\4S jrS
\
S\4S jrS
\
S\S\4S jrS
\
S\\\4   4S jrS rS\\
\
4   4S jrS\\\\4      4S jrSrg)�GradientFieldManageri|  u   梯度场管理器r�   c                 �   � Xl         0 U l        [        R                  S[        R                  S[        R
                  S0U l        g )Nr�   r�   r�   )r�   �gradient_fieldsr   r"   r#   r$   r�   r�   s     r'   r�   �GradientFieldManager.__init__  s:   � ���9;��������!�!�4�����'
��#r&   r"  r9   c                 �   � [        UR                  SSS9nUR                  (       a  U R                  X!5        X R                  UR                  '   U$ )uT   
为航点创建梯度场

Args:
    waypoint: 航点

Returns:
    梯度场对象
r+   )r/   r�   r�   )r�   r/   r8   �_build_gradient_from_objectsr|  �rE   r"  �gradient_fields      r'   �create_gradient_field�*GradientFieldManager.create_gradient_field�  sS   � � '�#�2�2�"�"�
�� �$�$��-�-�n�G� 9G���X�4�4�5��r&   r�  c                 �  � UR                   UR                  4nUR                   H�  n[        US   5      nUS   nUS   nUS   UR                   -
  nUS   UR                  -
  n	[        R
                  " X�-  X�-  -   5      n
U
S:X  a  Md  [        R                  " X�5      nU R                  R                  US5      nX�-  n
UR                  X�U
5        M�     g)	ui   
基于检测到的物体建立梯度场

Args:
    gradient_field: 梯度场对象
    waypoint: 航点
rT  rU  r^  r,   r-   r   r+   N)
r,   r-   r8   r   r�   r�   r�   r�   r�   r�   )rE   r�  r"  rN  rW  rX  rY  �obj_positionr�   r�   r�   r�   r�   r�   s                 r'   r  �1GradientFieldManager._build_gradient_from_objects�  s�   � � !�
�
�H�J�J�/���,�,�C�!�#�f�+�.�H��G��I��z�?�L� �c�"�X�Z�Z�/�B��c�"�X�Z�Z�/�B��y�y������/�H��1�}���J�J�r�&�E� "�8�8�<�<�X�s�K�N�'�3�J� 
�,�,�U�j�I�) -r&   r/   c                 �8   � U R                   R                  U5      $ )u   获取指定航点的梯度场)r|  r�   )rE   r/   s     r'   �get_gradient_field�'GradientFieldManager.get_gradient_field�  s   � ��#�#�'�'��7�7r&   c                 ��   � UR                   U R                  ;   aE  U R                  UR                      nUR                  R                  5         U R	                  X!5        gU R                  U5        g)u   更新航点的梯度场N)r/   r|  r�   r  r  r�  r�  s      r'   �update_gradient_field�*GradientFieldManager.update_gradient_field�  sZ   � ��"�"�d�&:�&:�:�!�1�1�(�2I�2I�J�N��)�)�/�/�1��-�-�n�G��&�&�x�0r&   c                 �L   � U R                  U5      nU(       a  UR                  $ g)u!   获取指定航点的梯度方向r+   )r�  r�   �rE   r/   r�  s      r'   �"get_gradient_direction_at_waypoint�7GradientFieldManager.get_gradient_direction_at_waypoint�  �$   � ��0�0��@���!�4�4�4�r&   c                 �L   � U R                  U5      nU(       a  UR                  $ g)u!   获取指定航点的梯度强度r+   )r�  r�   r�  s      r'   �"get_gradient_magnitude_at_waypoint�7GradientFieldManager.get_gradient_magnitude_at_waypoint�  r�  r&   c                 �"  � U R                  U5      nU[        R                  -   nUS[        R                  -  :�  a/  US[        R                  -  -  nUS[        R                  -  :�  a  M/  US:  a  US[        R                  -  -
  nUS:  a  M  U$ )u�   
找到梯度下降方向（与梯度上升方向相反）

Args:
    waypoint_index: 航点索引

Returns:
    梯度下降方向（弧度）
r�   r   )r�  r�   r�   )rE   r/   r�   �descent_directions       r'   �find_gradient_descent_direction�4GradientFieldManager.find_gradient_descent_direction�  s�   � � "�D�D�^�T��.����8��  �1�t�w�w�;�.���T�W�W��,��  �1�t�w�w�;�.��!�#���T�W�W��,��  �!�#� !� r&   �	directionc                 �V  � U R                  U5      nU(       d  gUR                  [        R                  " UR                  5      -  nUR                  [        R
                  " UR                  5      -  n[        R                  " U5      n[        R
                  " U5      nXF-  XW-  -   nU$ )u�   
计算指定方向上的梯度强度分量

Args:
    waypoint_index: 航点索引
    direction: 方向角（弧度）

Returns:
    该方向上的梯度强度分量
r+   )r�  r�   r�   r�   r�   r�   )	rE   r/   r�  r�  �
gradient_x�
gradient_y�direction_x�direction_y�
projections	            r'   �(calculate_gradient_strength_in_direction�=GradientFieldManager.calculate_gradient_strength_in_direction�  s�   � � �0�0��@���� $�6�6����.�Bc�Bc�9d�d�
�#�6�6����.�Bc�Bc�9d�d�
��h�h�y�)���h�h�y�)��  �-�
�0H�H�
��r&   c           	      �4  � U R                  U5      nU(       d  0 $ U[        R                  " UR                  5      UR                  / UR                  [        R
                  " UR                  5      -  UR                  [        R                  " UR                  5      -  S.S.nUR                   Hd  u  pEn[        R                  " U5      UUU[        R
                  " U5      -  U[        R                  " U5      -  S.nUS   R                  U5        Mf     U$ )ui   
可视化梯度场信息

Args:
    waypoint_index: 航点索引

Returns:
    梯度场可视化数据
r]  )r/   �gradient_direction_degreesr�   r�   �gradient_vector)�
angle_degreesr�   r�   r�   r�   r�   )	r�  r�   r  r�   r�   r�   r�   r�   r�   )rE   r/   r�  �visualization_datar�   r�   r�   �vector_infos           r'   �visualize_gradient_field�-GradientFieldManager.visualize_gradient_field  s�   � � �0�0��@����I� -�*.�,�,�~�7X�7X�*Y�"0�"C�"C� �#�6�6����.�Bc�Bc�9d�d�#�6�6����.�Bc�Bc�9d�d� �	
�� &4�%B�%B�!�E�T�!%���e�!4�$�� �4�8�8�E�?�2� �4�8�8�E�?�2��K� 
�/�0�7�7��D� &C� "�!r&   c                 �8   � U R                   R                  5         g)u   清空所有梯度场N)r|  r  rD   s    r'   �clear_gradient_fields�*GradientFieldManager.clear_gradient_fields5  s   � ����"�"�$r&   c                 �6   � U R                   R                  5       $ )u   获取所有梯度场)r|  �copyrD   s    r'   �get_all_gradient_fields�,GradientFieldManager.get_all_gradient_fields9  s   � ��#�#�(�(�*�*r&   c                 �   � / nU R                   R                  5        H;  u  p#UUR                  UR                  UR                  S.nUR                  U5        M=     U$ )u   导出梯度场数据)r/   r�   r�   r�   )r|  �itemsr�   r�   r�   r�   )rE   �
exported_datar/   r�  �
field_datas        r'   �export_gradient_fields�+GradientFieldManager.export_gradient_fields=  sa   � ��
�.2�.B�.B�.H�.H�.J�*�N�"0�&4�&G�&G�&4�&G�&G�"0�"?�"?�	�J� 
� � ��,� /K� �r&   )r�   r|  r�   N)r   r   r   r    r!   r   rR   r   r�   r)   r�   r�  r  rP   r   r�  r�  rN   r�  r�  r�  r�  r�  r�  r�  r   r�  r%   r   r&   r'   rz  rz  |  s  � ��
�t�C��H�~� 
��.?� �M� �2J�=� J�->�J�B8�� 8��-�9P� 8�1�.?� 1��� �� ��� �� �!�c� !�e� !�,�s� �:?��DI��8$"�s� $"�t�C��H�~� $"�L%�+��c�=�.@�)A� +�
��T�#�s�(�^�(<� 
r&   rz  c                   �t  � \ rS rSrSrS\\\4   S\S\	S\
4S jrS\S	\
4S
 jrS	\
4S jrS\S
\S\\   S\\   S\\   S	\\\      4S jrS\S
\S\\   S	\\   4S jrS\S\\   S	\\   4S jrS\S\S\\   S	\\\      4S jrS\\   S\S\S	\\   4S jrS\S\S\S\4S  jrS	\\\4   4S! jrS" rS# rS$rg%)&�PathSwitchingStrategyiM  u   换路策略管理器r�   �cluster_manager�gradient_field_managerr�  c                 ��   � Xl         X l        X0l        X@l        UR	                  SS5      U l        SU l        UR	                  SS5      U l        / U l        / U l	        S U l
        g )N�collisionThreshold�   r�   �enablePathSwitchingT)r�   r�  r�  r�  r�   �collision_threshold�monitoring_window�enable_path_switchingrX  �switch_history�current_path_id)rE   r�   r�  r�  r�  s        r'   r�   �PathSwitchingStrategy.__init__P  sd   � ���.��&<�#�$�
� $*�:�:�.B�B�#G�� �!"���%+�Z�Z�0E�t�%L��"� :<���46���#��r&   �current_waypointr9   c                 �@  � U R                   (       d  gU R                  R                  U5        [        U R                  5      U R                  :�  a  U R                  R                  S5        [        U R                  5      U R                  :  a  gU R
                  5       $ )ue   
判断是否需要换路

Args:
    current_waypoint: 当前航点

Returns:
    是否需要换路
Fr   )r�  rX  r�   r]   r�  �pop�_check_collision_cost_anomaly)rE   r�  s     r'   �should_switch_path�(PathSwitchingStrategy.should_switch_patha  s�   � � �)�)�� 	
���$�$�%5�6� �t�$�$�%��(>�(>�>��!�!�%�%�a�(� �t�$�$�%��(>�(>�>�� �1�1�3�3r&   c                 ��   � SnU R                    HT  nUR                  S:�  d  M  UR                  UR                  -
  UR                  -  S-  nX0R                  :�  d  MO  US-
  nMV     U[	        U R                   5      :H  $ )uG   
检查碰撞代价持续异常

Returns:
    是否存在持续异常
r   r�  r
   )rX  r4   r5   r�  r]   )rE   �
anomaly_countr"  �deviation_percents       r'   r�  �3PathSwitchingStrategy._check_collision_cost_anomaly|  s�   � � �
��-�-�H��0�0�1�4� �3�3�h�6W�6W�W��5�5�6�8;�<� "� %�'?�'?�?�!�Q�&�M� .� ��D�$9�$9� :�:�:r&   r[  �current_pathr^  r�  r  c              �   ��  #   �  [        SUR                  S SUR                  S S35        U R                  XU5      I Sh  v�N nU(       d  [        S5        gU R                  R                  Xc5      nU(       d  [        SUR                   S35        gU R                  XR                  5      nU(       d  [        S	5        gU R                  XU5      I Sh  v�N n	U	(       d  [        S
5        gU R                  X�U5      n
U R                  XXv5        [        SUR                   SUR                   35        [        S
UR                   35        U
$  GN N}! [         a  n[        SU 35         SnAgSnAff = f7f)u�   
执行换路操作

Args:
    current_position: 当前位置
    current_path: 当前路径
    all_paths: 所有可用路径
    clusters: 所有簇
    buildings: 建筑物列表

Returns:
    新的路径航点列表，如果换路失败则返回None
u*   开始执行换路操作，当前位置: (z.1fr`  r&  Nu   未找到合适的目标簇u
   目标簇 u    中没有可用路径u'   未找到目标路径上的最近航点u   生成换路路径失败u   换路成功：从路径 u    切换到路径 u   目标簇: u   换路操作失败: )r�  r,   r-   �"_select_target_cluster_by_gradientr�  rI  r�   rc  r�   �_generate_switch_path�_merge_paths�_record_switch_historyr�   r�  )rE   r[  r�  r^  r�  r  re  rf  rg  �switch_path�merged_pathr�  s               r'   �execute_path_switch�)PathSwitchingStrategy.execute_path_switch�  s�  � � � 2	��>�?O�?Q�?Q�RU�>V�VX�Yi�Yk�Yk�lo�Xp�pq�r�s� $(�#J�#J� ��$� �N� "��3�4�� �.�.�L�L���K� ��
�>�#<�#<�"=�=S�T�U��  $�:�:�;K�Mb�Mb�c��#��?�@�� !%� :� :� �I�!� �K� ��0�1�� �+�+�K�FV�W�K� 
�'�'�(8��d��-�l�.B�.B�-C�CT�U`�Uh�Uh�Ti�j�k��K�� 9� 9�:�;�<���U�2��& � 	��(���,�-���	�s�   �E=�=E �E�E �E=�;E �E=�-E �E=�E �E�E �/E=�0A"E �E=�E �E �
E:�"E5�0E=�5E:�:E=c              �   �8  #   � U R                  XR                  5      nU(       d  gU R                  R                  UR                  5      nU R
                  R
                  UR                  UR                  5      nU(       d  gUS   nU R
                  R                  U5      nU(       d  gSn	[        S5      n
U H�  nUR                  S   UR                  S   -   S-  nUR                  S   UR                  S   -   S-  n
X�R                  -
  nX�R                  -
  n[        R                  " X�5      n[        UU-
  5      n[!        US[        R"                  -  U-
  5      nU[        R"                  -  nUR$                  S-  nUU-   nUU
:  d  M�  Un
Un	M�     U	$ 7f)u�   
根据梯度场选择目标簇

Args:
    current_position: 当前位置
    current_path: 当前路径
    clusters: 所有簇

Returns:
    目标簇
Nr   r�   r
   r�   r�  )rc  r�   r�  r�  r/   r�  r@  r�   r�   r<  rN   r�   r�   r�   r�   rd   r~   r�   r�   )rE   r[  r�  r�  rg  �gradient_descent_direction�current_clustersr5  r7  rm  rn  r  r:  r;  r�   r�   �cluster_direction�direction_diff�direction_score�
cost_score�total_scores                        r'   r�  �8PathSwitchingStrategy._select_target_cluster_by_gradient�  s�  � � �  �6�6�7G�I_�I_�`���� &*�%@�%@�%`�%`��+�+�&
�"�
  �/�/�G�G��)�)�<�+D�+D�
��  ��*�1�-�� !�0�0�G�G��X�� �� ���5�\�
�(�G� '���� 2�W�_�_�Q�5G� G�1�L�� '���� 2�W�_�_�Q�5G� G�1�L��!�$A�$A�A�B�!�$=�$=�=�B� $�
�
�2� 2�� !�!2�5O�!O�P�N� ���T�W�W��~�1M�N�N� -�t�w�w�6�O� �-�-��5�J�)�J�6�K��Z�'�(�
�&��+ )�. ��s   �F
F�
Fr^  r�   c                 �2  � U(       d  g[        S5      nSnU H{  nUR                  UR                  -
  nUR                  UR                  -
  nUR                  UR                  -
  n[        R
                  " Xf-  Xw-  -   X�-  -   5      n	X�:  d  Mw  U	nUnM}     U$ )rt  Nr�   )rN   r,   r-   r.   r�   r�   )
rE   r^  r�   r�  rg  r"  r�   r�   r�   r�   s
             r'   rc  �,PathSwitchingStrategy._find_nearest_waypoint  s�   � � ���U�|����!�H����h�j�j�(�B����h�j�j�(�B����h�j�j�(�B��y�y��������!6�7�H��&�'��#+� � "�  �r&   rv  r]  c           	   �   �p  #   �  SSK JnJn  [        UR                  UR
                  UR                  5      nUR                  5       UR                  5       USSS.S.nU" U5      nU R                  R                  U5      I Sh  v�N n	U	R                  (       au  U	R                  (       ad  / n
[        U	R                  5       HG  u  p�[        UR                  UR
                  UR                  SU-   US-   S	9n
U
R                  U
5        MI     U
$ g N�! [         a  n[!        S
U 35         SnAgSnAff = f7f)u   生成换路路径r
   r�  r�   i�  r�  r�  Ni�  r�  u   生成换路路径失败: )r�  r   r�  r   r,   r-   r.   rF   r�  r�  r�  r�  r�  r)   r�   r�  r�  )rE   rv  r]  r  r   r�  �
target_pos�request_data�request�response�switch_waypointsrf   rm   r�   r�  s                  r'   r�  �+PathSwitchingStrategy._generate_switch_path2  s  � � �	4�H� ��!2�!2�O�4E�4E��GX�GX�Y�J� (�/�/�1�&�.�.�0�&�+,�t�D�	�L� *�,�7�G�!�Z�Z�6�6�w�?�?�H����H�M�M�#%� � )�(�-�-� 8�H�A�*��'�'�U�W�W����'+�a�x�'(�1�u��B�
 %�+�+�B�/�
 !9� (�'�
 �% @�� � 	4��.�q�c�2�3�3���	4�sB   �D6�A?D �D�B	D �
D6�D �
D3�D.�)D6�.D3�3D6r�  rf  �connection_waypointc                 �6  � / nUR                  USS 5        Sn[        UR                  5       H#  u  pgUR                  UR                  :X  d  M!  Un  O   US:�  a   UR                  US nUR                  U5        [        U5       H  u  pgUS-   Ul        M     U$ )u!   合并换路路径和目标路径Nr�  r   r
   )�extendr�  r�   r/   r1   )	rE   r�  rf  r�  r�  �connection_indexrf   r"  �remaining_waypointss	            r'   r�  �"PathSwitchingStrategy._merge_pathsV  s�   � � �� 	���;�s��+�,� ��$�[�%:�%:�;�K�A��&�&�*=�*L�*L�L�#$� �� <� �q� �"-�"7�"7�8H�8I�"J�����2�3� %�[�1�K�A�&'�!�e�H�#� 2� �r&   �old_pathrh  re  c           	      �6  � [         R                   " 5       UR                  UR                  UR                  S.UR                  UR                  UR
                  S[
        U R                  5      S.nU R                  R                  U5        [        SU 35        g)u   记录换路历史)r,   r-   r.   �collision_cost_anomaly)�	timestampr^  �old_path_id�new_path_id�target_cluster_id�reason�anomaly_waypointsu   记录换路历史: N)�timer,   r-   r.   r�   r�   r]   rX  r�  r�   r�  )rE   r[  r�  rh  re  �
switch_records         r'   r�  �,PathSwitchingStrategy._record_switch_historyq  s�   � � ����.�0�0�7G�7I�7I�P`�Pb�Pb�c�#�+�+�#�+�+�!/�!:�!:�.�!$�T�%:�%:�!;�
�
� 	
���"�"�=�1�
�$�]�O�4�5r&   c                 �   � [        U R                  5      U R                  [        U R                  5      U R                  U R                  S.$ )u   获取换路统计信息)�total_switchesr�  �current_monitoring_windowr�  r�  )r]   r�  rX  r�  r�  rD   s    r'   �get_switch_statistics�+PathSwitchingStrategy.get_switch_statistics�  sE   � � "�$�"5�"5�6�"�1�1�),�T�-B�-B�)C�#'�#;�#;�%)�%?�%?�
� 	
r&   c                 �8   � U R                   R                  5         g)u   重置监控状态N)rX  r  rD   s    r'   �reset_monitoring�&PathSwitchingStrategy.reset_monitoring�  s   � ����#�#�%r&   c                 �8   � U R                   R                  5         g)u   清空换路历史N)r�  r  rD   s    r'   �
clear_history�#PathSwitchingStrategy.clear_history�  s   � ����!�!�#r&   )
r�  r�  r�  r�   r�  r�  r�  r�  rX  r�  N)r   r   r   r    r!   r   rR   r   r�  rz  r   r�   r)   r�   r�  r�  r   r�   r   r�   r   r�  r�  rc  r�  r�  r�  r   r  r  r%   r   r&   r'   r�  r�  M  s�  � ��$�t�C��H�~� $�� $�)=�$�P^�$�"4�3D� 4�� 4�6;�t� ;�0B�'� B�QY� B�+/��>�B�EI�+�EV�B�+/��:�B�:B�4�HY�CZ�:[�B�HC�� C�=E�C�9=�k�9J�C�OW�Xc�Od�C�J �w�  �(,�->�(?� �DL�M^�D_� �*"�W� "�O`� "�-1�$�Z�"�<D�T�J[�E\�<]�"�H��->�(?� �!)��):��?C�DU�?V��66�w� 6�(� 6�'/�6�AL�6� 
�t�C��H�~� 
�&�$r&   r�  c            
       �  � \ rS rSrSr\S\S\S\4S j5       r\S\S\S\4S j5       r	\S\S\4S	 j5       r
\S
\S\S\4S j5       r\S\S\S
\S\4S j5       r\S\S\S\S\S
\S\4S j5       r
\S\\   S\\\\4      4S j5       rSrg)�	MathUtilsi�  u   数学计算工具类r�   r�   r9   c                 ��   � UR                   U R                   -
  nUR                  U R                  -
  nUR                  U R                  -
  n[        R                  " X"-  X3-  -   XD-  -   5      $ )u    计算3D空间中两点间距离)r,   r-   r.   r�   r�   )r�   r�   r�   r�   r�   s        r'   �distance_3d�MathUtils.distance_3d�  sU   � � �T�T�B�D�D�[��
�T�T�B�D�D�[��
�T�T�B�D�D�[���y�y��������.�/�/r&   c                 �   � UR                   U R                   -
  nUR                  U R                  -
  n[        R                  " X"-  X3-  -   5      $ )u    计算2D平面中两点间距离)r,   r-   r�   r�   )r�   r�   r�   r�   s       r'   �distance_2d�MathUtils.distance_2d�  s=   � � �T�T�B�D�D�[��
�T�T�B�D�D�[���y�y������'�'r&   r�   c                 ��   � U S[         R                  -  :�  a/  U S[         R                  -  -  n U S[         R                  -  :�  a  M/  U S:  a  U S[         R                  -  -
  n U S:  a  M  U $ )u#   将角度标准化到[0, 2π)范围r�   r   )r�   r�   )r�   s    r'   �normalize_angle�MathUtils.normalize_angle�  s_   � � �q�4�7�7�{�"��Q����[� �E� �q�4�7�7�{�"��a�i��Q����[� �E� �a�i��r&   �angle1�angle2c                 �\   � [        X-
  5      n[        US[        R                  -  U-
  5      $ )u'   计算两个角度之间的最小差值r�   )rd   r~   r�   r�   )r  r  �diffs      r'   �angle_difference�MathUtils.angle_difference�  s*   � � �6�?�#���4��T�W�W��t�+�,�,r&   �tc                 �
  � U R                   X!R                   U R                   -
  -  -   nU R                  X!R                  U R                  -
  -  -   nU R                  X!R                  U R                  -
  -  -   n[        X4U5      $ )u   线性插值�r,   r-   r.   r   )r�   r�   r  r,   r-   r.   s         r'   �interpolate_linear�MathUtils.interpolate_linear�  sh   � � 
�D�D�1���r�t�t��$�$���D�D�1���r�t�t��$�$���D�D�1���r�t�t��$�$���q�Q��r&   �p0�p3c                 ��  � SU-
  nXD-  nXU-  nXu-  nXd-  n	X�R                   -  SU-  U-  UR                   -  -   SU-  U-  UR                   -  -   X�R                   -  -   n
X�R                  -  SU-  U-  UR                  -  -   SU-  U-  UR                  -  -   X�R                  -  -   nX�R                  -  SU-  U-  UR                  -  -   SU-  U-  UR                  -  -   X�R                  -  -   n[        X�U5      $ )u$   计算三次贝塞尔曲线上的点r
   r\   r  )
r  r�   r�   r  r  �u�tt�uu�uuu�tttr,   r-   r.   s
                r'   �calculate_bezier_point� MathUtils.calculate_bezier_point�  s�   � � 
��E��
�U��
�U���f���f���$�$�J��R��!��b�d�d�*�*�Q��U�R�Z�"�$�$�->�>��t�t��K���$�$�J��R��!��b�d�d�*�*�Q��U�R�Z�"�$�$�->�>��t�t��K���$�$�J��R��!��b�d�d�*�*�Q��U�R�Z�"�$�$�->�>��t�t��K���q�Q��r&   �pointsc                 �$  � [        U 5      S:  a  / $ / n[        [        U 5      S-
  5       GH`  nUS:�  a  XS-
     OX   nX   nXS-      nUS-   [        U 5      :  a  XS-      OXS-      nUR                  UR                  UR                  -
  S-  -   nUR                  UR                  UR                  -
  S-  -   nUR                  UR                  UR                  -
  S-  -   n	UR                  UR                  UR                  -
  S-  -
  n
UR                  UR                  UR                  -
  S-  -
  nUR                  UR                  UR                  -
  S-  -
  n[        XxU	5      n
[        X�U5      nUR
                  X�45        GMc     U$ )u   计算样条曲线的控制点r�   r
   r   r  )r]   rb   r,   r-   r.   r   r�   )r(  �control_pointsrf   r  r�   r�   r  �cp1_x�cp1_y�cp1_z�cp2_x�cp2_y�cp2_z�cp1�cp2s                  r'   �calculate_spline_control_points�)MathUtils.calculate_spline_control_points�  se  � � �v�;��?��I����s�6�{�Q��'�A� !�A���!���6�9�B���B��!���B� !�!��c�&�k� 1��!���v��c�{�B� �D�D�B�D�D�2�4�4�K�1�,�,�E��D�D�B�D�D�2�4�4�K�1�,�,�E��D�D�B�D�D�2�4�4�K�1�,�,�E��D�D�B�D�D�2�4�4�K�1�,�,�E��D�D�B�D�D�2�4�4�K�1�,�,�E��D�D�B�D�D�2�4�4�K�1�,�,�E��%��.�C��%��.�C��!�!�3�*�-�% (�( �r&   r   N)r   r   r   r    r!   �staticmethodr   rN   r  r  r  r  r  r&  r   r   r3  r%   r   r&   r'   r	  r	  �  sF  � ���0�� 0�W� 0�� 0� �0� �(�� (�W� (�� (� �(� ��u� �� � �� �-�� -�� -�%� -� �-�
 � �w�  �G�  ��  �'�  � � � � �7�  ��  �W�  �'�  �V[�  �`g�  � � � ���W�
� �$�u�W�V]�M]�G^�B_� � �r&   r	  c                   ��   � \ rS rSrSrS\\\4   4S jrS\	\
   S\	\
   4S jrS\	\
   S\	\
   4S jrS\	\
   S\	\
   4S	 jr
S\	\
   S\	\
   4S
 jrS\	\
   S\	\
   S\4S
 jrSrg)�PathSmootheri�  u   路径平滑器r�   c                 �   � Xl         UR                  SS5      U l        UR                  SS5      U l        UR                  SS5      U l        g )N�smoothingMethod�cubic_spline�smoothingFactor�      �?�minSegmentLengthrl  )r�   r�   �smoothing_method�smoothing_factor�min_segment_lengthr�   s     r'   r�   �PathSmoother.__init__�  sC   � ��� &�
�
�+<�n� M��� &�
�
�+<�c� B���"(�*�*�-?��"E��r&   r�   r9   c                 ��   � [        U5      S:  a  U$ U R                  S:X  a  U R                  U5      $ U R                  S:X  a  U R                  U5      $ U R                  S:X  a  U R	                  U5      $ U$ )u^   
平滑路径

Args:
    waypoints: 原始航点列表

Returns:
    平滑后的航点列表
r\   r:  �bezier�moving_average)r]   r>  �_smooth_with_cubic_spline�_smooth_with_bezier�_smooth_with_moving_average)rE   r�   s     r'   �smooth_path�PathSmoother.smooth_path�  sz   � � �y�>�A����� � �N�2��1�1�)�<�<�
�
"�
"�h�
.��+�+�I�6�6�
�
"�
"�&6�
6��3�3�I�>�>��r&   c           
      ��  � [        U5      S:  a  U$ U Vs/ s H.  n[        UR                  UR                  UR                  5      PM0     nn[
        R
                  U5      n/ nSn[        US   R                  US   R                  US   R                  UUS9nUR                  U5        US-
  n[        [        U5      S-
  5       H�  nX8   n	X8S-      n
U[        U5      :  d  M  XH   u  p�[
        R                  X�5      n
[        S[        X�R                  -  5      5      n[        SU5       H_  nX�-  n[
        R                  X�X�U5      n[        UR                  UR                  UR                  UUS9nUR                  U5        US-
  nMa     M�     [        US   R                  US   R                  US   R                  UUS9nUR                  U5        U$ s  snf )u$   使用三次样条进行路径平滑r�  r
   r   r�  r�   r�  )r]   r   r,   r-   r.   r	  r3  r)   r�   rb   r  r   rP   r@  r&  )rE   r�   r�   r(  r*  �smoothed_waypointsr/   �start_wprf   r�   r�   r1  r2  r�   �
num_pointsrg   r  �smooth_point�	smooth_wp�end_wps                       r'   rE  �&PathSmoother._smooth_with_cubic_spline
  s�  � ��y�>�A���� 7@�@�i��'�"�$�$����b�d�d�+�i��@� #�B�B�6�J������ %���l�n�n�	�!����)�A�,�.�.�)�)�
��
 	�!�!�(�+��!��� �s�6�{�Q��'�A���B��A���B��3�~�&�&�)�,��� "+�!6�!6�r�!>�� ��C��9P�9P�(P�$Q�R�
� �q�*�-�A���A�#,�#C�#C�B�S�VW�#X�L� 1�&�.�.�L�N�N�l�n�n�'5�'5�!�I�
 '�-�-�i�8�"�a�'�N� .� (�2 #���m�o�o��2����I�b�M�O�O�)�)�
��
 	�!�!�&�)�!�!��e As   �5Gc           
      �  � [        U5      S:  a  U$ / nSnUR                  US   5        US-
  n[        S[        U5      S-
  S5       GHT  n[        X   R                  X   R
                  X   R                  5      n[        XS-      R                  XS-      R
                  XS-      R                  5      n[        XS-      R                  XS-      R
                  XS-      R                  5      n[        XS-      R                  XS-      R
                  XS-      R                  5      nSn	[        SU	5       H_  n
X�-  n[        R                  XVXxU5      n[        UR                  UR
                  UR                  UUS9n
UR                  U
5        US-
  nMa     GMW     [        U5      S:�  a  UR                  US   5        U$ )	u'   使用贝塞尔曲线进行路径平滑r�  r
   r   r\   r�   r5  r�  r�  )
r]   r�   rb   r   r,   r-   r.   r	  r&  r)   )rE   r�   rK  r/   rf   r  r�   r�   r  rM  rg   r  rN  rO  s                 r'   rF  � PathSmoother._smooth_with_bezierD  s�  � ��y�>�A�������� 	�!�!�)�A�,�/��!��� �q�#�i�.�1�,�a�0�A��������������H�B���Q�3��)�)�9�q�S�>�+;�+;�Y��s�^�=M�=M�N�B���Q�3��)�)�9�q�S�>�+;�+;�Y��s�^�=M�=M�N�B���Q�3��)�)�9�q�S�>�+;�+;�Y��s�^�=M�=M�N�B� �J��1�j�)���N��(�?�?���PQ�R��-�"�n�n����,�.�.�#1�#1��	�
 #�)�)�)�4��!�#�� *� 1�* �y�>�A���%�%�i��m�4�!�!r&   c           	      �  � [        U5      S:  a  U$ / nSn[        [        U5      5       H�  nUS:X  d  U[        U5      S-
  :X  a  UR                  X   5        M0  [        SXCS-  -
  5      n[	        [        U5      XCS-  -   S-   5      n[        S XU  5       5      Xe-
  -  n[        S XU  5       5      Xe-
  -  n[        S XU  5       5      Xe-
  -  n	[
        XxU	X   R                  X   R                  S9n
UR                  U
5        M�     U$ )	u$   使用移动平均进行路径平滑r\   r   r
   r�   c              3   �8   #   � U  H  oR                   v �  M     g 7frq   rr   �rt   r�   s     r'   rv   �;PathSmoother._smooth_with_moving_average.<locals>.<genexpr>|  �   � � �H�+G�R�D�D�+G�ry   c              3   �8   #   � U  H  oR                   v �  M     g 7frq   r|   rV  s     r'   rv   rW  }  rX  ry   c              3   �8   #   � U  H  oR                   v �  M     g 7frq   )r.   rV  s     r'   rv   rW  ~  rX  ry   r�  )	r]   rb   r�   r   r~   r�   r)   r/   r1   )rE   r�   rK  �window_sizerf   �	start_idx�end_idx�avg_x�avg_y�avg_zrO  s              r'   rG  �(PathSmoother._smooth_with_moving_averagek  s   � ��y�>�A���������s�9�~�&�A��A�v��c�)�n�q�0�0�"�)�)�)�,�7�  ��1�a�'7�#7�8�	��c�)�n�a��2B�.B�Q�.F�G���H�9�w�+G�H�H�G�L_�`���H�9�w�+G�H�H�G�L_�`���H�9�w�+G�H�H�G�L_�`��-���#,�<�#>�#>�#,�<�#>�#>��	�
 #�)�)�)�4�% '�( "�!r&   �original_waypointsrK  c           	      ��  � U(       d  g[        U5      S:�  Ga  [        U5      S:�  Ga  [        R                  [        US   R                  US   R
                  US   R                  5      [        US   R                  US   R
                  US   R                  5      5      n[        R                  [        US   R                  US   R
                  US   R                  5      [        US   R                  US   R
                  US   R                  5      5      nUS:�  d  US:�  a  g[        S[        U5      S-
  5       GH  nX%S-
     nX%   nX%S-      nUR                  UR                  -
  n	UR
                  UR
                  -
  n
UR                  UR                  -
  nUR
                  UR
                  -
  n[        R                  " X�-  X�-  -   5      n
[        R                  " X�-  X�-  -   5      nU
S:�  d  M�  US:�  d  M�  X�-  X�-  -   X�-  -  n[        S[        SU5      5      n[        R                  " U5      nU[        R                  " S5      :�  d  GM    g   g)	u$   验证平滑后的路径是否合理Fr   r�  r  r
   r  �x   T)r]   r	  r  r   r,   r-   r.   rb   r�   r�   r   r~   r  r  )rE   rb  rK  �start_distance�end_distancerf   �prev_wp�curr_wp�next_wpr  r  r  r  r  r  r  r�   s                    r'   �validate_smoothed_path�#PathSmoother.validate_smoothed_path�  s5  � � "�� �!�"�Q�&�3�/A�+B�Q�+F�&�2�2��*�1�-�/�/�1C�A�1F�1H�1H�J\�]^�J_�Ja�Ja�b��*�1�-�/�/�1C�A�1F�1H�1H�J\�]^�J_�Ja�Ja�b��N�
 %�0�0��*�2�.�0�0�2D�R�2H�2J�2J�L^�_a�Lb�Ld�Ld�e��*�2�.�0�0�2D�R�2H�2J�2J�L^�_a�Lb�Ld�Ld�e��L�
 ��#�|�c�'9�� �q�#�0�1�A�5�6�A�(�Q��/�G�(�+�G�(�Q��/�G� �9�9�w�y�y�(�D��9�9�w�y�y�(�D��9�9�w�y�y�(�D��9�9�w�y�y�(�D��9�9�T�Y���2�3�D��9�9�T�Y���2�3�D��a�x�D�1�H�!�Y���2�t�{�C�	���c�#�y�&9�:�	��	�	�)�,�� �4�<�<��,�,� �+ 7�. r&   )r�   r@  r?  r>  N)r   r   r   r    r!   r   rR   r   r�   r   r)   rH  rE  rF  rG  r�   rj  r%   r   r&   r'   r7  r7  �  s�   � ��F�t�C��H�~� F��T�*;�%<� ��FW�AX� �,8"�4�8I�3J� 8"�t�Te�Of� 8"�t%"�T�2C�-D� %"��N_�I`� %"�N"�T�:K�5L� "�QU�Vg�Qh� "�<-��>O�9P� -�15�6G�1H�-�MQ�-r&   r7  c                   ��   ^ � \ rS rSrSrU 4S jrS\S\4S jrS\4S jr	S\4S jr
S	 rS
 rS r
S\S\\   4S jrS\S\\   4S
 jrS rS rS rS\S\\   4S jrS\4S jrSS\S\4S jjrSrU =r$ )� ImprovedClusterBasedPathPlanningi�  u*   改进的基于分簇的路径规划算法c                 ��  >� [         TU ]  5         [        SSSSS/ SQ[        SSS	S
SS S
9/[        SSSSS S9[        SSSSS S9[        SSSSS S9[        SSSSS S9[        SS S!SS"9[        S#SS$S%S& S9/S'9U l        S U l        S U l        S U l        S U l        S U l	        S U l
        / U l        / U l        / U l
        S U l        S(U l        / U l        S)U l        S)U l        S)U l        g )*N�ImprovedClusterBasedz1.0.0ul   改进的基于分簇的路径规划算法，支持多路径生成、固定空间分簇、动态换路策略�System�advanced)r�   r�  �safety�	collisionr�  �numberu   飞行高度(米)r�  Tc                 �f   � [        U [        [        45      =(       a    SU s=:*  =(       a    S:*  $ s  $ )Nr�   i�  ��
isinstancerP   rN   rr   s    r'   r!  �;ImprovedClusterBasedPathPlanning.__init__.<locals>.<lambda>�  �'   � ��A��U�|�)D�)W��q���TW��)W��)Wr&   )�namerT  �description�
default_value�required�
validationr�  u   安全距离(米)r�  c                 �f   � [        U [        [        45      =(       a    SU s=:*  =(       a    S:*  $ s  $ )Nr5  r�  rv  rr   s    r'   r!  rx  �  ry  r&   )rz  rT  r{  r|  r~  r�   u   最大转向角度(度)r�   c                 �f   � [        U [        [        45      =(       a    SU s=:*  =(       a    S:*  $ s  $ )Nr�  �   rv  rr   s    r'   r!  rx  �  ry  r&   r�   u   风险边缘距离(米)r�   c                 �f   � [        U [        [        45      =(       a    SU s=:*  =(       a    S:*  $ s  $ )Nr�  r�  rv  rr   s    r'   r!  rx  �  ry  r&   r�   u   指数变化速率控制参数r�   c                 �f   � [        U [        [        45      =(       a    SU s=:*  =(       a    S:*  $ s  $ )Nr
   r5  rv  rr   s    r'   r!  rx  �  s'   � ��A��U�|�)D�)U��a���SU��)U��)Ur&   r�  �booleanu   是否启用动态换路)rz  rT  r{  r|  r�  u   碰撞代价异常阈值(%)r�  c                 �f   � [        U [        [        45      =(       a    SU s=:*  =(       a    S:*  $ s  $ )Nr5  r�   rv  rr   s    r'   r!  rx  �  s'   � ��A��U�|�)D�)V��q���TV��)V��)Vr&   )rz  �versionr{  �author�category�supported_optimizations�required_parameters�optional_parametersr   r+   )�superr�   r   r   �info�initial_path_generatorr�  �cost_calculatorr�  �path_switching_strategy�
path_smoother�initial_path_setr�  rC  r�  �flight_progress�switched_paths�risk_reference_value�collision_reference_value�turning_reference_value)rE   �	__class__s    �r'   r�   �)ImprovedClusterBasedPathPlanning.__init__�  sg  �� �
���� "�'�� G���$O�"�'�!� 3�"%�!�W�
�	!� #�)�!� 3�"$�W�� #�'�!� 9�"$�W�� #�+�!� 9�"$�W�� #�!�!� @�"#�U�� #�.�"� :�"&�	� #�-�!� =�"$�V��G*!�#<
��	�~ '+��#�#���#���&*��#�'+��$�!��� 13���+-��
�68���04��� ���)+��� %(��!�),��&�'*��$r&   r�  r9   c              �   �&	  #   � SSK Jn   U R                  SS5        U R                  U5      I Sh  v�N   SU l        U R                  U5      I Sh  v�N   SU l        U R
                  5       I Sh  v�N   SU l        U R                  5       I Sh  v�N   S	U l        U R                  5       I Sh  v�N   S
U l        U R                  U5      I Sh  v�N nU" 5       nSUl
        X4l        U R                  (       Ga�  U R                  R                  Ul
        U R                  R                  S-  Ul        UR                  R!                  [#        S
[%        U R                  R&                  S-  5      5      S[)        SS
[%        U R                  R*                  5      -
  5      SSS.5        UR,                  R!                  [/        U5      SU(       a%  [)        U Vs/ s H  oUR0                  PM     sn5      OSU(       a%  [#        U Vs/ s H  oUR0                  PM     sn5      OS[)        S[/        U5      S-
  5      [3        US5      (       a  [/        UR4                  5      OSSS.5        UR6                  R!                  U R8                  R:                  [/        U R<                  5      SS[/        U R<                  5       S3S[/        U R>                  5       S3S// S/S.5        UR@                  R!                  S[/        U R<                  5      [/        U R>                  5      [/        U RB                  5      U R                  (       a  U R                  R*                  OSU R                  (       a  U R                  R                  OSU R                  (       a  U R                  RD                  OSU R                  (       a  U R                  R&                  OSU R                  (       a  U R                  RF                  OSS .	5        S!U l        U R                  S"S5        U$  GN� GN� GN� GN� GN| GN_s  snf s  snf ! [H         aK  nU R                  S#[K        U5       3S$5        U" 5       nS%Ul
        S&[K        U5       3Ul&        Us SnA$ SnAff = f7f)'u�   
使用改进的基于分簇的算法计算路径

Args:
    request: 路径规划请求
    
Returns:
    PathPlanningResponse: 路径规划响应
r
   )r   u6   开始执行改进的基于分簇的路径规划算法r�  Nro  r  r<  rn  g�������?Tr�   r�  r5  �U   r   r-  �high)�
risk_score�
smoothness�
efficiency�
safety_margin�
complexityr�   r�  )�total_waypoints�
average_speed�max_altitude�min_altitude�turning_points�obstacles_avoided�no_fly_zones_crossedg{�G�z�?u   生成u	   条路径u   分为u   个簇�   选择最优路径u3   考虑增加更多中转点以提高路径多样性)�algorithm_version�iteration_count�convergence_time�optimization_steps�warnings�suggestions�improved_cluster_based)	�algorithm_type�initial_paths_count�clusters_count�
path_switchesr�   r�   r�   r3   r�   r  u6   改进的基于分簇的路径规划算法执行完成u   算法执行失败: �errorFu   改进算法执行失败: )'r�  r   r=  �_initialize_components�progress�_generate_initial_path_set�_perform_clustering�
_smooth_paths�_select_optimal_path�_simulate_flight_with_switchingr�  r�  r�  r�   �estimated_flight_time�quality�updater~   rP   r3   r   r�   �
statisticsr]   rK   r_  r�  �
debug_infor�  r�  r�  r�  �metadatar�  r�   r�   r�  rR   r�  )rE   r�  r   �
final_pathr�  ru   r�  s          r'   r�  �/ImprovedClusterBasedPathPlanning.calculate_path	  s�  � � � 	:�X	��H�H�M�v�V� �-�-�g�6�6�6�  �D�M��1�1�'�:�:�:�  �D�M��*�*�,�,�,�  �D�M��$�$�&�&�&�  �D�M��+�+�-�-�-�  �D�M�#�C�C�G�L�L�J� ,�-�H�#�H��&�M� � � � �'+�'8�'8�'D�'D��$�15�1B�1B�1N�1N�QU�1U��.� � � �'�'�"%�c�3�t�/@�/@�/K�/K�b�/P�+Q�"R�"$�"%�a��s�4�3D�3D�3O�3O�/P�)P�"Q�%)�"(�)� � �#�#�*�*�'*�:��%)�HR�C�
�(C�
�1���
�(C�$D�XY�HR�C�
�(C�
�1���
�(C�$D�XY�&)�!�S��_�q�-@�&A�CJ�7�T_�C`�C`��W�->�->�)?�fg�,-�,� � �#�#�*�*�)-���):�):�'*�4�+@�+@�'A�(,�-3�C��8M�8M�4N�3O�y�+Y�]c�dg�hl�hu�hu�dv�cw�w}�[~�  AU�  +V� "�$Y�#Z�
,� � 
���$�$�":�'*�4�+@�+@�'A�"%�d�m�m�"4�!$�T�%8�%8�!9�>B�>O�>O�d�/�/�:�:�UV�@D�@Q�@Q�t�0�0�<�<�WX�BF�BS�BS�� 1� 1� >� >�YZ�>B�>O�>O�d�/�/�:�:�UV�FJ�FW�FW�$�"3�"3�"B�"B�]^�
&� 

�  �D�M��H�H�M�v�V��O�[ 
7� 
;� 
-� 
'� 
.� M��2 )D��(C��B � 	��H�H�+�C��F�8�4�g�>�+�-�H�$�H��9�#�a�&��B�H�N��O��	�s�   �R�&P9 �P�P9 �P �P9 �/P#�0P9 �P&�P9 �-P)�.P9 �
P,�DP9 �P/�+P9 �P4�HP9 �R�P9 � P9 �#P9 �&P9 �)P9 �,P9 �/
P9 �9
R�A R	�R�R�	R�Rc           
   �   ��  #   �  UR                   R                  SS5      UR                   R                  SS5      UR                   R                  SS5      UR                   R                  SS5      UR                   R                  S	S
5      UR                   R                  SS5      UR                   R                  S
S5      UR                   R                  SS5      UR                   R                  SS5      UR                   R                  SS5      S.
n[        5       n[        X25      U l        [        5       U l        [        U5      U l        [        U5      U l
        [        U5      U l        [        X R                  U R                  U5      U l        U R                  U5      U l        U R#                  SS5        g! [$         a  nU R#                  SU 3S5        e SnAff = f7f)u   初始化算法组件r�  r�  r�  r�  r�   r�   r�   r�   r�   r�   r�  Tr�  r�  r9  r:  r;  r<  r=  rl  )
r�  r�  r�   r�   r�   r�  r�  r9  r;  r=  u   算法组件初始化完成r�  u   组件初始化失败: r�  N)r�  r�   r   r�  r�  r�  r�  r�   r�  rz  r�  r7  r�  r�  r�  �_generate_protection_zonesrC  r=  r�  )rE   r�  r�   r�  r�  s        r'   r�  �7ImprovedClusterBasedPathPlanning._initialize_componentsy	  s�  � � �%	� !(� 2� 2� 6� 6�~�s� K�")�"4�"4�"8�"8�9I�2�"N� '� 2� 2� 6� 6�~�r� J�$+�$6�$6�$:�$:�;M�r�$R�!�,�,�0�0��1�=�'.�'9�'9�'=�'=�>S�UY�'Z�&-�&8�&8�&<�&<�=Q�SU�&V�#*�#5�#5�#9�#9�:K�^�#\�#*�#5�#5�#9�#9�:K�S�#Q�$+�$6�$6�$:�$:�;M�s�$S��F� -�.�O� +B�/�*Z�D�'�#1�#3�D� �#1�&�#9�D� �*>�v�*F�D�'�!-�f�!5�D�� ,A��,�,�d�.I�.I�?�,�D�(�
 %)�$C�$C�G�$L�D�!��H�H�2�F�;��� 	��H�H�.�q�c�2�G�<���	�s)   �G%�F8F= �<G%�=
G"�G�G"�"G%c              �   �|  #   �  U R                  SS5        U R                  SS5        U R                  U5      I Sh  v�N U l        U R                  (       d  [        S5      eU R	                  5       I Sh  v�N   U R                  5         U R
                  5       I Sh  v�N   U R                  R                  S S9  [        U R                  5       H  u  p#US-   Ul	        M     U R                  S	[        U R                  5       S
3S5        g N� N� Nt! [         a  nU R                  SU 3S5        e SnAff = f7f)
u   生成初始路径集u   开始生成初始路径集...r�  uL   生成81条初始路径（优化版本，确保生成完整的81条路径）Nu   未能生成任何初始路径c                 �   � U R                   $ rq   rD  rE  s    r'   r!  �MImprovedClusterBasedPathPlanning._generate_initial_path_set.<locals>.<lambda>�	  s   � �Q�\�\r&   r#  r
   u"   初始路径集生成完成，共 r  u   生成初始路径集失败: r�  )r=  �_generate_optimized_path_setr�  r�  �_calculate_path_costs�_calculate_reference_values�_recalculate_final_costs�sortr�  r�   r]   )rE   r�  rf   r�  r�  s        r'   r�  �;ImprovedClusterBasedPathPlanning._generate_initial_path_set�	  s,  � � �	��H�H�5�v�>� 
�H�H�c�ek�l� +/�*K�*K�G�*T�$T�D�!��(�(�� @�A�A� �,�,�.�.�.� 
�,�,�.� �/�/�1�1�1� 
�!�!�&�&�+A�&�B�$�T�%:�%:�;�����E��	� <� 
�H�H�9�#�d�>S�>S�:T�9U�U_�`�bh�i�' %U� 
/� 
2�� � 	��H�H�4�Q�C�8�'�B���	�s]   �D<�8D �D�8D �5D�6'D �D�A/D �
D<�D �D �D �
D9�D4�4D9�9D<c              �   ��  #   �  U R                  SS5        U R                  R                  U R                  5        U R                   Vs0 s H  oR                  UR
                  _M     nnU R                  R
                  U5        U R                  R                  5       U l        U R                  S[        U R                  5       S3S5        gs  snf ! [         a  nU R                  SU 3S5        e SnAff = f7f)u   执行分簇u   开始执行分簇...r�  u   分簇完成，共 u    个簇u   分簇失败: r�  N)r=  r�  r  r�  r�   r�   r  r)  r�  r]   r�  )rE   r�  r�   r�  s       r'   r�  �4ImprovedClusterBasedPathPlanning._perform_clustering�	  s�   � � �	��H�H�,�f�5� 
� � �9�9�$�:O�:O�P� EI�DY�DY�Z�DY�D�,�,����7�DY�J�Z�� � �8�8��D� !�0�0�>�>�@�D�M��H�H�*�3�t�}�}�+=�*>�g�F��O��
 [�� � 	��H�H�~�a�S�)�7�3���	�s<   �C<�AC �	 C�)A%C �C<�C �
C9�C4�4C9�9C<c              �   ��  #   �  U R                  SS5        SnU R                   Hs  nUR                  (       d  M  U R                  R	                  UR                  5      nU R                  R                  UR                  U5      (       d  Mh  X2l        US-
  nMu     U R                  SU S3S5        g	! [         a  nU R                  SU 3S5         S	nAg	S	nAff = f7f)
u   平滑路径u   开始平滑路径...r�  r   r
   u"   路径平滑完成，成功平滑 r  u   路径平滑失败: r�  N)r=  r�  r�   r�  rH  rj  r�  )rE   �smoothed_countr�  rK  r�  s        r'   r�  �.ImprovedClusterBasedPathPlanning._smooth_paths�	  s�   � � �	:��H�H�,�f�5��N��-�-���>�>�>�)-�);�);�)G�)G����)W�&� �)�)�@�@����Qc�d�d�);��&�!�+�� .� 
�H�H�9�.�9I��T�V\�]��� 	:��H�H�+�A�3�/��9�9��	:�s:   �C�2B2 �AB2 �%B2 �1C�2
C�<C�C�C�Cc           	   �   �  #   �  U R                  SS5        U R                  R                  5       nU(       d  [        S5      eU R                  R	                  XR
                  5      U l        U R                  (       d  [        S5      eU R                  SU R                  R                   SUR                   SU R                  R                  S 3S5        g! [         a  nU R                  S	U 3S
5        e SnAff = f7f)r�  u   开始选择最优路径...r�  u   未找到领导者种群u   未找到最优路径u   选择最优路径: 路径ID u   , 来自簇 u   , 最终代价 r%  u   选择最优路径失败: r�  N)
r=  r�  r/  r�  rI  r�  r�  r�   r�   r�   )rE   �leader_clusterr�  s      r'   r�  �5ImprovedClusterBasedPathPlanning._select_optimal_path�	  s�   � � �	��H�H�2�F�;� "�1�1�D�D�F�N�!�� :�;�;� !%� 4� 4� R� R�� 5� 5�!�D�� �$�$�� 7�8�8��H�H�4�T�5F�5F�5N�5N�4O� P!�!/�!:�!:� ;� <$�$(�$5�$5�$@�$@��#E�G�HN�
P�� � 	��H�H�1�!��5�w�?���	�s)   �C?�CC �C?�
C<�!C7�7C<�<C?c              �   �  #   �  U R                  SS5        U R                  (       a  U R                  R                  (       d  / $ / nU R                  R                  R                  5       nSnU[	        U5      :  Gae  X4   nU R                  U5        U R                  R                  U5        U R                  R                  U5      (       a�  U R                  SUR                   S3S5        [        UR                  UR                  UR                  5      nU R                  R                  X`R                  U R                   U R"                  UR$                  =(       d    / 5      I Sh  v�N nU(       a6  UnSnU R&                  R)                  U R                  R*                  5        GM?  UR)                  UR-                  5       5        US-
  nU[	        U5      :  a  GMe  U R                  S[	        U5       S	3S5        U R&                  (       a)  U R                  S
[	        U R&                  5       S3S5        U$  N�! [.         a�  nU R                  SU 3S
5        U R                  (       aZ  U R                  R                  (       a?  U R                  R                   V	s/ s H  o�R-                  5       PM     Os  sn	f sn	s SnA$ / s SnA$ SnAff = f7f)u   模拟飞行和动态换路u$   开始模拟飞行和动态换路...r�  r   u
   在航点 u
    触发换路Nr
   u(   飞行模拟完成，最终路径包含 ra  u
   执行了 u
    次换路u   飞行模拟失败: r�  )r=  r�  r�   r�  r]   �&_simulate_object_detection_at_waypointr�  r�  r�  r�  r/   r   r,   r-   r.   r�  r�  r�  r�  r�  r�   r�   rL   r�  )
rE   r�  r�  �current_waypointsr/   r�  r[  rh  r�  r�   s
             r'   r�  �@ImprovedClusterBasedPathPlanning._simulate_flight_with_switching
  sf  � � �5	��H�H�;�V�D��$�$�D�,=�,=�,G�,G��	��J� $� 1� 1� ;� ;� @� @� B���N� �3�'8�#9�9�#4�#D� � �;�;�<L�M� �+�+�A�A�BR�S� �/�/�B�B�CS�T�T��H�H�z�*:�*I�*I�)J�-�X�Z`�a� (/�/?�/A�/A�CS�CU�CU�Wg�Wi�Wi�'j�$�%)�%A�%A�%U�%U�(�*;�*;�T�=R�=R��
�
�w�'8�'8�'>�B�&�  �H�
  �,4�)�)*���+�+�2�2�4�3D�3D�3L�3L�M� � �!�!�"2�"@�"@�"B�C��!�#��= !�3�'8�#9�9�@ 
�H�H�?��J��?P�PZ�[�]c�d��"�"����:�c�$�*=�*=�&>�%?�z�J�F�S���+ ��. � 	��H�H�+�A�3�/��9�� � �T�%6�%6�%@�%@�59�5F�5F�5P�5P�Q�5P�r�(�(�*�5P��Q�Q��I��	�sz   �K�?H> �K�D%H> �)H<�*A4H> �!AH> �;K�<H> �>
K�AK�!J;�:K�K�K�K�	K�
K�K�Kc                 �  � / nUR                   nUR                  n[        SS[        UR                  S-   UR
                  S-   S5      [        UR                  S-   UR
                  S-   S5      [        UR                  S-   UR
                  S-   S5      [        UR                  S-   UR
                  S-   S5      /SS9nUR
                  U5        [        SS	[        UR                  S-
  UR
                  S-
  S5      [        UR                  S-
  UR
                  S-
  S5      [        UR                  S-
  UR
                  S-
  S5      [        UR                  S-
  UR
                  S-
  S5      /S
S9nUR
                  U5        U$ )u   生成模拟保护区数据�vehicle_zone_1r   r�   r   r�  r�   r�  �pedestrian_zone_1r   r�   )r�   r�   rT   r   r,   r-   r�   )rE   r�  rC  �start�endr�  �pedestrian_zones          r'   r�  �;ImprovedClusterBasedPathPlanning._generate_protection_zonesD
  s^  � ��� �#�#������ &�$������"��e�g�g��l�A�6�����#�
�u�w�w��|�Q�7�����#�
�u�w�w��}�a�8�����"��e�g�g��m�Q�7�	� $'�

�� 	����-� )�'�"�������S�U�U�S�[�!�4������
�C�E�E�C�K��3������
�C�E�E�B�J��2�������S�U�U�R�Z��3�	� $(�

�� 	����0��r&   c              �   ��  #   � U R                    H�  nUR                  (       d  M  U R                  R                  UR                  5      Ul        U R                  R                  UR                  5      Ul        U R                  R                  UR                  / UR                  5      u  Ul        nU R                  R                  UR                  U R                  5      Ul        M�     g7f)u!   计算每条路径的四个指标N)r�  r�   r�  r�   r�   r  r�   r%  r3   rJ  rC  r�   )rE   r�  �_s      r'   r�  �6ImprovedClusterBasedPathPlanning._calculate_path_costsj
  s�   � � ��)�)�D��>�>��  $�3�3�I�I�$�.�.�Y�D�� !%� 4� 4� K� K�D�N�N� [�D�� "&�!5�!5�!J�!J�����D�$4�$4�"��D�O�Q�
 #'�"6�"6�"O�"O����� 5� 5�#�D��! *�s   �C*C,c                 ��  � U R                   (       d  gU R                    Vs/ s H  oR                  PM     nnU R                  R                  U5      U l        [        S U R                    5       5      [
        U R                   5      -  nU R                  R                  [        U5      5      U l	        U R                  R                  U R                  [        U5      5      U l        gs  snf )u   计算参考值用于标准化Nc              3   �L   #   � U  H  n[        UR                  5      v �  M     g 7frq   )r]   r�   )rt   r�  s     r'   rv   �OImprovedClusterBasedPathPlanning._calculate_reference_values.<locals>.<genexpr>�
  s   � � �R�<Q�D�C����/�/�<Q�s   �"$)
r�  r3   r�  r*  r�  r�   r]   r  rP   r�  r�  rC  r�  )rE   r�  r'  �
avg_waypointss       r'   r�  �<ImprovedClusterBasedPathPlanning._calculate_reference_values�
  s�   � ��$�$�� 7;�6K�6K�L�6K�d�/�/�6K��L�$(�$8�$8�$W�$W�Xf�$g��!� �R�D�<Q�<Q�R�R�UX�Y]�Yn�Yn�Uo�o�
�'+�';�';�'\�'\�]`�an�]o�'p��$� *.�)=�)=�)`�)`��!�!�3�}�#5�*
��&�� Ms   �C(c              �   �(  #   � U R                   U R                  l        U R                  U R                  l        U R
                  U R                  l        U R                   H-  nU R                  R                  UR                  5      Ul
        M/     g7f)uE   重新计算最终代价（使用动态权重模型 - 保密公式）N)r�  r�  r�  r�  r�  r�  r�  r�  r�  r�   r�   )rE   r�  s     r'   r�  �9ImprovedClusterBasedPathPlanning._recalculate_final_costs�
  sr   � � � 37�2K�2K����/�9=�9W�9W����6�<@�<X�<X����9��)�)�D�"�2�2�[�[�����D�O� *�s   �BBc              �   ��  #   � / n/ SQn/ SQnSn[        US5       GH�  u  pg[        US5       GH�  u  p� UR                  n
UR                  nSSKnU
R                  UR                  -   S-  n
UR                  U
R                  -
  S-  nUR
                  U
R
                  -
  S-  UR                  " UR                  " U
5      5      -  nUR                  " X�5      nUUR                  " U5      -   nUR                  " US-  US-  -   5      S-  nSnUS-  UUR                  " U5      -  -   nUS-  UUR                  " U5      -  -   nU
R
                  USUR                  " UR                  " U
5      5      -  -  -   nU
R                  US-  -   nU	n[        U
R
                  U
R                  U
R                  SSS	9[        UUUSSS	9[        UR
                  UR                  UR                  S
S
S	9/n[        UUUUS9nUR                  U5        US-
  nGM�     GM�     U R!                  S[#        U5       S3S5        U$ ! [         a'  nU R!                  SU S
U SU 3S5         SnAGM;  SnAff = f7f)u8   生成优化的81条路径集（按需求文档要求）)	i����i����i����i����r   r5  r�  r�  �(   )	�P   r�   r�  �n   rd  �   �   �   �   r   r
   Nr�   i�� r�   r�  r\   r�  u    生成优化路径失败 (方向r�  r�  r�  u
   生成了 u    条优化路径r�  )r�  r�   r�   r�   rJ   rI   r�   r  r�   r�   r�   r)   rK   r�   r�   r�  r=  r]   )rE   r�  �optimized_pathsr�  �heightsr�   r�  r�  �
height_idxr�  r�   r�   r�   �lat_avg�
lat_diff_m�
lng_diff_m�
base_angler�  �mid_distance�offset_distance�mid_x_m�mid_y_m�mid_lng�mid_lat�mid_altr�   r�  r�  s                               r'   r�  �=ImprovedClusterBasedPathPlanning._generate_optimized_path_set�
  s�  � � ��� =�
�=����(1�*�a�(@�$�M�&/���&;�"�
�>�")�"5�"5�K� '� 1� 1�I�  �
  +������>�!�C�G�"+�-�-�+�/�/�"A�V�!K�J�"+�-�-�+�/�/�"A�V�!K�d�h�h�W[�Wc�Wc�dk�Wl�Nm�!m�J�!%���J�!C�J� '1�4�<�<�	�3J�&J�O� $(�9�9�Z��]�Z��]�-J�#K�a�#O�L�&(�O� )�1�n�����/�AZ�/Z�Z�G�(�1�n�����/�AZ�/Z�Z�G� *�o�o��6�D�H�H�T�\�\�Za�Mb�Dc�;c�0d�d�G�)�o�o��&�0@�@�G�$�G� *�)�o�o����K�O�O�+,�Q�� *�%��G�+,�Q�� *�'�m�m�y�}�}�	�
�
�+,�Q��
!�I�  !)� '�)6�%/�"+�	!�I� $�*�*�9�5��q�L�G�w '<� )A�D 	
���:�c�/�2�3�3C�D�f�M���� !� ��H�H�?�
��h�Wa�Vb�be�fg�eh�i�kr�s����s/   �1I0�GH<�+I0�<
I-�I(�!I0�(I-�-I0r"  c                 �D   � U R                   R                  U5      Ul        g)u   在航点处模拟物体检测N)r�  rF  r5   )rE   r"  s     r'   r�  �GImprovedClusterBasedPathPlanning._simulate_object_detection_at_waypoint�
  s   � � *.�)=�)=�)]�)]�^f�)g��&r&   �message�levelc                 �B   � [        SUR                  5        SU 35        g)u   日志输出�[z] ImprovedClusterBased: N)r�  �upper)rE   r  r  s      r'   r=  �$ImprovedClusterBasedPathPlanning.log�
  s   � �
��%�+�+�-�� 8��	�B�Cr&   )r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  rC  r�  r�  r�  )r�  )r   r   r   r    r!   r�   r   r   r�  r�  r�  r�  r�  r�  r   r   r�  rT   r�  r�  r�  r�  r�   r�  r)   r�  rR   r=  r%   �
__classcell__)r�  s   @r'   rm  rm  �  s�   �� �4�U+�nd�,?� d�DX� d�L'�4G� '�R!�8K� !�F�*:�,�67�=P� 7�UY�Zc�Ud� 7�r$ �2E� $ �$�~�J^� $ �L�,
�$�L�:M� L�RV�W_�R`� L�\h�?P� h�
D�3� D�s� D� Dr&   rm  ).r!   �asyncior�  r�   �numpy�np�typingr   r   r   r   r   r   �dataclassesr	   r
   �enumr   �heapqr�  r   �baser   r   r   r�  r   r   r   r   r�  r   r   r)   rT   r�   r�   r�   r�   r�  r�  rz  r�  r	  r7  rm  r   r&   r'   �<module>r     s5  ���
 � � � � 8� 8� (� � � � J� J� Z� Z� !��� � � =�  =� � =�F �E� E� �E�P �#*� #*� �#*�L �#+� #+� �#+�L �
� 
� �
�>H � H �VS� S�la� a�HN� N�bD$� D$�N
T� T�nK� K�\{D�'<� {Dr&   