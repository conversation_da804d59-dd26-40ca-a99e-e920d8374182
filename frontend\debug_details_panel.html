<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详情面板调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .btn:hover:not(:disabled) {
            background: #0056b3;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #444;
            border-radius: 5px;
            background: #2a2a2a;
        }
        .log {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        /* 复制主页面的面板样式 */
        .path-details-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 15px;
            width: 400px;
            max-height: 600px;
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: none;
        }
        
        .path-details-panel h3 {
            margin-bottom: 15px;
            color: #00d4ff;
            font-size: 16px;
            font-weight: 600;
        }
        
        .path-details-content {
            max-height: 520px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.4;
        }
        
        .close-details-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: #fff;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <h1>详情面板调试</h1>
    
    <div class="test-section">
        <h3>测试1: 检查面板元素</h3>
        <button class="btn" onclick="checkPanelElements()">检查面板元素</button>
        <div id="element-check" class="log"></div>
    </div>
    
    <div class="test-section">
        <h3>测试2: 模拟数据设置</h3>
        <button class="btn" onclick="setupMockData()">设置模拟数据</button>
        <div id="mock-data" class="log"></div>
    </div>
    
    <div class="test-section">
        <h3>测试3: 显示详情面板</h3>
        <button class="btn" onclick="showDetailsPanel()">显示路径详情</button>
        <button class="btn" onclick="showFormulaPanel()">显示公式详情</button>
        <button class="btn" onclick="hidePanel()">隐藏面板</button>
        <div id="panel-test" class="log"></div>
    </div>
    
    <div class="test-section">
        <h3>测试4: 按钮事件测试</h3>
        <button class="btn" id="show-details-btn">查看详情</button>
        <button class="btn" id="show-formula-btn">公式详情</button>
        <div id="button-test" class="log"></div>
    </div>
    
    <!-- 详情面板 -->
    <div class="path-details-panel" id="path-details-panel">
        <button class="close-details-btn" id="close-details-btn">×</button>
        <h3>🛤️ 路径规划详情</h3>
        <div class="path-details-content" id="path-details-content">
            <!-- 详情内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        let mockCityManager = {
            lastPlanningDetails: null,
            
            log: function(message, type = 'info') {
                console.log(`[${type.toUpperCase()}] ${message}`);
                log('panel-test', `[${type.toUpperCase()}] ${message}`);
            }
        };
        
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            element.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }
        
        function checkPanelElements() {
            log('element-check', '开始检查面板元素...');
            
            const panel = document.getElementById('path-details-panel');
            const content = document.getElementById('path-details-content');
            const closeBtn = document.getElementById('close-details-btn');
            
            log('element-check', `面板元素: ${panel ? '✅ 存在' : '❌ 不存在'}`);
            log('element-check', `内容元素: ${content ? '✅ 存在' : '❌ 不存在'}`);
            log('element-check', `关闭按钮: ${closeBtn ? '✅ 存在' : '❌ 不存在'}`);
            
            if (panel) {
                log('element-check', `面板当前显示状态: ${panel.style.display}`);
                log('element-check', `面板计算样式: ${getComputedStyle(panel).display}`);
            }
        }
        
        function setupMockData() {
            log('mock-data', '设置模拟数据...');
            
            mockCityManager.lastPlanningDetails = {
                algorithm: 'ImprovedClusterBased',
                response: {
                    success: true,
                    pathLength: 362.28,
                    executionTime: 0.04,
                    metadata: {
                        algorithm_type: 'improved_cluster_based',
                        initial_paths_count: 81,
                        clusters_count: 13,
                        grid_points_count: 2505,
                        building_count: 0
                    }
                },
                requestData: {
                    startPoint: { lng: 139.767, lat: 35.681, alt: 100 },
                    endPoint: { lng: 139.769, lat: 35.684, alt: 100 }
                },
                timestamp: new Date(),
                pathLength: 362.28,
                pathPoints: 12,
                buildingCount: 0
            };
            
            log('mock-data', '✅ 模拟数据已设置');
            log('mock-data', `算法: ${mockCityManager.lastPlanningDetails.algorithm}`);
            log('mock-data', `路径长度: ${mockCityManager.lastPlanningDetails.pathLength}米`);
        }
        
        function showDetailsPanel() {
            log('panel-test', '尝试显示路径详情面板...');
            
            if (!mockCityManager.lastPlanningDetails) {
                mockCityManager.log('没有可显示的路径规划详情', 'warning');
                return;
            }
            
            const panel = document.getElementById('path-details-panel');
            const content = document.getElementById('path-details-content');
            
            if (!panel || !content) {
                mockCityManager.log('详情面板元素未找到', 'error');
                return;
            }
            
            // 生成简单的详情内容
            const details = mockCityManager.lastPlanningDetails;
            content.innerHTML = `
                <div style="color: #cccccc;">
                    <h4>📊 基本信息</h4>
                    <p>算法: ${details.algorithm}</p>
                    <p>路径长度: ${details.pathLength}米</p>
                    <p>路径点数: ${details.pathPoints}</p>
                    <p>执行时间: ${(details.response.executionTime * 1000).toFixed(0)}ms</p>
                    
                    <h4>🏗️ 环境信息</h4>
                    <p>建筑物数量: ${details.buildingCount}</p>
                    
                    <h4>📈 算法元数据</h4>
                    <pre>${JSON.stringify(details.response.metadata, null, 2)}</pre>
                </div>
            `;
            
            // 显示面板
            panel.style.display = 'block';
            mockCityManager.log('路径规划详情已显示', 'info');
        }
        
        function showFormulaPanel() {
            log('panel-test', '尝试显示公式详情面板...');
            
            if (!mockCityManager.lastPlanningDetails) {
                mockCityManager.log('没有可显示的路径详情', 'warning');
                return;
            }
            
            const response = mockCityManager.lastPlanningDetails.response;
            const isImprovedCluster = mockCityManager.lastPlanningDetails.algorithm === 'ImprovedClusterBased' ||
                                     (response.metadata && response.metadata.algorithm_type === 'improved_cluster_based');
            
            if (!isImprovedCluster) {
                mockCityManager.log('公式详情仅适用于改进分簇算法', 'warning');
                return;
            }
            
            const panel = document.getElementById('path-details-panel');
            const content = document.getElementById('path-details-content');
            
            if (!panel || !content) {
                mockCityManager.log('详情面板元素未找到', 'error');
                return;
            }
            
            // 生成公式详情内容
            content.innerHTML = `
                <div style="color: #cccccc;">
                    <h4>🧮 改进分簇算法 - 完整公式实现过程</h4>
                    <p style="font-size: 11px; color: #aaa;">以下是严格按照项目需求文档实现的14个核心公式的完整计算过程：</p>
                    
                    <div style="margin: 15px 0; padding: 10px; background: rgba(0,100,200,0.1); border-radius: 5px;">
                        <h5>📋 步骤1: 初始路径集生成</h5>
                        <p>生成了 ${response.metadata.initial_paths_count} 条初始路径</p>
                        <p>使用9个起飞方向 × 9个高度层的组合策略</p>
                    </div>
                    
                    <div style="margin: 15px 0; padding: 10px; background: rgba(0,150,100,0.1); border-radius: 5px;">
                        <h5>🎯 步骤2: 固定空间分簇</h5>
                        <p>创建了 ${response.metadata.clusters_count} 个分簇</p>
                        <p>包含9个3×3簇和4个4×4簇</p>
                    </div>
                    
                    <div style="margin: 15px 0; padding: 10px; background: rgba(150,100,0,0.1); border-radius: 5px;">
                        <h5>📊 步骤3: 四个核心指标计算</h5>
                        <p>处理了 ${response.metadata.grid_points_count} 个网格点</p>
                        <p>建筑物障碍: ${response.metadata.building_count} 个</p>
                    </div>
                    
                    <div style="margin: 15px 0; padding: 10px; background: rgba(150,0,100,0.1); border-radius: 5px;">
                        <h5>🎯 步骤4: 目标函数计算</h5>
                        <p>最终代价: ${response.metadata.final_cost || 0}</p>
                        <p>路径长度: ${response.pathLength}米</p>
                    </div>
                    
                    <div style="margin: 15px 0; padding: 10px; background: rgba(100,0,150,0.1); border-radius: 5px;">
                        <h5>🔄 步骤5: 最优簇选择与换路策略</h5>
                        <p>选择了最优路径</p>
                        <p>执行时间: ${(response.executionTime * 1000).toFixed(0)}ms</p>
                    </div>
                </div>
            `;
            
            panel.style.display = 'block';
            mockCityManager.log('公式实现详情已显示', 'info');
        }
        
        function hidePanel() {
            const panel = document.getElementById('path-details-panel');
            if (panel) {
                panel.style.display = 'none';
                mockCityManager.log('面板已隐藏', 'info');
            }
        }
        
        // 设置按钮事件
        document.getElementById('show-details-btn').addEventListener('click', () => {
            log('button-test', '查看详情按钮被点击');
            showDetailsPanel();
        });
        
        document.getElementById('show-formula-btn').addEventListener('click', () => {
            log('button-test', '公式详情按钮被点击');
            showFormulaPanel();
        });
        
        document.getElementById('close-details-btn').addEventListener('click', () => {
            log('button-test', '关闭按钮被点击');
            hidePanel();
        });
        
        // 初始检查
        checkPanelElements();
    </script>
</body>
</html>
