#!/usr/bin/env python3
"""
完整的端到端测试
测试从前端请求到后端响应的完整流程
"""

import requests
import json
import time

def test_complete_flow():
    """测试完整的端到端流程"""
    
    print("🔍 完整端到端测试")
    print("=" * 80)
    
    base_url = "http://localhost:5000"
    
    # 1. 测试保护区API
    print("📋 1. 测试保护区API")
    try:
        response = requests.get(f'{base_url}/api/protection-zones/info')
        if response.status_code == 200:
            zones_data = response.json()
            print(f"   ✅ 保护区API正常，返回 {len(zones_data.get('zones', []))} 个保护区")
        else:
            print(f"   ❌ 保护区API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 保护区API连接失败: {e}")
        return False
    
    # 2. 测试算法对比API（前端实际调用的API）
    print(f"\n🔄 2. 测试算法对比API（前端实际调用）")
    
    # 使用前端相同的请求格式
    request_data = {
        "start": {"lng": 139.7670, "lat": 35.6810, "alt": 120},
        "end": {"lng": 139.7016, "lat": 35.6598, "alt": 120}
    }
    
    try:
        print(f"   发送算法对比请求到: {base_url}/api/algorithm_comparison")
        print(f"   请求数据: {json.dumps(request_data, indent=2)}")
        
        response = requests.post(f'{base_url}/api/algorithm_comparison', 
                               json=request_data, 
                               timeout=120)
        
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 算法对比API成功")
            
            # 检查响应结构
            print(f"   响应字段: {list(result.keys())}")
            
            # 检查改进算法结果
            if 'improved' in result:
                improved = result['improved']
                print(f"   改进算法结果字段: {list(improved.keys())}")
                
                # 重点检查保护区信息
                if 'protectionZonesInfo' in improved:
                    protection_info = improved['protectionZonesInfo']
                    print(f"   ✅ 改进算法包含protectionZonesInfo")
                    print(f"   protectionZonesInfo字段: {list(protection_info.keys())}")
                    
                    if 'collision_cost_breakdown' in protection_info:
                        breakdown = protection_info['collision_cost_breakdown']
                        print(f"   collision_cost_breakdown类型: {type(breakdown)}")
                        print(f"   collision_cost_breakdown内容: {breakdown}")
                        
                        if isinstance(breakdown, dict) and breakdown:
                            print(f"   ✅ 活跃保护区数: {len(breakdown)}")
                            print(f"   活跃保护区ID: {list(breakdown.keys())}")
                            
                            for zone_id, info in breakdown.items():
                                print(f"     {zone_id}: {info.get('zone_name', 'Unknown')} (代价: {info.get('total_cost', 0):.4f})")
                        else:
                            print(f"   ❌ collision_cost_breakdown为空或格式错误")
                    else:
                        print(f"   ❌ protectionZonesInfo中没有collision_cost_breakdown")
                else:
                    print(f"   ❌ 改进算法结果中没有protectionZonesInfo")
            else:
                print(f"   ❌ 响应中没有improved字段")
            
            # 检查基准算法结果
            if 'baseline' in result:
                baseline = result['baseline']
                print(f"   基准算法结果字段: {list(baseline.keys())}")

                # 检查基准算法的保护区信息
                if 'metadata' in baseline and baseline['metadata'] is not None:
                    metadata = baseline['metadata']
                    print(f"   基准算法metadata字段: {list(metadata.keys())}")

                    if 'protection_zones' in metadata:
                        protection_info = metadata['protection_zones']
                        print(f"   ✅ 基准算法包含metadata.protection_zones")
                        print(f"   protection_zones字段: {list(protection_info.keys())}")

                        if 'active_zone_ids' in protection_info:
                            active_ids = protection_info['active_zone_ids']
                            print(f"   基准算法活跃保护区ID: {active_ids}")
                        else:
                            print(f"   ❌ 基准算法protection_zones中没有active_zone_ids")
                    else:
                        print(f"   ❌ 基准算法metadata中没有protection_zones")
                else:
                    print(f"   ❌ 基准算法结果中没有metadata或metadata为None")
            else:
                print(f"   ❌ 响应中没有baseline字段")
            
            return result
            
        else:
            print(f"   ❌ 算法对比API失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 算法对比API调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_protection_zone_flow(api_result):
    """分析保护区信息流"""
    
    print(f"\n🔍 3. 分析保护区信息流")
    
    if not api_result:
        print("   ❌ 没有API结果可分析")
        return
    
    # 分析改进算法的保护区信息
    improved_zones = []
    if 'improved' in api_result and 'protectionZonesInfo' in api_result['improved']:
        protection_info = api_result['improved']['protectionZonesInfo']
        if 'collision_cost_breakdown' in protection_info:
            breakdown = protection_info['collision_cost_breakdown']
            if isinstance(breakdown, dict):
                improved_zones = list(breakdown.keys())
    
    # 分析基准算法的保护区信息
    baseline_zones = []
    if ('baseline' in api_result and
        'metadata' in api_result['baseline'] and
        api_result['baseline']['metadata'] is not None and
        'protection_zones' in api_result['baseline']['metadata']):
        protection_info = api_result['baseline']['metadata']['protection_zones']
        if 'active_zone_ids' in protection_info:
            baseline_zones = protection_info['active_zone_ids']
    
    print(f"   改进算法活跃保护区: {improved_zones}")
    print(f"   基准算法活跃保护区: {baseline_zones}")
    
    # 检查前端需要的格式
    print(f"\n   前端期望的格式:")
    print(f"   - 改进算法: response.protectionZonesInfo.collision_cost_breakdown的键")
    print(f"   - 基准算法: response.metadata.protection_zones.active_zone_ids")
    
    # 模拟前端处理
    print(f"\n   模拟前端处理:")
    
    if improved_zones:
        print(f"   改进算法前端处理:")
        print(f"     activeZoneIds = Object.keys(response.protectionZonesInfo.collision_cost_breakdown)")
        print(f"     结果: {improved_zones}")
        print(f"     调用: cityManager.updateProtectionZoneStatus({improved_zones})")
    else:
        print(f"   ❌ 改进算法没有活跃保护区，前端不会更新状态")
    
    if baseline_zones:
        print(f"   基准算法前端处理:")
        print(f"     activeZoneIds = response.metadata.protection_zones.active_zone_ids")
        print(f"     结果: {baseline_zones}")
        print(f"     调用: cityManager.updateProtectionZoneStatus({baseline_zones})")
    else:
        print(f"   ❌ 基准算法没有活跃保护区，前端不会更新状态")

def generate_fix_recommendations(api_result):
    """生成修复建议"""
    
    print(f"\n💡 4. 修复建议")
    
    issues = []
    
    # 检查改进算法保护区
    if not api_result:
        issues.append("API调用失败，需要检查后端服务")
        return
    
    improved_has_zones = False
    if ('improved' in api_result and 
        'protectionZonesInfo' in api_result['improved'] and
        'collision_cost_breakdown' in api_result['improved']['protectionZonesInfo']):
        breakdown = api_result['improved']['protectionZonesInfo']['collision_cost_breakdown']
        if isinstance(breakdown, dict) and breakdown:
            improved_has_zones = True
    
    baseline_has_zones = False
    if ('baseline' in api_result and
        'metadata' in api_result['baseline'] and
        api_result['baseline']['metadata'] is not None and
        'protection_zones' in api_result['baseline']['metadata'] and
        'active_zone_ids' in api_result['baseline']['metadata']['protection_zones']):
        active_ids = api_result['baseline']['metadata']['protection_zones']['active_zone_ids']
        if active_ids:
            baseline_has_zones = True
    
    if not improved_has_zones:
        issues.append("改进算法没有生成保护区信息")
    
    if not baseline_has_zones:
        issues.append("基准算法没有生成保护区信息")
    
    if issues:
        print("   发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"     {i}. {issue}")
        
        print(f"\n   修复步骤:")
        if not improved_has_zones:
            print(f"     - 检查改进算法的_collect_protection_zones_info方法")
            print(f"     - 确认保护区检测逻辑是否正常工作")
            print(f"     - 验证路径点格式是否正确")
        
        if not baseline_has_zones:
            print(f"     - 检查基准算法的保护区计算方法")
            print(f"     - 确认保护区信息是否正确保存到metadata中")
    else:
        print("   ✅ 保护区信息流正常")

if __name__ == "__main__":
    print("🚀 启动完整端到端测试")
    print("请确保后端服务器正在运行 (http://localhost:5000)")
    
    input("按回车键开始测试...")
    
    # 运行完整测试
    result = test_complete_flow()
    
    # 分析保护区信息流
    analyze_protection_zone_flow(result)
    
    # 生成修复建议
    generate_fix_recommendations(result)
    
    print(f"\n🎉 完整端到端测试完成")
