"""
Flask版本的日志API接口
提供日志查看和分析功能
"""

from flask import Blueprint, jsonify, request
import json
import os
from datetime import datetime, timedelta
from pathlib import Path

# 创建蓝图
logs_bp = Blueprint('logs', __name__)

class SimpleLogViewer:
    """简化的日志查看器"""
    
    def __init__(self, log_dir: str = "logs"):
        # 如果当前在backend目录下，使用相对路径
        if Path.cwd().name == "backend" and Path("logs").exists():
            self.log_dir = Path("logs")
        else:
            self.log_dir = Path(log_dir)
    
    def get_available_dates(self):
        """获取可用的日志日期"""
        dates = []
        if not self.log_dir.exists():
            return dates
            
        for file in self.log_dir.glob("drone_steps_*.json"):
            date_str = file.stem.split("_")[-1]
            if len(date_str) == 8:  # YYYYMMDD
                dates.append(date_str)
        return sorted(dates, reverse=True)
    
    def load_steps_by_date(self, date: str):
        """根据日期加载步骤日志"""
        step_file = self.log_dir / f"drone_steps_{date}.json"
        if not step_file.exists():
            return []
        
        try:
            with open(step_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载日志失败: {e}")
            return []
    
    def get_log_statistics(self, date=None):
        """获取日志统计信息"""
        try:
            if date:
                steps = self.load_steps_by_date(date)
            else:
                # 获取最近一天的日志
                dates = self.get_available_dates()
                if not dates:
                    return {"message": "无可用日志"}
                steps = self.load_steps_by_date(dates[0])
            
            if not steps:
                return {"message": "无日志数据"}
            
            # 统计信息
            stats = {
                "total_steps": len(steps),
                "date_range": date or "最新",
                "step_types": {},
                "levels": {},
                "algorithms": {},
                "sessions": set(),
                "errors": [],
                "performance": {
                    "total_duration": 0,
                    "avg_step_duration": 0,
                    "max_step_duration": 0
                }
            }
            
            durations = []
            
            for step in steps:
                # 步骤类型统计
                step_type = step.get("step_type", "未知")
                stats["step_types"][step_type] = stats["step_types"].get(step_type, 0) + 1
                
                # 级别统计
                level = step.get("level", "未知")
                stats["levels"][level] = stats["levels"].get(level, 0) + 1
                
                # 算法统计
                algorithm = step.get("algorithm")
                if algorithm:
                    stats["algorithms"][algorithm] = stats["algorithms"].get(algorithm, 0) + 1
                
                # 会话统计
                session_id = step.get("session_id")
                if session_id:
                    stats["sessions"].add(session_id)
                
                # 错误收集
                if level == "ERROR":
                    stats["errors"].append({
                        "timestamp": step.get("timestamp"),
                        "message": step.get("message"),
                        "step_number": step.get("step_number")
                    })
                
                # 性能统计
                duration = step.get("duration_ms")
                if duration:
                    durations.append(duration)
                    stats["performance"]["total_duration"] += duration
            
            # 计算性能指标
            if durations:
                stats["performance"]["avg_step_duration"] = sum(durations) / len(durations)
                stats["performance"]["max_step_duration"] = max(durations)
            
            # 转换集合为列表
            stats["sessions"] = list(stats["sessions"])
            stats["session_count"] = len(stats["sessions"])
            
            return stats
            
        except Exception as e:
            return {"error": f"获取统计信息失败: {str(e)}"}

# 创建日志查看器实例
log_viewer = SimpleLogViewer()

@logs_bp.route('/dates', methods=['GET'])
def get_available_dates():
    """获取可用的日志日期"""
    try:
        dates = log_viewer.get_available_dates()
        return jsonify(dates)
    except Exception as e:
        return jsonify({"error": f"获取日志日期失败: {str(e)}"}), 500

@logs_bp.route('/steps/<date>', methods=['GET'])
def get_steps_by_date(date):
    """根据日期获取步骤日志"""
    try:
        # 验证日期格式
        if len(date) != 8 or not date.isdigit():
            return jsonify({"error": "日期格式错误，应为YYYYMMDD"}), 400
        
        steps = log_viewer.load_steps_by_date(date)
        return jsonify(steps)
    except Exception as e:
        return jsonify({"error": f"获取步骤日志失败: {str(e)}"}), 500

@logs_bp.route('/statistics', methods=['GET'])
def get_log_statistics():
    """获取日志统计信息"""
    try:
        date = request.args.get('date')
        stats = log_viewer.get_log_statistics(date)
        return jsonify(stats)
    except Exception as e:
        return jsonify({"error": f"获取统计信息失败: {str(e)}"}), 500

@logs_bp.route('/health', methods=['GET'])
def log_system_health():
    """检查日志系统健康状态"""
    try:
        log_dir = Path("logs")
        
        health_info = {
            "status": "healthy",
            "log_directory_exists": log_dir.exists(),
            "available_dates": len(log_viewer.get_available_dates()),
            "disk_usage": {},
            "recent_activity": {}
        }
        
        if log_dir.exists():
            # 计算日志目录大小
            total_size = sum(f.stat().st_size for f in log_dir.rglob('*') if f.is_file())
            health_info["disk_usage"] = {
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / 1024 / 1024, 2)
            }
        
        return jsonify(health_info)
        
    except Exception as e:
        return jsonify({
            "status": "unhealthy",
            "error": str(e)
        }), 500

@logs_bp.route('/test', methods=['GET'])
def test_logs():
    """测试日志API"""
    return jsonify({
        "message": "日志API正常工作",
        "timestamp": datetime.now().isoformat(),
        "log_directory": str(log_viewer.log_dir),
        "log_directory_exists": log_viewer.log_dir.exists()
    })
