#!/usr/bin/env python3
"""
调试碰撞代价计算问题
检查为什么3.8公里路径经过15个保护区只有20多碰撞代价
"""

import sys
import os
import math
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from algorithms.data_structures import Point3D
from algorithms.unified_detection_manager import UnifiedDetectionManager

def test_collision_cost_calculation():
    """详细测试碰撞代价计算过程"""
    
    print("🔍 调试碰撞代价计算问题")
    print("=" * 60)
    
    # 1. 创建测试路径（5个航点，约3.8公里）
    waypoints = [
        Point3D(lng=139.767300, lat=35.681200, alt=100.0, x=139.767300, y=35.681200, z=100),
        Point3D(lng=139.768000, lat=35.685000, alt=100.0, x=139.768000, y=35.685000, z=100),
        Point3D(lng=139.770000, lat=35.690000, alt=100.0, x=139.770000, y=35.690000, z=100),
        Point3D(lng=139.772000, lat=35.700000, alt=100.0, x=139.772000, y=35.700000, z=100),
        Point3D(lng=139.773400, lat=35.715300, alt=100.0, x=139.773400, y=35.715300, z=100)
    ]
    
    print(f"📍 测试路径：{len(waypoints)}个航点")
    
    # 2. 使用统一检测管理器
    manager = UnifiedDetectionManager()
    result = manager.detect_for_path(waypoints, 100.0)
    
    print(f"\n🛡️ 保护区信息：")
    print(f"   保护区数量: {len(result.protection_zones)}")
    print(f"   碰撞代价: {result.collision_cost:.4f}")
    
    # 3. 详细分析每个保护区
    print(f"\n🔍 保护区详细分析：")
    for i, zone in enumerate(result.protection_zones):
        print(f"   保护区{i}: ID={zone.zone_id}, 类型={zone.zone_type}, 密度={zone.collision_cost_density:.2f}/m²")
        if hasattr(zone, 'area'):
            print(f"            面积={zone.area:.2f}m², 总代价={zone.collision_cost_density * zone.area:.2f}")
    
    # 4. 手动计算理论碰撞代价
    print(f"\n📊 理论计算：")
    
    # 30米圆形面积
    circle_area = math.pi * 30 * 30
    print(f"   30米检测圆面积: {circle_area:.2f}m²")
    
    # 假设每个航点都经过所有保护区（最大值）
    total_theoretical_max = 0
    for zone in result.protection_zones:
        zone_max_cost = zone.collision_cost_density * circle_area
        total_theoretical_max += zone_max_cost
        print(f"   保护区{zone.zone_id}: 最大可能代价 = {zone.collision_cost_density:.2f} × {circle_area:.2f} = {zone_max_cost:.2f}")
    
    max_per_waypoint = total_theoretical_max
    max_total = max_per_waypoint * len(waypoints)
    print(f"   单个航点最大理论代价: {max_per_waypoint:.2f}")
    print(f"   全路径最大理论代价: {max_total:.2f}")
    
    # 5. 实际交集面积计算测试
    print(f"\n🔧 实际交集面积计算测试：")
    
    total_actual_cost = 0
    for i, waypoint in enumerate(waypoints):
        waypoint_cost = 0
        print(f"   航点{i} ({waypoint.lng:.6f}, {waypoint.lat:.6f}):")
        
        for j, zone in enumerate(result.protection_zones):
            # 使用保护区的交集面积计算方法
            intersection_area = zone.get_intersection_area(waypoint, 30.0)
            zone_cost = zone.collision_cost_density * intersection_area
            waypoint_cost += zone_cost
            
            if intersection_area > 0:
                print(f"      保护区{j}: 交集面积={intersection_area:.2f}m², 代价={zone_cost:.4f}")
        
        total_actual_cost += waypoint_cost
        print(f"      航点{i}总代价: {waypoint_cost:.4f}")
    
    print(f"   实际计算总代价: {total_actual_cost:.4f}")
    print(f"   统一检测返回代价: {result.collision_cost:.4f}")
    print(f"   差异: {abs(total_actual_cost - result.collision_cost):.4f}")
    
    # 6. 分析问题
    print(f"\n❗ 问题分析：")
    
    if result.collision_cost < 100:
        print("   🚨 碰撞代价过低！可能原因：")
        print("   1. 交集面积计算错误")
        print("   2. 保护区位置与路径不重叠")
        print("   3. 保护区密度设置过低")
    
    # 7. 检查保护区位置
    print(f"\n📍 保护区位置检查：")
    for i, zone in enumerate(result.protection_zones[:5]):  # 只检查前5个
        if hasattr(zone, 'polygon_points') and zone.polygon_points:
            center_x = sum(getattr(p, 'x', p.lng) for p in zone.polygon_points) / len(zone.polygon_points)
            center_y = sum(getattr(p, 'y', p.lat) for p in zone.polygon_points) / len(zone.polygon_points)
            print(f"   保护区{i}中心: ({center_x:.6f}, {center_y:.6f})")
            
            # 计算到第一个航点的距离
            distance = math.sqrt((center_x - waypoints[0].x)**2 + (center_y - waypoints[0].y)**2)
            print(f"   到航点0距离: {distance:.2f}m")
    
    # 8. 建议修复方案
    print(f"\n💡 建议修复方案：")
    print("   1. 修复交集面积计算算法")
    print("   2. 确保保护区位置与路径重叠")
    print("   3. 调整保护区密度设置")
    print("   4. 验证坐标系统一致性")
    
    return {
        'protection_zones_count': len(result.protection_zones),
        'reported_collision_cost': result.collision_cost,
        'calculated_collision_cost': total_actual_cost,
        'theoretical_max': max_total,
        'circle_area': circle_area
    }

if __name__ == "__main__":
    try:
        result = test_collision_cost_calculation()
        print(f"\n✅ 调试完成")
        print(f"   保护区数量: {result['protection_zones_count']}")
        print(f"   报告代价: {result['reported_collision_cost']:.4f}")
        print(f"   计算代价: {result['calculated_collision_cost']:.4f}")
        print(f"   理论最大: {result['theoretical_max']:.4f}")
    except Exception as e:
        print(f"\n❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
