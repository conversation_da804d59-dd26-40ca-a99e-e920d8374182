#!/usr/bin/env python3
"""
调试基准算法和改进算法的路径对比
检查路径点数量、路径质量、参数一致性
"""

import sys
import os
import math
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

def calculate_path_length(path_points):
    """计算路径总长度"""
    if len(path_points) < 2:
        return 0
    
    total_length = 0
    for i in range(1, len(path_points)):
        p1 = path_points[i-1]
        p2 = path_points[i]
        
        # 简化的距离计算
        lat_diff = p2[1] - p1[1]
        lng_diff = p2[0] - p1[0]
        
        # 转换为米
        lat_meters = lat_diff * 110540
        lng_meters = lng_diff * 111320 * math.cos(math.radians((p1[1] + p2[1]) / 2))
        
        distance = math.sqrt(lat_meters**2 + lng_meters**2)
        total_length += distance
    
    return total_length

def calculate_path_smoothness(path_points):
    """计算路径平滑度（转向角度变化）"""
    if len(path_points) < 3:
        return 0
    
    total_angle_change = 0
    angle_changes = []
    
    for i in range(1, len(path_points) - 1):
        p1 = path_points[i-1]
        p2 = path_points[i]
        p3 = path_points[i+1]
        
        # 计算两个向量的角度
        v1 = (p2[0] - p1[0], p2[1] - p1[1])
        v2 = (p3[0] - p2[0], p3[1] - p2[1])
        
        # 计算角度变化
        dot_product = v1[0] * v2[0] + v1[1] * v2[1]
        mag1 = math.sqrt(v1[0]**2 + v1[1]**2)
        mag2 = math.sqrt(v2[0]**2 + v2[1]**2)
        
        if mag1 > 0 and mag2 > 0:
            cos_angle = dot_product / (mag1 * mag2)
            cos_angle = max(-1, min(1, cos_angle))  # 限制范围
            angle = math.acos(cos_angle)
            angle_changes.append(angle)
            total_angle_change += angle
    
    return {
        'total_angle_change': total_angle_change,
        'average_angle_change': total_angle_change / len(angle_changes) if angle_changes else 0,
        'max_angle_change': max(angle_changes) if angle_changes else 0,
        'angle_changes_count': len(angle_changes)
    }

def analyze_path_quality(algorithm_name, path_points):
    """分析路径质量"""
    
    print(f"\n📊 {algorithm_name} 路径质量分析")
    print("=" * 50)
    
    # 基本信息
    print(f"路径点数量: {len(path_points)}")
    
    if len(path_points) < 2:
        print("❌ 路径点数量不足，无法分析")
        return
    
    # 路径长度
    path_length = calculate_path_length(path_points)
    print(f"路径总长度: {path_length:.2f}米")
    
    # 平均段长度
    if len(path_points) > 1:
        avg_segment_length = path_length / (len(path_points) - 1)
        print(f"平均段长度: {avg_segment_length:.2f}米")
    
    # 路径平滑度
    smoothness = calculate_path_smoothness(path_points)
    print(f"总转向角度: {smoothness['total_angle_change']:.2f}弧度 ({math.degrees(smoothness['total_angle_change']):.1f}度)")
    print(f"平均转向角度: {smoothness['average_angle_change']:.3f}弧度 ({math.degrees(smoothness['average_angle_change']):.1f}度)")
    print(f"最大转向角度: {smoothness['max_angle_change']:.3f}弧度 ({math.degrees(smoothness['max_angle_change']):.1f}度)")
    print(f"转向次数: {smoothness['angle_changes_count']}")
    
    # 路径质量评估
    print(f"\n📈 质量评估:")
    
    # 点密度评估
    if avg_segment_length < 10:
        print(f"✅ 点密度: 高 (平均{avg_segment_length:.1f}m/段)")
    elif avg_segment_length < 50:
        print(f"⚠️ 点密度: 中等 (平均{avg_segment_length:.1f}m/段)")
    else:
        print(f"❌ 点密度: 低 (平均{avg_segment_length:.1f}m/段)")
    
    # 平滑度评估
    avg_turn_degrees = math.degrees(smoothness['average_angle_change'])
    if avg_turn_degrees < 5:
        print(f"✅ 平滑度: 优秀 (平均转向{avg_turn_degrees:.1f}度)")
    elif avg_turn_degrees < 15:
        print(f"⚠️ 平滑度: 良好 (平均转向{avg_turn_degrees:.1f}度)")
    else:
        print(f"❌ 平滑度: 较差 (平均转向{avg_turn_degrees:.1f}度)")
    
    # 显示前几个和后几个路径点
    print(f"\n📍 路径点样本:")
    print("前5个点:")
    for i, point in enumerate(path_points[:5]):
        print(f"  {i+1}: ({point[0]:.6f}, {point[1]:.6f})")
    
    if len(path_points) > 10:
        print("后5个点:")
        for i, point in enumerate(path_points[-5:]):
            print(f"  {len(path_points)-4+i}: ({point[0]:.6f}, {point[1]:.6f})")

def main():
    """主函数"""
    
    print("🔍 基准算法和改进算法路径对比分析")
    print("=" * 60)
    
    # 模拟路径数据进行分析
    print("📝 注意: 这是模拟分析，实际需要从算法执行结果获取数据")
    
    # 模拟A*算法路径（网格化，可能有弯折）
    print("\n🔧 模拟A*算法路径特征:")
    astar_path = [
        (139.7673, 35.6812),  # 起点
        (139.7675, 35.6814),  # 网格点1
        (139.7677, 35.6816),  # 网格点2
        (139.7679, 35.6818),  # 网格点3
        (139.7681, 35.6820),  # 网格点4
        (139.7683, 35.6822),  # 网格点5
        (139.7685, 35.6824),  # 网格点6
        (139.7687, 35.6826),  # 网格点7
        (139.7689, 35.6828),  # 网格点8
        (139.7691, 35.6830),  # 终点
    ]
    
    analyze_path_quality("A*基准算法", astar_path)
    
    # 模拟改进算法路径（优化后，更平滑）
    print("\n🚀 模拟改进算法路径特征:")
    improved_path = [
        (139.7673, 35.6812),  # 起点
        (139.7680, 35.6819),  # 优化点1
        (139.7687, 35.6826),  # 优化点2
        (139.7691, 35.6830),  # 终点
    ]
    
    analyze_path_quality("改进分簇算法", improved_path)
    
    # 对比分析
    print("\n📊 对比分析")
    print("=" * 50)
    
    astar_length = calculate_path_length(astar_path)
    improved_length = calculate_path_length(improved_path)
    
    print(f"路径点数量对比:")
    print(f"  A*算法: {len(astar_path)} 个点")
    print(f"  改进算法: {len(improved_path)} 个点")
    print(f"  差异: {len(astar_path) - len(improved_path)} 个点")
    
    print(f"\n路径长度对比:")
    print(f"  A*算法: {astar_length:.2f}米")
    print(f"  改进算法: {improved_length:.2f}米")
    print(f"  差异: {astar_length - improved_length:.2f}米")
    
    # 问题诊断
    print(f"\n🔍 问题诊断:")
    if len(astar_path) > len(improved_path) * 2:
        print("❌ A*算法路径点过多，可能是网格分辨率过高或路径优化不足")
    
    if astar_length > improved_length * 1.2:
        print("❌ A*算法路径过长，可能存在不必要的弯折")
    
    print(f"\n💡 建议:")
    print("1. 检查A*算法的网格大小参数（gridSize）")
    print("2. 检查A*算法的路径优化和平滑处理")
    print("3. 确保两个算法使用相同的起终点和基础参数")
    print("4. 检查A*算法的路径点生成逻辑")

if __name__ == "__main__":
    main()
