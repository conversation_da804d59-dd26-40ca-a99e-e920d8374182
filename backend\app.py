"""
无人机路径规划系统 - Flask主应用
"""
import os
import math
import asyncio
from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS
from config import config

def create_app(config_name=None):
    """
    应用工厂函数
    """
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 配置CORS跨域支持
    CORS(app, resources={
        r"/api/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # 注册蓝图
    register_blueprints(app)
    
    # 静态文件路由
    @app.route('/')
    def index():
        """主页路由"""
        return send_from_directory(app.config['STATIC_FOLDER'], 'modern-city.html')

    @app.route('/cesium')
    def cesium_version():
        """Cesium.js版本的无人机路径规划系统"""
        return send_from_directory(app.config['STATIC_FOLDER'], 'cesium.html')

    @app.route('/classic')
    def classic_version():
        """经典版本的无人机路径规划系统"""
        return send_from_directory(app.config['STATIC_FOLDER'], 'index.html')
    
    @app.route('/data/<path:filename>')
    def data_files(filename):
        """数据文件服务"""
        return send_from_directory(app.config['TOKYO23_DATA_PATH'], filename)

    @app.route('/<path:filename>')
    def static_files(filename):
        """静态文件服务"""
        return send_from_directory(app.config['STATIC_FOLDER'], filename)
    
    # 健康检查接口
    @app.route('/api/health')
    def health_check():
        """系统健康检查"""
        return jsonify({
            'success': True,
            'status': 'healthy',
            'message': '无人机路径规划系统运行正常',
            'version': '1.0.0'
        })

    @app.route('/api/algorithms')
    def get_algorithms():
        """获取可用算法列表"""
        try:
            from algorithms.manager import algorithm_manager
            algorithms = algorithm_manager.list_algorithms()

            # 转换为前端期望的格式
            formatted_algorithms = []
            for alg in algorithms:
                formatted_algorithms.append({
                    'id': alg['name'],
                    'name': alg['info']['name'],
                    'description': alg['info']['description'],
                    'version': alg['info']['version'],
                    'category': alg['info']['category']
                })

            return jsonify({
                'success': True,
                'algorithms': formatted_algorithms,
                'total_count': len(formatted_algorithms)
            })
        except Exception as e:
            # 如果算法管理器不可用，返回默认列表
            return jsonify({
                'success': True,
                'algorithms': [
                    {
                        'id': 'ImprovedClusterBased',
                        'name': '改进分簇算法',
                        'description': '基于分簇的改进路径规划算法'
                    },
                    {
                        'id': 'AStar',
                        'name': 'A*算法',
                        'description': '经典A*最短路径算法'
                    },
                    {
                        'id': 'RRT',
                        'name': 'RRT算法',
                        'description': '快速随机树算法'
                    },
                    {
                        'id': 'StraightLine',
                        'name': '直线算法',
                        'description': '简单直线路径算法'
                    }
                ]
            })

    @app.route('/api/status')
    def status_check():
        """系统状态检查"""
        return jsonify({
            'status': 'running',
            'message': '无人机路径规划系统运行正常',
            'version': '2.0.0',
            'engine': 'Cesium.js'
        })
    
    # 系统信息接口
    @app.route('/api/info')
    def system_info():
        """获取系统配置信息"""
        return jsonify({
            'algorithm_config': app.config['ALGORITHM_CONFIG'],
            'math_model_params': app.config['MATH_MODEL_PARAMS'],
            'data_path': app.config['TOKYO23_DATA_PATH']
        })

    @app.route('/api/test-building-detector', methods=['GET'])
    def test_building_detector():
        """测试建筑物检测器的API"""
        try:
            from algorithms.improved_cluster_pathfinding import PathBasedBuildingDetector, ImprovedPathPoint

            # 创建检测器
            detector = PathBasedBuildingDetector(
                detection_radius=15.0,  # 15米检测半径
                max_buildings=10       # 最大10个建筑物
            )

            # 创建一些测试建筑物
            test_buildings = []
            for i in range(20):
                building = {
                    'x': i * 10,  # 每10米一个建筑物
                    'y': 0,
                    'lng': 139.7671 + i * 0.0001,
                    'lat': 35.6812,
                    'height': 30,
                    'id': f'test_building_{i}'
                }
                test_buildings.append(building)

            # 设置建筑物数据
            detector.set_all_buildings(test_buildings)

            # 创建一个简单的路径（45米长）
            waypoints = [
                ImprovedPathPoint(x=0, y=0, z=100, lng=139.7671, lat=35.6812, alt=100),
                ImprovedPathPoint(x=45, y=0, z=100, lng=139.7671 + 0.0005, lat=35.6812, alt=100)
            ]

            # 执行检测
            relevant_buildings = detector.detect_buildings_for_path(waypoints)

            return jsonify({
                'success': True,
                'detector_params': {
                    'detection_radius': detector.detection_radius,
                    'max_buildings': detector.max_buildings
                },
                'test_data': {
                    'total_buildings': len(test_buildings),
                    'path_length': 45,
                    'detected_buildings': len(relevant_buildings)
                },
                'result': {
                    'is_reasonable': len(relevant_buildings) <= 10,
                    'detection_ratio': len(relevant_buildings) / len(test_buildings) * 100
                },
                'building_details': [
                    {
                        'id': b.get('id', 'unknown'),
                        'x': b.get('x', 0),
                        'y': b.get('y', 0),
                        'distance_to_path': ((b.get('x', 0) - 22.5)**2 + (b.get('y', 0) - 0)**2)**0.5
                    }
                    for b in relevant_buildings[:5]  # 只显示前5个
                ]
            })
        except Exception as e:
            import traceback
            return jsonify({
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            })

    @app.route('/api/calculate_path', methods=['POST'])
    def calculate_path():
        """计算无人机路径 - 使用标准化参数"""
        try:
            print("🚀 API /api/calculate_path 被调用")
            data = request.get_json()
            print(f"📡 接收到请求数据: {data}")

            # 提取标准化参数
            start_point = data.get('startPoint') or data.get('start')
            end_point = data.get('endPoint') or data.get('end')
            flight_height = data.get('flightHeight', 70)  # 修改默认飞行高度为70米
            safety_distance = data.get('safetyDistance', 30)
            max_turn_angle = data.get('maxTurnAngle', 90)
            buildings = data.get('buildings', [])
            protection_zones = data.get('protectionZones', [])
            parameters = data.get('parameters', {})
            algorithm = data.get('algorithm', 'astar')

            if not start_point or not end_point:
                return jsonify({
                    'success': False,
                    'error': '起飞点和降落点不能为空'
                }), 400

            # 标准化算法名称
            algorithm_map = {
                'ImprovedClusterBased': 'improved_cluster',
                'improved_cluster': 'improved_cluster',
                'AStar': 'astar',
                'astar': 'astar',
                'A*': 'astar',
                'RRT': 'rrt',
                'rrt': 'rrt',
                'Dijkstra': 'dijkstra',
                'dijkstra': 'dijkstra',
                'StraightLine': 'straight_line',
                'straight_line': 'straight_line'
            }

            normalized_algorithm = algorithm_map.get(algorithm, 'straight_line')

            # 根据算法类型选择处理方式
            print(f"🔍 API调用: 原始算法={algorithm}, 标准化算法={normalized_algorithm}")

            if normalized_algorithm in ['improved_cluster', 'astar', 'rrt']:
                # 使用新的算法系统
                print(f"📡 使用新算法系统: {normalized_algorithm}")
                from function_call_adapter import wrap_algorithm_for_compatibility
                from algorithms.data_structures import PathPlanningRequest, Point3D

                # 根据算法类型创建实例
                if normalized_algorithm == 'improved_cluster':
                    from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathPlanning
                    algorithm_instance = ImprovedClusterBasedPathPlanning()
                elif normalized_algorithm == 'astar':
                    from algorithms.astar import AStarAlgorithm
                    algorithm_instance = AStarAlgorithm()
                elif normalized_algorithm == 'rrt':
                    from algorithms.rrt import RRTAlgorithm
                    algorithm_instance = RRTAlgorithm()

                print(f"✅ 算法实例创建成功: {type(algorithm_instance)}")
                algorithm_instance = wrap_algorithm_for_compatibility(algorithm_instance)
                print(f"✅ 算法包装完成: {type(algorithm_instance)}")

                # 提取坐标并进行简单的坐标转换
                start_lng = start_point.get('longitude') or start_point.get('lng')
                start_lat = start_point.get('latitude') or start_point.get('lat')
                start_alt = start_point.get('height') or start_point.get('alt', flight_height)

                end_lng = end_point.get('longitude') or end_point.get('lng')
                end_lat = end_point.get('latitude') or end_point.get('lat')
                end_alt = end_point.get('height') or end_point.get('alt', flight_height)

                # 简单的坐标转换：将经纬度转换为相对坐标（以起点为原点）
                # 这是一个简化的转换，适用于小范围区域
                start_x = 0.0
                start_y = 0.0
                start_z = start_alt

                # 计算终点相对于起点的坐标（米）
                # 1度经度 ≈ 111320 * cos(lat) 米
                # 1度纬度 ≈ 110540 米
                import math
                lat_rad = math.radians(start_lat)
                meters_per_lng = 111320 * math.cos(lat_rad)
                meters_per_lat = 110540

                end_x = (end_lng - start_lng) * meters_per_lng
                end_y = (end_lat - start_lat) * meters_per_lat
                end_z = end_alt

                # 创建PathPlanningRequest对象
                request_data = {
                    'startPoint': {
                        'lng': start_lng,
                        'lat': start_lat,
                        'alt': start_alt,
                        'x': start_x,
                        'y': start_y,
                        'z': start_z
                    },
                    'endPoint': {
                        'lng': end_lng,
                        'lat': end_lat,
                        'alt': end_alt,
                        'x': end_x,
                        'y': end_y,
                        'z': end_z
                    },
                    'flightHeight': flight_height,
                    'safetyDistance': safety_distance,
                    'maxTurnAngle': max_turn_angle,
                    'buildings': buildings,
                    'protectionZones': protection_zones,
                    'kValue': parameters.get('kValue', 5.0),
                    'enablePathSwitching': parameters.get('enablePathSwitching', True),
                    'parameters': {
                        'flightHeight': flight_height,
                        'safetyDistance': safety_distance,
                        'maxTurnAngle': max_turn_angle,
                        'kValue': parameters.get('kValue', 5.0),
                        'enablePathSwitching': parameters.get('enablePathSwitching', True),
                        # 传递所有前端参数，特别是A*算法需要的参数
                        'gridSize': parameters.get('gridSize', 10),
                        'heuristicWeight': parameters.get('heuristicWeight', 1.0),
                        'maxIterations': parameters.get('maxIterations', 10000),
                        'allowDiagonal': parameters.get('allowDiagonal', True),
                        'smoothPath': parameters.get('smoothPath', True),
                        'riskEdgeDistance': parameters.get('riskEdgeDistance', 50)
                    }
                }

                planning_request = PathPlanningRequest(request_data)

                # 调用标准化的execute函数（包含metadata处理）
                print(f"🔧 APP: 准备调用algorithm_instance.execute")
                print(f"🔧 APP: planning_request类型: {type(planning_request)}")

                try:
                    result = asyncio.run(algorithm_instance.execute(planning_request))
                    print(f"🔧 APP: execute调用成功，结果类型: {type(result)}")
                    print(f"🔧 APP: result.success: {result.success}")
                    print(f"🔧 APP: result.path长度: {len(getattr(result, 'path', []))}")

                    result_dict = result.to_dict()
                    print(f"🔧 APP: to_dict()成功，键数: {len(result_dict)}")

                    return jsonify(result_dict)
                except Exception as e:
                    print(f"❌ APP: execute调用失败: {e}")
                    import traceback
                    print(f"❌ APP: 错误堆栈: {traceback.format_exc()}")
                    raise
            else:
                # 使用简化的路径计算（向后兼容）
                path = calculate_simple_path(start_point, end_point)

                # 计算路径统计信息
                total_distance = calculate_path_distance(path)
                estimated_time = total_distance / 15.0  # 假设飞行速度15m/s
                max_altitude = max(point.get('height', flight_height) for point in path)

                return jsonify({
                    'success': True,
                    'path': path,
                    'total_distance': round(total_distance, 2),
                    'estimated_time': round(estimated_time, 1),
                    'max_altitude': round(max_altitude, 1),
                    'algorithm_used': normalized_algorithm,
                    'original_algorithm': algorithm
                })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'路径计算失败: {str(e)}'
            }), 500
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': '资源未找到'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({'error': '服务器内部错误'}), 500
    
    return app

def calculate_simple_path(start, end):
    """
    计算简单的直线路径（带中间航点）
    """
    path = []

    # 标准化坐标字段（支持lng/lat和longitude/latitude两种格式）
    def normalize_point(point):
        return {
            'longitude': point.get('longitude') or point.get('lng'),
            'latitude': point.get('latitude') or point.get('lat'),
            'height': point.get('height') or point.get('alt', 100)
        }

    start_norm = normalize_point(start)
    end_norm = normalize_point(end)

    # 起飞点
    path.append({
        'longitude': start_norm['longitude'],
        'latitude': start_norm['latitude'],
        'height': start_norm['height']
    })

    # 计算中间点
    mid_lon = (start_norm['longitude'] + end_norm['longitude']) / 2
    mid_lat = (start_norm['latitude'] + end_norm['latitude']) / 2
    mid_height = max(start_norm['height'], end_norm['height']) + 50  # 中间点高度更高

    path.append({
        'longitude': mid_lon,
        'latitude': mid_lat,
        'height': mid_height
    })

    # 降落点
    path.append({
        'longitude': end_norm['longitude'],
        'latitude': end_norm['latitude'],
        'height': end_norm['height']
    })

    return path

def calculate_path_distance(path):
    """
    计算路径总距离（米）
    """
    total_distance = 0

    for i in range(len(path) - 1):
        p1 = path[i]
        p2 = path[i + 1]

        # 使用Haversine公式计算地理距离
        distance = haversine_distance(
            p1['latitude'], p1['longitude'],
            p2['latitude'], p2['longitude']
        )

        # 加上高度差
        height_diff = abs(p2['height'] - p1['height'])
        distance_3d = math.sqrt(distance**2 + height_diff**2)

        total_distance += distance_3d

    return total_distance

def haversine_distance(lat1, lon1, lat2, lon2):
    """
    使用Haversine公式计算两点间距离（米）
    """
    R = 6371000  # 地球半径（米）

    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lon = math.radians(lon2 - lon1)

    a = (math.sin(delta_lat / 2)**2 +
         math.cos(lat1_rad) * math.cos(lat2_rad) *
         math.sin(delta_lon / 2)**2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

    return R * c

def register_blueprints(app):
    """
    注册所有API蓝图
    """
    # 初始化算法系统
    try:
        import algorithms
        print("✅ 算法系统初始化完成")
    except ImportError as e:
        print(f"❌ 算法系统初始化失败: {e}")

    # 导入并注册路径规划API
    try:
        from api.pathfinding import pathfinding_bp
        app.register_blueprint(pathfinding_bp, url_prefix='/api/pathfinding')
        print("✅ 路径规划API注册完成")
    except ImportError as e:
        print(f"❌ 路径规划API注册失败: {e}")

    # 导入并注册数学模型API
    try:
        from api.models import models_bp
        app.register_blueprint(models_bp, url_prefix='/api/models')
        print("✅ 数学模型API注册完成")
    except ImportError:
        print("⚠️ 数学模型API模块尚未实现")

    # 导入并注册保护区API
    try:
        from api.protection_zones import protection_zones_bp
        app.register_blueprint(protection_zones_bp, url_prefix='/api/protection-zones')
        print("✅ 保护区API注册完成")
    except ImportError as e:
        print(f"❌ 保护区API注册失败: {e}")

    # 导入并注册FBX模型加载API
    try:
        from api.models_loader import models_loader_bp
        app.register_blueprint(models_loader_bp, url_prefix='/api/models_loader')
        print("✅ FBX模型加载API注册完成")
    except ImportError:
        print("⚠️ FBX模型加载API模块尚未实现")

    # 导入并注册日志API
    try:
        from api.logs_flask import logs_bp
        app.register_blueprint(logs_bp, url_prefix='/api/logs')
        print("✅ 日志API注册完成")
    except ImportError as e:
        print(f"❌ 日志API注册失败: {e}")
    except Exception as e:
        print(f"⚠️ 日志API注册部分失败: {e}")

    # 导入并注册算法对比API
    try:
        from algorithm_comparison_api import algorithm_comparison_bp
        app.register_blueprint(algorithm_comparison_bp, url_prefix='/api')
        print("✅ 算法对比API注册完成")
    except ImportError as e:
        print(f"❌ 算法对比API注册失败: {e}")
    except Exception as e:
        print(f"⚠️ 算法对比API注册部分失败: {e}")

    # 导入并注册路径数据导出API
    try:
        from path_export_api import path_export_bp
        app.register_blueprint(path_export_bp, url_prefix='/api')
        print("✅ 路径数据导出API注册完成")
    except ImportError as e:
        print(f"❌ 路径数据导出API注册失败: {e}")
    except Exception as e:
        print(f"⚠️ 路径数据导出API注册部分失败: {e}")

    # 导出81条路径数据为简洁CSV格式
    @app.route('/api/export_calculated_paths', methods=['POST'])
    def export_calculated_paths():
        """导出已计算的81条路径数据为简洁CSV格式"""
        try:
            import glob
            import json
            import csv
            import os
            from datetime import datetime

            # 查找最新的JSON文件
            json_files = glob.glob('all_81_paths_data_*.json')
            if not json_files:
                return jsonify({
                    'success': False,
                    'error': '没有找到路径数据文件，请先运行算法对比'
                }), 400

            # 使用最新的文件
            latest_json = max(json_files, key=os.path.getctime)
            print(f"📂 使用JSON文件: {latest_json}")

            # 读取JSON数据
            with open(latest_json, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if not data.get('all_paths'):
                return jsonify({
                    'success': False,
                    'error': 'JSON文件中没有路径数据'
                }), 400

            # 创建CSV文件
            timestamp = datetime.now().strftime('%Y-%m-%dT%H-%M-%S')
            filename = f'路径数据_简洁格式_{timestamp}.csv'
            filepath = os.path.join('csv', filename)

            # 确保目录存在
            os.makedirs('csv', exist_ok=True)

            # 获取选中路径ID
            selected_path_id = data.get('selected_path', {}).get('selected_path_id')

            # 获取起点和终点坐标
            metadata = data.get('metadata', {})
            start_point = metadata.get('start_point', {})
            end_point = metadata.get('end_point', {})

            # 准备CSV数据
            csv_data = []

            # 按最终代价排序
            all_paths = sorted(data['all_paths'], key=lambda x: x.get('final_cost', float('inf')))

            for path_data in all_paths:
                # 判断是否选中
                path_id = path_data.get('path_id')
                is_selected = 1 if path_id == selected_path_id else 0

                # 处理基准路径的显示
                if path_id == 'BASELINE_A*':
                    display_id = 'BASELINE_A*'
                    # 基准路径使用metadata中的起点终点
                    start_lng = start_point.get('lng', 0)
                    start_lat = start_point.get('lat', 0)
                    start_alt = start_point.get('alt', 0)
                    end_lng = end_point.get('lng', 0)
                    end_lat = end_point.get('lat', 0)
                    end_alt = end_point.get('alt', 0)
                    flight_height = start_alt  # 基准路径的飞行高度
                    cluster_id = '基准算法'
                    # 基准路径的保护区信息
                    protection_zones_count = path_data.get('protection_zones_count', 0)
                    protection_zones_list = path_data.get('protection_zones_list', '')
                    protection_zones_types = path_data.get('protection_zones_types', '')
                else:
                    display_id = str(path_id)
                    # 改进算法路径也使用metadata中的起点终点（所有路径共享相同起终点）
                    start_lng = start_point.get('lng', 0)
                    start_lat = start_point.get('lat', 0)
                    start_alt = start_point.get('alt', 0)
                    end_lng = end_point.get('lng', 0)
                    end_lat = end_point.get('lat', 0)
                    end_alt = end_point.get('alt', 0)

                    # 改进算法路径的飞行高度从height_layer获取
                    height_layer = path_data.get('height_layer', 1)
                    if isinstance(height_layer, (int, float)):
                        flight_height = float(height_layer) * 10  # 假设每层10米
                    else:
                        flight_height = start_alt

                    # 簇ID
                    cluster_id = path_data.get('cluster_id', '')

                    # 保护区信息
                    protection_zones_count = path_data.get('protection_zones_count', 0)
                    protection_zones_list = path_data.get('protection_zones_list', '')
                    protection_zones_types = path_data.get('protection_zones_types', '')

                csv_data.append({
                    'path_id': display_id,
                    'direction': path_data.get('flight_direction', ''),
                    'height': path_data.get('height_layer', ''),
                    'start_lng': round(start_lng, 6),
                    'start_lat': round(start_lat, 6),
                    'start_alt': round(start_alt, 2),
                    'end_lng': round(end_lng, 6),
                    'end_lat': round(end_lat, 6),
                    'end_alt': round(end_alt, 2),
                    'flight_height': round(flight_height, 2),
                    'cluster_id': cluster_id,
                    'protection_zones_count': protection_zones_count,
                    'protection_zones_list': protection_zones_list,
                    'protection_zones_types': protection_zones_types,
                    'path_length': round(path_data.get('path_length', 0), 2),
                    'turning_cost': round(path_data.get('turning_cost', 0), 4),
                    'risk_value': round(path_data.get('risk_value', 0), 4),
                    'collision_cost': round(path_data.get('collision_cost', 0), 4),
                    'final_cost': round(path_data.get('final_cost', 0), 6),
                    'waypoint_count': path_data.get('waypoints_count', 0),
                    'selected': is_selected
                })

            # 写入CSV文件
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['path_id', 'direction', 'height', 'start_lng', 'start_lat', 'start_alt',
                             'end_lng', 'end_lat', 'end_alt', 'flight_height', 'cluster_id',
                             'protection_zones_count', 'protection_zones_list', 'protection_zones_types',
                             'path_length', 'turning_cost', 'risk_value', 'collision_cost',
                             'final_cost', 'waypoint_count', 'selected']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # 写入表头（中文）
                writer.writerow({
                    'path_id': '路径ID',
                    'direction': '方向',
                    'height': '高度层',
                    'start_lng': '起点经度',
                    'start_lat': '起点纬度',
                    'start_alt': '起点高度',
                    'end_lng': '终点经度',
                    'end_lat': '终点纬度',
                    'end_alt': '终点高度',
                    'flight_height': '飞行高度',
                    'cluster_id': '簇ID',
                    'protection_zones_count': '保护区数量',
                    'protection_zones_list': '保护区列表',
                    'protection_zones_types': '保护区类型',
                    'path_length': '路径长度',
                    'turning_cost': '转向成本',
                    'risk_value': '风险值',
                    'collision_cost': '碰撞代价',
                    'final_cost': '最终代价',
                    'waypoint_count': '航点数',
                    'selected': '选中'
                })

                # 写入数据
                writer.writerows(csv_data)

            print(f"✅ 简洁格式CSV导出成功: {filepath}")
            print(f"📊 导出了 {len(csv_data)} 条路径数据")

            return jsonify({
                'success': True,
                'message': '路径数据导出成功',
                'filepath': filepath,
                'filename': filename,
                'total_paths': len(csv_data),
                'source_json': os.path.basename(latest_json),  # 只返回文件名，不包含路径
                'export_time': datetime.now().isoformat()
            })

        except Exception as e:
            print(f"❌ 导出失败: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'error': f'导出失败: {str(e)}'
            }), 500



if __name__ == '__main__':
    # 开发环境启动
    app = create_app('development')
    print("=" * 50)
    print("无人机路径规划系统启动中...")
    print(f"数据路径: {app.config['TOKYO23_DATA_PATH']}")
    print("访问地址: http://localhost:5000")
    print("API文档: http://localhost:5000/api/health")
    print("=" * 50)

    # 🔧 优化：减少debug模式重启导致的重复初始化
    import os
    debug_mode = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    use_reloader = os.environ.get('FLASK_USE_RELOADER', 'False').lower() == 'true'

    app.run(
        host='0.0.0.0',
        port=5000,
        debug=debug_mode,
        use_reloader=use_reloader,  # 禁用自动重载减少重复初始化
        threaded=True
    )
