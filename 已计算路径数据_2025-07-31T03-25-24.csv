﻿路径ID,飞行方向,高度层级,航点数量,是否选中,3X3簇ID,4X4簇ID,所有簇ID,簇类型,经过保护区数量,经过保护区列表,保护区类型,保护区碰撞代价,保护区总代价,保护区详细信息,路径长度(米),转向成本,风险值,碰撞代价,实际碰撞代价,最终代价,权重α(风险),权重β(碰撞),权重γ(长度),权重δ(转向),风险参考值,碰撞参考值,转向参考值,长度参考值,风险归一化值,碰撞归一化值,长度归一化值,转向归一化值,风险加权项,碰撞加权项,长度加权项,转向加权项,风险贡献百分比,碰撞贡献百分比,长度贡献百分比,转向贡献百分比,基准路径长度,基准转向成本,基准风险值,基准碰撞代价,基准最终代价,长度改进百分比,转向改进百分比,风险改进百分比,碰撞改进百分比,最终代价改进百分比,起点经度,起点纬度,起点高度,终点经度,终点纬度,终点高度,算法名称,导出时间
BASELINE_A*,OPTIMAL,SINGLE,20,否,,,,,,,,,,,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,11.917669766382136,0.25661398073452013,0.5,0.4,0.05,0.05,100,50,4.241150082346221,3543.4994746482316,0.2362544992800597,0.23835339532764274,0.6666666666666666,0.4998872892014745,0.11812724964002985,0.0953413581310571,0.03333333333333333,0.024994364460073727,43.46168333476471,35.07823917364974,12.264086248009626,9.195991243575923,,,,,,,,,,,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
1,9,9,38,是,3x3_cluster_9,,3x3_cluster_9,3x3,,,,,,,2049.9248554889487,0.4989990775251312,20.499747553967012,13.949624277444745,13.949624277444745,0.2504545194098281,0.5,0.4,0.05,0.05,100,50,8.482300164692441,3074.8872832334227,0.20499747553967013,0.2789924855488949,0.6666666666666667,0.058828273915867056,0.10249873776983506,0.11159699421955796,0.03333333333333334,0.002941413695793353,40.93882720184966,44.57274462110336,13.313603690021178,1.1748244870257962,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.224559359115704,-76.46338477225937,-13.230234275173327,17.04993132797177,-2.4002828322375374,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
2,9,8,41,否,3x3_cluster_9,,3x3_cluster_9,3x3,,,,,,,2051.300663177314,0.4585314397448346,20.513465163212885,14.25650331588657,14.25650331588657,0.252513301297488,0.5,0.4,0.05,0.05,100,50,9.189158511750144,3076.9509947659712,0.20513465163212885,0.2851300663177314,0.6666666666666666,0.04989917620405744,0.10256732581606443,0.11405202652709256,0.03333333333333333,0.002494958810202872,40.62914749100416,45.17848711125699,13.204057974520087,0.9883074232187514,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.166319995816428,-78.37214826002844,-13.172171426475526,19.624923289130848,-1.5979953334165704,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
3,8,9,42,否,3x3_cluster_9,,3x3_cluster_9,3x3,,,,,,,2046.167938938841,0.34738132477482636,20.462026770713187,14.330839694694205,14.330839694694205,0.2521803541453324,0.5,0.4,0.05,0.05,100,50,9.42477796076938,3069.2519084082614,0.20462026770713188,0.2866167938938841,0.6666666666666666,0.0368583033171498,0.10231013385356594,0.11464671755755365,0.03333333333333333,0.0018429151658574902,40.577827302309785,45.470712730036695,13.220530483776535,0.7309294838769911,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.38359352478948,-83.61483828972386,-13.389895925507064,20.248672564491088,-1.7277416360936815,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
4,9,7,44,否,3x3_cluster_9,,3x3_cluster_9,3x3,,,,,,,2052.062502035384,0.41901905320236055,20.52104403940704,14.56031251017692,14.56031251017692,0.25458980006924214,0.5,0.4,0.05,0.05,100,50,9.896016858807847,3078.0937530530764,0.2052104403940704,0.2912062502035384,0.6666666666666666,0.04234219274085179,0.1026052201970352,0.11648250008141536,0.03333333333333333,0.0021171096370425895,40.31034831375458,45.762293007334485,13.095613210954138,0.8317454679567977,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.134070568512128,-80.23585478909138,-13.140092138177309,22.174156488622145,-0.7888037352774248,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
5,8,8,45,否,3x3_cluster_9,4x4_cluster_4,3x3_cluster_9; 4x4_cluster_4,3x3; 4x4,,,,,,,2047.903470944556,0.3186829098234171,20.479353392355385,14.63951735472278,14.63951735472278,0.25445639660518793,0.5,0.4,0.05,0.05,100,50,10.131636307827083,3071.855206416834,0.20479353392355384,0.2927903470944556,0.6666666666666666,0.03145423899367787,0.10239676696177692,0.11711613883778225,0.03333333333333333,0.0015727119496838937,40.24730332629276,46.03278896459644,13.101749374996196,0.6181583341146001,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.31012665885095,-84.96847516157426,-13.316557124785819,22.8387565832588,-0.8407897820517929,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
6,7,9,47,否,3x3_cluster_9,,3x3_cluster_9,3x3,,,,,,,2048.3471205218107,0.20618141599877093,20.483677386634103,14.841735602609052,14.841735602609052,0.2554799927094942,0.5,0.4,0.05,0.05,100,50,10.602875205865551,3072.520680782716,0.20483677386634103,0.29683471205218104,0.6666666666666666,0.01944580238808343,0.10241838693317051,0.11873388482087242,0.03333333333333333,0.0009722901194041717,40.09208126069774,46.47884721854584,13.048464721104688,0.38060679965171995,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.291346513103974,-90.27490655986354,-13.298254852059188,24.535550099527374,-0.44190422586488876,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
7,9,6,48,否,3x3_cluster_6,,3x3_cluster_6,3x3,,,,,,,2054.270653302593,0.3805429648232831,20.543087075990755,14.971353266512967,14.971353266512967,0.2576141220723678,0.5,0.4,0.05,0.05,100,50,10.838494654884785,3081.4059799538895,0.20543087075990754,0.2994270653302593,0.6666666666666666,0.035110315310417826,0.10271543537995377,0.11977082613210373,0.03333333333333333,0.0017555157655208915,39.877857428202304,46.49937870473337,12.941208975616458,0.6815548914478547,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.040597240111476,-82.05068156620356,-13.046790056520088,25.62315922484099,0.38974545930229126,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
8,8,7,49,否,3x3_cluster_9,4x4_cluster_4,3x3_cluster_9; 4x4_cluster_4,3x3; 4x4,,,,,,,2051.1037537685734,0.2909134256119653,20.511328451111346,15.055518768842868,15.055518768842868,0.2576761637018921,0.5,0.4,0.05,0.05,100,50,11.074114103904021,3076.65563065286,0.20511328451111346,0.30111037537685736,0.6666666666666666,0.02626967926124293,0.10255664225555673,0.12044415015074295,0.03333333333333333,0.0013134839630621466,39.805004352223165,46.74762955873176,12.937567467362632,0.509798621682461,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.174655374873891,-86.27829655082292,-13.18121553826197,26.329383713182818,0.4139224855682605,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
9,7,8,50,否,3x3_cluster_9,4x4_cluster_4,3x3_cluster_9; 4x4_cluster_4,3x3; 4x4,,,,,,,2049.991453326381,0.18930291434251872,20.500103836178152,15.149957266631905,15.149957266631905,0.2578882197127918,0.5,0.4,0.05,0.05,100,50,11.309733552923253,3074.9871799895714,0.20500103836178152,0.3029991453326381,0.6666666666666666,0.01673805253295195,0.10250051918089076,0.12119965813305525,0.03333333333333333,0.0008369026266475976,39.748848221687204,47.00021867351993,12.926389231759003,0.3245438730338499,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.221740203733766,-91.07102586548287,-13.228726231041998,27.121807900463402,0.4965586733132566,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
10,6,9,53,否,3x3_cluster_8,,3x3_cluster_8,3x3,,,,,,,2054.467375016696,0.08812662118998357,20.54476187678815,15.472336875083482,15.472336875083482,0.2602098587147519,0.5,0.4,0.05,0.05,100,50,12.016591899980957,3081.701062525044,0.2054476187678815,0.3094467375016696,0.6666666666666667,0.007333745035489074,0.10272380938394075,0.12377869500066785,0.03333333333333334,0.00036668725177445375,39.47840605924029,47.570135998900774,12.810534154967494,0.1409237868914341,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.032269806362267,-95.84327413077226,-13.039701087622143,29.826863626717525,1.4012790612339505,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
11,8,6,53,否,3x3_cluster_6,4x4_cluster_4,3x3_cluster_6; 4x4_cluster_4,3x3; 4x4,,,,,,,2053.577415153166,0.2639102195848127,20.536038061751242,15.46788707576583,15.46788707576583,0.26087669000766656,0.5,0.4,0.05,0.05,100,50,12.016591899980957,3080.366122729749,0.2053603806175124,0.3093577415153166,0.6666666666666666,0.021962152146086523,0.1026801903087562,0.12374309660612665,0.03333333333333333,0.0010981076073043263,39.36297844891952,47.43755178348317,12.778504575081213,0.42096519251609454,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.069942728422685,-87.55197439673935,-13.076626585606279,29.78952579637922,1.6611368020343402,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
12,9,5,53,否,3x3_cluster_6,,3x3_cluster_6,3x3,,,,,,,2057.482624236898,0.3429830189216949,20.575169225387903,15.487413121184492,15.487413121184492,0.2615641495726279,0.5,0.4,0.05,0.05,100,50,12.016591899980957,3086.223936355347,0.20575169225387901,0.30974826242368986,0.6666666666666666,0.028542453781944484,0.10287584612693951,0.12389930496947595,0.03333333333333333,0.0014271226890972242,39.33531164656714,47.37378070022187,12.745237140190296,0.5456705130207057,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.904631186329684,-83.82229605303777,-12.910995184909549,29.953366931445252,1.9290331820342157,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
13,7,7,55,否,3x3_cluster_9,4x4_cluster_4,3x3_cluster_9; 4x4_cluster_4,3x3; 4x4,,,,,,,2054.0150162699106,0.1729783243565464,20.540323141023464,15.670075081349554,15.670075081349554,0.2621014562371727,0.5,0.4,0.05,0.05,100,50,12.487830798019427,3081.022524404866,0.20540323141023464,0.3134015016269911,0.6666666666666666,0.013851751129105689,0.10270161570511732,0.12536060065079643,0.03333333333333333,0.0006925875564552845,39.18590775775426,47.83146691346559,12.718367829858702,0.26425749892144007,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-13.051418620268777,-91.84101845776598,-13.058489029346907,31.486065552448512,2.1384164210170664,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
14,6,8,57,否,3x3_cluster_8,4x4_cluster_4,3x3_cluster_8; 4x4_cluster_4,3x3; 4x4,,,,,,,2056.7985608060626,0.08112307500686304,20.568066731135634,15.883992804030314,15.883992804030314,0.26356440296165295,0.5,0.4,0.05,0.05,100,50,12.959069696057897,3085.197841209094,0.20568066731135634,0.3176798560806063,0.6666666666666666,0.006259945884197257,0.10284033365567817,0.1270719424322425,0.03333333333333333,0.0003129972942098629,39.01991095540428,48.21392251835226,12.647408388116036,0.11875813812742328,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.933588299307813,-96.17361496538734,-12.94105808010906,33.28102821607415,2.708512687905099,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
15,8,5,59,否,3x3_cluster_6,4x4_cluster_2; 4x4_cluster_4,3x3_cluster_6; 4x4_cluster_2; 4x4_cluster_4,3x3; 4x4; 4x4,,,,,,,2057.9612650131403,0.23768640322445483,20.579850336534626,16.0898063250657,16.0898063250657,0.2658517252826965,0.5,0.4,0.05,0.05,100,50,13.430308594096363,3086.9418975197104,0.20579850336534627,0.32179612650131406,0.6666666666666666,0.017697761861476213,0.10289925168267314,0.12871845060052564,0.03333333333333333,0.0008848880930738107,38.70780526828959,48.42026194365484,12.539062767809378,0.3328700202461897,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.884369826916487,-88.78889026146976,-12.891181335179752,35.00798931727831,3.599860195354395,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
16,9,4,59,否,3x3_cluster_6,,3x3_cluster_6,3x3,,,,,,,2060.910509716254,0.30624722059248877,20.609411344383133,16.10455254858127,16.10455254858127,0.2663773047049813,0.5,0.4,0.05,0.05,100,50,13.430308594096363,3091.3657645743815,0.20609411344383133,0.32209105097162544,0.6666666666666666,0.022802694252841486,0.10304705672191566,0.12883642038865017,0.03333333333333333,0.0011401347126420744,38.687580179804826,48.369837066922145,12.514535077635362,0.42804767563766855,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.759525246401626,-85.55503743333594,-12.76605775895755,35.13172343480828,3.8046734408293164,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
17,7,6,60,否,3x3_cluster_6,4x4_cluster_4,3x3_cluster_6; 4x4_cluster_4,3x3; 4x4,,,,,,,2057.4751129217016,0.15707492662618588,20.574908204143643,16.18737556460851,16.18737556460851,0.26629165667228466,0.5,0.4,0.05,0.05,100,50,13.6659280431156,3086.212669382552,0.20574908204143644,0.32374751129217016,0.6666666666666667,0.011493908509588162,0.10287454102071822,0.12949900451686808,0.03333333333333334,0.0005746954254794082,38.63374373256791,48.63235650421769,12.518077310234721,0.21582245297968403,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.904949148075564,-92.5911443999815,-12.912100015696076,35.82668325204427,3.771297226309959,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
18,5,9,61,否,3x3_cluster_8,,3x3_cluster_8,3x3,,,,,,,2063.821904256744,2.1297527462071096e-7,20.638219042780413,16.31910952128372,16.31910952128372,0.26707730549672515,0.5,0.4,0.05,0.05,100,50,13.901547492134833,3095.732856385116,0.20638219042780415,0.3263821904256744,0.6666666666666666,1.5320256593102842e-8,0.10319109521390207,0.13055287617026975,0.03333333333333333,7.660128296551421e-10,38.63716350855211,48.88205530376916,12.480780900865579,2.868131488253369e-7,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.636282902442536,-99.9999899544562,-12.64412273344453,36.93204998276887,4.077457016276072,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
19,6,7,62,否,3x3_cluster_8,4x4_cluster_4,3x3_cluster_8; 4x4_cluster_4,3x3; 4x4,,,,,,,2059.7307557344106,0.07428872330114676,20.597381846067407,16.398653778672053,16.398653778672053,0.2677766686694166,0.5,0.4,0.05,0.05,100,50,14.137166941154069,3089.596133601616,0.20597381846067406,0.3279730755734411,0.6666666666666666,0.005254852235272699,0.10298690923033703,0.13118923022937642,0.03333333333333333,0.000262742611763635,38.46064053906577,48.99284641277972,12.448391362390215,0.09812168576430838,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.809465453403943,-96.49597529363946,-12.816975300644096,37.59949805733051,4.349992117711153,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
20,7,5,65,否,3x3_cluster_6,4x4_cluster_2; 4x4_cluster_4,3x3_cluster_6; 4x4_cluster_2; 4x4_cluster_4,3x3; 4x4; 4x4,,,,,,,2060.0093111267097,0.14156347791524712,20.60023467474501,16.70004655563355,16.70004655563355,0.2704194066203309,0.5,0.4,0.05,0.05,100,50,14.844025288211771,3090.0139666900645,0.2060023467474501,0.334000931112671,0.6666666666666666,0.009536731120208228,0.10300117337372505,0.1336003724450684,0.03333333333333333,0.00047683655601041145,38.09049955694119,49.40628112032948,12.32688208276853,0.1763372399608138,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.797673915365404,-93.32278302694033,-12.804900065309754,40.128455335637355,5.379841677485683,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
21,5,8,65,否,3x3_cluster_8,4x4_cluster_3; 4x4_cluster_4,3x3_cluster_8; 4x4_cluster_3; 4x4_cluster_4,3x3; 4x4; 4x4,,,,,,,2064.9594817068696,2.579943612066621e-7,20.64959481732669,16.72479740853435,16.72479740853435,0.2703796875712755,0.5,0.4,0.05,0.05,100,50,14.844025288211771,3097.439222560304,0.2064959481732669,0.334495948170687,0.6666666666666667,1.7380350423651304e-8,0.10324797408663346,0.13379837926827481,0.03333333333333334,8.690175211825652e-10,38.186290922748526,49.48536647744292,12.328342278402198,3.214063634120188e-7,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.588128071677168,-99.99998783101157,-12.595972223799453,40.33613731865906,5.364363546114302,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
22,8,4,65,否,3x3_cluster_6,4x4_cluster_2,3x3_cluster_6; 4x4_cluster_2,3x3; 4x4,,,,,,,2061.315912115716,0.21208218314724747,20.613371203340307,16.706579560578582,16.706579560578582,0.27077871675487014,0.5,0.4,0.05,0.05,100,50,14.844025288211771,3091.973868173574,0.20613371203340308,0.33413159121157165,0.6666666666666666,0.014287376842160889,0.10306685601670154,0.13365263648462866,0.03333333333333333,0.0007143688421080445,38.06475010377,49.36071987752302,12.310698632869837,0.26383138583712934,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.742364143273399,-89.9965812238509,-12.749296770408163,40.18327314040203,5.5198613808201396,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
23,9,3,66,否,3x3_cluster_3,,3x3_cluster_3,3x3,,,,,,,2064.4441499751933,0.27025885415419804,20.64471175860609,16.822220749875967,16.822220749875967,0.2720449868566518,0.5,0.4,0.05,0.05,100,50,15.079644737231007,3096.66622496279,0.20644711758606088,0.33644441499751937,0.6666666666666667,0.017922096897080096,0.10322355879303044,0.13457776599900775,0.03333333333333334,0.0008961048448540049,37.94554618233693,49.47152466492829,12.253516098464848,0.3294130542699268,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.609942597206093,-87.25252420572446,-12.61664086179569,41.15360703590557,6.01331465961545,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
24,6,6,67,否,3x3_cluster_5,4x4_cluster_4,3x3_cluster_5; 4x4_cluster_4,3x3; 4x4,,,,,,,2062.272633092803,0.06759816876563689,20.622793929096797,16.911363165464017,16.911363165464017,0.2719623454421755,0.5,0.4,0.05,0.05,100,50,15.315264186250241,3093.4089496392044,0.20622793929096797,0.3382272633092803,0.6666666666666667,0.004413777519184113,0.10311396964548399,0.13529090532371213,0.03333333333333334,0.00022068887595920567,37.915277166979216,49.74682083481156,12.256754119522856,0.08114787868637588,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.701865154183716,-96.81155304689608,-12.709412976511311,41.90159231604404,5.981110095296805,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
25,5,7,71,否,3x3_cluster_8,4x4_cluster_3; 4x4_cluster_4,3x3_cluster_8; 4x4_cluster_3; 4x4_cluster_4,3x3; 4x4; 4x4,,,,,,,2067.7013897376046,2.852400485237085e-7,20.677013897661286,17.33850694868802,17.33850694868802,0.27542645930128834,0.5,0.4,0.05,0.05,100,50,16.25774198232718,3101.552084606407,0.20677013897661287,0.3467701389737604,0.6666666666666666,1.7544874856162433e-8,0.10338506948830643,0.13870805558950416,0.03333333333333333,8.772437428081217e-10,37.53636079678757,50.36119476243519,12.102444122273443,3.185038013684793e-7,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.472060267080957,-99.99998654589643,-12.479914834762841,45.4857139740288,7.331041945929925,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
26,4,9,71,否,3x3_cluster_8,,3x3_cluster_8,3x3,,,,,,,2073.993565319519,0.05650198436318096,20.739992155179554,17.369967826597595,17.369967826597595,0.27616936162604916,0.5,0.4,0.05,0.05,100,50,16.25774198232718,3110.9903479792783,0.20739992155179554,0.3473993565319519,0.6666666666666666,0.0034753894129086866,0.10369996077589777,0.13895974261278077,0.03333333333333333,0.00017376947064543435,37.549755600807224,50.317322537249744,12.069999936951001,0.06292192499203204,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.205705962800769,-97.33493402000721,-12.213345276467944,45.74969911983574,7.620543836136519,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
27,7,4,73,否,3x3_cluster_6,4x4_cluster_2,3x3_cluster_6; 4x4_cluster_2,3x3; 4x4,,,,,,,2064.7936721941883,0.12643491611726243,20.648063156858,17.52396836097094,17.52396836097094,0.2771486863269046,0.5,0.4,0.05,0.05,100,50,16.72898088036565,3097.1905082912826,0.20648063156858001,0.3504793672194188,0.6666666666666666,0.007557837325623084,0.10324031578429001,0.14019174688776753,0.03333333333333333,0.0003778918662811542,37.25160243898608,50.58457232155003,12.027472716123635,0.1363525233402487,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.595146959948533,-94.03636177693375,-12.602455319246767,47.04190252362325,8.002177252231878,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
28,8,3,73,否,3x3_cluster_3,4x4_cluster_2,3x3_cluster_3; 4x4_cluster_2,3x3; 4x4,,,,,,,2065.6657943815662,0.1870766819027704,20.656845020497567,17.528328971907833,17.528328971907833,0.27741131742369446,0.5,0.4,0.05,0.05,100,50,16.72898088036565,3098.4986915723493,0.20656845020497566,0.35056657943815667,0.6666666666666666,0.01118279010781447,0.10328422510248783,0.14022663177526268,0.03333333333333333,0.0005591395053907236,37.232510946444464,50.54972913215714,12.016197991351548,0.20156193004683934,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.558229125180215,-91.17603202421515,-12.565284117570915,47.07849198299217,8.104522064481827,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
29,6,5,74,否,3x3_cluster_5,4x4_cluster_2; 4x4_cluster_4,3x3_cluster_5; 4x4_cluster_2; 4x4_cluster_4,3x3; 4x4; 4x4,,,,,,,2065.8892672335132,0.06104769420397738,20.658953720029334,17.629446336167568,17.629446336167568,0.2778461335110257,0.5,0.4,0.05,0.05,100,50,16.964600329384883,3098.83390085027,0.20658953720029335,0.35258892672335135,0.6666666666666666,0.003598534184046463,0.10329476860014668,0.14103557068934056,0.03333333333333333,0.00017992670920232315,37.1773072507276,50.76077729643978,11.997157182483816,0.06475827034881984,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.548769288080793,-97.12052355658429,-12.556358575250268,47.92695788481613,8.273965711350426,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
30,9,2,76,否,3x3_cluster_3,,3x3_cluster_3,3x3,,,,,,,2069.369161926562,0.2349727175086609,20.693926591983125,17.84684580963281,17.84684580963281,0.2802607842677222,0.5,0.4,0.05,0.05,100,50,17.43583922742335,3104.053742889843,0.20693926591983125,0.3569369161926562,0.6666666666666666,0.013476421435401415,0.10346963295991563,0.1427747664770625,0.03333333333333333,0.0006738210717700708,36.92027092853143,50.9452185080531,11.894076188464696,0.2404343749507848,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.401461744311758,-88.9168884470745,-12.408328073988436,49.751135578332125,9.214931885439974,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
31,5,6,77,否,3x3_cluster_5,4x4_cluster_3; 4x4_cluster_4,3x3_cluster_5; 4x4_cluster_3; 4x4_cluster_4,3x3; 4x4; 4x4,,,,,,,2069.885956839491,3.227122761233506e-7,20.69885956871762,17.949429784197456,17.949429784197456,0.2804230703759291,0.5,0.4,0.05,0.05,100,50,17.671458676442587,3104.8289352592365,0.2069885956871762,0.35898859568394914,0.6666666666666666,1.826177917918858e-8,0.1034942978435881,0.14359543827357965,0.03333333333333333,9.13088958959429e-10,36.9064848014822,51.20671351590194,11.886801357004652,3.256112122927472e-7,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.379585280806129,-99.99998477841942,-12.387448146835613,50.611907663610225,9.278173220827227,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
32,4,8,77,否,3x3_cluster_8,4x4_cluster_3,3x3_cluster_8; 4x4_cluster_3,3x3; 4x4,,,,,,,2075.5937350794184,0.0523022619546308,20.75598965305614,17.977968675397094,17.977968675397094,0.28108701593016855,0.5,0.4,0.05,0.05,100,50,17.671458676442587,3113.3906026191275,0.2075598965305614,0.35955937350794187,0.6666666666666666,0.0029597025866548153,0.1037799482652807,0.14382374940317674,0.03333333333333333,0.00014798512933274078,36.92119547805007,51.16734836412778,11.858808339247668,0.05264781857447254,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.137969120816686,-97.53302506835867,-12.14563228930649,50.85137470506276,9.536906417022923,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
33,6,4,82,否,3x3_cluster_5,4x4_cluster_2,3x3_cluster_5; 4x4_cluster_2,3x3; 4x4,,,,,,,2069.4306607517633,0.05461498793495336,20.694361222505567,18.447153303758817,18.447153303758817,0.2845290704379022,0.5,0.4,0.05,0.05,100,50,18.84955592153876,3104.145991127645,0.20694361222505567,0.36894306607517635,0.6666666666666666,0.002897415098917351,0.10347180611252783,0.14757722643007054,0.03333333333333333,0.00014487075494586755,36.366221855506836,51.86752178018206,11.71534005954491,0.05091630476621508,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.398858435394624,-97.4239392123366,-12.406488403109082,54.78825697784748,10.878241950605808,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
34,7,3,82,否,3x3_cluster_3,4x4_cluster_2,3x3_cluster_3; 4x4_cluster_2,3x3; 4x4,,,,,,,2069.064231560658,0.11161478830854847,20.690753930394887,18.44532115780329,18.44532115780329,0.2846494873579697,0.5,0.4,0.05,0.05,100,50,18.84955592153876,3103.5963473409865,0.20690753930394887,0.36890642315606575,0.6666666666666667,0.0059213484271536596,0.10345376965197443,0.1475625692624263,0.03333333333333334,0.000296067421357683,36.34474549741802,51.840779150241886,11.710462756999009,0.10401259534109637,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.414369762278982,-94.7353924196148,-12.421757073638837,54.77288362054321,10.925167266102184,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
35,5,5,85,否,3x3_cluster_5,4x4_cluster_1; 4x4_cluster_2; 4x4_cluster_3; 4x4_cluster_4,3x3_cluster_5; 4x4_cluster_1; 4x4_cluster_2; 4x4_cluster_3; 4x4_cluster_4,3x3; 4x4; 4x4; 4x4; 4x4,,,,,,,2072.781760848171,4.0083371008353935e-7,20.727817608882546,18.763908804240856,18.763908804240856,0.28708369284898455,0.5,0.4,0.05,0.05,100,50,19.556414268596463,3109.1726412722564,0.20727817608882546,0.3752781760848171,0.6666666666666666,2.0496278334990836e-8,0.10363908804441273,0.15011127043392686,0.03333333333333333,1.0248139167495419e-9,36.10065309541704,52.28833060867208,11.611015938936967,3.569739216547006e-7,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.257002900193495,-99.99998109361474,-12.26487676617124,57.44612136485673,11.873753732064523,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
36,6,3,92,否,3x3_cluster_2,4x4_cluster_2,3x3_cluster_2; 4x4_cluster_2,3x3; 4x4,,,,,,,2073.334746756144,0.04828858012400069,20.733395756141565,19.46667373378072,19.46667373378072,0.29284883855689553,0.5,0.4,0.05,0.05,100,50,21.205750411731103,3110.002120134216,0.20733395756141565,0.3893334746756144,0.6666666666666666,0.00227714554714778,0.10366697878070782,0.15573338987024576,0.03333333333333333,0.00011385727735738901,35.39963899377651,53.17899533221171,11.382486306997594,0.038879367014175235,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.233594434412877,-97.72234101932725,-12.241266010498789,63.34295307202697,14.120375561245105,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
37,8,2,84,否,3x3_cluster_3,4x4_cluster_2,3x3_cluster_3; 4x4_cluster_2,3x3; 4x4,,,,,,,2070.8579512147035,0.16259193995280471,20.70874210408699,18.654289756073517,18.654289756073517,0.28653732586861796,0.5,0.4,0.05,0.05,100,50,19.320794819577227,3106.286926822055,0.2070874210408699,0.37308579512147033,0.6666666666666666,0.008415385674923415,0.10354371052043496,0.14923431804858814,0.03333333333333333,0.00042076928374617075,36.136858401120584,52.08292606865108,11.633366629895805,0.14684890033253586,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.338439752967057,-92.3309198309924,-12.345618105928512,56.52631866587142,11.660839775154345,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
38,7,2,93,否,3x3_cluster_3,4x4_cluster_2,3x3_cluster_3; 4x4_cluster_2,3x3; 4x4,,,,,,,2073.4722597330788,0.09707133726009681,20.73481966866805,19.567361298665396,19.567361298665396,0.293775201816478,0.5,0.4,0.05,0.05,100,50,21.44136986075034,3110.2083895996184,0.20734819668668047,0.39134722597330796,0.6666666666666666,0.0045272917677611395,0.10367409834334024,0.1565388903893232,0.03333333333333333,0.00022636458838805698,35.29058454141105,53.28571970800225,11.346641416173945,0.07705433441277286,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.227773367784305,-95.42137286893467,-12.235238982311715,64.1878125693819,14.481370413096462,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
39,5,4,94,否,3x3_cluster_5,4x4_cluster_1; 4x4_cluster_2,3x3_cluster_5; 4x4_cluster_1; 4x4_cluster_2,3x3; 4x4; 4x4,,,,,,,2075.6466459611825,4.3425166046218506e-7,20.756466460046077,19.67823322980591,19.67823322980591,0.2945415324846601,0.5,0.4,0.05,0.05,100,50,21.67698930976957,3113.469968941774,0.20756466460046077,0.3935646645961182,0.6666666666666666,2.0032840089397138e-8,0.10378233230023039,0.1574258658384473,0.03333333333333333,1.0016420044698568e-9,35.23521162826631,53.44776490987029,11.317023121795236,3.4006817173040094e-7,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.135729348433088,-99.99997951736846,-12.143614097096862,65.11812808670953,14.780002103384183,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
40,4,7,83,否,3x3_cluster_8,4x4_cluster_3,3x3_cluster_8; 4x4_cluster_3,3x3; 4x4,,,,,,,2076.7000333029355,0.04813733414429343,20.7670484703635,18.58350016651468,18.58350016651468,0.28596426526187374,0.5,0.4,0.05,0.05,100,50,19.085175370557995,3115.050049954403,0.20767048470363497,0.37167000333029354,0.6666666666666666,0.0025222369304792004,0.10383524235181749,0.1486680013321174,0.03333333333333333,0.00012611184652396002,36.310765843048706,51.98860100344418,11.656532348949357,0.04410080455776829,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.091138372085155,-97.72947493718293,-12.098823372053877,55.93233015178686,11.43752356880272,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
41,9,1,88,否,3x3_cluster_3,,3x3_cluster_3,3x3,,,,,,,2074.010883869352,0.20027281169716726,20.740309111505216,19.07005441934676,19.07005441934676,0.2900953049585514,0.5,0.4,0.05,0.05,100,50,20.263272615654166,3111.0163258040275,0.20740309111505215,0.3814010883869352,0.6666666666666667,0.009883537348377217,0.10370154555752607,0.1525604353547741,0.03333333333333334,0.0004941768674188608,35.748122125910434,52.59081767126784,11.490706955786559,0.17035324703516144,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.20497285066303,-90.55360155599371,-12.212003687941047,60.014959242622844,13.047350003377003,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
42,4,6,91,否,3x3_cluster_5,4x4_cluster_3,3x3_cluster_5; 4x4_cluster_3,3x3; 4x4,,,,,,,2078.57139197601,0.04400842622017265,20.78575792818632,19.39285695988005,19.39285695988005,0.2925111022680119,0.5,0.4,0.05,0.05,100,50,20.970130962711867,3117.857087964015,0.20785757928186321,0.387857139197601,0.6666666666666666,0.0020986242908271023,0.10392878964093161,0.1551428556790404,0.03333333333333333,0.00010493121454135512,35.53000638094131,53.03849560144219,11.395625313478448,0.03587270413804513,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.011921822747567,-97.92422583252079,-12.019631407964997,62.72356375055998,13.988762978050339,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
43,8,1,97,否,3x3_cluster_3,,3x3_cluster_3,3x3,,,,,,,2075.6558796495424,0.13854453690630888,20.75669734103233,19.978279398247714,19.978279398247714,0.29725582186117194,0.5,0.4,0.05,0.05,100,50,22.383847656827275,3113.4838194743133,0.2075669734103233,0.3995655879649543,0.6666666666666667,0.006189487126180095,0.10378348670516165,0.1598262351859817,0.03333333333333334,0.0003094743563090048,34.91424845114932,53.767830138025786,11.21380981360974,0.10411159721515423,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.135338476848684,-93.46517938822227,-12.142636841692394,67.6357861047911,15.837734565482545,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
44,5,3,106,否,3x3_cluster_2,4x4_cluster_1; 4x4_cluster_2,3x3_cluster_2; 4x4_cluster_1; 4x4_cluster_2,3x3; 4x4; 4x4,,,,,,,2079.1126276637387,4.3063603247123466e-7,20.791126277068024,20.895563138318693,20.895563138318693,0.30445347071244444,0.5,0.4,0.05,0.05,100,50,24.504422698000386,3118.668941495608,0.20791126277068023,0.41791126276637386,0.6666666666666666,1.757380852340486e-8,0.10395563138534011,0.16716450510654957,0.03333333333333333,8.78690426170243e-10,34.14499796799454,54.90642124067624,10.948580502716819,2.8861238603674367e-7,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.989010755950426,-99.99997968790913,-11.996908670840153,75.33262414487919,18.642589090816774,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
45,6,2,105,否,3x3_cluster_2,4x4_cluster_2,3x3_cluster_2; 4x4_cluster_2,3x3; 4x4,,,,,,,2077.6297122485025,0.042055246543579256,20.77633917773157,20.788148561242515,20.788148561242515,0.30360771183441965,0.5,0.4,0.05,0.05,100,50,24.26880324898115,3116.4445683727536,0.20776339177731568,0.4157629712248503,0.6666666666666667,0.0017328932997693165,0.10388169588865784,0.16630518848994014,0.03333333333333334,0.00008664466498846583,34.215858981372925,54.77649193695653,10.979110640770335,0.028538440900225375,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.051784100176056,-98.01635273332916,-12.05949837550828,74.43131894695217,18.313004991149278,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
46,4,5,100,否,3x3_cluster_5,4x4_cluster_1; 4x4_cluster_3,3x3_cluster_5; 4x4_cluster_1; 4x4_cluster_3,3x3; 4x4; 4x4,,,,,,,2080.548189657661,0.03990985110344016,20.805521806427716,20.302740948288307,20.302740948288307,0.29987018060581516,0.5,0.4,0.05,0.05,100,50,23.09070600388498,3120.8222844864918,0.20805521806427715,0.4060548189657661,0.6666666666666666,0.0017283945799113022,0.10402760903213858,0.16242192758630647,0.03333333333333333,0.00008641972899556511,34.69098457627363,54.164241946620415,11.115954344247239,0.028819132858707637,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.928241931056007,-98.11754600053186,-11.93597637366249,70.35831119904942,16.856525021544215,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
47,7,1,109,否,3x3_cluster_3,,3x3_cluster_3,3x3,,,,,,,2078.7799497360097,0.08277097795434517,20.787882268338052,21.19389974868005,21.19389974868005,0.30698964593850153,0.5,0.4,0.05,0.05,100,50,25.21128104505809,3118.1699246040143,0.2078788226833805,0.423877994973601,0.6666666666666667,0.0032830929061643185,0.10393941134169025,0.1695511979894404,0.03333333333333334,0.00016415464530821595,33.85779847901015,55.23054459611557,10.858184283183364,0.05347264169091629,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-12.003093356926204,-96.09588725134044,-12.010639663222761,77.83593742851218,19.63091218170904,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
48,4,4,111,否,3x3_cluster_5,4x4_cluster_1,3x3_cluster_5; 4x4_cluster_1,3x3; 4x4,,,,,,,2082.723757835242,0.03583955390709489,20.827273417906326,21.413618789176212,21.413618789176212,0.308849071009886,0.5,0.4,0.05,0.05,100,50,25.682519943096555,3124.085636752863,0.20827273417906325,0.42827237578352423,0.6666666666666666,0.0013954843211064472,0.10413636708953163,0.1713089503134097,0.03333333333333333,0.00006977421605532237,33.71762932111596,55.46699820141574,10.7927807430063,0.02259173446200733,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.836147878560201,-98.30953236541268,-11.8439078139318,79.67957838184648,20.35551224678038,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
49,3,9,87,否,3x3_cluster_7,,3x3_cluster_7,3x3,,,,,,,2085.5865506768955,0.08239228516059864,20.85594789905412,19.027932753384476,19.027932753384476,0.29004467992640903,0.5,0.4,0.05,0.05,100,50,20.02765316663493,3128.3798260153435,0.2085594789905412,0.38055865506768954,0.6666666666666666,0.004113926103826287,0.1042797394952706,0.15222346202707582,0.03333333333333333,0.0002056963051913144,35.95329517287852,52.48320612409216,11.492579270239133,0.07091943279019447,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.714962894811709,-96.11374930154729,-11.722536660217598,59.661520468198134,13.027621915297988,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
50,3,8,93,否,3x3_cluster_7,4x4_cluster_3,3x3_cluster_7; 4x4_cluster_3,3x3; 4x4,,,,,,,2085.502761200707,0.0764020588278625,20.855104014065898,19.627513806003535,19.627513806003535,0.29480910852317044,0.5,0.4,0.05,0.05,100,50,21.44136986075034,3128.2541418010605,0.20855104014065898,0.3925502761200707,0.6666666666666666,0.0035633011940958523,0.10427552007032949,0.1570201104480283,0.03333333333333333,0.00017816505970479264,35.370759335222665,53.26197878180231,11.306827435421903,0.06043444755311193,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.718509790054163,-96.39629421730136,-11.726108592141825,64.69254636816376,14.884273911858706,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
51,5,2,121,否,3x3_cluster_2,4x4_cluster_1; 4x4_cluster_2,3x3_cluster_2; 4x4_cluster_1; 4x4_cluster_2,3x3; 4x4; 4x4,,,,,,,2082.724757029435,3.3867243023749975e-7,20.827247570633023,22.413623785147173,22.413623785147173,0.3167785620767311,0.5,0.4,0.05,0.05,100,50,28.0387144332889,3124.0871355441523,0.20827247570633023,0.4482724757029435,0.6666666666666666,1.2078743162183345e-8,0.10413623785316511,0.17930899028117742,0.03333333333333333,6.039371581091673e-10,32.873511759177504,56.603890461704175,10.522597588468686,1.9064963050518467e-7,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.836105581635929,-99.99998402561641,-11.844017218296084,88.07052237990739,23.445558644154392,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
52,3,7,101,否,3x3_cluster_7,4x4_cluster_3,3x3_cluster_7; 4x4_cluster_3,3x3; 4x4,,,,,,,2086.134432811597,0.07043722248872451,20.86141476533846,20.430672164057984,20.430672164057984,0.3012383073587037,0.5,0.4,0.05,0.05,100,50,23.326325452904214,3129.2016492173952,0.2086141476533846,0.40861344328115967,0.6666666666666667,0.003019645019998416,0.1043070738266923,0.1634453773124639,0.03333333333333334,0.00015098225099992082,34.62627585643874,54.258110352941515,11.065492999375671,0.050120791244088116,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.691770477035668,-96.67764154665322,-11.699396926155389,71.43176950321012,17.38967085754757,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
53,6,1,123,否,3x3_cluster_2,,3x3_cluster_2,3x3,,,,,,,2082.442039892071,0.03590228687041929,20.824456301207583,22.612210199460357,22.612210199460357,0.31841678561754,0.5,0.4,0.05,0.05,100,50,28.50995333132737,3123.6630598381066,0.20824456301207583,0.4522442039892071,0.6666666666666666,0.0012592895699681508,0.10412228150603792,0.18089768159568287,0.03333333333333333,0.00006296447849840754,32.70005156373163,56.811697077491644,10.468477092752202,0.01977426602453004,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.84807329065013,-98.30657339878056,-11.855831890329616,89.73684153630292,24.08395860043102,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
54,3,6,110,否,3x3_cluster_4,4x4_cluster_3,3x3_cluster_4; 4x4_cluster_3,3x3; 4x4,,,,,,,2087.0091670628176,0.06449293559916747,20.870156163563777,21.33504583531409,21.33504583531409,0.30849238574949195,0.5,0.4,0.05,0.05,100,50,25.446900494077322,3130.5137505942266,0.20870156163563777,0.4267009167062818,0.6666666666666666,0.0025344122210159926,0.10435078081781889,0.17068036668251274,0.03333333333333333,0.00012672061105079965,33.82617732017211,55.32746667755454,10.805278457611683,0.04107754466167783,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.65474207090161,-96.95801960670235,-11.662397003394318,79.02028042006067,20.216515431652414,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
55,4,3,125,否,3x3_cluster_2,4x4_cluster_1,3x3_cluster_2; 4x4_cluster_1,3x3; 4x4,,,,,,,2085.3222232120215,0.031794636669447957,20.853254026756886,22.826611116060107,22.826611116060107,0.32026779593209864,0.5,0.4,0.05,0.05,100,50,28.981192229365842,3127.9833348180323,0.20853254026756887,0.45653222232120216,0.6666666666666666,0.0010970782850414046,0.10426627013378444,0.18261288892848088,0.03333333333333333,0.000054853914252070234,32.55601026303487,57.01889094613081,10.407971251956532,0.01712753887778507,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.72615214995758,-98.50032161721397,-11.733939077125811,91.53585863278718,24.80527951570633,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
56,3,5,121,否,3x3_cluster_4,4x4_cluster_1; 4x4_cluster_3,3x3_cluster_4; 4x4_cluster_1; 4x4_cluster_3,3x3; 4x4; 4x4,,,,,,,2088.1251609112423,0.05856836260983102,20.881310177475036,22.440625804556213,22.440625804556213,0.3173702177127306,0.5,0.4,0.05,0.05,100,50,28.0387144332889,3132.1877413668635,0.20881310177475038,0.44881251609112427,0.6666666666666666,0.0020888390853004255,0.10440655088737519,0.17952500643644972,0.03333333333333333,0.00010444195426502128,32.89749202554715,56.56658914056272,10.503010186603502,0.032908647286628866,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.607500896333542,-97.23746780835089,-11.6151851452276,88.29709368065956,23.67612115454684,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
57,2,1,141,否,3x3_cluster_1,,3x3_cluster_1,3x3,,,,,,,2086.8178152185806,0.03408010997075948,20.868212232295775,24.434089076092903,24.434089076092903,0.3331995130742121,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3130.2267228278706,0.20868212232295774,0.48868178152185804,0.6666666666666667,0.0010405789857000978,0.10434106116147887,0.19547271260874322,0.03333333333333334,0.000052028949285004896,31.31492548195921,58.66543200692577,10.004027539869481,0.015614971245529841,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.662842192501998,-98.3925212060929,-11.670625127192704,105.02404878693324,29.844645299713225,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
58,5,1,141,否,3x3_cluster_2,,3x3_cluster_2,3x3,,,,,,,2086.653106354735,5.79270518292484e-7,20.86653106412662,24.433265531773674,24.433265531773674,0.33313211379891683,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3129.9796595321022,0.2086653106412662,0.48866531063547347,0.6666666666666667,1.7687053500940627e-8,0.1043326553206331,0.1954661242541894,0.03333333333333334,8.843526750470314e-10,31.318702400938967,58.67525710113183,10.006040232463155,2.6546605338621027e-7,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.669814489169063,-99.99997267716932,-11.67774104741551,105.01713850719423,29.8183804504239,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
59,3,1,141,否,3x3_cluster_1,,3x3_cluster_1,3x3,,,,,,,2086.825058370714,0.035018251312622886,20.868285601958455,24.43412529185357,24.43412529185357,0.33320161225568207,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3130.237587556071,0.20868285601958456,0.4886825058370714,0.6666666666666666,0.0010692235577627214,0.10434142800979228,0.19547300233482856,0.03333333333333333,0.000053461177888136074,31.314839270154256,58.66515119186578,10.003964825685985,0.01604471229397964,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.662535582376107,-98.34827128101851,-11.670314573688312,105.02435266983463,29.845463330540685,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
60,2,2,141,否,3x3_cluster_1,4x4_cluster_1,3x3_cluster_1; 4x4_cluster_1,3x3; 4x4,,,,,,,2087.914536722687,0.03976952063512851,20.879185136747505,24.439572683613434,24.439572683613434,0.33330699522794427,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3131.8718050840307,0.20879185136747505,0.48879145367226867,0.6666666666666666,0.001214295595870664,0.10439592568373753,0.1955165814689075,0.03333333333333333,0.0000607147797935332,31.321293876291854,58.65968681984264,10.00080340656787,0.01821589729764555,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.616416836214258,-98.12416505933606,-11.624179855313573,105.07006120066866,29.886530061184335,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
61,4,1,141,否,3x3_cluster_2,,3x3_cluster_2,3x3,,,,,,,2086.731281382868,0.0237647802582803,20.86733657860894,24.433656406914338,24.433656406914338,0.33317581127154394,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3130.096922074302,0.2086733657860894,0.4886731281382868,0.6666666666666667,0.0007256176977646041,0.1043366828930447,0.19546925125531472,0.03333333333333334,0.00003628088488823021,31.315828368727356,58.66854642053354,10.004735790712314,0.010889420026799705,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.666505259323298,-98.87907109631325,-11.674331527238015,105.02041830222399,29.835408935193914,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
62,3,2,141,否,3x3_cluster_1,4x4_cluster_1,3x3_cluster_1; 4x4_cluster_1,3x3; 4x4,,,,,,,2087.9255356421963,0.040885394164163244,20.879296241816128,24.439627678210982,24.439627678210982,0.3333097066210631,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3131.8883034632945,0.20879296241816128,0.48879255356421963,0.6666666666666666,0.0012483669221078392,0.10439648120908064,0.19551702142568786,0.03333333333333333,0.00006241834610539196,31.321206914545236,58.65934380609066,10.000722422759479,0.018726856604632653,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.615951240568426,-98.07153192416882,-11.623709578265046,105.07052265495147,29.887586665002665,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
63,3,4,134,否,3x3_cluster_4,4x4_cluster_1,3x3_cluster_4; 4x4_cluster_1,3x3; 4x4,,,,,,,2089.504734886942,0.05266121080038742,20.895100010080224,23.74752367443471,23.74752367443471,0.3278743285502868,0.5,0.4,0.05,0.05,100,50,31.101767270538954,3134.257102330413,0.20895100010080225,0.4749504734886942,0.6666666666666666,0.001693190304663831,0.10447550005040113,0.18998018939547767,0.03333333333333333,0.00008465951523319156,31.864558118628572,57.94310420586515,10.1665169037259,0.025820771780384468,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.549102102193606,-97.51609429383518,-11.556816595010739,99.26314573191752,27.769472112078354,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
64,1,1,141,否,3x3_cluster_1,,3x3_cluster_1,3x3,,,,,,,2086.7241811684266,0.021893922521715674,20.86726370560679,24.433620905842133,24.433620905842133,0.3331722860259111,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3130.0862717526397,0.2086726370560679,0.48867241811684264,0.6666666666666667,0.0006684941953001485,0.10433631852803395,0.19546896724673707,0.03333333333333334,0.000033424709765007425,31.3160484092327,58.669078295654195,10.004841027885048,0.010032267228062886,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.666805818748756,-98.96731506443788,-11.674639978515652,105.0201204162035,29.834035180879066,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
65,4,2,141,否,3x3_cluster_2,4x4_cluster_1,3x3_cluster_2; 4x4_cluster_1,3x3; 4x4,,,,,,,2087.799758850527,0.027771546437160885,20.87802536005171,24.438998794252633,24.438998794252633,0.3332781555976881,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3131.69963827579,0.20878025360051708,0.4887799758850527,0.6666666666666666,0.0008479575813487328,0.10439012680025854,0.1955119903540211,0.03333333333333333,0.00004239787906743664,31.32225178236448,58.66336191024434,10.001664826118054,0.012721481273123638,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.621275502328702,-98.69008134040516,-11.629088869530577,105.06524575123895,29.87529153467318,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
66,1,2,141,否,3x3_cluster_1,,3x3_cluster_1,3x3,,,,,,,2087.786749043622,0.025543741623092406,20.87789303417784,24.438933745218108,24.438933745218108,0.3332735478161546,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3131.680123565433,0.2087789303417784,0.48877867490436216,0.6666666666666666,0.0007799352986815061,0.1043894651708892,0.19551146996174487,0.03333333333333333,0.00003899676493407531,31.32248399779836,58.664012496258536,10.00180236786741,0.011701138075704096,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.621826220919093,-98.79516166434341,-11.629648968382748,105.06469993116004,29.873495926530435,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
67,3,3,141,否,3x3_cluster_1,4x4_cluster_1,3x3_cluster_1; 4x4_cluster_1,3x3; 4x4,,,,,,,2089.34186043194,0.04676679017498757,20.893465371109574,24.4467093021597,24.4467093021597,0.33344624925184857,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3134.01279064791,0.20893465371109574,0.488934186043194,0.6666666666666666,0.0014279454827608167,0.10446732685554787,0.1955736744172776,0.03333333333333333,0.00007139727413804083,31.32963384070258,58.65232501685951,9.996629180216834,0.02141196222108839,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.555996746435863,-97.79412027925096,-11.563735569995897,105.12994386805383,29.94079601485752,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
68,2,3,141,否,3x3_cluster_1,4x4_cluster_1,3x3_cluster_1; 4x4_cluster_1,3x3; 4x4,,,,,,,2089.325150508404,0.045463268173291535,20.893296968352214,24.44662575254202,24.44662575254202,0.3334427343776421,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3133.987725762606,0.20893296968352215,0.4889325150508404,0.6666666666666667,0.0013881446252070584,0.10446648484176108,0.19557300602033617,0.03333333333333334,0.00006940723126035293,31.32971021540461,58.65274029133708,9.996734124142236,0.020815369116078054,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.55670409479257,-97.85560435242196,-11.564448372325051,105.12924281139328,29.939426302187766,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
69,1,3,141,否,3x3_cluster_1,,3x3_cluster_1,3x3,,,,,,,2089.157375583031,0.029193944968219757,20.891602949775276,24.445786877915154,24.445786877915154,0.3334025354789613,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3133.7360633745466,0.20891602949775276,0.4889157375583031,0.6666666666666666,0.0008913881343011876,0.10445801474887638,0.19556629502332123,0.03333333333333333,0.000044569406715059383,31.33093027840647,58.65776761038681,9.997934051535122,0.013368059671596311,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.563806181017226,-98.62299014037312,-11.571618684772433,105.12220389654408,29.92376117803291,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
70,2,4,141,否,3x3_cluster_4,4x4_cluster_1,3x3_cluster_4; 4x4_cluster_1,3x3; 4x4,,,,,,,2091.049038652487,0.05116197370347334,20.910541548498575,24.455245193262435,24.455245193262435,0.33360667586843046,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3136.5735579787306,0.20910541548498573,0.4891049038652487,0.6666666666666666,0.0015621450385122967,0.10455270774249287,0.19564196154609947,0.03333333333333333,0.00007810725192561484,31.340165736781046,58.64459785226322,9.991823394942582,0.02341301601315097,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.48373012556738,-97.5868097886618,-11.491456830581253,105.20156769443989,30.00331272424439,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
71,1,4,141,否,3x3_cluster_4,,3x3_cluster_4,3x3,,,,,,,2090.8351304339812,0.0328445871178137,20.90838414892693,24.45417565216991,24.45417565216991,0.3335591653667631,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3136.252695650972,0.2090838414892693,0.48908351304339814,0.6666666666666666,0.001002854368079125,0.10454192074463466,0.19563340521735928,0.03333333333333333,0.00005014271840395625,31.341376726833044,58.6503501140725,9.993240511746752,0.015032647347705753,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.492785081834604,-98.4507979190261,-11.500588506711097,105.19259327986478,29.984798338733764,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
72,2,9,110,否,3x3_cluster_7,,3x3_cluster_7,3x3,,,,,,,2095.8392768487083,0.07972173373542635,20.95847249022082,21.379196384243542,21.379196384243542,0.3093173741231316,0.5,0.4,0.05,0.05,100,50,25.446900494077322,3143.7589152730625,0.2095847249022082,0.42758392768487086,0.6666666666666666,0.0031328661718145715,0.1047923624511041,0.17103357107394834,0.03333333333333333,0.00015664330859072858,33.87874952650659,55.294139568061695,10.776469052412843,0.05064185301886624,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.280954385208474,-96.23971294390896,-11.288578401309826,79.3907433527893,20.53800546554622,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
73,2,8,119,否,3x3_cluster_7,4x4_cluster_3,3x3_cluster_7; 4x4_cluster_3,3x3; 4x4,,,,,,,2095.4271188869484,0.07400139318116747,20.954345190262664,22.277135594434743,22.277135594434743,0.3164575197587507,0.5,0.4,0.05,0.05,100,50,27.567475535250434,3143.140678330423,0.20954345190262663,0.4455427118886949,0.6666666666666666,0.0026843732240386738,0.10477172595131332,0.17821708475547796,0.03333333333333333,0.0001342186612019337,33.10779567108831,56.31648017255394,10.533311148745502,0.04241300761224071,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.298401458280251,-96.50952798097273,-11.306048121339428,86.92526333692365,23.320451540846353,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
74,2,5,141,否,3x3_cluster_4,4x4_cluster_1; 4x4_cluster_3,3x3_cluster_4; 4x4_cluster_1; 4x4_cluster_3,3x3; 4x4; 4x4,,,,,,,2093.085418341018,0.05686625917923939,20.93091104966936,24.465427091705088,24.465427091705088,0.3337987502065626,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3139.628127511527,0.2093091104966936,0.4893085418341018,0.6666666666666666,0.0017363158260951207,0.1046545552483468,0.19572341673364071,0.03333333333333333,0.00008681579130475604,31.352649589934884,58.635266155713474,9.986075782219762,0.02600847213188008,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.397528065861996,-97.31775203196578,-11.405238361799258,105.28700300723379,30.07816224630953,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
75,2,7,128,否,3x3_cluster_7,4x4_cluster_3,3x3_cluster_7; 4x4_cluster_3,3x3; 4x4,,,,,,,2094.996641705654,0.06828556975777458,20.950034702626297,23.17498320852827,23.17498320852827,0.32359929769835816,0.5,0.4,0.05,0.05,100,50,29.688050576423542,3142.494962558481,0.20950034702626297,0.46349966417056543,0.6666666666666666,0.002300102850538892,0.10475017351313148,0.1853998656682262,0.03333333333333333,0.0001150051425269446,32.370426042151394,57.2931999877382,10.30083450190926,0.03553946820115629,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.31662400287385,-96.77912995557624,-11.324293224181924,94.45901474717178,26.103533709310117,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
76,1,5,141,否,3x3_cluster_4,,3x3_cluster_4,3x3,,,,,,,2092.818873938294,0.03649572247401115,20.928225235105412,24.46409436969147,24.46409436969147,0.3337433349942516,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3139.228310907441,0.20928225235105413,0.4892818873938294,0.6666666666666666,0.0011143356610933048,0.10464112617552707,0.19571275495753176,0.03333333333333333,0.000055716783054665245,31.353810486345846,58.64176784953439,9.98772714332615,0.01669452079361684,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.408811166281406,-98.27858243428126,-11.416606672549449,105.27582026732117,30.05656747109409,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
77,2,6,140,否,3x3_cluster_4,4x4_cluster_3,3x3_cluster_4; 4x4_cluster_3,3x3; 4x4,,,,,,,2095.2658534230313,0.06257622436086162,20.952721110454675,24.376329267115157,24.376329267115157,0.3332045006665869,0.5,0.4,0.05,0.05,100,50,32.51548396465436,3142.898780134547,0.20952721110454675,0.48752658534230314,0.6666666666666666,0.001924505396533064,0.10476360555227338,0.19501063413692127,0.03333333333333333,0.0000962252698266532,31.441299915901098,58.525933718744106,10.003887561955853,0.028878803398941052,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.3052279922647,-97.04842637687604,-11.31292240230736,104.53939188579406,29.846588916487548,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
78,1,6,141,否,3x3_cluster_4,,3x3_cluster_4,3x3,,,,,,,2095.107260689463,0.0401474064605311,20.95111275430109,24.475536303447313,24.475536303447313,0.333954923360345,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3142.6608910341947,0.2095111275430109,0.48951072606894624,0.6666666666666666,0.0012258337056140086,0.10475556377150545,0.19580429042757852,0.03333333333333333,0.00006129168528070044,31.368216415871895,58.63202999590186,9.981400282882275,0.01835330534397344,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.311941386807417,-98.10634107193185,-11.319730129391868,105.37182841304207,30.13902142215622,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
79,1,7,141,否,3x3_cluster_7,,3x3_cluster_7,3x3,,,,,,,2097.698743722085,0.04379969306937582,20.977031236913923,24.488493718610428,24.488493718610428,0.33419379132145205,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3146.5481155831276,0.20977031236913923,0.48976987437220854,0.6666666666666666,0.0013373501501964492,0.10488515618456962,0.19590794974888343,0.03333333333333333,0.00006686750750982247,31.384577146964165,58.62114703607561,9.97426718538251,0.020008631577706226,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.202241228059156,-97.93407128530188,-11.21002435577987,105.48055281485145,30.232105969000994,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
80,1,8,141,否,3x3_cluster_7,,3x3_cluster_7,3x3,,,,,,,2100.5915793081367,0.047452637157649434,21.005963245718526,24.502957896540686,24.502957896540686,0.3344597820266803,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3150.887368962205,0.21005963245718526,0.4900591579308137,0.6666666666666666,0.0014488866698102742,0.10502981622859263,0.1960236631723255,0.03333333333333333,0.00007244433349051371,31.402873147960054,58.609130717713256,9.966335997266757,0.02166013705993514,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-11.07978450384846,-97.76177048691049,-11.087563158669266,105.60192031549371,30.33575998834433,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
81,1,9,141,否,3x3_cluster_7,,3x3_cluster_7,3x3,,,,,,,2103.7838323271594,0.051106293746681665,21.03788942956534,24.518919161635797,24.518919161635797,0.33475272140007145,0.5,0.4,0.05,0.05,100,50,32.75110341367359,3155.6757484907394,0.2103788942956534,0.49037838323271593,0.6666666666666666,0.0015604449444394835,0.1051894471478267,0.1961513532930864,0.03333333333333333,0.00007802224722197418,31.42308279594192,58.59599401071722,9.957615726661844,0.023307466679010857,2362.332983098821,2.1200970177606626,23.62544992800597,11.917669766382136,0.25661398073452013,-10.944653129827037,-97.58943608153074,-10.952428446127906,105.73584972793752,30.44991564445965,139.76929345032994,35.67808346400486,10.890592689333397,139.7526327140971,35.689955867230225,10.232214328210436,改进分簇算法,2025-07-31T11:25:24.897839
