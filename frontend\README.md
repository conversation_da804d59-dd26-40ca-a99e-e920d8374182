# 🚁 无人机路径规划系统

基于Mapbox的3D城市无人机路径规划与飞行模拟系统。

## ✨ 系统特性

- **真实3D地图**: 基于Mapbox GL JS的高质量3D城市地图
- **多种算法**: 直线、沿路飞行、A*、RRT、Dijkstra等路径规划算法
- **实时飞行**: 无人机飞行模拟与进度跟踪
- **全球城市**: 支持全球主要城市的3D建筑数据
- **交互控制**: 鼠标点击设置起点终点，键盘控制飞行
- **可视化**: 路径线条、起点终点标记、飞行轨迹

## 🚀 快速开始

### 1. 获取Mapbox访问令牌（免费）

1. 访问 [Mapbox官网](https://account.mapbox.com/)
2. 注册免费账户（每月50,000次地图加载免费）
3. 在控制台创建访问令牌
4. 复制访问令牌

### 2. 配置系统

编辑 `config/mapbox-config.js` 文件：

```javascript
const MAPBOX_CONFIG = {
    // 将您的访问令牌粘贴到这里
    accessToken: 'pk.eyJ1IjoieW91ci11c2VybmFtZSIsImEiOiJjbGV4YW1wbGUifQ.example',
    // ... 其他配置
};
```

### 3. 启动系统

1. 启动本地服务器：
   ```bash
   python -m http.server 5000
   ```

2. 打开浏览器访问：
   ```
   http://localhost:5000/modern-city.html
   ```

## 🎮 操作指南

### 基本操作
- **鼠标拖拽**: 旋转地图视角
- **滚轮**: 缩放地图
- **鼠标点击**: 设置起点/终点（需先点击对应按钮）

### 路径规划流程
1. 选择城市位置
2. 点击"设置起点"按钮，然后点击地图设置起点
3. 点击"设置终点"按钮，然后点击地图设置终点
4. 选择路径算法
5. 点击"规划路径"生成飞行路径
6. 点击"开始飞行"启动无人机模拟

### 键盘快捷键
- **空格键**: 暂停/继续飞行

## 🛠️ 技术架构

### 前端技术
- **Mapbox GL JS**: 3D地图渲染引擎
- **Three.js**: 3D图形库（集成在Mapbox中）
- **原生JavaScript**: 系统逻辑和交互
- **CSS3**: 现代化UI样式

### 核心模块
- **ModernCityManager**: 城市管理和地图控制
- **PathPlanner**: 路径规划算法实现
- **无人机模拟**: 飞行动画和状态管理

## 📊 定价说明

### Mapbox免费额度
- **地图加载**: 每月50,000次免费
- **适用场景**: 开发测试、小型项目、演示使用
- **超出费用**: 每1000次加载$5.00

### 成本估算
- **开发阶段**: 完全免费
- **演示使用**: 免费额度充足
- **小规模部署**: 成本极低

## 🌍 支持的城市

- 东京, 日本
- 北京, 中国
- 上海, 中国
- 纽约, 美国
- 伦敦, 英国
- 巴黎, 法国
- 香港
- 新加坡
- 迪拜, 阿联酋
- 悉尼, 澳大利亚

## 🔧 系统要求

- **浏览器**: Chrome 60+, Firefox 60+, Safari 12+
- **WebGL**: 支持WebGL 2.0
- **网络**: 稳定的互联网连接（加载地图数据）

## 📝 版本历史

### v2.0.0 (当前版本)
- ✅ 集成Mapbox真实3D地图
- ✅ 多种路径规划算法
- ✅ 无人机飞行模拟
- ✅ 全球城市支持
- ✅ 现代化UI界面

### v1.0.0 (演示版)
- ✅ 基础3D城市生成
- ✅ 简单路径规划
- ✅ 无人机模型

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进系统！

## 📄 许可证

MIT License - 详见 LICENSE 文件

---

**🚁 享受您的无人机路径规划之旅！**
