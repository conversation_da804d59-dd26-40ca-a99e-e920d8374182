#!/usr/bin/env python3
"""
测试缓存键生成问题
分析为什么建筑物检测器缓存不工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from algorithms.data_structures import Point3D
from algorithms.improved_cluster_pathfinding import PathBasedBuildingDetector

def test_cache_key_generation():
    """测试缓存键生成问题"""
    
    print("🔍 测试建筑物检测器缓存键生成问题")
    print("=" * 60)
    
    # 创建建筑物检测器
    detector = PathBasedBuildingDetector()
    
    # 创建测试路径
    waypoints = [
        Point3D(lng=139.767300, lat=35.681200, alt=100.0, x=139.767300, y=35.681200, z=100),
        Point3D(lng=139.773400, lat=35.715300, alt=100.0, x=139.773400, y=35.715300, z=100)
    ]
    
    print(f"📍 测试路径：{len(waypoints)}个航点")
    print(f"   起点: ({waypoints[0].x:.6f}, {waypoints[0].y:.6f})")
    print(f"   终点: ({waypoints[-1].x:.6f}, {waypoints[-1].y:.6f})")
    
    # 1. 测试不同飞行高度的缓存键
    print(f"\n🔧 1. 测试不同飞行高度的缓存键")
    
    flight_heights = [100.0, 120.0, 100.0]  # 模拟实际调用序列
    cache_keys = []
    
    for i, height in enumerate(flight_heights):
        # 🔧 修复：手动生成缓存键（与实际检测器逻辑一致，不包含飞行高度）
        cache_key = f"{waypoints[0].x:.1f},{waypoints[0].y:.1f}-{waypoints[-1].x:.1f},{waypoints[-1].y:.1f}-{len(waypoints)}"
        cache_keys.append(cache_key)
        print(f"   调用{i+1}: 飞行高度={height}m, 缓存键={cache_key}")
    
    # 检查缓存键是否一致
    unique_keys = set(cache_keys)
    print(f"   缓存键数量: {len(cache_keys)}, 唯一键数量: {len(unique_keys)}")
    
    if len(unique_keys) == 1:
        print(f"   ✅ 缓存键一致，缓存应该工作")
    else:
        print(f"   ❌ 缓存键不一致，这就是缓存失效的原因！")
        for i, key in enumerate(unique_keys):
            print(f"      唯一键{i+1}: {key}")
    
    # 2. 测试实际检测调用
    print(f"\n🔧 2. 测试实际检测调用")
    
    # 清空缓存
    detector._cache.clear()
    print(f"   初始缓存大小: {len(detector._cache)}")
    
    # 第一次调用（飞行高度100m）
    print(f"   第一次调用: 飞行高度=100m")
    result1 = detector.detect_buildings_for_path(waypoints, flight_height=100.0)
    print(f"   第一次结果: {len(result1)}个建筑物")
    print(f"   缓存大小: {len(detector._cache)}")
    
    # 显示缓存内容
    if detector._cache:
        for key in detector._cache.keys():
            print(f"   缓存键: {key}")
    
    # 第二次调用（飞行高度120m - 模拟风险计算）
    print(f"   第二次调用: 飞行高度=120m")
    result2 = detector.detect_buildings_for_path(waypoints, flight_height=120.0)
    print(f"   第二次结果: {len(result2)}个建筑物")
    print(f"   缓存大小: {len(detector._cache)}")
    
    # 显示缓存内容
    if detector._cache:
        for key in detector._cache.keys():
            print(f"   缓存键: {key}")
    
    # 第三次调用（飞行高度100m - 应该命中第一次的缓存）
    print(f"   第三次调用: 飞行高度=100m")
    result3 = detector.detect_buildings_for_path(waypoints, flight_height=100.0)
    print(f"   第三次结果: {len(result3)}个建筑物")
    print(f"   缓存大小: {len(detector._cache)}")
    
    # 3. 分析缓存命中情况
    print(f"\n📊 缓存命中分析:")
    
    # 检查是否有缓存命中的迹象
    cache_hit_detected = False
    
    # 如果第三次调用的结果与第一次相同，且没有重新生成，说明命中了缓存
    if len(result1) == len(result3):
        print(f"   第一次和第三次结果相同: {len(result1)} == {len(result3)}")
        cache_hit_detected = True
    
    # 检查缓存大小变化
    if len(detector._cache) == 2:  # 应该有两个不同的缓存条目
        print(f"   缓存条目数量正确: 2个（100m和120m各一个）")
    else:
        print(f"   ❌ 缓存条目数量异常: {len(detector._cache)}个")
    
    # 4. 识别问题
    print(f"\n❗ 问题识别:")
    
    problems = []
    
    # 检查飞行高度导致的缓存键不一致
    if len(unique_keys) > 1:
        problems.append("飞行高度变化导致缓存键不一致")
    
    # 检查缓存是否真的在工作
    if len(detector._cache) == 0:
        problems.append("缓存完全没有工作")
    elif len(detector._cache) != 2:
        problems.append("缓存条目数量不符合预期")
    
    if problems:
        for i, problem in enumerate(problems, 1):
            print(f"   {i}. {problem}")
    else:
        print(f"   未发现明显问题")
    
    # 5. 修复建议
    print(f"\n💡 修复建议:")
    print(f"   1. 统一飞行高度参数，避免在同一路径计算中使用不同高度")
    print(f"   2. 修改缓存键生成策略，不包含飞行高度或使用标准化高度")
    print(f"   3. 实现缓存键标准化，确保相似请求使用相同缓存键")
    print(f"   4. 添加缓存调试日志，便于追踪缓存命中情况")
    
    return {
        'unique_cache_keys': len(unique_keys),
        'cache_entries': len(detector._cache),
        'problems_found': len(problems),
        'cache_keys': cache_keys
    }

if __name__ == "__main__":
    try:
        result = test_cache_key_generation()
        print(f"\n✅ 缓存键测试完成")
        print(f"   唯一缓存键数量: {result['unique_cache_keys']}")
        print(f"   缓存条目数量: {result['cache_entries']}")
        print(f"   发现问题数量: {result['problems_found']}")
    except Exception as e:
        print(f"\n❌ 缓存键测试失败: {e}")
        import traceback
        traceback.print_exc()
