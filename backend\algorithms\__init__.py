"""
路径规划算法模块
包含所有路径规划算法的实现和管理功能
"""

from .base import PathPlanningAlgorithm, AlgorithmInfo, AlgorithmParameter
from .data_structures import (
    PathPlanningRequest,
    PathPlanningResponse,
    PathPoint,
    Point3D,
    DroneSpecs,
    WeatherCondition
)
from .manager import AlgorithmManager, algorithm_manager
from .astar import AStarAlgorithm
from .rrt import RRTAlgorithm
from .improved_cluster_pathfinding import ImprovedClusterBasedPathfinding

__all__ = [
    'PathPlanningAlgorithm',
    'AlgorithmInfo',
    'AlgorithmParameter',
    'PathPlanningRequest',
    'PathPlanningResponse',
    'PathPoint',
    'Point3D',
    'DroneSpecs',
    'WeatherCondition',
    'AlgorithmManager',
    'algorithm_manager',
    'AStarAlgorithm',
    'RRTAlgorithm',
    'ImprovedClusterBasedPathfinding'
]

# 自动注册默认算法
def register_default_algorithms():
    """注册默认算法"""
    try:
        # 注册A*算法
        astar = AStarAlgorithm()
        algorithm_manager.register_algorithm(astar)

        # 注册RRT算法
        rrt = RRTAlgorithm()
        algorithm_manager.register_algorithm(rrt)

        # 注册改进分簇算法
        improved_cluster = ImprovedClusterBasedPathfinding()
        algorithm_manager.register_algorithm(improved_cluster)
        print("✅ 改进算法注册完成")

        print("✅ 默认算法注册完成")

    except Exception as e:
        print(f"❌ 默认算法注册失败: {str(e)}")

# 模块导入时自动注册算法
register_default_algorithms()
