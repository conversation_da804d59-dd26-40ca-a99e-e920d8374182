"""
路径规划算法模块
包含所有路径规划算法的实现和管理功能
"""

from .base import PathPlanningAlgorithm, AlgorithmInfo, AlgorithmParameter
from .data_structures import (
    PathPlanningRequest,
    PathPlanningResponse,
    PathPoint,
    Point3D,
    DroneSpecs,
    WeatherCondition
)
from .manager import AlgorithmManager, algorithm_manager
from .straight_line import StraightLineAlgorithm
from .astar import AStarAlgorithm
from .rrt import RRTAlgorithm
from .improved_cluster_pathfinding import ImprovedClusterBasedPathPlanning

__all__ = [
    'PathPlanningAlgorithm',
    'AlgorithmInfo',
    'AlgorithmParameter',
    'PathPlanningRequest',
    'PathPlanningResponse',
    'PathPoint',
    'Point3D',
    'DroneSpecs',
    'WeatherCondition',
    'AlgorithmManager',
    'algorithm_manager',
    'StraightLineAlgorithm',
    'AStarAlgorithm',
    'RRTAlgorithm',
    'ImprovedClusterBasedPathPlanning'
]

# 自动注册默认算法
def register_default_algorithms():
    """注册默认算法"""
    try:
        # 注册直线算法
        straight_line = StraightLineAlgorithm()
        algorithm_manager.register_algorithm(straight_line)

        # 注册A*算法
        astar = AStarAlgorithm()
        algorithm_manager.register_algorithm(astar)

        # 注册RRT算法
        rrt = RRTAlgorithm()
        algorithm_manager.register_algorithm(rrt)

        # 🔧 修复：注册改进分簇算法时设置A*算法实例
        improved_cluster = ImprovedClusterBasedPathPlanning()
        improved_cluster.set_astar_algorithm(astar)  # 设置共享的A*算法实例
        algorithm_manager.register_algorithm(improved_cluster)
        print("✅ 改进算法已设置共享A*算法实例")

        print("✅ 默认算法注册完成")

    except Exception as e:
        print(f"❌ 默认算法注册失败: {str(e)}")

# 模块导入时自动注册算法
register_default_algorithms()
