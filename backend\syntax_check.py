#!/usr/bin/env python3
"""
语法检查脚本
检查更新后的函数是否有语法错误
"""

import ast
import sys

def check_syntax(file_path):
    """检查文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        return True, "语法正确"
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"检查失败: {e}"

def main():
    """主函数"""
    print("=== 语法检查 ===")
    
    files_to_check = [
        'algorithms/improved_cluster_pathfinding.py',
        'algorithm_input_parameters.py',
        'variable_mapper.py',
        'function_variable_standards.py'
    ]
    
    all_passed = True
    
    for file_path in files_to_check:
        print(f"\n检查文件: {file_path}")
        passed, message = check_syntax(file_path)
        
        if passed:
            print(f"  ✅ {message}")
        else:
            print(f"  ❌ {message}")
            all_passed = False
    
    print(f"\n总结: {'✅ 所有文件语法正确' if all_passed else '❌ 存在语法错误'}")
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
