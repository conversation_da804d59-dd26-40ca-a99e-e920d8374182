import math

# 选中路径的数据
path_length = 6350.58
turning_cost = 904.7517
risk_value = 1.5925
collision_cost = 29.3439

# 参考值
risk_reference = 100.0
collision_reference = 50.0
turning_reference = 1000.0
risk_density = 0.1
k_value = 5.0

print("🔍 权重和代价分析")
print("=" * 50)

# 使用固定权重（按照您的要求）
alpha = 0.5   # 风险权重
beta = 0.4    # 碰撞权重
gamma = 0.05  # 长度权重
delta = 0.05  # 转向权重

print(f"权重 (风险密度={risk_density}):")
print(f"  α (风险): {alpha:.4f}")
print(f"  β (碰撞): {beta:.4f}")
print(f"  γ (长度): {gamma:.4f}")
print(f"  δ (转向): {delta:.4f}")
print(f"  总和: {alpha + beta + gamma + delta:.4f}")

# 计算各项代价
length_manhattan = path_length * 1.5
risk_term = alpha * (risk_value / max(risk_reference, 1.0))
collision_term = beta * (collision_cost / max(collision_reference, 1.0))
length_term = gamma * (path_length / max(length_manhattan, 1.0))
orient_term = delta * (turning_cost / max(turning_reference, 1.0))

final_cost = risk_term + collision_term + length_term + orient_term

print(f"\n各项代价计算:")
print(f"  风险项: {alpha:.4f} × ({risk_value:.4f} / {risk_reference:.1f}) = {risk_term:.6f}")
print(f"  碰撞项: {beta:.4f} × ({collision_cost:.4f} / {collision_reference:.1f}) = {collision_term:.6f}")
print(f"  长度项: {gamma:.4f} × ({path_length:.2f} / {length_manhattan:.2f}) = {length_term:.6f}")
print(f"  转向项: {delta:.4f} × ({turning_cost:.4f} / {turning_reference:.1f}) = {orient_term:.6f}")

print(f"\n最终代价: {final_cost:.6f}")

print(f"\n各项占比:")
print(f"  风险项: {risk_term/final_cost*100:.1f}%")
print(f"  碰撞项: {collision_term/final_cost*100:.1f}%")
print(f"  长度项: {length_term/final_cost*100:.1f}%")
print(f"  转向项: {orient_term/final_cost*100:.1f}%")

# 分析问题
print(f"\n🔍 问题分析:")
print(f"  转向成本 {turning_cost:.1f} 远大于碰撞代价 {collision_cost:.1f}")
print(f"  转向项 {orient_term:.6f} 占最终代价的 {orient_term/final_cost*100:.1f}%")
print(f"  碰撞项 {collision_term:.6f} 只占最终代价的 {collision_term/final_cost*100:.1f}%")

# 如果碰撞代价是最低的
min_collision_cost = 17.8254
min_collision_term = beta * (min_collision_cost / max(collision_reference, 1.0))
min_final_cost = risk_term + min_collision_term + length_term + orient_term

print(f"\n如果使用最低碰撞代价 {min_collision_cost:.4f}:")
print(f"  碰撞项: {min_collision_term:.6f}")
print(f"  最终代价: {min_final_cost:.6f}")
print(f"  代价差异: {final_cost - min_final_cost:.6f}")
print(f"  差异占比: {(final_cost - min_final_cost)/final_cost*100:.1f}%")
