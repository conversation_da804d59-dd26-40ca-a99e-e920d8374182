#!/usr/bin/env python3
"""
算法对比API
提供改进算法与基准算法的性能对比功能
"""

import asyncio
import json
import time
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict, field
from flask import Blueprint, request, jsonify

from .algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathfinding
from .algorithms.astar import AStarAlgorithm
from .algorithms.data_structures import PathPlanningRequest, Point3D


# 创建算法对比API蓝图
algorithm_comparison_bp = Blueprint('algorithm_comparison', __name__)

# 🔧 修复：使用文件存储替代全局变量，解决模块导入问题
import json
import os
from pathlib import Path

# 数据存储目录
DATA_STORAGE_DIR = Path(__file__).parent / "data_storage"
DATA_STORAGE_DIR.mkdir(exist_ok=True)

BASELINE_RESULT_FILE = DATA_STORAGE_DIR / "latest_baseline_result.json"
IMPROVED_RESULT_FILE = DATA_STORAGE_DIR / "latest_improved_result.json"

# 保留全局变量作为缓存（仅在当前进程中有效）
latest_baseline_result = None
latest_improved_result = None


@dataclass
class AlgorithmPerformanceMetrics:
    """算法性能指标"""
    path_length: float = 0.0          # 路径长度 (m)
    turning_cost: float = 0.0         # 转向成本 (rad)
    risk_value: float = 0.0           # 风险值
    collision_cost: float = 0.0       # 碰撞代价
    final_cost: float = 0.0           # 最终代价
    execution_time: float = 0.0       # 执行时间 (s)
    waypoint_count: int = 0           # 航点数量
    success: bool = False             # 是否成功
    path: List[Dict[str, float]] = field(default_factory=list)  # 🔧 新增：路径数据（用于前端可视化）

    # 🔧 新增：保护区信息字段
    protectionZonesInfo: Optional[Dict[str, Any]] = None  # 改进算法的保护区信息
    metadata: Optional[Dict[str, Any]] = None             # 基准算法的元数据（包含保护区信息）

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        # 🔧 修复：保留保护区信息字段，即使它们是None
        # 只移除其他None值，保持响应简洁
        filtered_result = {}
        for k, v in result.items():
            if k in ['protectionZonesInfo', 'metadata']:
                # 保护区信息字段总是包含，即使是None
                filtered_result[k] = v
            elif v is not None:
                # 其他字段只在非None时包含
                filtered_result[k] = v
        return filtered_result


@dataclass
class ComparisonResult:
    """对比结果"""
    improved_metrics: AlgorithmPerformanceMetrics
    baseline_metrics: AlgorithmPerformanceMetrics
    improvement_percentages: Dict[str, float]
    overall_improvement: float
    performance_rating: str
    summary: str
    timestamp: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class AlgorithmComparisonAPI:
    """算法对比API类"""
    
    def __init__(self):
        # 🔧 修复：创建统一的A*算法实例
        self.baseline_algorithm = AStarAlgorithm()

        # 创建改进算法实例
        self.improved_algorithm = ImprovedClusterBasedPathfinding()

        self.comparison_history: List[ComparisonResult] = []
        self.shared_environment_data = None  # 存储共享的环境数据

        # 🔧 新增：共享算法实例，避免多头运算
        self._shared_improved_instance = None
        self._shared_improved_result = None
        self._last_request_hash = None  # 用于检测请求是否变化

    def _generate_request_hash(self, request: PathPlanningRequest) -> str:
        """生成请求的哈希值，用于检测请求是否变化"""
        import hashlib
        request_str = f"{request.start_point}_{request.end_point}_{request.algorithm}_{getattr(request, 'flight_height', 100)}"
        return hashlib.md5(request_str.encode()).hexdigest()
    
    async def run_algorithm_comparison(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行算法对比
        
        Args:
            request_data: 路径规划请求数据
            
        Returns:
            对比结果
        """
        try:
            # 🔧 修复：转换前端数据格式为PathPlanningRequest期望的格式
            converted_data = request_data.copy()

            # 转换start -> startPoint
            if 'start' in converted_data and 'startPoint' not in converted_data:
                converted_data['startPoint'] = converted_data.pop('start')
                print(f"🔧 转换start -> startPoint: {converted_data['startPoint']}")

            # 转换end -> endPoint
            if 'end' in converted_data and 'endPoint' not in converted_data:
                converted_data['endPoint'] = converted_data.pop('end')
                print(f"🔧 转换end -> endPoint: {converted_data['endPoint']}")

            # 创建路径规划请求
            request = PathPlanningRequest(converted_data)
            print(f"🔧 创建请求对象: start=({request.start_point.lng:.6f}, {request.start_point.lat:.6f}), end=({request.end_point.lng:.6f}, {request.end_point.lat:.6f})")
            
            # 运行改进算法
            print("🚀 运行改进算法...")
            improved_metrics = await self._run_improved_algorithm(request)

            # 运行基准算法（使用改进算法的环境数据）
            print("🔄 运行基准算法...")
            baseline_metrics = await self._run_baseline_algorithm(request)
            
            # 计算对比结果
            comparison_result = self._calculate_comparison(improved_metrics, baseline_metrics)
            
            # 保存对比历史
            self.comparison_history.append(comparison_result)

            print(f"✅ 算法对比完成: {comparison_result.summary}")

            # 🔧 自动生成JSON文件供前端导出使用
            try:
                print("📊 自动生成JSON文件...")
                from export_all_paths_data import export_all_paths_data
                json_result = export_all_paths_data()
                if json_result:
                    print("✅ JSON文件生成成功")
                else:
                    print("⚠️ JSON文件生成失败")
            except Exception as e:
                print(f"⚠️ JSON文件生成失败: {e}")

            # 🔧 禁用自动导出，避免与手动导出混淆
            # try:
            #     await self._export_academic_data(request, improved_metrics, baseline_metrics)
            # except Exception as e:
            #     print(f"⚠️ 数据导出失败: {e}")
            print("📝 提示：如需导出数据，请使用前端的'📊 导出81条路径数据'按钮")

            # 🔧 新增：自动生成CSV文件
            try:
                print("📊 自动生成CSV文件...")
                csv_result = self._auto_generate_csv()
                if csv_result:
                    print(f"✅ CSV文件自动生成成功: {csv_result['filename']}")
                else:
                    print("⚠️ CSV文件自动生成失败")
            except Exception as e:
                print(f"⚠️ CSV文件自动生成失败: {e}")

            return {
                'success': True,
                'comparison': comparison_result.to_dict(),
                'improved_algorithm': improved_metrics.to_dict(),
                'baseline_algorithm': baseline_metrics.to_dict(),
                # 保持向后兼容
                'improved': improved_metrics.to_dict(),
                'baseline': baseline_metrics.to_dict()
            }
            
        except Exception as e:
            print(f"❌ 算法对比失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'comparison': None,
                'improved': None,
                'baseline': None
            }

    async def _export_academic_data(self, request: PathPlanningRequest,
                                  improved_metrics: AlgorithmPerformanceMetrics,
                                  baseline_metrics: AlgorithmPerformanceMetrics):
        """导出学术验证数据 - 🔧 修复：使用新的简单导出器"""
        try:
            # 检查是否存在simple_path_exporter模块
            try:
                from simple_path_exporter import export_comparison_data
            except ImportError:
                print("⚠️ simple_path_exporter模块未找到，跳过数据导出")
                return

            # 构建对比数据
            comparison_data = {
                'improved': {
                    'algorithmName': 'ImprovedClusterBased',
                    'paths': improved_metrics.paths if hasattr(improved_metrics, 'paths') else [],
                    'executionTime': improved_metrics.execution_time,
                    'metadata': {
                        'alpha': 0.35,
                        'beta': 0.25,
                        'gamma': 0.25,
                        'delta': 0.15,
                        'riskReference': improved_metrics.risk_value,
                        'collisionReference': improved_metrics.collision_cost,
                        'lengthReference': improved_metrics.path_length,
                        'turningReference': improved_metrics.turning_cost
                    }
                },
                'baseline': {
                    'algorithmName': 'Baseline_AStar',
                    'paths': baseline_metrics.paths if hasattr(baseline_metrics, 'paths') else [],
                    'executionTime': baseline_metrics.execution_time,
                    'metadata': {
                        'alpha': 0.35,
                        'beta': 0.25,
                        'gamma': 0.25,
                        'delta': 0.15,
                        'riskReference': baseline_metrics.risk_value,
                        'collisionReference': baseline_metrics.collision_cost,
                        'lengthReference': baseline_metrics.path_length,
                        'turningReference': baseline_metrics.turning_cost
                    }
                }
            }

            # 导出数据
            csv_file = export_comparison_data(comparison_data)
            if csv_file:
                print(f"✅ 学术验证数据已导出到: {csv_file}")
            else:
                print("⚠️ 学术验证数据导出失败")

            # 获取改进算法的详细数据（保留原有逻辑用于其他用途）
            improved_algorithm = self.improved_algorithm

            # 创建基准路径对象（模拟）
            class BaselinePath:
                def __init__(self):
                    self.waypoints = getattr(baseline_metrics, 'waypoints', [])
                    self.path_length = baseline_metrics.path_length
                    self.risk_value = baseline_metrics.risk_value
                    self.collision_cost = baseline_metrics.collision_cost
                    self.turning_cost = baseline_metrics.turning_cost
                    self.final_cost = baseline_metrics.final_cost
                    self.execution_time = baseline_metrics.execution_time
                    self.algorithm_name = 'Baseline_AStar'
                    self.protection_zones = getattr(improved_algorithm, 'protection_zones', [])
                    # 添加权重信息
                    self.weights = {
                        'alpha': 0.5,
                        'beta': 0.4,
                        'gamma': 0.05,
                        'delta': 0.05
                    }
                    # 添加参考值
                    self.risk_reference = getattr(improved_algorithm, 'risk_reference_value', 20.0)
                    self.collision_reference = getattr(improved_algorithm, 'collision_reference_value', 10.0)
                    self.turning_reference = getattr(improved_algorithm, 'turning_reference_value', 3.0)

            baseline_path = BaselinePath()

            # 获取81条改进路径
            improved_paths = getattr(improved_algorithm, 'initial_path_set', [])

            # 准备算法元数据
            algorithm_metadata = {
                'algorithm_name': 'ImprovedClusterBased',
                'baseline_algorithm': 'AStar',
                'initial_paths_count': len(improved_paths),
                'cluster_count': getattr(improved_algorithm, 'cluster_count', 9),
                'total_execution_time': improved_metrics.execution_time,
                'baseline_execution_time': baseline_metrics.execution_time,
                'flight_height': getattr(request, 'flight_height', 100.0),
                'safety_distance': 30.0,
                'alpha': 0.5,
                'beta': 0.4,
                'gamma': 0.05,
                'delta': 0.05,
                'weight_type': 'fixed',
                'risk_reference': getattr(improved_algorithm, 'risk_reference_value', 0.0),
                'collision_reference': getattr(improved_algorithm, 'collision_reference_value', 0.0),
                'turning_reference': getattr(improved_algorithm, 'turning_reference_value', 0.0),
                'buildings_count': len(getattr(improved_algorithm, 'buildings', [])),
                'protection_zones_count': len(getattr(improved_algorithm, 'protection_zones', [])),
                'comparison_summary': {
                    'improved_final_cost': improved_metrics.final_cost,
                    'baseline_final_cost': baseline_metrics.final_cost,
                    'improvement_percentage': ((baseline_metrics.final_cost - improved_metrics.final_cost) / max(baseline_metrics.final_cost, 0.001)) * 100
                }
            }

            # 导出数据
            export_files = exporter.export_comprehensive_data(
                baseline_path=baseline_path,
                improved_paths=improved_paths,
                algorithm_metadata=algorithm_metadata,
                start_point={
                    'lng': request.start_point.lng,
                    'lat': request.start_point.lat,
                    'alt': getattr(request.start_point, 'alt', 100.0)
                },
                end_point={
                    'lng': request.end_point.lng,
                    'lat': request.end_point.lat,
                    'alt': getattr(request.end_point, 'alt', 100.0)
                }
            )

            print(f"📊 学术验证数据已自动导出到csv文件夹")
            print(f"📄 导出报告: {export_files.get('report', '')}")

        except Exception as e:
            print(f"⚠️ 数据导出失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def _run_improved_algorithm(self, request: PathPlanningRequest) -> AlgorithmPerformanceMetrics:
        """运行改进算法"""
        # 🔧 修复：检查是否可以复用已有结果，避免多头运算
        current_request_hash = self._generate_request_hash(request)

        if (self._last_request_hash == current_request_hash and
            self._shared_improved_result is not None):
            print("🔄 复用已有的改进算法结果，避免重复运算")
            return self._shared_improved_result

        start_time = time.time()

        try:
            response = await self.improved_algorithm.calculate_path(request)
            execution_time = time.time() - start_time

            # 🔧 修复：确保基准算法能获取到建筑物数据
            buildings_data = None

            # 尝试多种方式获取建筑物数据
            if hasattr(response, 'buildings') and response.buildings:
                buildings_data = response.buildings
                print(f"🏗️ 从响应获取建筑物数据: {len(buildings_data)} 个建筑物")
            elif hasattr(self.improved_algorithm, 'buildings') and self.improved_algorithm.buildings:
                buildings_data = self.improved_algorithm.buildings
                print(f"🏗️ 从算法实例获取建筑物数据: {len(buildings_data)} 个建筑物")
            elif hasattr(self.improved_algorithm, '_buildings_data') and self.improved_algorithm._buildings_data:
                buildings_data = self.improved_algorithm._buildings_data
                print(f"🏗️ 从算法内部数据获取建筑物: {len(buildings_data)} 个建筑物")
            elif request.buildings:
                buildings_data = request.buildings
                print(f"🏗️ 从请求获取建筑物数据: {len(buildings_data)} 个建筑物")
            else:
                print("⚠️ 未找到建筑物数据，生成基础建筑物用于基准算法")
                # 生成基础建筑物数据
                buildings_data = self._generate_basic_buildings_for_baseline(request)
                print(f"🏗️ 生成基础建筑物: {len(buildings_data)} 个建筑物")

            # 保存共享环境数据
            self.shared_environment_data = {
                'buildings': buildings_data,
                'traffic_data': getattr(response, 'traffic_data', getattr(self.improved_algorithm, 'traffic_data', [])),
                'path_waypoints': response.path if response.success else []
            }

            print(f"✅ 共享环境数据设置完成: {len(self.shared_environment_data['buildings'])} 个建筑物")

            if response.success and response.path:
                # 从响应中提取性能指标
                path_length = getattr(response, 'path_length', 0.0)
                if path_length == 0.0:
                    path_length = self._calculate_path_length(response.path)
                
                turning_cost = getattr(response, 'turning_cost', 0.0)
                if turning_cost == 0.0:
                    turning_cost = self._calculate_turning_cost(response.path)
                
                risk_value = getattr(response, 'risk_value', 0.0)
                collision_cost = getattr(response, 'collision_cost', 0.0)

                # 🔧 重要修复：设置estimated_collision_cost，确保data_structures.py能正确识别
                if hasattr(response, 'collision_cost') and response.collision_cost > 0:
                    response.estimated_collision_cost = response.collision_cost
                    print(f"🔧 基准算法设置estimated_collision_cost = {response.collision_cost}")

                # 从响应中提取最终代价
                final_cost = getattr(response, 'final_cost', 0.0)
                
                # 如果响应中没有最终代价，则使用与基准算法相同的方法计算
                if final_cost == 0.0:
                    # 计算风险密度
                    risk_density = risk_value / max(path_length, 1.0)  # 避免除零
                    
                    # 使用固定权重（按照您的要求）
                    alpha = 0.5   # 风险权重
                    beta = 0.4    # 碰撞权重
                    gamma = 0.05  # 长度权重
                    delta = 0.05  # 转向权重
                    
                    # 计算参考值 - 使用与基准算法一致的方法
                    manhattan_length = self._calculate_manhattan_distance(
                        request.start_point, request.end_point
                    )

                    # 🔧 修复：使用固定参考值确保算法对比的公平性
                    risk_reference = 100.0      # 固定风险参考值
                    collision_reference = 50.0  # 固定碰撞代价参考值
                    turning_reference = 30.0    # 固定转向成本参考值
                    
                    # 计算最终代价 - 公式14
                    final_cost = (
                        alpha * (risk_value / risk_reference) +
                        beta * (collision_cost / collision_reference) +
                        gamma * (path_length / manhattan_length) +
                        delta * (turning_cost / turning_reference)
                    )
                    
                    print(f"📊 改进算法最终代价计算: final_cost = {final_cost:.6f}")
                    print(f"  📏 路径长度: {path_length:.2f}m, 转向成本: {turning_cost:.4f}")
                    print(f"  ⚠️ 风险值: {risk_value:.4f}, 碰撞代价: {collision_cost:.4f}")
                    print(f"  ⚖️ 权重: alpha={alpha:.4f}, beta={beta:.4f}, gamma={gamma:.4f}, delta={delta:.4f}")
                    print(f"  📐 参考值: risk_ref={risk_reference:.2f}, collision_ref={collision_reference:.2f}, turning_ref={turning_reference:.2f}")
                    print(f"  🧮 计算项: risk_term={alpha * (risk_value / risk_reference):.6f}, collision_term={beta * (collision_cost / collision_reference):.6f}")
                    print(f"           length_term={gamma * (path_length / manhattan_length):.6f}, turning_term={delta * (turning_cost / turning_reference):.6f}")
                
                # 🔧 修复：创建路径数据用于前端可视化
                path_data = [{'lng': p.lng, 'lat': p.lat, 'alt': getattr(p, 'alt', 100.0)} for p in response.path]

                # 🔧 提取保护区信息
                protection_zones_info = None
                updated_collision_cost = collision_cost  # 默认使用原始值

                if hasattr(response, 'protectionZonesInfo') and response.protectionZonesInfo:
                    protection_zones_info = response.protectionZonesInfo
                    print(f"🛡️ 改进算法提取到保护区信息: {len(protection_zones_info.get('collision_cost_breakdown', {}))} 个活跃保护区")

                    # 🔧 确保保护区信息被包含在结果数据中
                    if 'protectionZonesInfo' not in improved_result_data:
                        improved_result_data['protectionZonesInfo'] = protection_zones_info

                    # 🔧 重要修复：使用新保护区系统的碰撞代价
                    if 'total_estimated_collision_cost' in protection_zones_info:
                        updated_collision_cost = protection_zones_info['total_estimated_collision_cost']
                        print(f"🔧 使用新保护区系统碰撞代价: {updated_collision_cost:.2f} (原值: {collision_cost:.2f})")
                    elif 'collision_cost_breakdown' in protection_zones_info:
                        # 从breakdown中计算总代价
                        breakdown = protection_zones_info['collision_cost_breakdown']
                        if isinstance(breakdown, dict):
                            updated_collision_cost = sum(info.get('total_cost', 0) for info in breakdown.values())
                            print(f"🔧 从breakdown计算碰撞代价: {updated_collision_cost:.2f} (原值: {collision_cost:.2f})")
                else:
                    print(f"⚠️ 改进算法响应中没有保护区信息")

                # 创建并返回指标，包含最终代价和路径数据
                metrics = AlgorithmPerformanceMetrics(
                    path_length=path_length,
                    turning_cost=turning_cost,
                    risk_value=risk_value,
                    collision_cost=updated_collision_cost,  # 🔧 使用更新后的碰撞代价
                    final_cost=final_cost,  # 添加最终代价
                    execution_time=execution_time,
                    waypoint_count=len(response.path),
                    success=True,
                    path=path_data,  # 🔧 新增：包含路径数据
                    protectionZonesInfo=protection_zones_info  # 🔧 新增：保护区信息
                )

                # 🔧 获取选中路径的信息
                selected_path_id = None
                if hasattr(self.improved_algorithm, 'current_path') and self.improved_algorithm.current_path:
                    # 找到current_path在initial_path_set中的索引
                    if hasattr(self.improved_algorithm, 'initial_path_set') and self.improved_algorithm.initial_path_set:
                        for i, path in enumerate(self.improved_algorithm.initial_path_set):
                            if path == self.improved_algorithm.current_path:
                                selected_path_id = i + 1  # 路径ID从1开始
                                break

                # 🔧 新增：保存改进算法结果供路径导出使用
                improved_result_data = {
                    'path_length': path_length,
                    'turning_cost': turning_cost,
                    'risk_value': risk_value,
                    'collision_cost': collision_cost,
                    'final_cost': final_cost,
                    'execution_time': execution_time,
                    'path': [{'lng': p.lng, 'lat': p.lat, 'alt': getattr(p, 'alt', 100.0)} for p in response.path],
                    'waypoint_count': len(response.path),
                    'success': True,
                    'algorithm': '改进分簇算法',
                    'timestamp': time.time(),
                    'selected_path_id': selected_path_id,  # 🔧 添加选中路径ID
                    'initial_path_set': None  # 将在下面尝试获取
                }

                # 尝试获取初始路径集数据
                print(f"🔍 检查改进算法是否有initial_path_set属性: {hasattr(self.improved_algorithm, 'initial_path_set')}")
                if hasattr(self.improved_algorithm, 'initial_path_set'):
                    print(f"🔍 initial_path_set长度: {len(self.improved_algorithm.initial_path_set) if self.improved_algorithm.initial_path_set else 0}")

                if hasattr(self.improved_algorithm, 'initial_path_set') and self.improved_algorithm.initial_path_set:
                    print(f"✅ 保存改进算法的初始路径集数据: {len(self.improved_algorithm.initial_path_set)} 条路径")
                    improved_result_data['initial_path_set'] = []

                    # 🔧 修复：先按最终代价排序，获取正确的排名
                    sorted_paths = sorted(self.improved_algorithm.initial_path_set, key=lambda p: getattr(p, 'final_cost', float('inf')))

                    for i, path in enumerate(self.improved_algorithm.initial_path_set):
                        try:
                            # 🔧 关键修复：路径索引编号 vs 排序序号
                            path_index = i + 1  # 路径索引编号：按生成顺序（1-81）

                            # 🔧 计算排序序号：按最终代价排序后的排名
                            rank_by_cost = 1
                            for j, sorted_path in enumerate(sorted_paths):
                                if sorted_path == path:
                                    rank_by_cost = j + 1
                                    break

                            path_data = {
                                'path_id': path_index,  # 路径索引编号（按论文要求：1-81）
                                'path_index': path_index,  # 路径索引编号
                                'rank': rank_by_cost,  # 🔧 排序序号：按最终代价排序（1-81）
                                'original_path_id': getattr(path, 'path_id', i),  # 保留原始路径ID
                                'flight_direction': getattr(path, 'flight_direction', ''),
                                'height_layer': getattr(path, 'height_layer', ''),
                                'cluster_id': getattr(path, 'cluster_id', ''),
                                'cluster_type': getattr(path, 'cluster_type', ''),
                                'cluster_3x3_ids': getattr(path, 'cluster_3x3_ids', ''),  # 🔧 添加3x3簇ID
                                'cluster_4x4_ids': getattr(path, 'cluster_4x4_ids', ''),  # 🔧 添加4x4簇ID
                                'path_length': getattr(path, 'path_length', 0.0),
                                'turning_cost': getattr(path, 'turning_cost', 0.0),
                                'risk_value': getattr(path, 'risk_value', 0.0),
                                'collision_cost': getattr(path, 'collision_cost', 0.0),
                                'final_cost': getattr(path, 'final_cost', 0.0),
                                'waypoint_count': len(getattr(path, 'waypoints', [])),
                                'generation_time': datetime.now().isoformat()
                            }
                            improved_result_data['initial_path_set'].append(path_data)
                        except Exception as e:
                            print(f"⚠️ 处理路径 {i+1} 时出错: {e}")
                else:
                    print("⚠️ 改进算法没有initial_path_set数据，将保存空的初始路径集")
                    improved_result_data['initial_path_set'] = []

                set_latest_improved_result(improved_result_data)
                print("✅ 已保存改进算法结果供路径导出使用")

                # 🔍 调试：验证保存是否成功
                saved_improved = get_latest_improved_result()
                print(f"🔍 调试：改进算法保存验证 - 成功: {saved_improved is not None}")
                if saved_improved:
                    print(f"🔍 调试：改进算法保存数据 - 成功: {saved_improved.get('success', False)}, 路径长度: {saved_improved.get('path_length', 0)}")

                # 🔧 保存结果到共享缓存
                self._shared_improved_result = metrics
                self._last_request_hash = current_request_hash
                print("🔄 已缓存改进算法结果，避免重复运算")
            else:
                metrics = AlgorithmPerformanceMetrics(
                    execution_time=execution_time,
                    success=False
                )

            return metrics
            
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"改进算法执行失败: {e}")
            return AlgorithmPerformanceMetrics(
                execution_time=execution_time,
                success=False
            )
    
    async def _run_baseline_algorithm(self, request: PathPlanningRequest) -> AlgorithmPerformanceMetrics:
        """运行基准算法（使用改进算法的环境数据）"""
        print("🔄 开始运行基准算法...")
        start_time = time.time()

        try:
            # 如果有共享的环境数据，将其设置到基准算法中
            if self.shared_environment_data and self.shared_environment_data.get('buildings'):
                buildings = self.shared_environment_data['buildings']
                print(f"🔄 基准算法使用共享环境数据: {len(buildings)} 个建筑物")

                # 将建筑物数据设置到基准算法中
                if hasattr(self.baseline_algorithm, 'set_buildings'):
                    self.baseline_algorithm.set_buildings(buildings)
                elif hasattr(self.baseline_algorithm, 'buildings'):
                    self.baseline_algorithm.buildings = buildings

                # 🔧 修复：创建基准算法专用的坐标转换请求
                enhanced_request = PathPlanningRequest({
                    'startPoint': {
                        'lng': request.start_point.lng,
                        'lat': request.start_point.lat,
                        'alt': request.start_point.alt
                    },
                    'endPoint': {
                        'lng': request.end_point.lng,
                        'lat': request.end_point.lat,
                        'alt': request.end_point.alt
                    },
                    'algorithm': 'AStar',
                    'buildings': buildings,  # 添加建筑物数据
                    'traffic_data': self.shared_environment_data.get('traffic_data', []),
                    'flightHeight': request.flight_height,  # 添加飞行高度
                    'safetyDistance': request.safety_distance,  # 添加安全距离
                    'parameters': {
                        'gridSize': 10,  # 🔧 修复：使用更精细的网格大小（10米而不是50米）
                        'maxIterations': 10000,  # 增加迭代次数以确保找到路径
                        'heuristicWeight': 1.0,
                        'allowDiagonal': True,
                        'smoothPath': True
                    }
                })

                print(f"🔄 基准算法参数: gridSize=10m, buildings={len(buildings)}, safety={request.safety_distance}m")

                # 🔧 详细调试信息
                print(f"🔍 基准算法调用详细调试:")
                print(f"   - 起点: lng={enhanced_request.start_point.lng:.6f}, lat={enhanced_request.start_point.lat:.6f}, alt={enhanced_request.start_point.alt}")
                print(f"   - 终点: lng={enhanced_request.end_point.lng:.6f}, lat={enhanced_request.end_point.lat:.6f}, alt={enhanced_request.end_point.alt}")
                print(f"   - 建筑物数量: {len(enhanced_request.buildings) if enhanced_request.buildings else 0}")
                print(f"   - 飞行高度: {getattr(enhanced_request, 'flight_height', 'None')}")
                print(f"   - 安全距离: {getattr(enhanced_request, 'safety_distance', 'None')}")
                print(f"   - 网格大小: {enhanced_request.parameters.get('gridSize', 'None')}")
                print(f"   - 最大迭代: {enhanced_request.parameters.get('maxIterations', 'None')}")
                print(f"   - 允许对角: {enhanced_request.parameters.get('allowDiagonal', 'None')}")
                print(f"   - 路径平滑: {enhanced_request.parameters.get('smoothPath', 'None')}")

                if enhanced_request.buildings:
                    print(f"   - 建筑物样例: {enhanced_request.buildings[0]}")

                print(f"🔄 调用基准算法...")
                response = await self.baseline_algorithm.calculate_path(enhanced_request)
                print(f"🔄 基准算法响应: success={response.success}, path_length={len(response.path) if response.path else 0}")

                # 简化响应调试信息
                if not response.success and hasattr(response, 'error'):
                    print(f"   - 错误: {response.error}")
                elif response.path:
                    print(f"   - 路径点数: {len(response.path)}")
            else:
                print("⚠️ 基准算法使用原始请求（无共享环境数据）")
                print(f"🔄 调用基准算法，原始请求: start={request.start_point}, end={request.end_point}")
                response = await self.baseline_algorithm.calculate_path(request)
                print(f"🔄 基准算法响应: success={response.success}, path_length={len(response.path) if response.path else 0}")

            execution_time = time.time() - start_time

            # 🔧 添加详细调试信息
            print(f"🔍 基准算法执行结果调试:")
            print(f"   response.success: {response.success}")
            print(f"   response.path 存在: {response.path is not None}")
            print(f"   response.path 长度: {len(response.path) if response.path else 0}")
            print(f"   执行时间: {execution_time:.3f}秒")

            if hasattr(response, 'error_message'):
                print(f"   错误信息: {response.error_message}")

            if response.success and response.path:
                print("✅ 基准算法执行成功，开始计算性能指标...")

                # 🔧 新增：为基准算法收集保护区信息
                try:
                    from protection_zones import ProtectionZoneManager
                    protection_manager = ProtectionZoneManager()

                    # 提取路径点坐标
                    path_points = [(point.lng, point.lat) for point in response.path]

                    # 获取相关保护区
                    relevant_zones = protection_manager.get_zones_for_path(path_points, buffer_distance=500)

                    # 计算碰撞代价
                    collision_cost_breakdown = {}
                    total_collision_cost = 0.0

                    for i, (lng, lat) in enumerate(path_points):
                        for zone in relevant_zones:
                            distance = zone.get_distance_to_point(lng, lat)

                            if distance <= zone.radius:
                                collision_cost = zone.get_collision_cost(lng, lat)

                                if collision_cost > 0:
                                    total_collision_cost += collision_cost

                                    if zone.id not in collision_cost_breakdown:
                                        collision_cost_breakdown[zone.id] = {
                                            'zone_name': zone.name,
                                            'zone_type': zone.zone_type.value,
                                            'total_cost': 0.0,
                                            'average_crash_cost': zone.average_crash_cost,
                                            'waypoints_affected': []
                                        }

                                    collision_cost_breakdown[zone.id]['total_cost'] += collision_cost
                                    collision_cost_breakdown[zone.id]['waypoints_affected'].append(i)

                    # 保存基准算法的保护区信息
                    self.baseline_protection_zones_info = {
                        'collision_cost_breakdown': collision_cost_breakdown,
                        'active_zones': len(collision_cost_breakdown),
                        'total_collision_cost': total_collision_cost,
                        'relevant_zones': len(relevant_zones)
                    }

                except Exception as e:
                    print(f"⚠️ 基准算法保护区信息收集失败: {e}")
                    self.baseline_protection_zones_info = None

                # 🔧 修复：转换路径格式 - PathPoint转Point3D
                converted_path = []
                for point in response.path:
                    if hasattr(point, 'lng') and hasattr(point, 'lat') and hasattr(point, 'alt'):
                        # PathPoint格式
                        converted_point = Point3D(
                            lng=point.lng, lat=point.lat, alt=point.alt,
                            x=getattr(point, 'x', point.lng * 111320),  # 简化坐标转换
                            y=getattr(point, 'y', point.lat * 110540),
                            z=point.alt
                        )
                    else:
                        # 已经是Point3D格式或其他格式
                        converted_point = point
                    converted_path.append(converted_point)

                print(f"🔄 路径转换完成: {len(response.path)} -> {len(converted_path)} 个点")

                # 计算性能指标
                path_length = self._calculate_path_length(converted_path)
                turning_cost = self._calculate_turning_cost(converted_path)
                risk_value = self._estimate_risk_value(converted_path)
                collision_cost = self._estimate_collision_cost(converted_path)
                
                # 计算最终代价 - 使用公式14和公式15
                # 计算风险密度 (RiskDensity = RiskSum / Length)
                risk_density = risk_value / max(path_length, 1.0)  # 避免除零
                
                # 使用固定权重（按照您的要求）
                alpha = 0.5   # 风险权重
                beta = 0.4    # 碰撞权重
                gamma = 0.05  # 长度权重
                delta = 0.05  # 转向权重
                
                # 计算参考值 - 使用与改进算法一致的方法
                manhattan_length = self._calculate_manhattan_distance(
                    request.start_point, request.end_point
                )

                # 🔧 修复：使用固定参考值确保算法对比的公平性
                risk_reference = 100.0      # 固定风险参考值
                collision_reference = 50.0  # 固定碰撞代价参考值
                turning_reference = 30.0    # 固定转向成本参考值
                
                # 计算最终代价 - 公式14
                final_cost = (
                    alpha * (risk_value / risk_reference) +
                    beta * (collision_cost / collision_reference) +
                    gamma * (path_length / manhattan_length) +
                    delta * (turning_cost / turning_reference)
                )
                
                print(f"📊 基准算法最终代价计算: final_cost = {final_cost:.6f}")
                print(f"  📏 路径长度: {path_length:.2f}m, 转向成本: {turning_cost:.4f}")
                print(f"  ⚠️ 风险值: {risk_value:.4f}, 碰撞代价: {collision_cost:.4f}")
                print(f"  ⚖️ 权重: alpha={alpha:.4f}, beta={beta:.4f}, gamma={gamma:.4f}, delta={delta:.4f}")
                print(f"  📐 参考值: risk_ref={risk_reference:.2f}, collision_ref={collision_reference:.2f}, turning_ref={turning_reference:.2f}")
                print(f"  🧮 计算项: risk_term={alpha * (risk_value / risk_reference):.6f}, collision_term={beta * (collision_cost / collision_reference):.6f}")
                print(f"           length_term={gamma * (path_length / manhattan_length):.6f}, turning_term={delta * (turning_cost / turning_reference):.6f}")
                
                # 🔧 修复：创建路径数据用于前端可视化
                path_data = [{'lng': p.lng, 'lat': p.lat, 'alt': getattr(p, 'alt', 120.0)} for p in response.path]

                # 🔧 提取基准算法的保护区信息
                metadata_info = None
                updated_collision_cost = collision_cost  # 默认使用原始值

                if hasattr(self, 'baseline_protection_zones_info') and self.baseline_protection_zones_info:
                    metadata_info = {
                        'protection_zones': {
                            'collision_cost_breakdown': self.baseline_protection_zones_info['collision_cost_breakdown'],
                            'active_zones': self.baseline_protection_zones_info['active_zones'],
                            'active_zone_ids': list(self.baseline_protection_zones_info['collision_cost_breakdown'].keys())
                        }
                    }
                    print(f"🛡️ 基准算法提取到保护区信息: {len(self.baseline_protection_zones_info['collision_cost_breakdown'])} 个活跃保护区")

                    # 🔧 重要修复：使用新保护区系统的碰撞代价
                    if 'total_collision_cost' in self.baseline_protection_zones_info:
                        updated_collision_cost = self.baseline_protection_zones_info['total_collision_cost']
                        print(f"🔧 基准算法使用新保护区系统碰撞代价: {updated_collision_cost:.2f} (原值: {collision_cost:.2f})")
                    elif 'collision_cost_breakdown' in self.baseline_protection_zones_info:
                        # 从breakdown中计算总代价
                        breakdown = self.baseline_protection_zones_info['collision_cost_breakdown']
                        if isinstance(breakdown, dict):
                            updated_collision_cost = sum(info.get('total_cost', 0) for info in breakdown.values())
                            print(f"🔧 基准算法从breakdown计算碰撞代价: {updated_collision_cost:.2f} (原值: {collision_cost:.2f})")
                else:
                    print(f"⚠️ 基准算法没有保护区信息")

                # 创建并返回指标，包含最终代价和路径数据
                metrics = AlgorithmPerformanceMetrics(
                    path_length=path_length,
                    turning_cost=turning_cost,
                    risk_value=risk_value,
                    collision_cost=updated_collision_cost,  # 🔧 使用更新后的碰撞代价
                    final_cost=final_cost,  # 添加最终代价
                    execution_time=execution_time,
                    waypoint_count=len(response.path),
                    success=True,
                    path=path_data,  # 🔧 新增：包含路径数据
                    metadata=metadata_info  # 🔧 新增：基准算法的保护区信息
                )
                
                # 添加计算过程到metadata
                if not hasattr(response, 'metadata'):
                    response.metadata = {}
                
                response.metadata['calculations'] = {
                    'path_length': path_length,
                    'turning_cost': turning_cost,
                    'risk_value': risk_value,
                    'collision_cost': collision_cost,
                    'final_cost': final_cost,
                    'weights': {
                        'alpha': alpha,
                        'beta': beta,
                        'gamma': gamma,
                        'delta': delta
                    },
                    'reference_values': {
                        'manhattan_length': manhattan_length,
                        'risk_reference': risk_reference,
                        'collision_reference': collision_reference,
                        'turning_reference': turning_reference
                    }
                }
                
                # 将最终代价添加到响应中
                response.final_cost = final_cost

                # 🔧 新增：保存基准算法结果供路径导出使用
                baseline_result_data = {
                    'path_length': path_length,
                    'turning_cost': turning_cost,
                    'risk_value': risk_value,
                    'collision_cost': updated_collision_cost,  # 🔧 使用更新后的碰撞代价
                    'final_cost': final_cost,
                    'execution_time': execution_time,
                    'path': path_data,  # 🔧 使用已创建的路径数据
                    'waypoint_count': len(response.path),
                    'success': True,
                    'algorithm': 'A*基准算法',
                    'timestamp': time.time()
                }

                # 🔧 添加保护区信息（如果存在）
                if metadata_info:
                    baseline_result_data['metadata'] = metadata_info
                    print(f"🔄 基准算法保护区信息已添加到结果中")
                set_latest_baseline_result(baseline_result_data)
                print("✅ 已保存基准算法结果供路径导出使用")

                # 🔍 调试：验证保存是否成功
                saved_baseline = get_latest_baseline_result()
                print(f"🔍 调试：基准算法保存验证 - 成功: {saved_baseline is not None}")
                if saved_baseline:
                    print(f"🔍 调试：基准算法保存数据 - 成功: {saved_baseline.get('success', False)}, 路径长度: {saved_baseline.get('path_length', 0)}")

                return metrics
            else:
                print("❌ 基准算法执行失败或没有生成路径")
                print(f"   原因: success={response.success}, path={'存在' if response.path else '不存在'}")
                if hasattr(response, 'error_message'):
                    print(f"   错误详情: {response.error_message}")

                # 🔧 即使失败也保存一个空的基准算法结果，避免导出时找不到数据
                baseline_result_data = {
                    'path_length': 0.0,
                    'turning_cost': 0.0,
                    'risk_value': 0.0,
                    'collision_cost': 0.0,
                    'final_cost': 0.0,
                    'execution_time': execution_time,
                    'path': [],
                    'waypoint_count': 0,
                    'success': False,
                    'algorithm': 'A*基准算法(失败)',
                    'timestamp': time.time(),
                    'error': '基准算法执行失败'
                }
                set_latest_baseline_result(baseline_result_data)
                print("⚠️ 已保存失败的基准算法结果供调试使用")

                metrics = AlgorithmPerformanceMetrics(
                    execution_time=execution_time,
                    success=False
                )

            return metrics
            
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"❌ 基准算法执行异常: {e}")
            import traceback
            traceback.print_exc()

            # 🔧 即使异常也保存一个错误的基准算法结果
            baseline_result_data = {
                'path_length': 0.0,
                'turning_cost': 0.0,
                'risk_value': 0.0,
                'collision_cost': 0.0,
                'final_cost': 0.0,
                'execution_time': execution_time,
                'path': [],
                'waypoint_count': 0,
                'success': False,
                'algorithm': 'A*基准算法(异常)',
                'timestamp': time.time(),
                'error': str(e)
            }
            set_latest_baseline_result(baseline_result_data)
            print("⚠️ 已保存异常的基准算法结果供调试使用")

            return AlgorithmPerformanceMetrics(
                execution_time=execution_time,
                success=False
            )
            
    def _calculate_path_length(self, path: List[Point3D]) -> float:
        """计算路径长度 - 🔧 修复：正确处理经纬度到米的转换"""
        if len(path) < 2:
            return 0.0

        total_length = 0.0
        for i in range(len(path) - 1):
            p1 = path[i]
            p2 = path[i + 1]

            # 🔧 修复：检查是否使用经纬度坐标
            if hasattr(p1, 'lng') and hasattr(p1, 'lat') and p1.lng != 0 and p1.lat != 0:
                # 使用经纬度计算地理距离
                import math

                # 经纬度转米的转换系数
                lat_to_meters = 111320.0
                lng_to_meters = 111320.0 * math.cos(math.radians((p1.lat + p2.lat) / 2))

                dx_meters = abs(p2.lng - p1.lng) * lng_to_meters
                dy_meters = abs(p2.lat - p1.lat) * lat_to_meters
                dz_meters = abs(p2.alt - p1.alt) if hasattr(p1, 'alt') else abs(p2.z - p1.z)

                # 使用欧几里得距离（基准算法特征）
                segment_length = math.sqrt(dx_meters*dx_meters + dy_meters*dy_meters + dz_meters*dz_meters)
            else:
                # 使用原有的坐标计算（假设已经是米制）
                dx = p2.x - p1.x
                dy = p2.y - p1.y
                dz = p2.z - p1.z
                segment_length = (dx*dx + dy*dy + dz*dz) ** 0.5

            total_length += segment_length

        return total_length
    
    def _calculate_turning_cost(self, path: List[Point3D]) -> float:
        """
        计算转向成本 - 严格按照论文公式3
        公式(3): OrientAdjustCost = Σ Δθ
        其中Δθ是目标航点的前一个航点与目标航点连线的延长线与目标航点与后一个航点的连线的夹角
        """
        if len(path) < 3:
            return 0.0

        import math
        total_turning = 0.0

        # 计算转向成本（论文公式3）

        for i in range(1, len(path) - 1):
            # 计算前一段和后一段的方向向量
            v1_x = path[i].x - path[i-1].x
            v1_y = path[i].y - path[i-1].y
            v2_x = path[i+1].x - path[i].x
            v2_y = path[i+1].y - path[i].y

            # 计算向量长度
            len1 = (v1_x*v1_x + v1_y*v1_y) ** 0.5
            len2 = (v2_x*v2_x + v2_y*v2_y) ** 0.5

            if len1 > 0 and len2 > 0:
                # 计算夹角
                cos_angle = (v1_x*v2_x + v1_y*v2_y) / (len1 * len2)
                cos_angle = max(-1, min(1, cos_angle))  # 限制范围
                angle = math.acos(cos_angle)

                # 🔧 论文公式3：直接使用夹角Δθ，不是偏离角
                total_turning += angle

        # 转向成本计算完成
        return total_turning
    
    def _estimate_risk_value(self, path: List[Point3D]) -> float:
        """估算风险值（基准算法）- 使用与改进算法一致的计算方法"""
        if not path:
            return 0.0

        # 计算路径长度
        path_length = self._calculate_path_length(path)

        # 计算转向成本
        turning_cost = self._calculate_turning_cost(path)

        # 如果有共享的环境数据，考虑环境风险
        environment_risk = 0.0
        if self.shared_environment_data:
            buildings = self.shared_environment_data.get('buildings', [])
            traffic_data = self.shared_environment_data.get('traffic_data', [])

            # 🔧 修复：建筑物风险 - 使用与改进算法相同的计算方法
            if buildings:
                print(f"🔄 基准算法: 使用{len(buildings)}个建筑物计算风险值")
                for waypoint in path:
                    waypoint_risk = 0.0
                    search_radius = 30.0  # 与改进算法一致的30米范围

                    for building in buildings:
                        distance = self._calculate_distance_to_building(waypoint, building)
                        if distance <= search_radius:
                            # 🔧 使用与改进算法完全相同的风险计算公式(5)
                            building_risk = self._calculate_building_risk_baseline(distance)
                            waypoint_risk += building_risk

                    environment_risk += waypoint_risk

            # 交通风险
            if traffic_data:
                for waypoint in path:
                    for traffic_point in traffic_data:
                        distance = self._calculate_distance_to_traffic(waypoint, traffic_point)
                        if distance < 50.0:  # 50米交通风险范围
                            risk = max(0, (50.0 - distance) / 50.0) * 0.3
                            environment_risk += risk

            # 环境风险计算完成

        # 🔧 修复：如果没有建筑物数据，使用简化计算
        if environment_risk == 0.0:
            base_risk = path_length * 0.01  # 基础风险：每公里1%
            turning_risk = turning_cost * 0.001  # 转向风险（降低以匹配改进算法量级）
            environment_risk = base_risk + turning_risk
            # 简化风险值计算完成
        return environment_risk

    def _calculate_building_risk_baseline(self, distance: float) -> float:
        """
        基准算法的建筑物风险计算 - 与改进算法使用相同的公式(5)
        ZoneRisk = e^(ln(0.05)/d_max * d)
        """
        import math

        risk_edge_distance = 30.0  # 与改进算法一致的风险边缘距离

        if distance <= 0:
            return 1.0  # 建筑物内部风险值为1

        if distance >= risk_edge_distance:
            return 0.05  # 超过风险边缘距离时的最小风险值

        # 使用与改进算法完全相同的公式(5)计算风险值
        # ZoneRisk = e^(ln(0.05)/d_max * d)
        ln_005 = math.log(0.05)  # ln(0.05) ≈ -2.996
        exponent = (ln_005 / risk_edge_distance) * distance
        risk = math.exp(exponent)

        return risk

    def _estimate_collision_cost(self, path: List[Point3D]) -> float:
        """估算碰撞代价（基准算法）- 🔧 修复：使用与改进算法完全相同的保护区计算方法"""
        if not path:
            return 0.0

        try:
            # 🔧 关键修复：使用与改进算法相同的保护区管理器和计算方法
            from .algorithms.improved_cluster_pathfinding import ImprovedPathPoint, LegacyProtectionZone
            from .algorithms.data_structures import Point3D as DataPoint3D

            # 将路径点转换为改进算法的格式
            waypoints = []
            for point in path:
                improved_point = ImprovedPathPoint(
                    x=point.x, y=point.y, z=point.z,
                    lng=point.lng, lat=point.lat
                )
                waypoints.append(improved_point)

            # 🔧 统一：直接使用前端保护区系统
            import sys
            import os

            # 确保能找到protection_zones模块
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)

            from protection_zones import ProtectionZoneManager
            protection_manager = ProtectionZoneManager()

            # 获取路径相关的保护区
            path_points = [(waypoint.lng, waypoint.lat) for waypoint in waypoints]
            relevant_zones = protection_manager.get_zones_for_path(path_points, buffer_distance=500)

            print(f"🔄 基准算法检测到前端保护区数量: {len(relevant_zones)}")

            # 🔧 使用与改进算法完全相同的碰撞代价计算方法
            total_collision_cost = 0.0

            for waypoint in waypoints:
                waypoint_cost = 0.0

                for zone in relevant_zones:
                    # 使用前端保护区的碰撞代价计算方法
                    zone_cost = zone.get_collision_cost(waypoint.lng, waypoint.lat)
                    waypoint_cost += zone_cost

                total_collision_cost += waypoint_cost

            print(f"🔄 基准算法碰撞代价计算完成（使用前端保护区方法）: {total_collision_cost:.4f}")

            # 🔧 保存保护区信息用于前端显示
            if relevant_zones:
                collision_cost_breakdown = {}
                for zone in relevant_zones:
                    zone_total_cost = 0.0
                    for waypoint in waypoints:
                        cost = zone.get_collision_cost(waypoint.lng, waypoint.lat)
                        zone_total_cost += cost

                    if zone_total_cost > 0:
                        collision_cost_breakdown[zone.id] = {
                            'zone_name': zone.name,
                            'zone_type': zone.zone_type.value,
                            'total_cost': zone_total_cost,
                            'average_crash_cost': zone.average_crash_cost
                        }

                # 保存到共享数据中，供前端使用
                if not hasattr(self, 'baseline_protection_zones_info'):
                    self.baseline_protection_zones_info = {}
                self.baseline_protection_zones_info = {
                    'collision_cost_breakdown': collision_cost_breakdown,
                    'total_zones': len(relevant_zones),
                    'active_zones': len(collision_cost_breakdown)
                }

                print(f"🔄 基准算法活跃保护区: {list(collision_cost_breakdown.keys())}")

            return total_collision_cost

        except Exception as e:
            print(f"⚠️ 基准算法保护区计算失败，回退到简化方法: {e}")
            # 回退到简化计算
            path_length = self._calculate_path_length(path)
            turning_cost = self._calculate_turning_cost(path)

            base_collision = path_length * 0.005  # 基础碰撞代价
            turning_collision = turning_cost * 0.05  # 转向增加碰撞风险
            min_collision = max(2.0, path_length / 200)  # 最小碰撞代价

            collision_cost = max(base_collision + turning_collision, min_collision)
            print(f"🔄 基准算法使用简化碰撞代价计算: {collision_cost:.4f}")
            return collision_cost

    def _calculate_distance_to_building(self, waypoint: Point3D, building: dict) -> float:
        """计算航点到建筑物的距离"""
        try:
            # 🔧 修复：正确处理建筑物坐标格式
            building_lng = None
            building_lat = None

            # 优先使用经纬度坐标
            if 'lng' in building and 'lat' in building:
                building_lng = building['lng']
                building_lat = building['lat']
            elif 'longitude' in building and 'latitude' in building:
                building_lng = building['longitude']
                building_lat = building['latitude']
            elif 'x' in building and 'y' in building and 'lng' in building and 'lat' in building:
                # 🔧 关键修复：如果同时有x,y和lng,lat，优先使用lng,lat
                building_lng = building['lng']
                building_lat = building['lat']
            elif 'x' in building and 'y' in building:
                # 🔧 如果只有x,y坐标，假设它们已经是相对于某个参考点的米制坐标
                # 这种情况下使用简单的欧几里得距离
                waypoint_x = getattr(waypoint, 'x', 0)
                waypoint_y = getattr(waypoint, 'y', 0)

                dx = building['x'] - waypoint_x
                dy = building['y'] - waypoint_y
                distance = (dx*dx + dy*dy) ** 0.5

                # 🔧 性能优化：减少日志输出
                import random
                if random.random() < 0.01:  # 1%的概率输出日志
                    print(f"🔍 建筑物距离计算(x,y): waypoint({waypoint_x:.1f},{waypoint_y:.1f}) -> building({building['x']:.1f},{building['y']:.1f}) = {distance:.1f}m")
                return distance
            else:
                print(f"⚠️ 建筑物坐标格式不支持: {list(building.keys())}")
                return 1000.0  # 无法计算距离，返回大值

            if building_lng is None or building_lat is None:
                return 1000.0

            # 使用Haversine公式计算距离
            distance = self._haversine_distance(
                waypoint.lat, waypoint.lng,
                building_lat, building_lng
            )

            # 🔧 性能优化：减少日志输出，只偶尔输出
            import random
            if random.random() < 0.01:  # 1%的概率输出日志
                print(f"🔍 建筑物距离计算(lng,lat): waypoint({waypoint.lng:.6f},{waypoint.lat:.6f}) -> building({building_lng:.6f},{building_lat:.6f}) = {distance:.1f}m")
            return distance

        except Exception as e:
            print(f"⚠️ 计算建筑物距离失败: {e}")
            return 1000.0

    def _calculate_distance_to_traffic(self, waypoint: Point3D, traffic_point: dict) -> float:
        """计算航点到交通点的距离"""
        try:
            # 交通点可能有不同的格式
            if 'longitude' in traffic_point and 'latitude' in traffic_point:
                traffic_lng = traffic_point['longitude']
                traffic_lat = traffic_point['latitude']
            elif 'lng' in traffic_point and 'lat' in traffic_point:
                traffic_lng = traffic_point['lng']
                traffic_lat = traffic_point['lat']
            elif 'x' in traffic_point and 'y' in traffic_point:
                traffic_lng = traffic_point['x']
                traffic_lat = traffic_point['y']
            else:
                return 1000.0

            return self._haversine_distance(
                waypoint.lat, waypoint.lng,
                traffic_lat, traffic_lng
            )
        except Exception as e:
            print(f"⚠️ 计算交通点距离失败: {e}")
            return 1000.0

    def _haversine_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """使用Haversine公式计算两点间距离（米）"""
        import math

        # 转换为弧度
        lat1, lng1, lat2, lng2 = map(math.radians, [lat1, lng1, lat2, lng2])

        # Haversine公式
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlng/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # 地球半径（米）
        r = 6371000

        return c * r
    
    def _calculate_comparison(self, improved: AlgorithmPerformanceMetrics, 
                            baseline: AlgorithmPerformanceMetrics) -> ComparisonResult:
        """计算对比结果"""
        import datetime
        
        # 计算改进百分比
        improvement_percentages = {}
        metrics_to_compare = ['path_length', 'turning_cost', 'risk_value', 'collision_cost', 'final_cost', 'execution_time']
        
        total_improvement = 0.0
        valid_comparisons = 0
        
        for metric in metrics_to_compare:
            improved_value = getattr(improved, metric)
            baseline_value = getattr(baseline, metric)
            
            if baseline_value > 0:
                # 对于这些指标，越小越好
                improvement_percent = ((baseline_value - improved_value) / baseline_value) * 100
                improvement_percentages[metric] = improvement_percent
                
                # 最终代价权重更高
                if metric == 'final_cost':
                    total_improvement += improvement_percent * 2  # 最终代价权重加倍
                    valid_comparisons += 2
                else:
                    total_improvement += improvement_percent
                    valid_comparisons += 1
            else:
                improvement_percentages[metric] = 0.0
        
        overall_improvement = total_improvement / valid_comparisons if valid_comparisons > 0 else 0.0
        
        # 生成性能评级
        if overall_improvement > 10:
            performance_rating = "A+ (优秀)"
        elif overall_improvement > 5:
            performance_rating = "A (良好)"
        elif overall_improvement > 0:
            performance_rating = "B (一般)"
        elif overall_improvement > -5:
            performance_rating = "C (需要改进)"
        else:
            performance_rating = "D (性能不佳)"
        
        # 生成总结
        if overall_improvement > 5:
            summary = f"改进算法显著优于基准算法，最终代价降低{overall_improvement:.1f}%"
        elif overall_improvement > 0:
            summary = f"改进算法略优于基准算法，最终代价降低{overall_improvement:.1f}%"
        else:
            summary = f"改进算法性能需要优化，最终代价增加{abs(overall_improvement):.1f}%"
        
        # 简化对比信息输出
        print(f"📊 算法对比: 路径长度改进{improvement_percentages['path_length']:.1f}%, 最终代价改进{improvement_percentages['final_cost']:.1f}%")
        
        return ComparisonResult(
            improved_metrics=improved,
            baseline_metrics=baseline,
            improvement_percentages=improvement_percentages,
            overall_improvement=overall_improvement,
            performance_rating=performance_rating,
            summary=summary,
            timestamp=datetime.datetime.now().isoformat()
        )
    
    def get_comparison_history(self) -> List[Dict[str, Any]]:
        """获取对比历史"""
        return [result.to_dict() for result in self.comparison_history]
    
    def clear_comparison_history(self):
        """清空对比历史"""
        self.comparison_history.clear()
    
    def _calculate_manhattan_distance(self, point1: Point3D, point2: Point3D) -> float:
        """
        计算曼哈顿距离（使用地理坐标）

        Args:
            point1: 起点
            point2: 终点

        Returns:
            曼哈顿距离（米）
        """
        import math

        # 地球半径（米）
        R = 6371000

        # 将经纬度转换为弧度
        lat1_rad = math.radians(point1.lat)
        lng1_rad = math.radians(point1.lng)
        lat2_rad = math.radians(point2.lat)
        lng2_rad = math.radians(point2.lng)

        # 计算经度和纬度的距离（近似）
        lat_distance = abs(lat2_rad - lat1_rad) * R
        lng_distance = abs(lng2_rad - lng1_rad) * R * math.cos((lat1_rad + lat2_rad) / 2)
        alt_distance = abs(point2.alt - point1.alt)

        manhattan_distance = lat_distance + lng_distance + alt_distance

        print(f"🧮 曼哈顿距离计算:")
        print(f"   纬度距离: {lat_distance:.2f}m")
        print(f"   经度距离: {lng_distance:.2f}m")
        print(f"   高度距离: {alt_distance:.2f}m")
        print(f"   总曼哈顿距离: {manhattan_distance:.2f}m")

        return manhattan_distance

    def _generate_protection_zones_for_baseline(self, waypoints) -> List:
        """
        🔥 破釜沉舟：LegacyProtectionZone已删除，基准算法保护区生成已废弃
        """
        raise RuntimeError("🔥 LegacyProtectionZone已被删除！基准算法保护区生成功能已废弃！")

    def _create_path_buffer_for_baseline(self, waypoints, buffer_distance):
        """为基准算法创建路径缓冲区"""
        buffer_points = []
        for waypoint in waypoints:
            # 创建缓冲区的四个角点
            buffer_points.extend([
                (waypoint.x - buffer_distance, waypoint.y - buffer_distance),
                (waypoint.x + buffer_distance, waypoint.y - buffer_distance),
                (waypoint.x + buffer_distance, waypoint.y + buffer_distance),
                (waypoint.x - buffer_distance, waypoint.y + buffer_distance)
            ])
        return buffer_points

    def _filter_buildings_in_buffer(self, buildings, buffer_points):
        """筛选缓冲区内的建筑物"""
        if not buffer_points:
            return buildings[:10]  # 如果没有缓冲区，返回前10个建筑物

        # 计算缓冲区边界
        min_x = min(point[0] for point in buffer_points)
        max_x = max(point[0] for point in buffer_points)
        min_y = min(point[1] for point in buffer_points)
        max_y = max(point[1] for point in buffer_points)

        # 筛选在缓冲区内的建筑物
        buildings_in_buffer = []
        for building in buildings:
            if (min_x <= building['x'] <= max_x and
                min_y <= building['y'] <= max_y):
                buildings_in_buffer.append(building)

        return buildings_in_buffer[:20]  # 限制数量避免性能问题

    def _generate_basic_buildings_for_baseline(self, request: PathPlanningRequest) -> List[Dict]:
        """为基准算法生成基础建筑物数据"""
        import random
        import math

        buildings = []

        # 基于起点和终点生成建筑物
        start_x = request.start_point.x if hasattr(request.start_point, 'x') else 0
        start_y = request.start_point.y if hasattr(request.start_point, 'y') else 0
        end_x = request.end_point.x if hasattr(request.end_point, 'x') else 1000
        end_y = request.end_point.y if hasattr(request.end_point, 'y') else 1000

        # 计算路径区域
        min_x = min(start_x, end_x) - 500
        max_x = max(start_x, end_x) + 500
        min_y = min(start_y, end_y) - 500
        max_y = max(start_y, end_y) + 500

        # 生成10-15个建筑物
        num_buildings = random.randint(10, 15)

        for i in range(num_buildings):
            x = random.uniform(min_x, max_x)
            y = random.uniform(min_y, max_y)

            # 确保不在起点和终点附近
            start_dist = math.sqrt((x - start_x)**2 + (y - start_y)**2)
            end_dist = math.sqrt((x - end_x)**2 + (y - end_y)**2)

            if start_dist < 100 or end_dist < 100:
                continue

            building = {
                'id': f'baseline_building_{i}',
                'x': x,
                'y': y,
                'lng': request.start_point.lng + (x - start_x) / 111320,
                'lat': request.start_point.lat + (y - start_y) / 110540,
                'height': random.randint(20, 80),
                'width': random.randint(15, 30),
                'length': random.randint(15, 30),
                'type': random.choice(['residential', 'commercial', 'office']),
                'area': random.randint(100, 400),
                'source': 'baseline_generated'
            }
            buildings.append(building)

        return buildings

    def _auto_generate_csv(self):
        """自动生成CSV文件，包含您要求的所有表头"""
        try:
            import glob
            import json
            import csv
            import os
            from datetime import datetime

            # 查找最新的JSON文件
            json_files = glob.glob('json/all_81_paths_data_*.json')
            if not json_files:
                print("❌ 没有找到JSON文件，无法生成CSV")
                return None

            latest_json = max(json_files, key=os.path.getctime)
            print(f"📂 使用JSON文件: {latest_json}")

            # 读取JSON数据
            with open(latest_json, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if not data.get('all_paths'):
                print("❌ JSON文件中没有路径数据")
                return None

            # 创建CSV文件
            timestamp = datetime.now().strftime('%Y-%m-%dT%H-%M-%S')
            filename = f'路径数据_完整版_{timestamp}.csv'
            filepath = os.path.join('csv', filename)

            # 确保目录存在
            os.makedirs('csv', exist_ok=True)

            # 获取起点和终点坐标
            metadata = data.get('metadata', {})
            start_point = metadata.get('start_point')
            end_point = metadata.get('end_point')

            # 检查起点终点数据是否有效
            if not start_point or not end_point:
                print(f"⚠️ 起点终点数据无效，使用默认值")
                start_point = {'lng': 0, 'lat': 0, 'alt': 0}
                end_point = {'lng': 0, 'lat': 0, 'alt': 0}

            # 准备CSV数据
            csv_data = []

            for path_data in data['all_paths']:
                path_id = path_data.get('path_id', '')

                # 处理基准路径
                if path_id == 'BASELINE_A*':
                    display_id = 'BASELINE_A*'
                    start_lng = start_point.get('lng', 0) if start_point else 0
                    start_lat = start_point.get('lat', 0) if start_point else 0
                    start_alt = start_point.get('alt', 0) if start_point else 0
                    end_lng = end_point.get('lng', 0) if end_point else 0
                    end_lat = end_point.get('lat', 0) if end_point else 0
                    end_alt = end_point.get('alt', 0) if end_point else 0
                    flight_height = start_alt
                    cluster_id = '基准算法'

                    # 提取保护区信息
                    try:
                        from export_all_paths_data import extract_protection_zones_info
                        protection_info = extract_protection_zones_info(path_data)
                        protection_zones_count = protection_info.get('protection_zones_count', 0)
                        protection_zones_list = protection_info.get('protection_zones_list', '')
                        protection_zones_types = protection_info.get('protection_zones_types', '')
                    except Exception as e:
                        print(f"⚠️ 提取保护区信息失败: {e}")
                        protection_zones_count = 0
                        protection_zones_list = ''
                        protection_zones_types = ''
                else:
                    # 处理改进算法的81条路径
                    display_id = str(path_id)
                    start_lng = start_point.get('lng', 0) if start_point else 0
                    start_lat = start_point.get('lat', 0) if start_point else 0
                    start_alt = start_point.get('alt', 0) if start_point else 0
                    end_lng = end_point.get('lng', 0) if end_point else 0
                    end_lat = end_point.get('lat', 0) if end_point else 0
                    end_alt = end_point.get('alt', 0) if end_point else 0

                    # 改进算法路径的飞行高度从height_layer获取
                    height_layer = path_data.get('height_layer', 1)
                    if isinstance(height_layer, (int, float)):
                        flight_height = float(height_layer) * 10  # 假设每层10米
                    else:
                        flight_height = start_alt

                    # 簇ID
                    cluster_id = path_data.get('cluster_id', '')

                    # 提取保护区信息
                    try:
                        from export_all_paths_data import extract_protection_zones_info
                        protection_info = extract_protection_zones_info(path_data)
                        protection_zones_count = protection_info.get('protection_zones_count', 0)
                        protection_zones_list = protection_info.get('protection_zones_list', '')
                        protection_zones_types = protection_info.get('protection_zones_types', '')
                    except Exception as e:
                        print(f"⚠️ 提取保护区信息失败: {e}")
                        protection_zones_count = 0
                        protection_zones_list = ''
                        protection_zones_types = ''

                # 构建CSV行数据
                csv_data.append({
                    'path_id': display_id,
                    'direction': path_data.get('flight_direction', ''),
                    'height': path_data.get('height_layer', ''),
                    'start_lng': start_lng,
                    'start_lat': start_lat,
                    'start_alt': start_alt,
                    'end_lng': end_lng,
                    'end_lat': end_lat,
                    'end_alt': end_alt,
                    'flight_height': flight_height,
                    'cluster_id': cluster_id,
                    'protection_zones_count': protection_zones_count,
                    'protection_zones_list': protection_zones_list,
                    'protection_zones_types': protection_zones_types,
                    'path_length': path_data.get('path_length', 0),
                    'turning_cost': path_data.get('turning_cost', 0),
                    'risk_value': path_data.get('risk_value', 0),
                    'collision_cost': path_data.get('collision_cost', 0),
                    'final_cost': path_data.get('final_cost', 0),
                    'waypoint_count': path_data.get('waypoints_count', 0),
                    'selected': '是' if path_data.get('is_selected', False) else '否'
                })

            # 写入CSV文件
            fieldnames = [
                'path_id', 'direction', 'height', 'start_lng', 'start_lat', 'start_alt',
                'end_lng', 'end_lat', 'end_alt', 'flight_height', 'cluster_id',
                'protection_zones_count', 'protection_zones_list', 'protection_zones_types',
                'path_length', 'turning_cost', 'risk_value', 'collision_cost', 'final_cost',
                'waypoint_count', 'selected'
            ]

            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # 写入表头（中文）
                writer.writerow({
                    'path_id': '路径ID',
                    'direction': '方向',
                    'height': '高度层',
                    'start_lng': '起点经度',
                    'start_lat': '起点纬度',
                    'start_alt': '起点高度',
                    'end_lng': '终点经度',
                    'end_lat': '终点纬度',
                    'end_alt': '终点高度',
                    'flight_height': '飞行高度',
                    'cluster_id': '簇ID',
                    'protection_zones_count': '保护区数量',
                    'protection_zones_list': '保护区列表',
                    'protection_zones_types': '保护区类型',
                    'path_length': '路径长度',
                    'turning_cost': '转向成本',
                    'risk_value': '风险值',
                    'collision_cost': '碰撞代价',
                    'final_cost': '最终代价',
                    'waypoint_count': '航点数',
                    'selected': '选中'
                })

                # 写入数据
                writer.writerows(csv_data)

            print(f"✅ 自动CSV导出成功: {filepath}")
            print(f"📊 导出了 {len(csv_data)} 条路径数据")

            return {
                'success': True,
                'filename': filename,
                'filepath': filepath,
                'total_paths': len(csv_data),
                'source_json': os.path.basename(latest_json)
            }

        except Exception as e:
            print(f"❌ 自动CSV生成失败: {e}")
            import traceback
            traceback.print_exc()
            return None


# 全局API实例
comparison_api = AlgorithmComparisonAPI()


async def run_comparison(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """运行算法对比的便捷函数"""
    return await comparison_api.run_algorithm_comparison(request_data)


if __name__ == "__main__":
    # 测试用例
    async def test_comparison():
        test_request = {
            'startPoint': {'lng': 0, 'lat': 0, 'alt': 100, 'x': 0, 'y': 0, 'z': 100},
            'endPoint': {'lng': 1000, 'lat': 800, 'alt': 120, 'x': 1000, 'y': 800, 'z': 120},
            'flightHeight': 100,
            'parameters': {'maxIterations': 1000, 'stepSize': 10.0}
        }
        
        result = await run_comparison(test_request)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    asyncio.run(test_comparison())


@algorithm_comparison_bp.route('/algorithm_comparison', methods=['POST'])
def algorithm_comparison_endpoint():
    """算法对比API端点"""
    try:
        data = request.get_json()
        print("🚀 算法对比API被调用")

        if not data:
            print("❌ 请求数据为空")
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400

        # 异步运行算法对比
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(run_comparison(data))
            # 简化输出，只显示关键信息
            if result.get('success'):
                print(f"✅ 算法对比完成")
            else:
                print(f"❌ 算法对比失败: {result.get('error', '未知错误')}")
            return jsonify(result)
        finally:
            loop.close()

    except Exception as e:
        print(f"❌ 算法对比API异常: {e}")
        return jsonify({
            'success': False,
            'error': f'算法对比失败: {str(e)}'
        }), 500

def get_latest_baseline_result():
    """获取最新的基准算法结果 - 🔧 修复：从文件读取"""
    global latest_baseline_result

    # 先检查内存缓存
    if latest_baseline_result is not None:
        return latest_baseline_result

    # 从文件读取
    try:
        if BASELINE_RESULT_FILE.exists():
            with open(BASELINE_RESULT_FILE, 'r', encoding='utf-8') as f:
                latest_baseline_result = json.load(f)
                print(f"🔍 从文件读取基准算法结果: 成功={latest_baseline_result.get('success', False)}")
                return latest_baseline_result
    except Exception as e:
        print(f"⚠️ 读取基准算法结果文件失败: {e}")

    return None

def set_latest_baseline_result(result):
    """设置最新的基准算法结果 - 🔧 修复：保存到文件"""
    global latest_baseline_result
    latest_baseline_result = result

    # 保存到文件
    try:
        with open(BASELINE_RESULT_FILE, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        print(f"🔍 基准算法结果已保存到文件: 成功={result.get('success', False)}")
    except Exception as e:
        print(f"⚠️ 保存基准算法结果文件失败: {e}")

def get_latest_improved_result():
    """获取最新的改进算法结果 - 🔧 修复：从文件读取"""
    global latest_improved_result

    # 先检查内存缓存
    if latest_improved_result is not None:
        return latest_improved_result

    # 从文件读取
    try:
        if IMPROVED_RESULT_FILE.exists():
            with open(IMPROVED_RESULT_FILE, 'r', encoding='utf-8') as f:
                latest_improved_result = json.load(f)
                print(f"🔍 从文件读取改进算法结果: 成功={latest_improved_result.get('success', False)}")
                return latest_improved_result
    except Exception as e:
        print(f"⚠️ 读取改进算法结果文件失败: {e}")

    return None

def set_latest_improved_result(result):
    """设置最新的改进算法结果 - 🔧 修复：保存到文件"""
    global latest_improved_result
    latest_improved_result = result

    # 保存到文件
    try:
        with open(IMPROVED_RESULT_FILE, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        print(f"🔍 改进算法结果已保存到文件: 成功={result.get('success', False)}")
    except Exception as e:
        print(f"⚠️ 保存改进算法结果文件失败: {e}")


