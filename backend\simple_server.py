#!/usr/bin/env python3
"""
简单的Flask服务器，用于测试算法API
"""

import sys
import os
import json
import asyncio
from flask import Flask, request, jsonify
from flask_cors import CORS

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)
CORS(app)

# 全局变量存储算法管理器
algorithm_manager = None

def init_algorithms():
    """初始化算法系统"""
    global algorithm_manager
    
    try:
        print("初始化算法系统...")
        
        # 导入算法模块
        from algorithms import algorithm_manager as mgr
        algorithm_manager = mgr
        
        # 获取已注册的算法
        algorithms = algorithm_manager.list_algorithms()
        print(f"✅ 算法系统初始化完成，已注册 {len(algorithms)} 个算法:")
        
        for alg in algorithms:
            print(f"  - {alg['name']}: {alg['info']['description']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法系统初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

@app.route('/api/pathfinding/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        if algorithm_manager is None:
            return jsonify({
                'success': False,
                'status': 'unhealthy',
                'error': '算法系统未初始化'
            }), 500
        
        algorithms = algorithm_manager.list_algorithms()
        
        return jsonify({
            'success': True,
            'status': 'healthy',
            'message': '路径规划服务运行正常',
            'available_algorithms': len(algorithms),
            'algorithms': [alg['name'] for alg in algorithms]
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'status': 'unhealthy',
            'error': str(e)
        }), 500

@app.route('/api/pathfinding/algorithms', methods=['GET'])
def list_algorithms():
    """获取算法列表"""
    try:
        if algorithm_manager is None:
            return jsonify({
                'success': False,
                'error': '算法系统未初始化'
            }), 500
        
        algorithms = algorithm_manager.list_algorithms()
        
        return jsonify({
            'success': True,
            'algorithms': algorithms,
            'total_count': len(algorithms)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/pathfinding/calculate', methods=['POST'])
def calculate_path():
    """路径规划计算"""
    try:
        if algorithm_manager is None:
            return jsonify({
                'success': False,
                'error': '算法系统未初始化'
            }), 500
        
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据不能为空'
            }), 400
        
        # 详细的请求日志
        algorithm_name = data.get('algorithm', 'Unknown')
        start_point = data.get('startPoint', {})
        end_point = data.get('endPoint', {})

        print("=" * 80)
        print(f"🚁 收到路径规划请求")
        print("=" * 80)
        print(f"📍 算法类型: {algorithm_name}")
        print(f"🛫 起点: ({start_point.get('x', 0):.3f}, {start_point.get('y', 0):.3f}, {start_point.get('z', 0):.1f})")
        print(f"🛬 终点: ({end_point.get('x', 0):.3f}, {end_point.get('y', 0):.3f}, {end_point.get('z', 0):.1f})")
        print(f"⚙️ 参数: {data.get('parameters', {})}")

        # 统一建筑物数据字段名
        buildings = data.get('buildings', []) or data.get('obstacles', [])
        print(f"🏢 建筑物数量: {len(buildings)}")
        if buildings:
            print(f"🏗️ 第一个建筑物: {buildings[0] if buildings else 'None'}")
        print("-" * 80)

        # 导入请求类
        from algorithms.data_structures import PathPlanningRequest
        
        # 预处理数据：确保坐标格式正确
        processed_data = data.copy()

        # 统一建筑物数据字段名 - 确保使用 buildings 字段
        buildings = data.get('buildings', []) or data.get('obstacles', [])
        processed_data['buildings'] = buildings
        processed_data['obstacles'] = buildings  # 保持兼容性

        # 确保飞行高度存在
        flight_height = processed_data.get('flightHeight', 100.0)

        # 🔧 修复：处理起点坐标 - 起飞高度应该是地面高度1米
        if 'startPoint' in processed_data:
            start_point = processed_data['startPoint']
            if 'lng' in start_point and 'lat' in start_point:
                # 保持经纬度格式，设置正确的起飞高度（地面高度）
                processed_data['startPoint'] = {
                    'lng': start_point.get('lng', 0),
                    'lat': start_point.get('lat', 0),
                    'alt': start_point.get('alt', 1.0)  # 🔧 使用前端传递的起飞高度，默认1米
                }

        # 🔧 修复：处理终点坐标 - 降落高度应该是地面高度1米
        if 'endPoint' in processed_data:
            end_point = processed_data['endPoint']
            if 'lng' in end_point and 'lat' in end_point:
                # 保持经纬度格式，设置正确的降落高度（地面高度）
                processed_data['endPoint'] = {
                    'lng': end_point.get('lng', 0),
                    'lat': end_point.get('lat', 0),
                    'alt': end_point.get('alt', 1.0)  # 🔧 使用前端传递的降落高度，默认1米
                }

        # 确保必需参数存在
        if 'flightHeight' not in processed_data:
            processed_data['flightHeight'] = 100.0
        if 'safetyDistance' not in processed_data:
            processed_data['safetyDistance'] = 30.0

        print(f"🔄 处理后的坐标:")
        print(f"  起点: {processed_data.get('startPoint', {})}")
        print(f"  终点: {processed_data.get('endPoint', {})}")
        print(f"  飞行高度: {processed_data.get('flightHeight')} 米")

        # 创建请求对象
        planning_request = PathPlanningRequest(processed_data)
        algorithm_name = planning_request.algorithm
        
        print(f"🚀 开始执行算法: {algorithm_name}")

        # 执行算法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        import time
        start_time = time.time()

        try:
            response = loop.run_until_complete(
                algorithm_manager.execute_algorithm(algorithm_name, planning_request)
            )
        finally:
            loop.close()

        execution_time = time.time() - start_time

        # 详细的结果日志
        print("=" * 80)
        print(f"📊 算法执行完成")
        print("=" * 80)
        print(f"✅ 执行状态: {'成功' if response.success else '失败'}")
        print(f"⏱️ 执行时间: {execution_time:.3f} 秒")

        if response.success:
            print(f"📍 路径点数: {len(response.path) if response.path else 0}")
            print(f"📏 路径长度: {response.path_length:.2f} 米" if hasattr(response, 'path_length') and response.path_length else "📏 路径长度: 未计算")
            print(f"⏰ 预计飞行时间: {response.estimated_flight_time:.2f} 秒" if hasattr(response, 'estimated_flight_time') and response.estimated_flight_time else "⏰ 预计飞行时间: 未计算")

            # 显示质量评估
            if hasattr(response, 'quality') and response.quality:
                print(f"📈 质量评估:")
                for key, value in response.quality.items():
                    print(f"  - {key}: {value}")

            # 显示元数据
            if hasattr(response, 'metadata') and response.metadata:
                print(f"📊 元数据:")
                for key, value in response.metadata.items():
                    if isinstance(value, (int, float)):
                        print(f"  - {key}: {value}")
                    else:
                        print(f"  - {key}: {str(value)[:50]}...")
        else:
            print(f"❌ 错误信息: {response.error if hasattr(response, 'error') else '未知错误'}")

        print("=" * 80)

        # 返回结果
        return jsonify(response.to_dict())
        
    except Exception as e:
        print(f"路径规划API错误: {e}")
        import traceback
        traceback.print_exc()
        
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/api/pathfinding/test', methods=['GET'])
def test_algorithm():
    """测试算法功能"""
    try:
        if algorithm_manager is None:
            return jsonify({
                'success': False,
                'error': '算法系统未初始化'
            }), 500
        
        # 导入请求类
        from algorithms.data_structures import PathPlanningRequest
        
        # 创建测试请求
        test_data = {
            'startPoint': {'lng': 139.767, 'lat': 35.681, 'alt': 0},
            'endPoint': {'lng': 139.777, 'lat': 35.691, 'alt': 0},
            'flightHeight': 100,
            'algorithm': 'StraightLine',
            'parameters': {'pointCount': 10}
        }
        
        request_obj = PathPlanningRequest(test_data)
        
        # 执行算法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            response = loop.run_until_complete(
                algorithm_manager.execute_algorithm('StraightLine', request_obj)
            )
        finally:
            loop.close()
        
        return jsonify({
            'success': True,
            'test_result': response.to_dict(),
            'message': '算法测试完成'
        })
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        
        return jsonify({
            'success': False,
            'error': f'算法测试失败: {str(e)}'
        }), 500

@app.route('/api/export-comparison-data', methods=['POST', 'OPTIONS'])
def export_comparison_data():
    """导出算法对比数据到CSV"""
    if request.method == 'OPTIONS':
        return jsonify({'success': True})

    try:
        # 获取对比数据
        comparison_data = request.get_json()

        if not comparison_data:
            return jsonify({
                'success': False,
                'error': '没有接收到对比数据'
            })

        print(f"📊 接收到导出请求，数据类型: {type(comparison_data)}")

        # 使用简单导出器导出数据
        from simple_path_exporter import export_comparison_data as do_export
        csv_filepath = do_export(comparison_data)

        if csv_filepath:
            # 统计路径数量
            improved_count = len(comparison_data.get('improved', {}).get('paths', []))
            baseline_count = len(comparison_data.get('baseline', {}).get('paths', []))
            total_count = improved_count + baseline_count

            response_data = {
                'success': True,
                'filepath': csv_filepath,
                'pathCount': total_count,
                'improvedCount': improved_count,
                'baselineCount': baseline_count,
                'message': f'成功导出{total_count}条路径数据'
            }

            print(f"✅ 导出成功: {csv_filepath}, 路径数: {total_count}")
        else:
            response_data = {
                'success': False,
                'error': '导出失败，请检查服务器日志'
            }
            print(f"❌ 导出失败")

        return jsonify(response_data)

    except Exception as e:
        print(f"❌ 导出异常: {e}")
        import traceback
        traceback.print_exc()

        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        })

@app.route('/')
def index():
    """主页"""
    return jsonify({
        'message': '无人机路径规划算法API服务器',
        'version': '1.0.0',
        'endpoints': [
            '/api/pathfinding/health',
            '/api/pathfinding/algorithms',
            '/api/pathfinding/calculate',
            '/api/pathfinding/test',
            '/api/export-comparison-data'
        ]
    })

if __name__ == '__main__':
    print("🚁 无人机路径规划算法API服务器")
    print("=" * 50)
    
    # 初始化算法系统
    if init_algorithms():
        print("✅ 服务器准备就绪")
        print("访问地址: http://localhost:5000")
        print("健康检查: http://localhost:5000/api/pathfinding/health")
        print("算法测试: http://localhost:5000/api/pathfinding/test")
        print("=" * 50)
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
    else:
        print("❌ 算法系统初始化失败，服务器无法启动")
        sys.exit(1)
