"""
日志API接口
提供日志查看和分析功能
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
import json
import os
from datetime import datetime, timedelta
from pathlib import Path

from utils.log_viewer import LogViewer

router = APIRouter(prefix="/api/logs", tags=["logs"])

# 初始化日志查看器
log_viewer = LogViewer()


@router.get("/dates")
async def get_available_dates() -> List[str]:
    """获取可用的日志日期"""
    try:
        dates = log_viewer.get_available_dates()
        return dates
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取日志日期失败: {str(e)}")


@router.get("/steps/{date}")
async def get_steps_by_date(date: str) -> List[Dict[str, Any]]:
    """根据日期获取步骤日志"""
    try:
        # 验证日期格式
        if len(date) != 8 or not date.isdigit():
            raise HTTPException(status_code=400, detail="日期格式错误，应为YYYYMMDD")
        
        steps = log_viewer.load_steps_by_date(date)
        return steps
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取步骤日志失败: {str(e)}")


@router.get("/session/{session_id}")
async def get_session_steps(
    session_id: str,
    date: Optional[str] = Query(None, description="指定日期，格式YYYYMMDD")
) -> List[Dict[str, Any]]:
    """根据会话ID获取步骤"""
    try:
        steps = log_viewer.get_steps_by_session(session_id, date)
        return steps
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取会话步骤失败: {str(e)}")


@router.get("/algorithm/{algorithm_name}")
async def get_algorithm_steps(
    algorithm_name: str,
    date: Optional[str] = Query(None, description="指定日期，格式YYYYMMDD")
) -> List[Dict[str, Any]]:
    """根据算法名称获取步骤"""
    try:
        steps = log_viewer.get_steps_by_algorithm(algorithm_name, date)
        return steps
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取算法步骤失败: {str(e)}")


@router.get("/errors")
async def get_error_steps(
    date: Optional[str] = Query(None, description="指定日期，格式YYYYMMDD")
) -> List[Dict[str, Any]]:
    """获取错误步骤"""
    try:
        errors = log_viewer.get_error_steps(date)
        return errors
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取错误日志失败: {str(e)}")


@router.get("/recent-sessions")
async def get_recent_sessions(
    hours: int = Query(24, description="最近多少小时内的会话")
) -> List[str]:
    """获取最近的会话ID"""
    try:
        if hours <= 0 or hours > 168:  # 最多7天
            raise HTTPException(status_code=400, detail="小时数必须在1-168之间")
        
        sessions = log_viewer.get_recent_sessions(hours)
        return sessions
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取最近会话失败: {str(e)}")


@router.get("/session/{session_id}/analysis")
async def analyze_session_performance(session_id: str) -> Dict[str, Any]:
    """分析会话性能"""
    try:
        analysis = log_viewer.analyze_session_performance(session_id)
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析会话性能失败: {str(e)}")


@router.get("/statistics")
async def get_log_statistics(
    date: Optional[str] = Query(None, description="指定日期，格式YYYYMMDD")
) -> Dict[str, Any]:
    """获取日志统计信息"""
    try:
        if date:
            steps = log_viewer.load_steps_by_date(date)
        else:
            # 获取最近一天的日志
            dates = log_viewer.get_available_dates()
            if not dates:
                return {"message": "无可用日志"}
            steps = log_viewer.load_steps_by_date(dates[0])
        
        if not steps:
            return {"message": "无日志数据"}
        
        # 统计信息
        stats = {
            "total_steps": len(steps),
            "date_range": date or "最新",
            "step_types": {},
            "levels": {},
            "algorithms": {},
            "sessions": set(),
            "errors": [],
            "performance": {
                "total_duration": 0,
                "avg_step_duration": 0,
                "max_step_duration": 0
            }
        }
        
        durations = []
        
        for step in steps:
            # 步骤类型统计
            step_type = step.get("step_type", "未知")
            stats["step_types"][step_type] = stats["step_types"].get(step_type, 0) + 1
            
            # 级别统计
            level = step.get("level", "未知")
            stats["levels"][level] = stats["levels"].get(level, 0) + 1
            
            # 算法统计
            algorithm = step.get("algorithm")
            if algorithm:
                stats["algorithms"][algorithm] = stats["algorithms"].get(algorithm, 0) + 1
            
            # 会话统计
            session_id = step.get("session_id")
            if session_id:
                stats["sessions"].add(session_id)
            
            # 错误收集
            if level == "ERROR":
                stats["errors"].append({
                    "timestamp": step.get("timestamp"),
                    "message": step.get("message"),
                    "step_number": step.get("step_number")
                })
            
            # 性能统计
            duration = step.get("duration_ms")
            if duration:
                durations.append(duration)
                stats["performance"]["total_duration"] += duration
        
        # 计算性能指标
        if durations:
            stats["performance"]["avg_step_duration"] = sum(durations) / len(durations)
            stats["performance"]["max_step_duration"] = max(durations)
        
        # 转换集合为列表
        stats["sessions"] = list(stats["sessions"])
        stats["session_count"] = len(stats["sessions"])
        
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/export/{date}")
async def export_logs(date: str, format: str = Query("json", description="导出格式: json, csv")):
    """导出指定日期的日志"""
    try:
        # 验证日期格式
        if len(date) != 8 or not date.isdigit():
            raise HTTPException(status_code=400, detail="日期格式错误，应为YYYYMMDD")
        
        steps = log_viewer.load_steps_by_date(date)
        
        if format.lower() == "csv":
            # 转换为CSV格式
            import csv
            import io
            
            output = io.StringIO()
            if steps:
                fieldnames = steps[0].keys()
                writer = csv.DictWriter(output, fieldnames=fieldnames)
                writer.writeheader()
                for step in steps:
                    # 处理嵌套字典
                    row = {}
                    for key, value in step.items():
                        if isinstance(value, dict):
                            row[key] = json.dumps(value, ensure_ascii=False)
                        else:
                            row[key] = value
                    writer.writerow(row)
            
            from fastapi.responses import Response
            return Response(
                content=output.getvalue(),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=logs_{date}.csv"}
            )
        else:
            # JSON格式
            from fastapi.responses import JSONResponse
            return JSONResponse(
                content=steps,
                headers={"Content-Disposition": f"attachment; filename=logs_{date}.json"}
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出日志失败: {str(e)}")


@router.delete("/clean")
async def clean_old_logs(days: int = Query(30, description="保留最近多少天的日志")):
    """清理旧日志文件"""
    try:
        if days <= 0 or days > 365:
            raise HTTPException(status_code=400, detail="天数必须在1-365之间")
        
        log_dir = Path("logs")
        if not log_dir.exists():
            return {"message": "日志目录不存在"}
        
        cutoff_date = datetime.now() - timedelta(days=days)
        deleted_files = []
        
        for log_file in log_dir.glob("*.log"):
            try:
                # 从文件名提取日期
                parts = log_file.stem.split("_")
                if len(parts) >= 2:
                    date_str = parts[-1]
                    if len(date_str) == 8 and date_str.isdigit():
                        file_date = datetime.strptime(date_str, "%Y%m%d")
                        if file_date < cutoff_date:
                            log_file.unlink()
                            deleted_files.append(str(log_file))
            except Exception:
                continue
        
        for json_file in log_dir.glob("*.json"):
            try:
                # 从文件名提取日期
                parts = json_file.stem.split("_")
                if len(parts) >= 2:
                    date_str = parts[-1]
                    if len(date_str) == 8 and date_str.isdigit():
                        file_date = datetime.strptime(date_str, "%Y%m%d")
                        if file_date < cutoff_date:
                            json_file.unlink()
                            deleted_files.append(str(json_file))
            except Exception:
                continue
        
        return {
            "message": f"清理完成，删除了 {len(deleted_files)} 个文件",
            "deleted_files": deleted_files
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理日志失败: {str(e)}")


@router.get("/health")
async def log_system_health():
    """检查日志系统健康状态"""
    try:
        log_dir = Path("logs")
        
        health_info = {
            "status": "healthy",
            "log_directory_exists": log_dir.exists(),
            "available_dates": len(log_viewer.get_available_dates()),
            "disk_usage": {},
            "recent_activity": {}
        }
        
        if log_dir.exists():
            # 计算日志目录大小
            total_size = sum(f.stat().st_size for f in log_dir.rglob('*') if f.is_file())
            health_info["disk_usage"] = {
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / 1024 / 1024, 2)
            }
            
            # 最近活动
            recent_sessions = log_viewer.get_recent_sessions(24)
            health_info["recent_activity"] = {
                "sessions_last_24h": len(recent_sessions),
                "latest_session": recent_sessions[0] if recent_sessions else None
            }
        
        return health_info
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }
