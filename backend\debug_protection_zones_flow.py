#!/usr/bin/env python3
"""
调试保护区流程
端到端测试保护区从检测到前端显示的完整流程
"""

import json
import requests
import time

def debug_protection_zones_flow():
    """调试保护区完整流程"""
    
    print("🔍 调试保护区完整流程")
    print("=" * 80)
    
    # 1. 测试保护区API
    print("📋 1. 测试保护区API")
    try:
        response = requests.get('http://localhost:5000/api/protection-zones/info')
        if response.status_code == 200:
            zones_data = response.json()
            print(f"   ✅ 保护区API正常，返回 {len(zones_data.get('zones', []))} 个保护区")
            
            # 显示前几个保护区的详细信息
            for i, zone in enumerate(zones_data['zones'][:3]):
                print(f"     保护区{i+1}: {zone['name']} (ID: {zone['id']})")
                print(f"       人数: {zone['people_count']}, 车辆: {zone['vehicle_count']}")
                print(f"       总代价: {zone['total_crash_cost']}")
        else:
            print(f"   ❌ 保护区API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 保护区API连接失败: {e}")
        return False
    
    # 2. 测试改进算法API
    print(f"\n🚁 2. 测试改进算法API")
    
    # 构造测试请求（经过东京站和涩谷的路径）
    test_request = {
        "start": {"lng": 139.7670, "lat": 35.6810, "alt": 120},
        "end": {"lng": 139.7016, "lat": 35.6598, "alt": 120},
        "algorithm": "ImprovedClusterBased"
    }
    
    try:
        print(f"   发送改进算法请求...")
        response = requests.post('http://localhost:5000/api/pathfinding/calculate',
                               json=test_request,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 改进算法API成功")
            
            # 检查保护区信息
            if 'protectionZonesInfo' in result:
                protection_info = result['protectionZonesInfo']
                print(f"   ✅ 响应包含protectionZonesInfo")
                print(f"     字段: {list(protection_info.keys())}")
                
                if 'collision_cost_breakdown' in protection_info:
                    breakdown = protection_info['collision_cost_breakdown']
                    print(f"     活跃保护区数: {len(breakdown)}")
                    print(f"     活跃保护区ID: {list(breakdown.keys())}")
                    
                    for zone_id, info in breakdown.items():
                        print(f"       {zone_id}: {info['zone_name']} (代价: {info['total_cost']:.4f})")
                else:
                    print(f"   ❌ 响应中没有collision_cost_breakdown")
            else:
                print(f"   ❌ 响应中没有protectionZonesInfo")
                print(f"   响应字段: {list(result.keys())}")
        else:
            print(f"   ❌ 改进算法API失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 改进算法API调用失败: {e}")
        return False
    
    # 3. 测试基准算法API
    print(f"\n⭐ 3. 测试基准算法API")
    
    test_request['algorithm'] = 'AStar'
    
    try:
        print(f"   发送基准算法请求...")
        response = requests.post('http://localhost:5000/api/pathfinding/calculate',
                               json=test_request,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 基准算法API成功")
            
            # 检查保护区信息
            if 'metadata' in result and 'protection_zones' in result['metadata']:
                protection_info = result['metadata']['protection_zones']
                print(f"   ✅ 响应包含metadata.protection_zones")
                print(f"     字段: {list(protection_info.keys())}")
                
                if 'active_zone_ids' in protection_info:
                    active_ids = protection_info['active_zone_ids']
                    print(f"     活跃保护区ID: {active_ids}")
                    
                    if 'collision_cost_breakdown' in protection_info:
                        breakdown = protection_info['collision_cost_breakdown']
                        print(f"     活跃保护区数: {len(breakdown)}")
                        
                        for zone_id, info in breakdown.items():
                            print(f"       {zone_id}: {info['zone_name']} (代价: {info['total_cost']:.4f})")
                    else:
                        print(f"   ❌ 响应中没有collision_cost_breakdown")
                else:
                    print(f"   ❌ 响应中没有active_zone_ids")
            else:
                print(f"   ❌ 响应中没有metadata.protection_zones")
                print(f"   响应字段: {list(result.keys())}")
                if 'metadata' in result:
                    print(f"   metadata字段: {list(result['metadata'].keys())}")
        else:
            print(f"   ❌ 基准算法API失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 基准算法API调用失败: {e}")
        return False
    
    # 4. 测试算法对比API
    print(f"\n🔄 4. 测试算法对比API")
    
    comparison_request = {
        "start": {"lng": 139.7670, "lat": 35.6810, "alt": 120},
        "end": {"lng": 139.7016, "lat": 35.6598, "alt": 120}
    }
    
    try:
        print(f"   发送算法对比请求...")
        response = requests.post('http://localhost:5000/api/algorithm-comparison', 
                               json=comparison_request, 
                               timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 算法对比API成功")
            
            # 检查改进算法结果
            if 'improved' in result:
                improved = result['improved']
                print(f"   改进算法结果:")
                if 'protectionZonesInfo' in improved:
                    protection_info = improved['protectionZonesInfo']
                    if 'collision_cost_breakdown' in protection_info:
                        breakdown = protection_info['collision_cost_breakdown']
                        print(f"     活跃保护区: {list(breakdown.keys())}")
                    else:
                        print(f"     ❌ 没有collision_cost_breakdown")
                else:
                    print(f"     ❌ 没有protectionZonesInfo")
            
            # 检查基准算法结果
            if 'baseline' in result:
                baseline = result['baseline']
                print(f"   基准算法结果:")
                if 'metadata' in baseline and 'protection_zones' in baseline['metadata']:
                    protection_info = baseline['metadata']['protection_zones']
                    if 'active_zone_ids' in protection_info:
                        active_ids = protection_info['active_zone_ids']
                        print(f"     活跃保护区: {active_ids}")
                    else:
                        print(f"     ❌ 没有active_zone_ids")
                else:
                    print(f"     ❌ 没有metadata.protection_zones")
        else:
            print(f"   ❌ 算法对比API失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 算法对比API调用失败: {e}")
        return False
    
    # 5. 生成前端调试代码
    print(f"\n🖥️ 5. 生成前端调试代码")
    
    frontend_debug_code = '''
// 在浏览器控制台中运行这段代码来调试前端
console.log("🔍 开始前端保护区调试");

// 检查cityManager是否存在
if (typeof cityManager !== 'undefined') {
    console.log("✅ cityManager存在");
    
    // 检查updateProtectionZoneStatus方法
    if (typeof cityManager.updateProtectionZoneStatus === 'function') {
        console.log("✅ updateProtectionZoneStatus方法存在");
        
        // 模拟调用
        const testZoneIds = ['tokyo_station', 'shibuya_crossing'];
        console.log("🧪 模拟调用updateProtectionZoneStatus:", testZoneIds);
        cityManager.updateProtectionZoneStatus(testZoneIds);
        
    } else {
        console.error("❌ updateProtectionZoneStatus方法不存在");
    }
} else {
    console.error("❌ cityManager不存在");
}

// 检查算法对比管理器
if (typeof algorithmComparisonManager !== 'undefined') {
    console.log("✅ algorithmComparisonManager存在");
} else {
    console.error("❌ algorithmComparisonManager不存在");
}
'''
    
    print("   请在浏览器控制台中运行以下代码:")
    print("   " + "="*50)
    print(frontend_debug_code)
    print("   " + "="*50)
    
    return True

if __name__ == "__main__":
    print("🚀 启动保护区流程调试")
    print("请确保后端服务器正在运行 (http://localhost:5000)")
    
    input("按回车键开始调试...")
    
    success = debug_protection_zones_flow()
    
    if success:
        print(f"\n✅ 后端调试完成")
        print(f"💡 下一步:")
        print(f"   1. 在浏览器中打开前端页面")
        print(f"   2. 打开浏览器开发者工具 (F12)")
        print(f"   3. 在控制台中运行上面提供的调试代码")
        print(f"   4. 运行一次算法，观察控制台输出")
    else:
        print(f"\n❌ 后端调试失败")
        print(f"💡 请检查:")
        print(f"   1. 后端服务器是否正在运行")
        print(f"   2. 保护区API是否正常")
        print(f"   3. 算法API是否正常")
