#!/usr/bin/env python3
"""
测试东京站保护区检测问题
验证在东京站范围内的路径是否能正确检测到东京站保护区
"""

import sys
import os
import math
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

def calculate_distance(lng1, lat1, lng2, lat2):
    """计算两点间距离（米）"""
    # 简化的距离计算
    lat_diff = lat2 - lat1
    lng_diff = lng2 - lng1
    
    # 转换为米（大约）
    lat_meters = lat_diff * 110540  # 1度纬度约110540米
    lng_meters = lng_diff * 111320 * math.cos(math.radians((lat1 + lat2) / 2))  # 1度经度约111320米*cos(纬度)
    
    return math.sqrt(lat_meters**2 + lng_meters**2)

def test_tokyo_station_detection():
    """测试东京站保护区检测"""
    
    print("=" * 60)
    print("东京站保护区检测测试")
    print("=" * 60)
    
    try:
        from protection_zones import ProtectionZoneManager
        
        # 创建保护区管理器
        manager = ProtectionZoneManager()
        
        # 找到东京站保护区
        tokyo_station = None
        for zone in manager.zones:
            if zone.id == "tokyo_station":
                tokyo_station = zone
                break
        
        if not tokyo_station:
            print("❌ 未找到东京站保护区")
            return False
        
        print(f"✅ 找到东京站保护区")
        print(f"   中心坐标: {tokyo_station.center}")
        print(f"   半径: {tokyo_station.radius}米")
        
        # 测试不同的路径点
        test_scenarios = [
            {
                "name": "东京站中心点",
                "path": [(139.7673, 35.6812)],  # 东京站中心
                "expected": True
            },
            {
                "name": "东京站内部路径",
                "path": [
                    (139.7670, 35.6810),  # 东京站西南
                    (139.7673, 35.6812),  # 东京站中心
                    (139.7676, 35.6814),  # 东京站东北
                ],
                "expected": True
            },
            {
                "name": "经过东京站边缘",
                "path": [
                    (139.7640, 35.6812),  # 东京站西侧约300米
                    (139.7673, 35.6812),  # 东京站中心
                    (139.7706, 35.6812),  # 东京站东侧约300米
                ],
                "expected": True
            },
            {
                "name": "距离东京站较远",
                "path": [
                    (139.7500, 35.6700),  # 距离东京站约2公里
                    (139.7520, 35.6720),
                ],
                "expected": False
            }
        ]
        
        print(f"\n开始测试不同缓冲区大小的检测效果:")
        print("-" * 50)
        
        # 测试不同的缓冲区大小
        buffer_sizes = [50, 200, 500, 1000]
        
        for buffer_size in buffer_sizes:
            print(f"\n🔍 测试缓冲区大小: {buffer_size}米")
            print("-" * 30)
            
            for scenario in test_scenarios:
                path_points = scenario["path"]
                expected = scenario["expected"]
                
                # 检测保护区
                detected_zones = manager.get_zones_for_path(path_points, buffer_distance=buffer_size)
                tokyo_detected = any(zone.id == "tokyo_station" for zone in detected_zones)
                
                # 计算路径到东京站的最近距离
                min_distance = float('inf')
                for lng, lat in path_points:
                    distance = calculate_distance(lng, lat, tokyo_station.center[0], tokyo_station.center[1])
                    min_distance = min(min_distance, distance)
                
                # 判断检测结果
                status = "✅" if tokyo_detected == expected else "❌"
                detection_status = "检测到" if tokyo_detected else "未检测到"
                
                print(f"   {status} {scenario['name']}: {detection_status}")
                print(f"      最近距离: {min_distance:.0f}米")
                print(f"      检测范围: {tokyo_station.radius + buffer_size:.0f}米")
                print(f"      预期: {'应检测到' if expected else '不应检测到'}")
                
                if tokyo_detected != expected:
                    if expected and not tokyo_detected:
                        print(f"      ⚠️ 问题: 缓冲区{buffer_size}米太小，无法检测到东京站")
                    elif not expected and tokyo_detected:
                        print(f"      ⚠️ 问题: 缓冲区{buffer_size}米太大，检测到了不相关的保护区")
        
        # 推荐最佳缓冲区大小
        print(f"\n" + "=" * 60)
        print("缓冲区大小分析:")
        print(f"🏢 东京站半径: {tokyo_station.radius}米")
        print(f"📏 建议缓冲区大小:")
        print(f"   - 50米: 太小，可能检测不到保护区内的路径")
        print(f"   - 200米: 适中，能检测到保护区附近的路径")
        print(f"   - 500米: 较大，确保检测到保护区内的路径")
        print(f"   - 1000米: 过大，可能检测到不相关的保护区")
        
        print(f"\n💡 推荐使用500米缓冲区，确保在东京站范围内能检测到东京站保护区")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_collision_cost_calculation():
    """测试碰撞代价计算"""
    
    print(f"\n" + "=" * 60)
    print("碰撞代价计算测试")
    print("=" * 60)
    
    try:
        from protection_zones import ProtectionZoneManager
        
        manager = ProtectionZoneManager()
        
        # 模拟东京站内的路径
        tokyo_path = [
            (139.7670, 35.6810),  # 东京站西南
            (139.7673, 35.6812),  # 东京站中心
            (139.7676, 35.6814),  # 东京站东北
        ]
        
        print(f"📍 测试路径（东京站内）:")
        for i, (lng, lat) in enumerate(tokyo_path):
            print(f"   点{i+1}: ({lng}, {lat})")
        
        # 计算碰撞代价
        total_collision_cost = manager.calculate_path_collision_cost(tokyo_path)
        
        print(f"\n💰 碰撞代价计算结果:")
        print(f"   总碰撞代价: {total_collision_cost:.4f}")
        
        if total_collision_cost > 0:
            print(f"   ✅ 碰撞代价计算正常，检测到了保护区影响")
        else:
            print(f"   ❌ 碰撞代价为0，可能是保护区检测有问题")
        
        # 获取相关保护区
        relevant_zones = manager.get_zones_for_path(tokyo_path)
        print(f"\n🛡️ 检测到的保护区:")
        for zone in relevant_zones:
            print(f"   - {zone.name}: 碰撞代价 {zone.average_crash_cost}/m²")
        
        return total_collision_cost > 0
        
    except Exception as e:
        print(f"❌ 碰撞代价测试失败: {e}")
        return False

def main():
    """主测试函数"""
    
    print("🚀 开始东京站保护区检测测试...")
    
    # 执行检测测试
    detection_ok = test_tokyo_station_detection()
    
    # 执行碰撞代价测试
    collision_ok = test_collision_cost_calculation()
    
    # 总结
    print(f"\n" + "=" * 60)
    print("测试总结:")
    print(f"🔍 保护区检测: {'✅ 正常' if detection_ok else '❌ 异常'}")
    print(f"💰 碰撞代价计算: {'✅ 正常' if collision_ok else '❌ 异常'}")
    
    if detection_ok and collision_ok:
        print(f"\n🎉 所有测试通过！")
        print(f"💡 建议: 使用500米缓冲区确保在保护区范围内能正确检测")
    else:
        print(f"\n⚠️ 部分测试失败，需要调整缓冲区大小")

if __name__ == "__main__":
    main()
