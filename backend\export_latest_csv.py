#!/usr/bin/env python3
"""
导出最新JSON文件的CSV数据
"""

import json
import csv
import os
from datetime import datetime

def export_latest_csv():
    """导出最新的CSV文件"""
    print("📊 导出最新JSON文件的CSV数据...")
    
    # 使用最新的JSON文件
    latest_json = 'all_81_paths_data_20250731_122209.json'
    print(f"📂 使用JSON文件: {latest_json}")
    
    # 读取JSON数据
    with open(latest_json, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 获取选中路径ID
    selected_path_id = data.get('selected_path', {}).get('selected_path_id')
    print(f"🎯 选中路径ID: {selected_path_id}")
    
    # 获取起点和终点坐标
    metadata = data.get('metadata', {})
    start_point = metadata.get('start_point', {})
    end_point = metadata.get('end_point', {})
    
    # 创建CSV文件
    timestamp = datetime.now().strftime('%Y-%m-%dT%H-%M-%S')
    filename = f'路径数据_完整版_{timestamp}.csv'
    filepath = os.path.join('csv', filename)
    os.makedirs('csv', exist_ok=True)
    
    # 准备CSV数据
    csv_data = []
    all_paths = sorted(data['all_paths'], key=lambda x: x.get('final_cost', float('inf')))
    
    protection_zones_found = 0
    
    for path_data in all_paths:
        path_id = path_data.get('path_id')
        is_selected = 1 if path_id == selected_path_id else 0
        
        # 处理基准路径的显示
        if path_id == 'BASELINE_A*':
            display_id = 'BASELINE_A*'
            start_lng = start_point.get('lng', 0)
            start_lat = start_point.get('lat', 0)
            start_alt = start_point.get('alt', 0)
            end_lng = end_point.get('lng', 0)
            end_lat = end_point.get('lat', 0)
            end_alt = end_point.get('alt', 0)
            flight_height = start_alt
            cluster_id = '基准算法'
        else:
            display_id = str(path_id)
            start_lng = start_point.get('lng', 0)
            start_lat = start_point.get('lat', 0)
            start_alt = start_point.get('alt', 0)
            end_lng = end_point.get('lng', 0)
            end_lat = end_point.get('lat', 0)
            end_alt = end_point.get('alt', 0)
            
            height_layer = path_data.get('height_layer', 1)
            if isinstance(height_layer, (int, float)):
                flight_height = float(height_layer) * 10
            else:
                flight_height = start_alt
            
            cluster_id = path_data.get('cluster_id', '')
        
        # 使用修复后的保护区信息提取
        from export_all_paths_data import extract_protection_zones_info
        protection_info = extract_protection_zones_info(path_data)
        
        protection_zones_count = protection_info['protection_zones_count']
        protection_zones_list = protection_info['protection_zones_list']
        protection_zones_types = protection_info['protection_zones_types']
        
        if protection_zones_count > 0:
            protection_zones_found += 1
            print(f"   路径{path_id}: 找到{protection_zones_count}个保护区 - {protection_zones_list}")
        
        csv_data.append({
            'path_id': display_id,
            'direction': path_data.get('flight_direction', ''),
            'height': path_data.get('height_layer', ''),
            'start_lng': round(start_lng, 6),
            'start_lat': round(start_lat, 6),
            'start_alt': round(start_alt, 2),
            'end_lng': round(end_lng, 6),
            'end_lat': round(end_lat, 6),
            'end_alt': round(end_alt, 2),
            'flight_height': round(flight_height, 2),
            'cluster_id': cluster_id,
            'protection_zones_count': protection_zones_count,
            'protection_zones_list': protection_zones_list,
            'protection_zones_types': protection_zones_types,
            'path_length': round(path_data.get('path_length', 0), 2),
            'turning_cost': round(path_data.get('turning_cost', 0), 4),
            'risk_value': round(path_data.get('risk_value', 0), 4),
            'collision_cost': round(path_data.get('collision_cost', 0), 4),
            'final_cost': round(path_data.get('final_cost', 0), 6),
            'waypoint_count': path_data.get('waypoints_count', 0),
            'selected': is_selected
        })
    
    # 写入CSV文件
    with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['path_id', 'direction', 'height', 'start_lng', 'start_lat', 'start_alt',
                     'end_lng', 'end_lat', 'end_alt', 'flight_height', 'cluster_id',
                     'protection_zones_count', 'protection_zones_list', 'protection_zones_types',
                     'path_length', 'turning_cost', 'risk_value', 'collision_cost',
                     'final_cost', 'waypoint_count', 'selected']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writerow({
            'path_id': '路径ID',
            'direction': '方向',
            'height': '高度层',
            'start_lng': '起点经度',
            'start_lat': '起点纬度',
            'start_alt': '起点高度',
            'end_lng': '终点经度',
            'end_lat': '终点纬度',
            'end_alt': '终点高度',
            'flight_height': '飞行高度',
            'cluster_id': '簇ID',
            'protection_zones_count': '保护区数量',
            'protection_zones_list': '保护区列表',
            'protection_zones_types': '保护区类型',
            'path_length': '路径长度',
            'turning_cost': '转向成本',
            'risk_value': '风险值',
            'collision_cost': '碰撞代价',
            'final_cost': '最终代价',
            'waypoint_count': '航点数',
            'selected': '选中'
        })
        
        writer.writerows(csv_data)
    
    print(f"✅ CSV导出成功: {filepath}")
    print(f"📊 统计信息:")
    print(f"   总路径数: {len(csv_data)}")
    print(f"   有保护区的路径数: {protection_zones_found}")
    selected_count = sum(1 for row in csv_data if row['selected'] == 1)
    print(f"   选中路径: {selected_count}")
    
    # 显示文件位置
    abs_path = os.path.abspath(filepath)
    print(f"\n📁 文件完整路径: {abs_path}")
    
    return filepath

if __name__ == "__main__":
    export_latest_csv()
