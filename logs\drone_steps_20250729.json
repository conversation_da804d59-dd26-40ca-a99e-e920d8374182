[{"session_id": "20250729_000346", "step_number": 1, "timestamp": "2025-07-29T00:03:46.616705", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "85c73e86-3b3e-4903-aef1-b5891141b276", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "85c73e86-3b3e-4903-aef1-b5891141b276", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-627.0796106856593, 971.8577657925093, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_000346", "step_number": 2, "timestamp": "2025-07-29T00:03:46.621941", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "85c73e86-3b3e-4903-aef1-b5891141b276", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "85c73e86-3b3e-4903-aef1-b5891141b276", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-627.0796106856593, 971.8577657925093, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_000346", "step_number": 3, "timestamp": "2025-07-29T00:03:48.466354", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1840.463399887085}, {"session_id": "20250729_000346", "step_number": 4, "timestamp": "2025-07-29T00:03:48.467502", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1841.611623764038}, {"session_id": "20250729_000346", "step_number": 5, "timestamp": "2025-07-29T00:03:48.498102", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.578685760498047}, {"session_id": "20250729_000346", "step_number": 6, "timestamp": "2025-07-29T00:03:48.499108", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.10337257385254}, {"session_id": "20250729_001414", "step_number": 1, "timestamp": "2025-07-29T00:14:14.480716", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ce3e6933-cc2b-41a5-b0ff-21576c670b8a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ce3e6933-cc2b-41a5-b0ff-21576c670b8a", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-949.8214357706838, 1262.633230917564, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_001414", "step_number": 2, "timestamp": "2025-07-29T00:14:14.486932", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ce3e6933-cc2b-41a5-b0ff-21576c670b8a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ce3e6933-cc2b-41a5-b0ff-21576c670b8a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-949.8214357706838, 1262.633230917564, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_001414", "step_number": 3, "timestamp": "2025-07-29T00:14:17.069532", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2576.6162872314453}, {"session_id": "20250729_001414", "step_number": 4, "timestamp": "2025-07-29T00:14:17.071728", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2578.81236076355}, {"session_id": "20250729_001414", "step_number": 5, "timestamp": "2025-07-29T00:14:17.110466", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.18709945678711}, {"session_id": "20250729_001414", "step_number": 6, "timestamp": "2025-07-29T00:14:17.112521", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.24226760864258}, {"session_id": "20250729_001414", "step_number": 7, "timestamp": "2025-07-29T00:17:42.484023", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5940f3cb-8b7b-4a4a-8702-fa8337062d79", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5940f3cb-8b7b-4a4a-8702-fa8337062d79", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1109.7039373774335, 1287.8695963027487, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_001414", "step_number": 8, "timestamp": "2025-07-29T00:17:42.490783", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5940f3cb-8b7b-4a4a-8702-fa8337062d79", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5940f3cb-8b7b-4a4a-8702-fa8337062d79", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1109.7039373774335, 1287.8695963027487, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_001414", "step_number": 9, "timestamp": "2025-07-29T00:17:45.387454", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2891.988754272461}, {"session_id": "20250729_001414", "step_number": 10, "timestamp": "2025-07-29T00:17:45.388568", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2893.1028842926025}, {"session_id": "20250729_001414", "step_number": 11, "timestamp": "2025-07-29T00:17:45.426606", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.43886947631836}, {"session_id": "20250729_001414", "step_number": 12, "timestamp": "2025-07-29T00:17:45.428363", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.19553756713867}, {"session_id": "20250729_002741", "step_number": 1, "timestamp": "2025-07-29T00:27:41.369712", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "acf64460-a934-4fe5-9f56-481778a64939", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "acf64460-a934-4fe5-9f56-481778a64939", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1109.7039373774335, 1287.8695963027487, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 67}}, "duration_ms": null}, {"session_id": "20250729_002741", "step_number": 2, "timestamp": "2025-07-29T00:27:41.380799", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "acf64460-a934-4fe5-9f56-481778a64939", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "acf64460-a934-4fe5-9f56-481778a64939", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1109.7039373774335, 1287.8695963027487, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_002741", "step_number": 3, "timestamp": "2025-07-29T00:27:44.250851", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2860.302686691284}, {"session_id": "20250729_002741", "step_number": 4, "timestamp": "2025-07-29T00:27:44.252864", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2862.316131591797}, {"session_id": "20250729_002741", "step_number": 5, "timestamp": "2025-07-29T00:27:44.300970", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 47.05357551574707}, {"session_id": "20250729_002741", "step_number": 6, "timestamp": "2025-07-29T00:27:44.302739", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 48.82216453552246}, {"session_id": "20250729_002741", "step_number": 7, "timestamp": "2025-07-29T00:30:35.672182", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f59b78d4-2c5a-4666-8d6f-c57ccf6bec5e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f59b78d4-2c5a-4666-8d6f-c57ccf6bec5e", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(373.43817161602925, 1809.8242637057049, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_002741", "step_number": 8, "timestamp": "2025-07-29T00:30:35.677874", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f59b78d4-2c5a-4666-8d6f-c57ccf6bec5e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f59b78d4-2c5a-4666-8d6f-c57ccf6bec5e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(373.43817161602925, 1809.8242637057049, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_002741", "step_number": 9, "timestamp": "2025-07-29T00:30:38.712800", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3030.1589965820312}, {"session_id": "20250729_002741", "step_number": 10, "timestamp": "2025-07-29T00:30:38.714048", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 3031.407356262207}, {"session_id": "20250729_002741", "step_number": 11, "timestamp": "2025-07-29T00:30:38.770426", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 53.544044494628906}, {"session_id": "20250729_002741", "step_number": 12, "timestamp": "2025-07-29T00:30:38.774829", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 57.94787406921387}, {"session_id": "20250729_002741", "step_number": 13, "timestamp": "2025-07-29T00:32:44.772941", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7903c7fb-e439-4038-b2cc-27b60c8fda95", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7903c7fb-e439-4038-b2cc-27b60c8fda95", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1130.5715587012467, 1266.6033702496372, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_002741", "step_number": 14, "timestamp": "2025-07-29T00:32:44.779711", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7903c7fb-e439-4038-b2cc-27b60c8fda95", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7903c7fb-e439-4038-b2cc-27b60c8fda95", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1130.5715587012467, 1266.6033702496372, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_002741", "step_number": 15, "timestamp": "2025-07-29T00:32:47.579376", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2792.1674251556396}, {"session_id": "20250729_002741", "step_number": 16, "timestamp": "2025-07-29T00:32:47.581026", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2793.8175201416016}, {"session_id": "20250729_002741", "step_number": 17, "timestamp": "2025-07-29T00:32:47.633902", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 51.35965347290039}, {"session_id": "20250729_002741", "step_number": 18, "timestamp": "2025-07-29T00:32:47.635670", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 53.12776565551758}, {"session_id": "20250729_003612", "step_number": 1, "timestamp": "2025-07-29T00:36:12.553741", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "820191b4-6de3-4421-ab7b-544dd7c21e11", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "820191b4-6de3-4421-ab7b-544dd7c21e11", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1331.2012902048873, 1292.94930030753, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_003612", "step_number": 2, "timestamp": "2025-07-29T00:36:12.560516", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "820191b4-6de3-4421-ab7b-544dd7c21e11", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "820191b4-6de3-4421-ab7b-544dd7c21e11", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1331.2012902048873, 1292.94930030753, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_003612", "step_number": 3, "timestamp": "2025-07-29T00:36:15.477905", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2910.9139442443848}, {"session_id": "20250729_003612", "step_number": 4, "timestamp": "2025-07-29T00:36:15.480461", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2913.4702682495117}, {"session_id": "20250729_003612", "step_number": 5, "timestamp": "2025-07-29T00:36:15.522125", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 40.29202461242676}, {"session_id": "20250729_003612", "step_number": 6, "timestamp": "2025-07-29T00:36:15.524786", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 42.9530143737793}, {"session_id": "20250729_003904", "step_number": 1, "timestamp": "2025-07-29T00:39:04.934251", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "54d3b82c-33a5-40eb-903d-823d30fb8167", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "54d3b82c-33a5-40eb-903d-823d30fb8167", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(1421.361402635262, -2091.0285910855337, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 71}}, "duration_ms": null}, {"session_id": "20250729_003904", "step_number": 2, "timestamp": "2025-07-29T00:39:04.944009", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "54d3b82c-33a5-40eb-903d-823d30fb8167", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "54d3b82c-33a5-40eb-903d-823d30fb8167", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(1421.361402635262, -2091.0285910855337, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_003904", "step_number": 3, "timestamp": "2025-07-29T00:39:09.213430", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 4262.291193008423}, {"session_id": "20250729_003904", "step_number": 4, "timestamp": "2025-07-29T00:39:09.216677", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 4265.538692474365}, {"session_id": "20250729_003904", "step_number": 5, "timestamp": "2025-07-29T00:39:09.267924", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 47.74594306945801}, {"session_id": "20250729_003904", "step_number": 6, "timestamp": "2025-07-29T00:39:09.269966", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.78799819946289}, {"session_id": "20250729_004425", "step_number": 1, "timestamp": "2025-07-29T00:44:25.211529", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "195b2611-2eba-4cd1-ad8f-3aa776d35b02", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "195b2611-2eba-4cd1-ad8f-3aa776d35b02", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-795.5521775931394, 1289.8561174075078, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_004425", "step_number": 2, "timestamp": "2025-07-29T00:44:25.218589", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "195b2611-2eba-4cd1-ad8f-3aa776d35b02", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "195b2611-2eba-4cd1-ad8f-3aa776d35b02", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-795.5521775931394, 1289.8561174075078, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_004425", "step_number": 3, "timestamp": "2025-07-29T00:44:27.508950", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2285.4413986206055}, {"session_id": "20250729_004425", "step_number": 4, "timestamp": "2025-07-29T00:44:27.511661", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2288.1526947021484}, {"session_id": "20250729_004425", "step_number": 5, "timestamp": "2025-07-29T00:44:27.556992", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 43.708086013793945}, {"session_id": "20250729_004425", "step_number": 6, "timestamp": "2025-07-29T00:44:27.560212", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 46.92792892456055}, {"session_id": "20250729_004728", "step_number": 1, "timestamp": "2025-07-29T00:47:28.731315", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a0fd3e8c-0653-4690-91af-804b692edb64", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a0fd3e8c-0653-4690-91af-804b692edb64", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-540.9235849805546, -856.5609525426709, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 12}}, "duration_ms": null}, {"session_id": "20250729_004728", "step_number": 2, "timestamp": "2025-07-29T00:47:28.738233", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a0fd3e8c-0653-4690-91af-804b692edb64", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a0fd3e8c-0653-4690-91af-804b692edb64", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-540.9235849805546, -856.5609525426709, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_004728", "step_number": 3, "timestamp": "2025-07-29T00:47:30.453216", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1709.8417282104492}, {"session_id": "20250729_004728", "step_number": 4, "timestamp": "2025-07-29T00:47:30.455831", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1712.456464767456}, {"session_id": "20250729_004728", "step_number": 5, "timestamp": "2025-07-29T00:47:30.496656", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.70534896850586}, {"session_id": "20250729_004728", "step_number": 6, "timestamp": "2025-07-29T00:47:30.500803", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 42.85311698913574}, {"session_id": "20250729_010933", "step_number": 1, "timestamp": "2025-07-29T01:09:33.337616", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "148796f9-77c8-4d8c-bcea-593b9a8bbab5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "148796f9-77c8-4d8c-bcea-593b9a8bbab5", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(384.4025893989705, 13.51583396430243, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_010933", "step_number": 2, "timestamp": "2025-07-29T01:09:33.353827", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "148796f9-77c8-4d8c-bcea-593b9a8bbab5", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "148796f9-77c8-4d8c-bcea-593b9a8bbab5", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(384.4025893989705, 13.51583396430243, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_010933", "step_number": 3, "timestamp": "2025-07-29T01:09:34.315929", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 956.3632011413574}, {"session_id": "20250729_010933", "step_number": 4, "timestamp": "2025-07-29T01:09:34.319924", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 960.3579044342041}, {"session_id": "20250729_010933", "step_number": 5, "timestamp": "2025-07-29T01:09:34.347035", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 25.088787078857422}, {"session_id": "20250729_010933", "step_number": 6, "timestamp": "2025-07-29T01:09:34.349260", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 27.313947677612305}, {"session_id": "20250729_010933", "step_number": 7, "timestamp": "2025-07-29T01:15:57.456255", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "09e94581-8487-4352-810b-0a86a975b963", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "09e94581-8487-4352-810b-0a86a975b963", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_010933", "step_number": 8, "timestamp": "2025-07-29T01:15:57.462741", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "09e94581-8487-4352-810b-0a86a975b963", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "09e94581-8487-4352-810b-0a86a975b963", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_010933", "step_number": 9, "timestamp": "2025-07-29T01:15:59.927758", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2459.336280822754}, {"session_id": "20250729_010933", "step_number": 10, "timestamp": "2025-07-29T01:15:59.929370", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2461.4593982696533}, {"session_id": "20250729_010933", "step_number": 11, "timestamp": "2025-07-29T01:15:59.967019", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.45331954956055}, {"session_id": "20250729_010933", "step_number": 12, "timestamp": "2025-07-29T01:15:59.969176", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.60981559753418}, {"session_id": "20250729_010933", "step_number": 13, "timestamp": "2025-07-29T01:18:34.027139", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a22c3777-f63b-4918-90b5-7076b56828e8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a22c3777-f63b-4918-90b5-7076b56828e8", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 107}}, "duration_ms": null}, {"session_id": "20250729_010933", "step_number": 14, "timestamp": "2025-07-29T01:18:34.041610", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a22c3777-f63b-4918-90b5-7076b56828e8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a22c3777-f63b-4918-90b5-7076b56828e8", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_010933", "step_number": 15, "timestamp": "2025-07-29T01:18:36.179205", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2127.3880004882812}, {"session_id": "20250729_010933", "step_number": 16, "timestamp": "2025-07-29T01:18:36.181817", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2129.999876022339}, {"session_id": "20250729_010933", "step_number": 17, "timestamp": "2025-07-29T01:18:36.215266", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.207799911499023}, {"session_id": "20250729_010933", "step_number": 18, "timestamp": "2025-07-29T01:18:36.218262", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.20400619506836}, {"session_id": "20250729_010933", "step_number": 19, "timestamp": "2025-07-29T01:21:00.662573", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d9f47241-8c0a-408b-a966-628669f5d5bd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d9f47241-8c0a-408b-a966-628669f5d5bd", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_010933", "step_number": 20, "timestamp": "2025-07-29T01:21:00.668796", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d9f47241-8c0a-408b-a966-628669f5d5bd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d9f47241-8c0a-408b-a966-628669f5d5bd", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_010933", "step_number": 21, "timestamp": "2025-07-29T01:21:02.701376", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2027.3160934448242}, {"session_id": "20250729_010933", "step_number": 22, "timestamp": "2025-07-29T01:21:02.703901", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2029.8409461975098}, {"session_id": "20250729_010933", "step_number": 23, "timestamp": "2025-07-29T01:21:02.756239", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.329519271850586}, {"session_id": "20250729_010933", "step_number": 24, "timestamp": "2025-07-29T01:21:02.758983", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 52.07324028015137}, {"session_id": "20250729_012153", "step_number": 1, "timestamp": "2025-07-29T01:21:53.378888", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d291452c-63c2-4fc1-9928-22edd3a25e8f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d291452c-63c2-4fc1-9928-22edd3a25e8f", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_012153", "step_number": 2, "timestamp": "2025-07-29T01:21:53.384846", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d291452c-63c2-4fc1-9928-22edd3a25e8f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d291452c-63c2-4fc1-9928-22edd3a25e8f", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_012153", "step_number": 3, "timestamp": "2025-07-29T01:21:55.708477", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2320.347547531128}, {"session_id": "20250729_012153", "step_number": 4, "timestamp": "2025-07-29T01:21:55.712594", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2324.465036392212}, {"session_id": "20250729_012153", "step_number": 5, "timestamp": "2025-07-29T01:21:55.753304", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.59567642211914}, {"session_id": "20250729_012153", "step_number": 6, "timestamp": "2025-07-29T01:21:55.756421", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.71323776245117}, {"session_id": "20250729_012726", "step_number": 1, "timestamp": "2025-07-29T01:27:26.607053", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "da9031c9-bb6a-4a7e-a1aa-41976a2925ce", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "da9031c9-bb6a-4a7e-a1aa-41976a2925ce", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_012726", "step_number": 2, "timestamp": "2025-07-29T01:27:26.613802", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "da9031c9-bb6a-4a7e-a1aa-41976a2925ce", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "da9031c9-bb6a-4a7e-a1aa-41976a2925ce", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_012726", "step_number": 3, "timestamp": "2025-07-29T01:27:28.949061", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2330.268144607544}, {"session_id": "20250729_012726", "step_number": 4, "timestamp": "2025-07-29T01:27:28.952158", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2333.3654403686523}, {"session_id": "20250729_012726", "step_number": 5, "timestamp": "2025-07-29T01:27:28.985874", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.671524047851562}, {"session_id": "20250729_012726", "step_number": 6, "timestamp": "2025-07-29T01:27:28.988402", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.19923782348633}, {"session_id": "20250729_013120", "step_number": 1, "timestamp": "2025-07-29T01:31:20.056687", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9a01c309-6100-4d60-a29b-31bcb3181a90", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9a01c309-6100-4d60-a29b-31bcb3181a90", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_013120", "step_number": 2, "timestamp": "2025-07-29T01:31:20.061442", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9a01c309-6100-4d60-a29b-31bcb3181a90", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9a01c309-6100-4d60-a29b-31bcb3181a90", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_013120", "step_number": 3, "timestamp": "2025-07-29T01:31:22.381160", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2314.2263889312744}, {"session_id": "20250729_013120", "step_number": 4, "timestamp": "2025-07-29T01:31:22.385827", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2318.8931941986084}, {"session_id": "20250729_013120", "step_number": 5, "timestamp": "2025-07-29T01:31:22.430309", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.67771339416504}, {"session_id": "20250729_013120", "step_number": 6, "timestamp": "2025-07-29T01:31:22.433861", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.22991180419922}, {"session_id": "20250729_013757", "step_number": 1, "timestamp": "2025-07-29T01:37:57.234468", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f9721ec8-4af5-489f-a59f-f4475869922c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f9721ec8-4af5-489f-a59f-f4475869922c", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1290.2806165181362, 1288.082104220688, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_013757", "step_number": 2, "timestamp": "2025-07-29T01:37:57.246368", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f9721ec8-4af5-489f-a59f-f4475869922c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f9721ec8-4af5-489f-a59f-f4475869922c", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1290.2806165181362, 1288.082104220688, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_013757", "step_number": 3, "timestamp": "2025-07-29T01:38:00.246674", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2992.7544593811035}, {"session_id": "20250729_013757", "step_number": 4, "timestamp": "2025-07-29T01:38:00.249942", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2996.023178100586}, {"session_id": "20250729_013757", "step_number": 5, "timestamp": "2025-07-29T01:38:00.293950", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 40.67373275756836}, {"session_id": "20250729_013757", "step_number": 6, "timestamp": "2025-07-29T01:38:00.297188", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 43.91145706176758}, {"session_id": "20250729_013757", "step_number": 7, "timestamp": "2025-07-29T01:40:55.266144", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4a3760a5-5782-4084-9136-e355d55944c7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4a3760a5-5782-4084-9136-e355d55944c7", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1004.1784141082004, 1484.97939299117, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_013757", "step_number": 8, "timestamp": "2025-07-29T01:40:55.275825", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4a3760a5-5782-4084-9136-e355d55944c7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4a3760a5-5782-4084-9136-e355d55944c7", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1004.1784141082004, 1484.97939299117, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250729_013757", "step_number": 9, "timestamp": "2025-07-29T01:40:57.805974", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2522.571563720703}, {"session_id": "20250729_013757", "step_number": 10, "timestamp": "2025-07-29T01:40:57.809278", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2525.8755683898926}, {"session_id": "20250729_013757", "step_number": 11, "timestamp": "2025-07-29T01:40:57.853323", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.37063026428223}, {"session_id": "20250729_013757", "step_number": 12, "timestamp": "2025-07-29T01:40:57.856381", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 44.42954063415527}, {"session_id": "20250729_015029", "step_number": 1, "timestamp": "2025-07-29T01:50:29.576492", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8e1ee4a7-e34c-426b-8dad-667ceac98308", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8e1ee4a7-e34c-426b-8dad-667ceac98308", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_015029", "step_number": 2, "timestamp": "2025-07-29T01:50:29.582254", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8e1ee4a7-e34c-426b-8dad-667ceac98308", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8e1ee4a7-e34c-426b-8dad-667ceac98308", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_015029", "step_number": 3, "timestamp": "2025-07-29T01:50:31.986219", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2398.8349437713623}, {"session_id": "20250729_015029", "step_number": 4, "timestamp": "2025-07-29T01:50:31.990442", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2403.057098388672}, {"session_id": "20250729_015029", "step_number": 5, "timestamp": "2025-07-29T01:50:32.041052", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 46.4322566986084}, {"session_id": "20250729_015029", "step_number": 6, "timestamp": "2025-07-29T01:50:32.044242", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.622297286987305}, {"session_id": "20250729_015521", "step_number": 1, "timestamp": "2025-07-29T01:55:21.149991", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "95610223-500a-47fb-bcd1-e5ff5776332e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "95610223-500a-47fb-bcd1-e5ff5776332e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_015521", "step_number": 2, "timestamp": "2025-07-29T01:55:21.155261", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "95610223-500a-47fb-bcd1-e5ff5776332e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "95610223-500a-47fb-bcd1-e5ff5776332e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_015521", "step_number": 3, "timestamp": "2025-07-29T01:55:23.484626", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2325.0889778137207}, {"session_id": "20250729_015521", "step_number": 4, "timestamp": "2025-07-29T01:55:23.488759", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2329.2222023010254}, {"session_id": "20250729_015521", "step_number": 5, "timestamp": "2025-07-29T01:55:23.518623", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.520729064941406}, {"session_id": "20250729_015521", "step_number": 6, "timestamp": "2025-07-29T01:55:23.523099", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 30.99656105041504}, {"session_id": "20250729_015717", "step_number": 1, "timestamp": "2025-07-29T01:57:17.302891", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e2136921-32fb-46b3-9b79-7c8fc393a835", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e2136921-32fb-46b3-9b79-7c8fc393a835", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_015717", "step_number": 2, "timestamp": "2025-07-29T01:57:17.308117", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e2136921-32fb-46b3-9b79-7c8fc393a835", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e2136921-32fb-46b3-9b79-7c8fc393a835", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_015717", "step_number": 3, "timestamp": "2025-07-29T01:57:19.517710", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2203.724145889282}, {"session_id": "20250729_015717", "step_number": 4, "timestamp": "2025-07-29T01:57:19.521939", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2207.9529762268066}, {"session_id": "20250729_015717", "step_number": 5, "timestamp": "2025-07-29T01:57:19.555770", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 30.6851863861084}, {"session_id": "20250729_015717", "step_number": 6, "timestamp": "2025-07-29T01:57:19.561545", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.45968437194824}, {"session_id": "20250729_020136", "step_number": 1, "timestamp": "2025-07-29T02:01:36.674828", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "87e55fb9-f080-43af-bb7c-4d7142757f5b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "87e55fb9-f080-43af-bb7c-4d7142757f5b", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_020136", "step_number": 2, "timestamp": "2025-07-29T02:01:36.681829", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "87e55fb9-f080-43af-bb7c-4d7142757f5b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "87e55fb9-f080-43af-bb7c-4d7142757f5b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_020136", "step_number": 3, "timestamp": "2025-07-29T02:01:38.871591", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2182.7075481414795}, {"session_id": "20250729_020136", "step_number": 4, "timestamp": "2025-07-29T02:01:38.876579", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2187.695264816284}, {"session_id": "20250729_020136", "step_number": 5, "timestamp": "2025-07-29T02:01:38.918774", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.70565986633301}, {"session_id": "20250729_020136", "step_number": 6, "timestamp": "2025-07-29T02:01:38.922063", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 40.99416732788086}, {"session_id": "20250729_020338", "step_number": 1, "timestamp": "2025-07-29T02:03:38.278146", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "413cb553-54e8-4090-945f-114e70453382", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "413cb553-54e8-4090-945f-114e70453382", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_020338", "step_number": 2, "timestamp": "2025-07-29T02:03:38.282851", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "413cb553-54e8-4090-945f-114e70453382", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "413cb553-54e8-4090-945f-114e70453382", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_020338", "step_number": 3, "timestamp": "2025-07-29T02:03:40.469121", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2181.316614151001}, {"session_id": "20250729_020338", "step_number": 4, "timestamp": "2025-07-29T02:03:40.472830", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2185.025691986084}, {"session_id": "20250729_020338", "step_number": 5, "timestamp": "2025-07-29T02:03:40.506042", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.60681915283203}, {"session_id": "20250729_020338", "step_number": 6, "timestamp": "2025-07-29T02:03:40.512824", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.388397216796875}, {"session_id": "20250729_020338", "step_number": 7, "timestamp": "2025-07-29T02:05:13.859319", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5a7461b6-2ad1-49aa-b785-a4df8fa28113", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5a7461b6-2ad1-49aa-b785-a4df8fa28113", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_020338", "step_number": 8, "timestamp": "2025-07-29T02:05:13.866709", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5a7461b6-2ad1-49aa-b785-a4df8fa28113", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5a7461b6-2ad1-49aa-b785-a4df8fa28113", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_020338", "step_number": 9, "timestamp": "2025-07-29T02:05:16.437560", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2565.067768096924}, {"session_id": "20250729_020338", "step_number": 10, "timestamp": "2025-07-29T02:05:16.441210", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2568.7177181243896}, {"session_id": "20250729_020338", "step_number": 11, "timestamp": "2025-07-29T02:05:16.474510", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.499530792236328}, {"session_id": "20250729_020338", "step_number": 12, "timestamp": "2025-07-29T02:05:16.478511", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.499956130981445}, {"session_id": "20250729_020725", "step_number": 1, "timestamp": "2025-07-29T02:07:25.492052", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ebdcfa49-38f2-459d-96df-e3c26a495778", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ebdcfa49-38f2-459d-96df-e3c26a495778", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_020725", "step_number": 2, "timestamp": "2025-07-29T02:07:25.497781", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ebdcfa49-38f2-459d-96df-e3c26a495778", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ebdcfa49-38f2-459d-96df-e3c26a495778", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_020725", "step_number": 3, "timestamp": "2025-07-29T02:07:27.600787", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2096.6556072235107}, {"session_id": "20250729_020725", "step_number": 4, "timestamp": "2025-07-29T02:07:27.605108", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2100.9771823883057}, {"session_id": "20250729_020725", "step_number": 5, "timestamp": "2025-07-29T02:07:27.634849", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.50761604309082}, {"session_id": "20250729_020725", "step_number": 6, "timestamp": "2025-07-29T02:07:27.638377", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 30.03549575805664}, {"session_id": "20250729_020725", "step_number": 7, "timestamp": "2025-07-29T02:09:21.513742", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8a9d990c-32fa-4744-b6f2-b8a7d6d046e4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8a9d990c-32fa-4744-b6f2-b8a7d6d046e4", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 97}}, "duration_ms": null}, {"session_id": "20250729_020725", "step_number": 8, "timestamp": "2025-07-29T02:09:21.525065", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8a9d990c-32fa-4744-b6f2-b8a7d6d046e4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8a9d990c-32fa-4744-b6f2-b8a7d6d046e4", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_020725", "step_number": 9, "timestamp": "2025-07-29T02:09:23.924182", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2386.484146118164}, {"session_id": "20250729_020725", "step_number": 10, "timestamp": "2025-07-29T02:09:23.928037", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2390.339136123657}, {"session_id": "20250729_020725", "step_number": 11, "timestamp": "2025-07-29T02:09:23.959866", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.02252769470215}, {"session_id": "20250729_020725", "step_number": 12, "timestamp": "2025-07-29T02:09:23.963426", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.58283233642578}, {"session_id": "20250729_065751", "step_number": 1, "timestamp": "2025-07-29T06:57:51.952330", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7f3c6799-9e0e-4eb2-917c-20e062c7ad3e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7f3c6799-9e0e-4eb2-917c-20e062c7ad3e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_065751", "step_number": 2, "timestamp": "2025-07-29T06:57:51.961452", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7f3c6799-9e0e-4eb2-917c-20e062c7ad3e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7f3c6799-9e0e-4eb2-917c-20e062c7ad3e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_065751", "step_number": 3, "timestamp": "2025-07-29T06:57:54.921235", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2953.2768726348877}, {"session_id": "20250729_065751", "step_number": 4, "timestamp": "2025-07-29T06:57:54.924999", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2957.040548324585}, {"session_id": "20250729_065751", "step_number": 5, "timestamp": "2025-07-29T06:57:54.961797", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.635616302490234}, {"session_id": "20250729_065751", "step_number": 6, "timestamp": "2025-07-29T06:57:54.966298", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.13648223876953}, {"session_id": "20250729_153517", "step_number": 1, "timestamp": "2025-07-29T15:35:17.850323", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4f6b0a23-46ae-4093-8020-b1a56413a87a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4f6b0a23-46ae-4093-8020-b1a56413a87a", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_153517", "step_number": 2, "timestamp": "2025-07-29T15:35:17.860816", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4f6b0a23-46ae-4093-8020-b1a56413a87a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4f6b0a23-46ae-4093-8020-b1a56413a87a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250729_153517", "step_number": 3, "timestamp": "2025-07-29T15:35:22.360391", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 4492.090702056885}, {"session_id": "20250729_153517", "step_number": 4, "timestamp": "2025-07-29T15:35:22.364368", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 4496.067523956299}, {"session_id": "20250729_153517", "step_number": 5, "timestamp": "2025-07-29T15:35:22.409861", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.506290435791016}, {"session_id": "20250729_153517", "step_number": 6, "timestamp": "2025-07-29T15:35:22.413619", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.26472091674805}, {"session_id": "20250729_154126", "step_number": 1, "timestamp": "2025-07-29T15:41:26.347199", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "aa5af3e2-332d-4dd4-b279-990c16fd9626", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "aa5af3e2-332d-4dd4-b279-990c16fd9626", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_154126", "step_number": 2, "timestamp": "2025-07-29T15:41:26.354259", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "aa5af3e2-332d-4dd4-b279-990c16fd9626", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "aa5af3e2-332d-4dd4-b279-990c16fd9626", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250729_154126", "step_number": 3, "timestamp": "2025-07-29T15:41:31.140315", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 4782.069444656372}, {"session_id": "20250729_154126", "step_number": 4, "timestamp": "2025-07-29T15:41:31.144394", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 4786.1480712890625}, {"session_id": "20250729_154126", "step_number": 5, "timestamp": "2025-07-29T15:41:31.197654", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 48.44832420349121}, {"session_id": "20250729_154126", "step_number": 6, "timestamp": "2025-07-29T15:41:31.202432", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 53.22742462158203}, {"session_id": "20250729_154252", "step_number": 1, "timestamp": "2025-07-29T15:42:52.372876", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "050818b9-ef8b-48bd-9985-de99d4029c28", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "050818b9-ef8b-48bd-9985-de99d4029c28", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_154252", "step_number": 2, "timestamp": "2025-07-29T15:42:52.380177", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "050818b9-ef8b-48bd-9985-de99d4029c28", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "050818b9-ef8b-48bd-9985-de99d4029c28", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250729_154252", "step_number": 3, "timestamp": "2025-07-29T15:42:57.550493", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 5162.219762802124}, {"session_id": "20250729_154252", "step_number": 4, "timestamp": "2025-07-29T15:42:57.555473", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 5167.19913482666}, {"session_id": "20250729_154252", "step_number": 5, "timestamp": "2025-07-29T15:42:57.611205", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 52.21891403198242}, {"session_id": "20250729_154252", "step_number": 6, "timestamp": "2025-07-29T15:42:57.616420", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 57.4338436126709}, {"session_id": "20250729_154338", "step_number": 1, "timestamp": "2025-07-29T15:43:38.119124", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "20fd3a2a-b944-4468-be54-559bb24823ca", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "20fd3a2a-b944-4468-be54-559bb24823ca", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_154338", "step_number": 2, "timestamp": "2025-07-29T15:43:38.125429", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "20fd3a2a-b944-4468-be54-559bb24823ca", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "20fd3a2a-b944-4468-be54-559bb24823ca", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250729_154338", "step_number": 3, "timestamp": "2025-07-29T15:43:43.112755", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 4980.569362640381}, {"session_id": "20250729_154338", "step_number": 4, "timestamp": "2025-07-29T15:43:43.116741", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 4984.555244445801}, {"session_id": "20250729_154338", "step_number": 5, "timestamp": "2025-07-29T15:43:43.173450", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 52.15859413146973}, {"session_id": "20250729_154338", "step_number": 6, "timestamp": "2025-07-29T15:43:43.177945", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 56.653499603271484}, {"session_id": "20250729_154428", "step_number": 1, "timestamp": "2025-07-29T15:44:28.299733", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4dd76d4e-f99c-4f56-9122-0eb44654e0ee", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4dd76d4e-f99c-4f56-9122-0eb44654e0ee", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_154428", "step_number": 2, "timestamp": "2025-07-29T15:44:28.305061", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4dd76d4e-f99c-4f56-9122-0eb44654e0ee", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4dd76d4e-f99c-4f56-9122-0eb44654e0ee", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250729_154428", "step_number": 3, "timestamp": "2025-07-29T15:44:33.250407", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 4940.131664276123}, {"session_id": "20250729_154428", "step_number": 4, "timestamp": "2025-07-29T15:44:33.254627", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 4944.351673126221}, {"session_id": "20250729_154428", "step_number": 5, "timestamp": "2025-07-29T15:44:33.309785", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.93939399719238}, {"session_id": "20250729_154428", "step_number": 6, "timestamp": "2025-07-29T15:44:33.314561", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 54.71515655517578}, {"session_id": "20250729_155015", "step_number": 1, "timestamp": "2025-07-29T15:50:15.628003", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "abc05892-df17-4b67-b973-e268796b5328", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "abc05892-df17-4b67-b973-e268796b5328", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_155015", "step_number": 2, "timestamp": "2025-07-29T15:50:15.633601", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "abc05892-df17-4b67-b973-e268796b5328", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "abc05892-df17-4b67-b973-e268796b5328", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250729_155015", "step_number": 3, "timestamp": "2025-07-29T15:50:20.300110", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 4660.962820053101}, {"session_id": "20250729_155015", "step_number": 4, "timestamp": "2025-07-29T15:50:20.304096", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 4664.9489402771}, {"session_id": "20250729_155015", "step_number": 5, "timestamp": "2025-07-29T15:50:20.353790", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.02606391906738}, {"session_id": "20250729_155015", "step_number": 6, "timestamp": "2025-07-29T15:50:20.358309", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.544334411621094}, {"session_id": "20250729_191319", "step_number": 1, "timestamp": "2025-07-29T19:13:19.840411", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3ec93378-bbcf-4023-9132-b4c7b4baec92", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3ec93378-bbcf-4023-9132-b4c7b4baec92", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_191347", "step_number": 1, "timestamp": "2025-07-29T19:13:47.333365", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "00eac591-93cf-4756-a549-b0b9fc0788d7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "00eac591-93cf-4756-a549-b0b9fc0788d7", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_191347", "step_number": 2, "timestamp": "2025-07-29T19:13:47.339862", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "00eac591-93cf-4756-a549-b0b9fc0788d7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "00eac591-93cf-4756-a549-b0b9fc0788d7", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_191347", "step_number": 3, "timestamp": "2025-07-29T19:13:47.667584", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 316.8377876281738}, {"session_id": "20250729_191347", "step_number": 4, "timestamp": "2025-07-29T19:13:47.673083", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 322.33715057373047}, {"session_id": "20250729_191347", "step_number": 5, "timestamp": "2025-07-29T19:13:47.696657", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.75758171081543}, {"session_id": "20250729_191347", "step_number": 6, "timestamp": "2025-07-29T19:13:47.701203", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 23.303985595703125}, {"session_id": "20250729_191532", "step_number": 1, "timestamp": "2025-07-29T19:15:32.324987", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "78c8e642-3acf-4b34-8817-c1e6b13d34f4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "78c8e642-3acf-4b34-8817-c1e6b13d34f4", "parameters": {"start_point": "(0, 0, 120)", "end_point": "(6000, -2000, 120)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_191532", "step_number": 2, "timestamp": "2025-07-29T19:15:32.332495", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "78c8e642-3acf-4b34-8817-c1e6b13d34f4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "78c8e642-3acf-4b34-8817-c1e6b13d34f4", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0, 0, 120)", "end_point": "(6000, -2000, 120)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_191532", "step_number": 3, "timestamp": "2025-07-29T19:15:40.090756", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 7749.6795654296875}, {"session_id": "20250729_191532", "step_number": 4, "timestamp": "2025-07-29T19:15:40.095757", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 7754.679918289185}, {"session_id": "20250729_191532", "step_number": 5, "timestamp": "2025-07-29T19:15:40.177092", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 75.6981372833252}, {"session_id": "20250729_191532", "step_number": 6, "timestamp": "2025-07-29T19:15:40.182284", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 81.8946361541748}, {"session_id": "20250729_192227", "step_number": 1, "timestamp": "2025-07-29T19:22:27.938617", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "64078a5d-a83a-4e01-b97f-e314d814ecfc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "64078a5d-a83a-4e01-b97f-e314d814ecfc", "parameters": {"start_point": "(0, 0, 120)", "end_point": "(6000, -2000, 120)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_192227", "step_number": 2, "timestamp": "2025-07-29T19:22:27.949194", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "64078a5d-a83a-4e01-b97f-e314d814ecfc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "64078a5d-a83a-4e01-b97f-e314d814ecfc", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0, 0, 120)", "end_point": "(6000, -2000, 120)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_192227", "step_number": 3, "timestamp": "2025-07-29T19:22:37.444354", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 9482.683420181274}, {"session_id": "20250729_192227", "step_number": 4, "timestamp": "2025-07-29T19:22:37.449857", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 9488.186120986938}, {"session_id": "20250729_192227", "step_number": 5, "timestamp": "2025-07-29T19:22:37.551314", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 94.83909606933594}, {"session_id": "20250729_192227", "step_number": 6, "timestamp": "2025-07-29T19:22:37.557801", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 101.32670402526855}, {"session_id": "20250729_195404", "step_number": 1, "timestamp": "2025-07-29T19:54:04.095704", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "af8dcfb0-844e-45ac-a1c7-6a87f44fb4fb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "af8dcfb0-844e-45ac-a1c7-6a87f44fb4fb", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 89}}, "duration_ms": null}, {"session_id": "20250729_195404", "step_number": 2, "timestamp": "2025-07-29T19:54:04.112650", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "af8dcfb0-844e-45ac-a1c7-6a87f44fb4fb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "af8dcfb0-844e-45ac-a1c7-6a87f44fb4fb", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_195404", "step_number": 3, "timestamp": "2025-07-29T19:54:07.909114", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3783.473253250122}, {"session_id": "20250729_195404", "step_number": 4, "timestamp": "2025-07-29T19:54:07.915409", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3789.768695831299}, {"session_id": "20250729_195404", "step_number": 5, "timestamp": "2025-07-29T19:54:07.972349", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 48.943281173706055}, {"session_id": "20250729_195404", "step_number": 6, "timestamp": "2025-07-29T19:54:07.978020", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 55.61256408691406}, {"session_id": "20250729_195849", "step_number": 1, "timestamp": "2025-07-29T19:58:49.393781", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "576107cb-dd6d-4035-9b2b-0f4f69f5eb36", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "576107cb-dd6d-4035-9b2b-0f4f69f5eb36", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250729_195849", "step_number": 2, "timestamp": "2025-07-29T19:58:49.413716", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "576107cb-dd6d-4035-9b2b-0f4f69f5eb36", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "576107cb-dd6d-4035-9b2b-0f4f69f5eb36", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_195849", "step_number": 3, "timestamp": "2025-07-29T19:59:00.031712", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 10598.268270492554}, {"session_id": "20250729_195849", "step_number": 4, "timestamp": "2025-07-29T19:59:00.041090", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 10607.646465301514}, {"session_id": "20250729_195849", "step_number": 5, "timestamp": "2025-07-29T19:59:00.184268", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 136.10458374023438}, {"session_id": "20250729_195849", "step_number": 6, "timestamp": "2025-07-29T19:59:00.191968", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 143.80526542663574}, {"session_id": "20250729_195849", "step_number": 7, "timestamp": "2025-07-29T20:02:10.049576", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b035c3fe-a79e-4360-af08-72f46ca48271", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b035c3fe-a79e-4360-af08-72f46ca48271", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250729_195849", "step_number": 8, "timestamp": "2025-07-29T20:02:10.060470", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b035c3fe-a79e-4360-af08-72f46ca48271", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b035c3fe-a79e-4360-af08-72f46ca48271", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_195849", "step_number": 9, "timestamp": "2025-07-29T20:02:13.761196", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3691.14089012146}, {"session_id": "20250729_195849", "step_number": 10, "timestamp": "2025-07-29T20:02:13.767414", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3697.3583698272705}, {"session_id": "20250729_195849", "step_number": 11, "timestamp": "2025-07-29T20:02:13.809322", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.9267463684082}, {"session_id": "20250729_195849", "step_number": 12, "timestamp": "2025-07-29T20:02:13.814306", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.90969467163086}, {"session_id": "20250729_195849", "step_number": 13, "timestamp": "2025-07-29T20:05:33.959127", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "35e85f81-a03d-422a-8880-7af0004b2c38", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "35e85f81-a03d-422a-8880-7af0004b2c38", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 83}}, "duration_ms": null}, {"session_id": "20250729_195849", "step_number": 14, "timestamp": "2025-07-29T20:05:33.975098", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "35e85f81-a03d-422a-8880-7af0004b2c38", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "35e85f81-a03d-422a-8880-7af0004b2c38", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250729_195849", "step_number": 15, "timestamp": "2025-07-29T20:05:39.663872", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 5673.886299133301}, {"session_id": "20250729_195849", "step_number": 16, "timestamp": "2025-07-29T20:05:39.670084", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 5680.098295211792}, {"session_id": "20250729_195849", "step_number": 17, "timestamp": "2025-07-29T20:05:39.741044", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 64.98122215270996}, {"session_id": "20250729_195849", "step_number": 18, "timestamp": "2025-07-29T20:05:39.748032", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 71.96974754333496}]