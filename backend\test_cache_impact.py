#!/usr/bin/env python3
"""
测试缓存机制对路径计算的影响
分析哪个缓存层影响了路径计算结果的一致性
"""

import sys
import os
import time
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from algorithms.data_structures import Point3D, PathPlanningRequest
from algorithms.unified_detection_manager import UnifiedDetectionManager
from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathPlanning
from algorithms.manager import AlgorithmManager

async def test_cache_impact_on_path_calculation():
    """测试缓存对路径计算的影响"""
    
    print("🔍 测试缓存机制对路径计算的影响")
    print("=" * 60)
    
    # 创建相同的测试路径
    waypoints = [
        Point3D(lng=139.767300, lat=35.681200, alt=100.0, x=139.767300, y=35.681200, z=100),
        Point3D(lng=139.773400, lat=35.715300, alt=100.0, x=139.773400, y=35.715300, z=100)
    ]
    
    print(f"📍 测试路径：起点({waypoints[0].lng:.6f}, {waypoints[0].lat:.6f}) → 终点({waypoints[-1].lng:.6f}, {waypoints[-1].lat:.6f})")
    
    # 1. 测试算法管理器缓存
    print(f"\n🔧 1. 测试算法管理器缓存影响")
    
    manager = AlgorithmManager()
    
    # 创建算法请求
    request_data = {
        'startPoint': {'lng': 139.767300, 'lat': 35.681200, 'alt': 100.0},
        'endPoint': {'lng': 139.773400, 'lat': 35.715300, 'alt': 100.0},
        'flightHeight': 100.0,
        'safetyDistance': 30.0,
        'algorithm': 'ImprovedClusterBased'
    }
    request = PathPlanningRequest(request_data)
    
    # 注册算法
    algorithm = ImprovedClusterBasedPathPlanning()
    manager.register_algorithm(algorithm)
    
    print(f"   缓存状态: {'启用' if manager.cache_enabled else '禁用'}")
    print(f"   缓存大小: {len(manager.cache.cache)}")
    
    # 第一次执行
    print(f"   第一次执行...")
    start_time = time.time()
    result1 = await manager.execute_algorithm('ImprovedClusterBased', request)
    time1 = time.time() - start_time
    print(f"   第一次结果: 成功={result1.success}, 耗时={time1:.3f}s")
    if hasattr(result1, 'risk_value'):
        print(f"   第一次风险值: {result1.risk_value:.4f}")
    if hasattr(result1, 'collision_cost'):
        print(f"   第一次碰撞代价: {result1.collision_cost:.4f}")
    
    # 第二次执行（应该使用缓存）
    print(f"   第二次执行...")
    start_time = time.time()
    result2 = await manager.execute_algorithm('ImprovedClusterBased', request)
    time2 = time.time() - start_time
    print(f"   第二次结果: 成功={result2.success}, 耗时={time2:.3f}s")
    if hasattr(result2, 'risk_value'):
        print(f"   第二次风险值: {result2.risk_value:.4f}")
    if hasattr(result2, 'collision_cost'):
        print(f"   第二次碰撞代价: {result2.collision_cost:.4f}")
    
    print(f"   缓存命中: {'是' if time2 < time1 * 0.1 else '否'}")
    print(f"   缓存大小: {len(manager.cache.cache)}")
    
    # 2. 测试建筑物检测器缓存
    print(f"\n🔧 2. 测试建筑物检测器缓存影响")
    
    unified_manager = UnifiedDetectionManager()
    
    # 第一次检测
    print(f"   第一次建筑物检测...")
    start_time = time.time()
    detection_result1 = unified_manager.detect_for_path(waypoints, 100.0)
    time1 = time.time() - start_time
    print(f"   第一次检测: 建筑物={len(detection_result1.buildings) if detection_result1.buildings else 0}, 耗时={time1:.3f}s")
    
    # 检查建筑物检测器缓存
    if hasattr(unified_manager, 'building_detector') and hasattr(unified_manager.building_detector, '_cache'):
        cache_size = len(unified_manager.building_detector._cache)
        print(f"   建筑物检测器缓存条目: {cache_size}")
        
        # 显示缓存键
        for i, key in enumerate(unified_manager.building_detector._cache.keys()):
            if i < 3:  # 只显示前3个
                print(f"   缓存键{i}: {key}")
    
    # 第二次检测（应该使用缓存）
    print(f"   第二次建筑物检测...")
    start_time = time.time()
    detection_result2 = unified_manager.detect_for_path(waypoints, 100.0)
    time2 = time.time() - start_time
    print(f"   第二次检测: 建筑物={len(detection_result2.buildings) if detection_result2.buildings else 0}, 耗时={time2:.3f}s")
    
    print(f"   建筑物检测缓存命中: {'是' if time2 < time1 * 0.5 else '否'}")
    
    # 3. 测试全局建筑物缓存
    print(f"\n🔧 3. 测试全局建筑物缓存影响")
    
    # 检查算法实例的全局缓存
    if hasattr(algorithm, '_global_buildings_cache'):
        global_cache_size = len(algorithm._global_buildings_cache)
        print(f"   算法全局建筑物缓存: {global_cache_size}个建筑物")
    else:
        print(f"   算法全局建筑物缓存: 不存在")
    
    # 检查CostCalculator的缓存
    if hasattr(algorithm, 'cost_calculator') and algorithm.cost_calculator:
        if hasattr(algorithm.cost_calculator, '_global_buildings_cache_ref'):
            cache_ref = algorithm.cost_calculator._global_buildings_cache_ref
            print(f"   CostCalculator全局缓存引用: {'存在' if cache_ref else '不存在'}")
        
        if hasattr(algorithm.cost_calculator, 'building_detector'):
            detector = algorithm.cost_calculator.building_detector
            if hasattr(detector, '_cache'):
                detector_cache_size = len(detector._cache)
                print(f"   CostCalculator建筑物检测器缓存: {detector_cache_size}个条目")
    
    # 4. 测试缓存一致性问题
    print(f"\n🔧 4. 测试缓存一致性问题")
    
    # 清除所有缓存
    print(f"   清除所有缓存...")
    manager.clear_cache()
    
    if hasattr(unified_manager, 'building_detector') and hasattr(unified_manager.building_detector, '_cache'):
        unified_manager.building_detector._cache.clear()
        print(f"   建筑物检测器缓存已清除")
    
    if hasattr(algorithm, '_global_buildings_cache'):
        algorithm._global_buildings_cache = []
        print(f"   算法全局缓存已清除")
    
    # 重新执行，观察结果差异
    print(f"   清除缓存后重新执行...")
    start_time = time.time()
    result3 = await manager.execute_algorithm('ImprovedClusterBased', request)
    time3 = time.time() - start_time
    print(f"   清除缓存后结果: 成功={result3.success}, 耗时={time3:.3f}s")
    if hasattr(result3, 'risk_value'):
        print(f"   清除缓存后风险值: {result3.risk_value:.4f}")
    if hasattr(result3, 'collision_cost'):
        print(f"   清除缓存后碰撞代价: {result3.collision_cost:.4f}")
    
    # 5. 分析缓存影响
    print(f"\n📊 缓存影响分析:")
    
    # 比较结果差异
    if hasattr(result1, 'risk_value') and hasattr(result3, 'risk_value'):
        risk_diff = abs(result1.risk_value - result3.risk_value)
        print(f"   风险值差异: {risk_diff:.6f} (缓存前后)")
    
    if hasattr(result1, 'collision_cost') and hasattr(result3, 'collision_cost'):
        collision_diff = abs(result1.collision_cost - result3.collision_cost)
        print(f"   碰撞代价差异: {collision_diff:.6f} (缓存前后)")
    
    # 6. 识别问题缓存
    print(f"\n❗ 问题缓存识别:")
    
    problems = []
    
    # 检查算法管理器缓存
    if len(manager.cache.cache) > 0:
        problems.append("算法管理器缓存可能影响结果一致性")
    
    # 检查建筑物检测器缓存
    if hasattr(unified_manager, 'building_detector') and hasattr(unified_manager.building_detector, '_cache'):
        if len(unified_manager.building_detector._cache) > 0:
            problems.append("建筑物检测器缓存可能导致建筑物数据不一致")
    
    # 检查全局缓存
    if hasattr(algorithm, '_global_buildings_cache') and algorithm._global_buildings_cache:
        problems.append("算法全局建筑物缓存可能与实时数据不同步")
    
    if problems:
        for i, problem in enumerate(problems, 1):
            print(f"   {i}. {problem}")
    else:
        print(f"   未发现明显的缓存问题")
    
    # 7. 建议修复方案
    print(f"\n💡 建议修复方案:")
    print(f"   1. 统一缓存键生成策略，确保相同输入产生相同缓存键")
    print(f"   2. 实现缓存失效机制，当环境数据更新时清除相关缓存")
    print(f"   3. 添加缓存版本控制，避免不同版本数据混用")
    print(f"   4. 考虑禁用某些层级的缓存，简化缓存架构")
    print(f"   5. 实现缓存一致性检查，定期验证缓存数据的正确性")
    
    return {
        'algorithm_cache_size': len(manager.cache.cache),
        'building_detector_cache_size': len(unified_manager.building_detector._cache) if hasattr(unified_manager, 'building_detector') and hasattr(unified_manager.building_detector, '_cache') else 0,
        'problems_found': len(problems),
        'execution_times': [time1, time2, time3]
    }

if __name__ == "__main__":
    try:
        result = asyncio.run(test_cache_impact_on_path_calculation())
        print(f"\n✅ 缓存影响测试完成")
        print(f"   算法缓存条目: {result['algorithm_cache_size']}")
        print(f"   建筑物检测器缓存条目: {result['building_detector_cache_size']}")
        print(f"   发现问题数量: {result['problems_found']}")
    except Exception as e:
        print(f"\n❌ 缓存影响测试失败: {e}")
        import traceback
        traceback.print_exc()
