#!/usr/bin/env python3
"""
测试简洁CSV导出功能
"""

import os
import sys
import json
import glob
import requests

def test_json_file_exists():
    """检查JSON文件是否存在"""
    print("🔍 检查JSON文件...")
    
    json_files = glob.glob('all_81_paths_data_*.json')
    if not json_files:
        print("❌ 没有找到JSON文件")
        return False, None
    
    latest_json = max(json_files, key=os.path.getctime)
    print(f"✅ 找到JSON文件: {latest_json}")
    
    # 检查文件内容
    try:
        with open(latest_json, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data.get('all_paths'):
            print("❌ JSON文件中没有路径数据")
            return False, None
        
        print(f"✅ JSON文件包含 {len(data['all_paths'])} 条路径数据")
        
        # 检查选中路径
        selected_path = data.get('selected_path', {})
        if selected_path:
            print(f"✅ 选中路径ID: {selected_path.get('selected_path_id')}")
        else:
            print("⚠️ 没有选中路径信息")
        
        return True, latest_json
        
    except Exception as e:
        print(f"❌ 读取JSON文件失败: {e}")
        return False, None

def test_api_export():
    """测试API导出功能"""
    print("\n🌐 测试API导出...")
    
    try:
        response = requests.post('http://localhost:5000/api/export_calculated_paths', 
                               json={'test': True})
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ API导出成功")
                print(f"   文件名: {data.get('filename')}")
                print(f"   路径数: {data.get('total_paths')}")
                print(f"   源JSON: {data.get('source_json')}")
                
                # 检查生成的CSV文件
                csv_path = data.get('filepath')
                if csv_path and os.path.exists(csv_path):
                    print(f"✅ CSV文件已创建: {csv_path}")
                    
                    # 读取并显示前几行
                    with open(csv_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    print(f"\n📋 CSV文件预览 (前5行):")
                    for i, line in enumerate(lines[:5]):
                        print(f"   {i+1}: {line.strip()}")
                    
                    print(f"\n📊 CSV文件统计:")
                    print(f"   总行数: {len(lines)}")
                    print(f"   数据行数: {len(lines) - 1}")  # 减去表头
                    
                    return True
                else:
                    print(f"❌ CSV文件未创建: {csv_path}")
                    return False
            else:
                print(f"❌ API返回失败: {data.get('error')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_csv_format():
    """测试CSV格式是否符合要求"""
    print("\n📋 检查CSV格式...")
    
    # 查找最新的CSV文件
    csv_files = glob.glob('csv/路径数据_简洁格式_*.csv')
    if not csv_files:
        print("❌ 没有找到CSV文件")
        return False
    
    latest_csv = max(csv_files, key=os.path.getctime)
    print(f"✅ 找到CSV文件: {latest_csv}")
    
    try:
        with open(latest_csv, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) < 2:
            print("❌ CSV文件内容不足")
            return False
        
        # 检查表头
        header = lines[0].strip()
        expected_columns = ['路径ID', '方向', '高度', '路径长度', '转向成本', '风险值', '碰撞代价', '最终代价', '航点数', '选中']
        
        print(f"📋 表头: {header}")
        
        # 检查是否包含基准路径
        has_baseline = False
        has_selected = False
        
        for line in lines[1:]:
            if '🎯基准A*' in line:
                has_baseline = True
            if '✓' in line:
                has_selected = True
        
        print(f"✅ 包含基准路径: {'是' if has_baseline else '否'}")
        print(f"✅ 包含选中标记: {'是' if has_selected else '否'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查CSV格式失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 简洁CSV导出功能测试")
    print("=" * 50)
    
    # 检查JSON文件
    json_ok, json_file = test_json_file_exists()
    
    if not json_ok:
        print("\n❌ 测试终止：没有可用的JSON文件")
        print("   请先运行算法对比生成路径数据")
        return False
    
    # 测试API导出
    api_ok = test_api_export()
    
    # 测试CSV格式
    format_ok = test_csv_format()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   JSON文件:   {'✅ 通过' if json_ok else '❌ 失败'}")
    print(f"   API导出:    {'✅ 通过' if api_ok else '❌ 失败'}")
    print(f"   CSV格式:    {'✅ 通过' if format_ok else '❌ 失败'}")
    
    if json_ok and api_ok and format_ok:
        print("\n🎉 所有测试通过！简洁CSV导出功能正常工作")
        print("\n📝 使用说明:")
        print("   1. 在前端点击 '📊 导出81条路径数据' 按钮")
        print("   2. 系统会自动生成简洁格式的CSV文件")
        print("   3. 文件保存在 csv/ 目录下")
        return True
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
