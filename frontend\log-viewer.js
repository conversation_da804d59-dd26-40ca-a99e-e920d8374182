/**
 * 日志查看器 JavaScript
 * 用于前端日志查看和分析
 */

class LogViewer {
    constructor() {
        this.baseUrl = 'http://localhost:5000'; // 后端API地址
        this.allLogs = [];
        this.filteredLogs = [];
        this.currentDate = '';
        
        this.init();
    }

    async init() {
        console.log('🚀 初始化日志查看器...');
        
        // 绑定事件
        this.bindEvents();
        
        // 加载可用日期
        await this.loadAvailableDates();
        
        // 加载最新日志
        if (this.currentDate) {
            await this.loadLogs();
        }
    }

    bindEvents() {
        // 日期选择变化
        document.getElementById('dateSelect').addEventListener('change', (e) => {
            this.currentDate = e.target.value;
            if (this.currentDate) {
                this.loadLogs();
            }
        });

        // 筛选器变化
        document.getElementById('levelFilter').addEventListener('change', () => {
            this.filterLogs();
        });

        document.getElementById('stepTypeFilter').addEventListener('change', () => {
            this.filterLogs();
        });

        // 模态框点击外部关闭
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('detailModal');
            if (e.target === modal) {
                this.closeDetailModal();
            }
        });
    }

    async loadAvailableDates() {
        try {
            console.log('📅 加载可用日期...');
            const response = await fetch(`${this.baseUrl}/api/logs/dates`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const dates = await response.json();
            console.log('📅 可用日期:', dates);

            const dateSelect = document.getElementById('dateSelect');
            dateSelect.innerHTML = '<option value="">请选择日期</option>';

            if (dates.length === 0) {
                dateSelect.innerHTML = '<option value="">暂无日志数据</option>';
                return;
            }

            dates.forEach(date => {
                const option = document.createElement('option');
                option.value = date;
                option.textContent = this.formatDate(date);
                dateSelect.appendChild(option);
            });

            // 默认选择最新日期
            if (dates.length > 0) {
                this.currentDate = dates[0];
                dateSelect.value = this.currentDate;
            }

        } catch (error) {
            console.error('❌ 加载可用日期失败:', error);
            this.showError('加载可用日期失败，请检查后端服务');
        }
    }

    async loadLogs() {
        if (!this.currentDate) return;

        try {
            console.log('📋 加载日志数据...', this.currentDate);
            
            // 显示加载状态
            this.showLoading();

            const response = await fetch(`${this.baseUrl}/api/logs/steps/${this.currentDate}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const logs = await response.json();
            console.log('📋 日志数据加载完成:', logs.length, '条记录');

            this.allLogs = logs;
            this.filterLogs();
            this.updateStats();
            this.hideLoading();

        } catch (error) {
            console.error('❌ 加载日志失败:', error);
            this.showError('加载日志失败，请检查后端服务');
        }
    }

    filterLogs() {
        const levelFilter = document.getElementById('levelFilter').value;
        const stepTypeFilter = document.getElementById('stepTypeFilter').value;

        this.filteredLogs = this.allLogs.filter(log => {
            if (levelFilter && log.level !== levelFilter) return false;
            if (stepTypeFilter && log.step_type !== stepTypeFilter) return false;
            return true;
        });

        console.log('🔍 筛选结果:', this.filteredLogs.length, '条记录');
        this.renderLogTable();
    }

    renderLogTable() {
        const tbody = document.getElementById('logTableBody');
        tbody.innerHTML = '';

        if (this.filteredLogs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 30px; color: #666;">暂无符合条件的日志记录</td></tr>';
            return;
        }

        this.filteredLogs.forEach(log => {
            const row = document.createElement('tr');
            
            // 根据日志级别添加样式类
            if (log.level === 'ERROR') {
                row.classList.add('error-row');
            } else if (log.level === 'WARNING') {
                row.classList.add('warning-row');
            }

            row.innerHTML = `
                <td>${log.step_number || '-'}</td>
                <td>${this.formatTimestamp(log.timestamp)}</td>
                <td><span class="level-tag level-${log.level.toLowerCase()}">${log.level}</span></td>
                <td>${log.step_type || '-'}</td>
                <td>${log.algorithm || '-'}</td>
                <td>${log.message || '-'}</td>
                <td>${log.duration_ms ? log.duration_ms.toFixed(2) : '-'}</td>
            `;

            // 点击行显示详情
            row.addEventListener('click', () => {
                this.showLogDetail(log);
            });

            tbody.appendChild(row);
        });

        // 显示表格
        document.getElementById('logTable').style.display = 'table';
    }

    updateStats() {
        const sessions = new Set();
        const algorithms = new Set();
        let errorCount = 0;

        this.allLogs.forEach(log => {
            if (log.session_id) sessions.add(log.session_id);
            if (log.algorithm) algorithms.add(log.algorithm);
            if (log.level === 'ERROR') errorCount++;
        });

        document.getElementById('totalSteps').textContent = this.allLogs.length;
        document.getElementById('errorCount').textContent = errorCount;
        document.getElementById('sessionCount').textContent = sessions.size;
        document.getElementById('algorithmCount').textContent = algorithms.size;
    }

    showLogDetail(log) {
        const detailContent = document.getElementById('detailContent');
        
        detailContent.innerHTML = `
            <div class="detail-section">
                <h4>📊 基本信息</h4>
                <div class="detail-content">
                    <p><strong>步骤编号:</strong> ${log.step_number || '无'}</p>
                    <p><strong>时间戳:</strong> ${this.formatTimestamp(log.timestamp)}</p>
                    <p><strong>日志级别:</strong> <span class="level-tag level-${log.level.toLowerCase()}">${log.level}</span></p>
                    <p><strong>步骤类型:</strong> ${log.step_type || '无'}</p>
                    <p><strong>算法名称:</strong> ${log.algorithm || '无'}</p>
                    <p><strong>请求ID:</strong> ${log.request_id || '无'}</p>
                    <p><strong>会话ID:</strong> ${log.session_id || '无'}</p>
                    <p><strong>耗时:</strong> ${log.duration_ms ? log.duration_ms.toFixed(2) + 'ms' : '无'}</p>
                </div>
            </div>

            <div class="detail-section">
                <h4>💬 消息内容</h4>
                <div class="detail-content">
                    <pre>${log.message || '无消息内容'}</pre>
                </div>
            </div>

            ${log.details && Object.keys(log.details).length > 0 ? `
            <div class="detail-section">
                <h4>🔍 详细信息</h4>
                <div class="detail-content">
                    <pre>${JSON.stringify(log.details, null, 2)}</pre>
                </div>
            </div>
            ` : ''}
        `;

        document.getElementById('detailModal').style.display = 'block';
    }

    closeDetailModal() {
        document.getElementById('detailModal').style.display = 'none';
    }

    showLoading() {
        document.getElementById('loadingMessage').style.display = 'block';
        document.getElementById('errorMessage').style.display = 'none';
        document.getElementById('logTable').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('loadingMessage').style.display = 'none';
        document.getElementById('errorMessage').style.display = 'none';
    }

    showError(message) {
        document.getElementById('loadingMessage').style.display = 'none';
        document.getElementById('errorMessage').style.display = 'block';
        document.getElementById('errorMessage').textContent = `❌ ${message}`;
        document.getElementById('logTable').style.display = 'none';
    }

    formatDate(dateStr) {
        if (dateStr.length === 8) {
            return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`;
        }
        return dateStr;
    }

    formatTimestamp(timestamp) {
        if (!timestamp) return '-';
        try {
            return new Date(timestamp).toLocaleString('zh-CN');
        } catch (error) {
            return timestamp;
        }
    }
}

// 全局函数
function refreshLogs() {
    if (window.logViewer) {
        window.logViewer.loadLogs();
    }
}

function closeDetailModal() {
    if (window.logViewer) {
        window.logViewer.closeDetailModal();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 页面加载完成，初始化日志查看器...');
    window.logViewer = new LogViewer();
});

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LogViewer;
}
