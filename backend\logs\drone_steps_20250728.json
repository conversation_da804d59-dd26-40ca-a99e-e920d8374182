[{"session_id": "20250728_095415", "step_number": 1, "timestamp": "2025-07-28T09:54:15.794462", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "efc8c7b4-0a7f-4c87-a37d-f4d4db6f8184", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "efc8c7b4-0a7f-4c87-a37d-f4d4db6f8184", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_095415", "step_number": 2, "timestamp": "2025-07-28T09:54:15.796716", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "efc8c7b4-0a7f-4c87-a37d-f4d4db6f8184", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "efc8c7b4-0a7f-4c87-a37d-f4d4db6f8184", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_095415", "step_number": 3, "timestamp": "2025-07-28T09:54:16.102386", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 303.1644821166992}, {"session_id": "20250728_095415", "step_number": 4, "timestamp": "2025-07-28T09:54:16.103417", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 304.1949272155762}, {"session_id": "20250728_095415", "step_number": 5, "timestamp": "2025-07-28T09:54:16.122831", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.89801025390625}, {"session_id": "20250728_095415", "step_number": 6, "timestamp": "2025-07-28T09:54:16.122831", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.89801025390625}, {"session_id": "20250728_095711", "step_number": 1, "timestamp": "2025-07-28T09:57:11.036184", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5f25a75d-75c6-47dc-94a1-4071beb3907e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5f25a75d-75c6-47dc-94a1-4071beb3907e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_095711", "step_number": 2, "timestamp": "2025-07-28T09:57:11.037621", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5f25a75d-75c6-47dc-94a1-4071beb3907e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5f25a75d-75c6-47dc-94a1-4071beb3907e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_095711", "step_number": 3, "timestamp": "2025-07-28T09:57:11.340920", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 301.3038635253906}, {"session_id": "20250728_095711", "step_number": 4, "timestamp": "2025-07-28T09:57:11.341965", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 302.34813690185547}, {"session_id": "20250728_095711", "step_number": 5, "timestamp": "2025-07-28T09:57:11.361846", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.342660903930664}, {"session_id": "20250728_095711", "step_number": 6, "timestamp": "2025-07-28T09:57:11.362654", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.15089988708496}, {"session_id": "20250728_095711", "step_number": 7, "timestamp": "2025-07-28T09:58:05.577704", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b79b35ed-25ce-42ae-b45f-a6bcfa1f56fe", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b79b35ed-25ce-42ae-b45f-a6bcfa1f56fe", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 88}}, "duration_ms": null}, {"session_id": "20250728_095711", "step_number": 8, "timestamp": "2025-07-28T09:58:05.593248", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b79b35ed-25ce-42ae-b45f-a6bcfa1f56fe", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b79b35ed-25ce-42ae-b45f-a6bcfa1f56fe", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_095711", "step_number": 9, "timestamp": "2025-07-28T09:58:07.910843", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2307.1393966674805}, {"session_id": "20250728_095711", "step_number": 10, "timestamp": "2025-07-28T09:58:07.911843", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2308.1395626068115}, {"session_id": "20250728_095711", "step_number": 11, "timestamp": "2025-07-28T09:58:07.941561", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.572559356689453}, {"session_id": "20250728_095711", "step_number": 12, "timestamp": "2025-07-28T09:58:07.942566", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.577970504760742}, {"session_id": "20250728_095711", "step_number": 13, "timestamp": "2025-07-28T09:59:00.839547", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "828e4f29-d595-450f-8113-e707560c1afc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "828e4f29-d595-450f-8113-e707560c1afc", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 61}}, "duration_ms": null}, {"session_id": "20250728_095711", "step_number": 14, "timestamp": "2025-07-28T09:59:00.848116", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "828e4f29-d595-450f-8113-e707560c1afc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "828e4f29-d595-450f-8113-e707560c1afc", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_095711", "step_number": 15, "timestamp": "2025-07-28T09:59:03.014026", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2157.8023433685303}, {"session_id": "20250728_095711", "step_number": 16, "timestamp": "2025-07-28T09:59:03.015576", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2159.351348876953}, {"session_id": "20250728_095711", "step_number": 17, "timestamp": "2025-07-28T09:59:03.042764", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.674985885620117}, {"session_id": "20250728_095711", "step_number": 18, "timestamp": "2025-07-28T09:59:03.044266", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.17678451538086}, {"session_id": "20250728_095711", "step_number": 19, "timestamp": "2025-07-28T10:02:36.915383", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4cd0ba61-4ba4-4e68-82a5-062db327aca8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4cd0ba61-4ba4-4e68-82a5-062db327aca8", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_095711", "step_number": 20, "timestamp": "2025-07-28T10:02:36.918054", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4cd0ba61-4ba4-4e68-82a5-062db327aca8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4cd0ba61-4ba4-4e68-82a5-062db327aca8", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_095711", "step_number": 21, "timestamp": "2025-07-28T10:02:37.192441", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 270.6561088562012}, {"session_id": "20250728_095711", "step_number": 22, "timestamp": "2025-07-28T10:02:37.194009", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 272.22418785095215}, {"session_id": "20250728_095711", "step_number": 23, "timestamp": "2025-07-28T10:02:37.208823", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.705730438232422}, {"session_id": "20250728_095711", "step_number": 24, "timestamp": "2025-07-28T10:02:37.209833", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 14.71567153930664}, {"session_id": "20250728_104624", "step_number": 1, "timestamp": "2025-07-28T10:46:24.225165", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ba8fdcc2-3728-4af1-a507-ceabac9d44be", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ba8fdcc2-3728-4af1-a507-ceabac9d44be", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_104624", "step_number": 2, "timestamp": "2025-07-28T10:46:24.226616", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ba8fdcc2-3728-4af1-a507-ceabac9d44be", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ba8fdcc2-3728-4af1-a507-ceabac9d44be", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_104624", "step_number": 3, "timestamp": "2025-07-28T10:46:24.522515", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 292.9096221923828}, {"session_id": "20250728_104624", "step_number": 4, "timestamp": "2025-07-28T10:46:24.523522", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 293.9169406890869}, {"session_id": "20250728_104624", "step_number": 5, "timestamp": "2025-07-28T10:46:24.540039", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.047788619995117}, {"session_id": "20250728_104624", "step_number": 6, "timestamp": "2025-07-28T10:46:24.541119", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.1283016204834}, {"session_id": "20250728_104943", "step_number": 1, "timestamp": "2025-07-28T10:49:43.047362", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e121c59a-07b4-4bda-aa2a-067107006570", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e121c59a-07b4-4bda-aa2a-067107006570", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 2, "timestamp": "2025-07-28T10:49:43.049433", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e121c59a-07b4-4bda-aa2a-067107006570", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e121c59a-07b4-4bda-aa2a-067107006570", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 3, "timestamp": "2025-07-28T10:49:43.316860", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 264.85562324523926}, {"session_id": "20250728_104943", "step_number": 4, "timestamp": "2025-07-28T10:49:43.318456", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 266.4520740509033}, {"session_id": "20250728_104943", "step_number": 5, "timestamp": "2025-07-28T10:49:43.333738", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 14.254093170166016}, {"session_id": "20250728_104943", "step_number": 6, "timestamp": "2025-07-28T10:49:43.335744", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.260147094726562}, {"session_id": "20250728_104943", "step_number": 7, "timestamp": "2025-07-28T10:51:09.032366", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "51003218-fd09-4827-aafc-c176836a2879", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "51003218-fd09-4827-aafc-c176836a2879", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 8, "timestamp": "2025-07-28T10:51:09.040146", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "51003218-fd09-4827-aafc-c176836a2879", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "51003218-fd09-4827-aafc-c176836a2879", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 9, "timestamp": "2025-07-28T10:51:11.071775", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2025.120735168457}, {"session_id": "20250728_104943", "step_number": 10, "timestamp": "2025-07-28T10:51:11.073310", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2026.655912399292}, {"session_id": "20250728_104943", "step_number": 11, "timestamp": "2025-07-28T10:51:11.100122", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 25.243043899536133}, {"session_id": "20250728_104943", "step_number": 12, "timestamp": "2025-07-28T10:51:11.101742", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.862621307373047}, {"session_id": "20250728_104943", "step_number": 13, "timestamp": "2025-07-28T10:51:48.863946", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b47b4450-c2d5-4b24-bd49-47200ed41263", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b47b4450-c2d5-4b24-bd49-47200ed41263", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1012.5276143263497, 1799.0188434844029, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 14, "timestamp": "2025-07-28T10:51:48.868635", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b47b4450-c2d5-4b24-bd49-47200ed41263", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b47b4450-c2d5-4b24-bd49-47200ed41263", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1012.5276143263497, 1799.0188434844029, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 15, "timestamp": "2025-07-28T10:51:50.727536", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1853.8258075714111}, {"session_id": "20250728_104943", "step_number": 16, "timestamp": "2025-07-28T10:51:50.729529", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1855.8192253112793}, {"session_id": "20250728_104943", "step_number": 17, "timestamp": "2025-07-28T10:51:50.760456", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.92987632751465}, {"session_id": "20250728_104943", "step_number": 18, "timestamp": "2025-07-28T10:51:50.763022", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.98075294494629}, {"session_id": "20250728_104943", "step_number": 19, "timestamp": "2025-07-28T10:57:10.900888", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "99eb982c-8aeb-4a19-82b1-62a9ebc2461e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "99eb982c-8aeb-4a19-82b1-62a9ebc2461e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 20, "timestamp": "2025-07-28T10:57:10.907864", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "99eb982c-8aeb-4a19-82b1-62a9ebc2461e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "99eb982c-8aeb-4a19-82b1-62a9ebc2461e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 21, "timestamp": "2025-07-28T10:57:12.515037", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1600.2326011657715}, {"session_id": "20250728_104943", "step_number": 22, "timestamp": "2025-07-28T10:57:12.516962", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1602.1573543548584}, {"session_id": "20250728_104943", "step_number": 23, "timestamp": "2025-07-28T10:57:12.543938", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 25.363445281982422}, {"session_id": "20250728_104943", "step_number": 24, "timestamp": "2025-07-28T10:57:12.544948", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.372671127319336}, {"session_id": "20250728_104943", "step_number": 25, "timestamp": "2025-07-28T10:59:46.320561", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6001704d-5365-41fe-83b0-e131ddddc809", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6001704d-5365-41fe-83b0-e131ddddc809", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 26, "timestamp": "2025-07-28T10:59:46.325932", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6001704d-5365-41fe-83b0-e131ddddc809", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6001704d-5365-41fe-83b0-e131ddddc809", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 27, "timestamp": "2025-07-28T10:59:46.660318", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 331.40063285827637}, {"session_id": "20250728_104943", "step_number": 28, "timestamp": "2025-07-28T10:59:46.663548", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 334.63048934936523}, {"session_id": "20250728_104943", "step_number": 29, "timestamp": "2025-07-28T10:59:46.684794", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.707275390625}, {"session_id": "20250728_104943", "step_number": 30, "timestamp": "2025-07-28T10:59:46.686885", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.798683166503906}, {"session_id": "20250728_104943", "step_number": 31, "timestamp": "2025-07-28T11:00:39.763478", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b039f37e-50de-4830-9cbf-dd031b5d9a46", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b039f37e-50de-4830-9cbf-dd031b5d9a46", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 32, "timestamp": "2025-07-28T11:00:39.770152", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b039f37e-50de-4830-9cbf-dd031b5d9a46", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b039f37e-50de-4830-9cbf-dd031b5d9a46", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_104943", "step_number": 33, "timestamp": "2025-07-28T11:00:41.437388", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1659.9180698394775}, {"session_id": "20250728_104943", "step_number": 34, "timestamp": "2025-07-28T11:00:41.439003", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1661.5331172943115}, {"session_id": "20250728_104943", "step_number": 35, "timestamp": "2025-07-28T11:00:41.470365", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.82671356201172}, {"session_id": "20250728_104943", "step_number": 36, "timestamp": "2025-07-28T11:00:41.472036", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 30.498027801513672}, {"session_id": "20250728_110949", "step_number": 1, "timestamp": "2025-07-28T11:09:49.901346", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d7e49cdc-3790-42e6-a7ef-49af5d9d0a5d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d7e49cdc-3790-42e6-a7ef-49af5d9d0a5d", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_110949", "step_number": 2, "timestamp": "2025-07-28T11:09:49.904964", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d7e49cdc-3790-42e6-a7ef-49af5d9d0a5d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d7e49cdc-3790-42e6-a7ef-49af5d9d0a5d", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_110949", "step_number": 3, "timestamp": "2025-07-28T11:09:50.262233", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 354.13265228271484}, {"session_id": "20250728_110949", "step_number": 4, "timestamp": "2025-07-28T11:09:50.266290", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 358.18982124328613}, {"session_id": "20250728_110949", "step_number": 5, "timestamp": "2025-07-28T11:09:50.288275", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.914461135864258}, {"session_id": "20250728_110949", "step_number": 6, "timestamp": "2025-07-28T11:09:50.290372", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.0113525390625}, {"session_id": "20250728_110949", "step_number": 7, "timestamp": "2025-07-28T11:10:38.574842", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5d43fe26-a7bb-4f43-a4c4-2c3de1bd5dda", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5d43fe26-a7bb-4f43-a4c4-2c3de1bd5dda", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_110949", "step_number": 8, "timestamp": "2025-07-28T11:10:38.580631", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5d43fe26-a7bb-4f43-a4c4-2c3de1bd5dda", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5d43fe26-a7bb-4f43-a4c4-2c3de1bd5dda", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_110949", "step_number": 9, "timestamp": "2025-07-28T11:10:40.351161", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1763.716697692871}, {"session_id": "20250728_110949", "step_number": 10, "timestamp": "2025-07-28T11:10:40.353246", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1765.8021450042725}, {"session_id": "20250728_110949", "step_number": 11, "timestamp": "2025-07-28T11:10:40.382330", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.070926666259766}, {"session_id": "20250728_110949", "step_number": 12, "timestamp": "2025-07-28T11:10:40.384423", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 30.164003372192383}, {"session_id": "20250728_111237", "step_number": 1, "timestamp": "2025-07-28T11:12:37.826209", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d57ba55f-66f5-4d63-972d-41f4aebd8889", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d57ba55f-66f5-4d63-972d-41f4aebd8889", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_111237", "step_number": 2, "timestamp": "2025-07-28T11:12:37.830946", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d57ba55f-66f5-4d63-972d-41f4aebd8889", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d57ba55f-66f5-4d63-972d-41f4aebd8889", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_111237", "step_number": 3, "timestamp": "2025-07-28T11:12:38.229177", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 393.2356834411621}, {"session_id": "20250728_111237", "step_number": 4, "timestamp": "2025-07-28T11:12:38.233182", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 397.24063873291016}, {"session_id": "20250728_111237", "step_number": 5, "timestamp": "2025-07-28T11:12:38.255586", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.407033920288086}, {"session_id": "20250728_111237", "step_number": 6, "timestamp": "2025-07-28T11:12:38.257588", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.409273147583008}, {"session_id": "20250728_111237", "step_number": 7, "timestamp": "2025-07-28T11:17:38.937769", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "525e6380-6bc5-403a-918e-247a329d3a97", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "525e6380-6bc5-403a-918e-247a329d3a97", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-912.832526825709, 1452.3116514756814, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_111237", "step_number": 8, "timestamp": "2025-07-28T11:17:38.943036", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "525e6380-6bc5-403a-918e-247a329d3a97", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "525e6380-6bc5-403a-918e-247a329d3a97", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-912.832526825709, 1452.3116514756814, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_111237", "step_number": 9, "timestamp": "2025-07-28T11:17:40.693193", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1745.1367378234863}, {"session_id": "20250728_111237", "step_number": 10, "timestamp": "2025-07-28T11:17:40.698918", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1750.8621215820312}, {"session_id": "20250728_111237", "step_number": 11, "timestamp": "2025-07-28T11:17:40.727419", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 25.341033935546875}, {"session_id": "20250728_111237", "step_number": 12, "timestamp": "2025-07-28T11:17:40.730986", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.90801429748535}, {"session_id": "20250728_155648", "step_number": 1, "timestamp": "2025-07-28T15:56:48.233813", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fd440b74-d951-45d1-9da3-5b297410c834", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fd440b74-d951-45d1-9da3-5b297410c834", "parameters": {"start_point": "(0, 0, 100)", "end_point": "(1000, 1000, 100)", "flight_height": 100.0, "safety_distance": 30.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_155648", "step_number": 2, "timestamp": "2025-07-28T15:56:48.238796", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fd440b74-d951-45d1-9da3-5b297410c834", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fd440b74-d951-45d1-9da3-5b297410c834", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0, 0, 100)", "end_point": "(1000, 1000, 100)", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": null}, {"session_id": "20250728_155648", "step_number": 3, "timestamp": "2025-07-28T15:56:51.456854", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3207.720994949341}, {"session_id": "20250728_155648", "step_number": 4, "timestamp": "2025-07-28T15:56:51.459420", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 30.0}}, "duration_ms": 3210.2863788604736}, {"session_id": "20250728_155648", "step_number": 5, "timestamp": "2025-07-28T15:56:51.494318", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.41307830810547}, {"session_id": "20250728_155648", "step_number": 6, "timestamp": "2025-07-28T15:56:51.496363", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.45823287963867}, {"session_id": "20250728_171435", "step_number": 1, "timestamp": "2025-07-28T17:14:35.644934", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8bfd8a1d-f7ed-45fb-b96a-510c8deff729", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8bfd8a1d-f7ed-45fb-b96a-510c8deff729", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-745.8660110509384, 1401.4692886582502, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 78}}, "duration_ms": null}, {"session_id": "20250728_171435", "step_number": 2, "timestamp": "2025-07-28T17:14:35.655173", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8bfd8a1d-f7ed-45fb-b96a-510c8deff729", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8bfd8a1d-f7ed-45fb-b96a-510c8deff729", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-745.8660110509384, 1401.4692886582502, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_171435", "step_number": 3, "timestamp": "2025-07-28T17:14:37.381675", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1719.679832458496}, {"session_id": "20250728_171435", "step_number": 4, "timestamp": "2025-07-28T17:14:37.384325", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1722.3308086395264}, {"session_id": "20250728_171435", "step_number": 5, "timestamp": "2025-07-28T17:14:37.409524", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 23.009538650512695}, {"session_id": "20250728_171435", "step_number": 6, "timestamp": "2025-07-28T17:14:37.413497", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.982784271240234}, {"session_id": "20250728_174702", "step_number": 1, "timestamp": "2025-07-28T17:47:02.928889", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9b4d864f-8de0-4b27-a8d7-52da63d3d889", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9b4d864f-8de0-4b27-a8d7-52da63d3d889", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1406.9996619465473, 2006.237256673443, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 103}}, "duration_ms": null}, {"session_id": "20250728_174702", "step_number": 2, "timestamp": "2025-07-28T17:47:02.941494", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9b4d864f-8de0-4b27-a8d7-52da63d3d889", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9b4d864f-8de0-4b27-a8d7-52da63d3d889", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1406.9996619465473, 2006.237256673443, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_174702", "step_number": 3, "timestamp": "2025-07-28T17:47:05.833590", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2883.3117485046387}, {"session_id": "20250728_174702", "step_number": 4, "timestamp": "2025-07-28T17:47:05.837579", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2887.300729751587}, {"session_id": "20250728_174702", "step_number": 5, "timestamp": "2025-07-28T17:47:05.879443", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.8638973236084}, {"session_id": "20250728_174702", "step_number": 6, "timestamp": "2025-07-28T17:47:05.882670", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 42.09160804748535}, {"session_id": "20250728_174702", "step_number": 7, "timestamp": "2025-07-28T17:52:19.942450", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8905b1ae-2839-4ea4-a8e4-12871d029bc9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8905b1ae-2839-4ea4-a8e4-12871d029bc9", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1406.9996619465473, 2006.237256673443, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 16}}, "duration_ms": null}, {"session_id": "20250728_174702", "step_number": 8, "timestamp": "2025-07-28T17:52:19.950434", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8905b1ae-2839-4ea4-a8e4-12871d029bc9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8905b1ae-2839-4ea4-a8e4-12871d029bc9", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1406.9996619465473, 2006.237256673443, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_174702", "step_number": 9, "timestamp": "2025-07-28T17:52:22.503493", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2545.4564094543457}, {"session_id": "20250728_174702", "step_number": 10, "timestamp": "2025-07-28T17:52:22.506472", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2548.435926437378}, {"session_id": "20250728_174702", "step_number": 11, "timestamp": "2025-07-28T17:52:22.541676", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.219648361206055}, {"session_id": "20250728_174702", "step_number": 12, "timestamp": "2025-07-28T17:52:22.544378", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.92259979248047}, {"session_id": "20250728_174702", "step_number": 13, "timestamp": "2025-07-28T17:56:10.170862", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8f943eb8-0a64-404e-850e-7ce971b67058", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8f943eb8-0a64-404e-850e-7ce971b67058", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1190.2285784428136, 1844.1761987635584, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_174702", "step_number": 14, "timestamp": "2025-07-28T17:56:10.179531", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8f943eb8-0a64-404e-850e-7ce971b67058", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8f943eb8-0a64-404e-850e-7ce971b67058", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1190.2285784428136, 1844.1761987635584, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_174702", "step_number": 15, "timestamp": "2025-07-28T17:56:12.693409", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2507.1167945861816}, {"session_id": "20250728_174702", "step_number": 16, "timestamp": "2025-07-28T17:56:12.697614", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2511.321783065796}, {"session_id": "20250728_174702", "step_number": 17, "timestamp": "2025-07-28T17:56:12.747528", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 46.090126037597656}, {"session_id": "20250728_174702", "step_number": 18, "timestamp": "2025-07-28T17:56:12.753492", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 52.05368995666504}, {"session_id": "20250728_174702", "step_number": 19, "timestamp": "2025-07-28T18:04:36.294159", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "10aa8620-3f61-49a6-a4a0-f3df7cbb05e6", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "10aa8620-3f61-49a6-a4a0-f3df7cbb05e6", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1418.0325732103106, 2063.6136107450975, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 77}}, "duration_ms": null}, {"session_id": "20250728_174702", "step_number": 20, "timestamp": "2025-07-28T18:04:36.302652", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "10aa8620-3f61-49a6-a4a0-f3df7cbb05e6", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "10aa8620-3f61-49a6-a4a0-f3df7cbb05e6", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1418.0325732103106, 2063.6136107450975, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_174702", "step_number": 21, "timestamp": "2025-07-28T18:04:39.102389", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2791.4204597473145}, {"session_id": "20250728_174702", "step_number": 22, "timestamp": "2025-07-28T18:04:39.107067", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2796.098470687866}, {"session_id": "20250728_174702", "step_number": 23, "timestamp": "2025-07-28T18:04:39.152664", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.57590866088867}, {"session_id": "20250728_174702", "step_number": 24, "timestamp": "2025-07-28T18:04:39.156817", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.728445053100586}, {"session_id": "20250728_174702", "step_number": 25, "timestamp": "2025-07-28T18:20:05.169536", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8509ca1a-8ffd-4ff7-a567-55bb8863f51b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8509ca1a-8ffd-4ff7-a567-55bb8863f51b", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1074.7459209575263, 1786.200701079601, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 71}}, "duration_ms": null}, {"session_id": "20250728_174702", "step_number": 26, "timestamp": "2025-07-28T18:20:05.184420", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8509ca1a-8ffd-4ff7-a567-55bb8863f51b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8509ca1a-8ffd-4ff7-a567-55bb8863f51b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1074.7459209575263, 1786.200701079601, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_174702", "step_number": 27, "timestamp": "2025-07-28T18:20:07.468156", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2274.0252017974854}, {"session_id": "20250728_174702", "step_number": 28, "timestamp": "2025-07-28T18:20:07.472160", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2278.0301570892334}, {"session_id": "20250728_174702", "step_number": 29, "timestamp": "2025-07-28T18:20:07.513832", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.69707679748535}, {"session_id": "20250728_174702", "step_number": 30, "timestamp": "2025-07-28T18:20:07.518381", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 42.246103286743164}, {"session_id": "20250728_183339", "step_number": 1, "timestamp": "2025-07-28T18:33:39.464954", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6e92478f-bc4e-4749-afb6-960f566dbd4d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6e92478f-bc4e-4749-afb6-960f566dbd4d", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1196.5752512542272, 1577.1374095249953, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 2, "timestamp": "2025-07-28T18:33:39.472190", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6e92478f-bc4e-4749-afb6-960f566dbd4d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6e92478f-bc4e-4749-afb6-960f566dbd4d", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1196.5752512542272, 1577.1374095249953, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 3, "timestamp": "2025-07-28T18:33:41.563914", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2083.970069885254}, {"session_id": "20250728_183339", "step_number": 4, "timestamp": "2025-07-28T18:33:41.567184", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2087.2392654418945}, {"session_id": "20250728_183339", "step_number": 5, "timestamp": "2025-07-28T18:33:41.597887", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.279449462890625}, {"session_id": "20250728_183339", "step_number": 6, "timestamp": "2025-07-28T18:33:41.602638", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.03041648864746}, {"session_id": "20250728_183339", "step_number": 7, "timestamp": "2025-07-28T18:45:16.132833", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ca8000b8-d6a9-4e44-98d2-6aa135f7c48d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ca8000b8-d6a9-4e44-98d2-6aa135f7c48d", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1381.505034106671, 1984.0053946875873, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 8, "timestamp": "2025-07-28T18:45:16.139738", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ca8000b8-d6a9-4e44-98d2-6aa135f7c48d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ca8000b8-d6a9-4e44-98d2-6aa135f7c48d", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1381.505034106671, 1984.0053946875873, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 9, "timestamp": "2025-07-28T18:45:18.787846", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2641.383409500122}, {"session_id": "20250728_183339", "step_number": 10, "timestamp": "2025-07-28T18:45:18.791842", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2645.3795433044434}, {"session_id": "20250728_183339", "step_number": 11, "timestamp": "2025-07-28T18:45:18.830117", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.166574478149414}, {"session_id": "20250728_183339", "step_number": 12, "timestamp": "2025-07-28T18:45:18.834112", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.161277770996094}, {"session_id": "20250728_183339", "step_number": 13, "timestamp": "2025-07-28T18:52:33.369632", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bb67261c-1620-4c77-acef-2ac1a2fa9e59", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bb67261c-1620-4c77-acef-2ac1a2fa9e59", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1130.2371651711078, 1918.4611736739214, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 63}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 14, "timestamp": "2025-07-28T18:52:33.379781", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bb67261c-1620-4c77-acef-2ac1a2fa9e59", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bb67261c-1620-4c77-acef-2ac1a2fa9e59", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1130.2371651711078, 1918.4611736739214, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 15, "timestamp": "2025-07-28T18:52:35.837883", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2449.601173400879}, {"session_id": "20250728_183339", "step_number": 16, "timestamp": "2025-07-28T18:52:35.842465", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2454.183578491211}, {"session_id": "20250728_183339", "step_number": 17, "timestamp": "2025-07-28T18:52:35.879162", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.60581398010254}, {"session_id": "20250728_183339", "step_number": 18, "timestamp": "2025-07-28T18:52:35.883744", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.187503814697266}, {"session_id": "20250728_183339", "step_number": 19, "timestamp": "2025-07-28T18:57:48.886708", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c42ec0f5-6e35-47d7-a65f-4b04f72d2830", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c42ec0f5-6e35-47d7-a65f-4b04f72d2830", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1222.587439507973, 1863.8425400142005, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 100}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 20, "timestamp": "2025-07-28T18:57:48.904536", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c42ec0f5-6e35-47d7-a65f-4b04f72d2830", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c42ec0f5-6e35-47d7-a65f-4b04f72d2830", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1222.587439507973, 1863.8425400142005, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 21, "timestamp": "2025-07-28T18:57:51.478490", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2559.5054626464844}, {"session_id": "20250728_183339", "step_number": 22, "timestamp": "2025-07-28T18:57:51.482801", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2563.8163089752197}, {"session_id": "20250728_183339", "step_number": 23, "timestamp": "2025-07-28T18:57:51.520718", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.87308120727539}, {"session_id": "20250728_183339", "step_number": 24, "timestamp": "2025-07-28T18:57:51.525776", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.93160820007324}, {"session_id": "20250728_183339", "step_number": 25, "timestamp": "2025-07-28T19:02:36.381279", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "13b72c21-44dc-4b2d-ae9d-b9394d25d30c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "13b72c21-44dc-4b2d-ae9d-b9394d25d30c", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1311.304651243365, 1934.8383224386955, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 67}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 26, "timestamp": "2025-07-28T19:02:36.390155", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "13b72c21-44dc-4b2d-ae9d-b9394d25d30c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "13b72c21-44dc-4b2d-ae9d-b9394d25d30c", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1311.304651243365, 1934.8383224386955, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 27, "timestamp": "2025-07-28T19:02:38.954118", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2554.4354915618896}, {"session_id": "20250728_183339", "step_number": 28, "timestamp": "2025-07-28T19:02:38.958490", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2558.807373046875}, {"session_id": "20250728_183339", "step_number": 29, "timestamp": "2025-07-28T19:02:39.005470", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 42.80400276184082}, {"session_id": "20250728_183339", "step_number": 30, "timestamp": "2025-07-28T19:02:39.008511", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.84527015686035}, {"session_id": "20250728_183339", "step_number": 31, "timestamp": "2025-07-28T19:07:40.926172", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "df140cbb-2995-4bfe-bfaf-01cd449ede82", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "df140cbb-2995-4bfe-bfaf-01cd449ede82", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1202.1689732948669, 1883.398677146521, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 107}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 32, "timestamp": "2025-07-28T19:07:40.937422", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "df140cbb-2995-4bfe-bfaf-01cd449ede82", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "df140cbb-2995-4bfe-bfaf-01cd449ede82", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1202.1689732948669, 1883.398677146521, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_183339", "step_number": 33, "timestamp": "2025-07-28T19:07:43.434623", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2484.5714569091797}, {"session_id": "20250728_183339", "step_number": 34, "timestamp": "2025-07-28T19:07:43.438185", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2488.132953643799}, {"session_id": "20250728_183339", "step_number": 35, "timestamp": "2025-07-28T19:07:43.474776", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.801223754882812}, {"session_id": "20250728_183339", "step_number": 36, "timestamp": "2025-07-28T19:07:43.479676", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.70072555541992}, {"session_id": "20250728_191834", "step_number": 1, "timestamp": "2025-07-28T19:18:34.971910", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6a97bc67-51e4-4459-b751-7837fbd8465b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6a97bc67-51e4-4459-b751-7837fbd8465b", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1336.4001571668514, 2025.7849204721579, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 98}}, "duration_ms": null}, {"session_id": "20250728_191834", "step_number": 2, "timestamp": "2025-07-28T19:18:34.982888", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6a97bc67-51e4-4459-b751-7837fbd8465b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6a97bc67-51e4-4459-b751-7837fbd8465b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1336.4001571668514, 2025.7849204721579, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_191834", "step_number": 3, "timestamp": "2025-07-28T19:18:37.549085", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2555.202007293701}, {"session_id": "20250728_191834", "step_number": 4, "timestamp": "2025-07-28T19:18:37.553167", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2558.833122253418}, {"session_id": "20250728_191834", "step_number": 5, "timestamp": "2025-07-28T19:18:37.589501", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.33766555786133}, {"session_id": "20250728_191834", "step_number": 6, "timestamp": "2025-07-28T19:18:37.593504", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.341190338134766}, {"session_id": "20250728_193101", "step_number": 1, "timestamp": "2025-07-28T19:31:01.726404", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "eb1e2a03-d377-4823-8903-a184c0896d53", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "eb1e2a03-d377-4823-8903-a184c0896d53", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1398.6041791185569, 1950.27377862746, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 101}}, "duration_ms": null}, {"session_id": "20250728_193101", "step_number": 2, "timestamp": "2025-07-28T19:31:01.736829", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "eb1e2a03-d377-4823-8903-a184c0896d53", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "eb1e2a03-d377-4823-8903-a184c0896d53", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1398.6041791185569, 1950.27377862746, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_193101", "step_number": 3, "timestamp": "2025-07-28T19:31:04.418147", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2669.1274642944336}, {"session_id": "20250728_193101", "step_number": 4, "timestamp": "2025-07-28T19:31:04.422429", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2673.4087467193604}, {"session_id": "20250728_193101", "step_number": 5, "timestamp": "2025-07-28T19:31:04.465448", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.3537540435791}, {"session_id": "20250728_193101", "step_number": 6, "timestamp": "2025-07-28T19:31:04.469980", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.886091232299805}, {"session_id": "20250728_193449", "step_number": 1, "timestamp": "2025-07-28T19:34:49.054187", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "da6dc931-6f73-4e1e-9619-54e66<PERSON><PERSON>dd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "da6dc931-6f73-4e1e-9619-54e66<PERSON><PERSON>dd", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1398.6041791185569, 1950.27377862746, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 74}}, "duration_ms": null}, {"session_id": "20250728_193449", "step_number": 2, "timestamp": "2025-07-28T19:34:49.071311", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "da6dc931-6f73-4e1e-9619-54e66<PERSON><PERSON>dd", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "da6dc931-6f73-4e1e-9619-54e66<PERSON><PERSON>dd", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1398.6041791185569, 1950.27377862746, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_193449", "step_number": 3, "timestamp": "2025-07-28T19:34:51.789025", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2704.151391983032}, {"session_id": "20250728_193449", "step_number": 4, "timestamp": "2025-07-28T19:34:51.794489", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2709.615707397461}, {"session_id": "20250728_193449", "step_number": 5, "timestamp": "2025-07-28T19:34:51.835648", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.52238845825195}, {"session_id": "20250728_193449", "step_number": 6, "timestamp": "2025-07-28T19:34:51.840898", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.771888732910156}, {"session_id": "20250728_194000", "step_number": 1, "timestamp": "2025-07-28T19:40:00.186902", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "cad51fbe-f3da-4914-bb8d-2c8d023e710a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "cad51fbe-f3da-4914-bb8d-2c8d023e710a", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1334.4431537442686, 2097.1500828178487, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 70}}, "duration_ms": null}, {"session_id": "20250728_194000", "step_number": 2, "timestamp": "2025-07-28T19:40:00.199785", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "cad51fbe-f3da-4914-bb8d-2c8d023e710a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "cad51fbe-f3da-4914-bb8d-2c8d023e710a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1334.4431537442686, 2097.1500828178487, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_194000", "step_number": 3, "timestamp": "2025-07-28T19:40:03.346870", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3136.2407207489014}, {"session_id": "20250728_194000", "step_number": 4, "timestamp": "2025-07-28T19:40:03.352577", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 3141.947031021118}, {"session_id": "20250728_194000", "step_number": 5, "timestamp": "2025-07-28T19:40:03.407757", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.683570861816406}, {"session_id": "20250728_194000", "step_number": 6, "timestamp": "2025-07-28T19:40:03.413392", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 55.318355560302734}, {"session_id": "20250728_194000", "step_number": 7, "timestamp": "2025-07-28T20:04:13.407959", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f8da0bc1-f5c9-4c5a-a6ec-5f41e0cc9292", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f8da0bc1-f5c9-4c5a-a6ec-5f41e0cc9292", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1432.3829992460685, 1844.2714831950339, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_194000", "step_number": 8, "timestamp": "2025-07-28T20:04:13.417392", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "f8da0bc1-f5c9-4c5a-a6ec-5f41e0cc9292", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "f8da0bc1-f5c9-4c5a-a6ec-5f41e0cc9292", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1432.3829992460685, 1844.2714831950339, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_194000", "step_number": 9, "timestamp": "2025-07-28T20:04:16.040055", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2615.2946949005127}, {"session_id": "20250728_194000", "step_number": 10, "timestamp": "2025-07-28T20:04:16.044651", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2619.8911666870117}, {"session_id": "20250728_194000", "step_number": 11, "timestamp": "2025-07-28T20:04:16.082793", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.59055519104004}, {"session_id": "20250728_194000", "step_number": 12, "timestamp": "2025-07-28T20:04:16.087790", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.58780860900879}, {"session_id": "20250728_201607", "step_number": 1, "timestamp": "2025-07-28T20:16:07.514592", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5a14e590-8340-437c-8f33-35d3f3e8ca35", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5a14e590-8340-437c-8f33-35d3f3e8ca35", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1007.4398249770352, 1675.0577118471047, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_201607", "step_number": 2, "timestamp": "2025-07-28T20:16:07.525309", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5a14e590-8340-437c-8f33-35d3f3e8ca35", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5a14e590-8340-437c-8f33-35d3f3e8ca35", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1007.4398249770352, 1675.0577118471047, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_201607", "step_number": 3, "timestamp": "2025-07-28T20:16:10.066980", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2534.1155529022217}, {"session_id": "20250728_201607", "step_number": 4, "timestamp": "2025-07-28T20:16:10.074103", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2541.2380695343018}, {"session_id": "20250728_201607", "step_number": 5, "timestamp": "2025-07-28T20:16:10.128216", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 48.61021041870117}, {"session_id": "20250728_201607", "step_number": 6, "timestamp": "2025-07-28T20:16:10.134401", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 54.795265197753906}, {"session_id": "20250728_202033", "step_number": 1, "timestamp": "2025-07-28T20:20:33.808443", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1dbece35-3f4f-4805-bdeb-ad3d4fb65bca", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1dbece35-3f4f-4805-bdeb-ad3d4fb65bca", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1445.949894303017, 1692.4291044490078, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_202033", "step_number": 2, "timestamp": "2025-07-28T20:20:33.818656", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1dbece35-3f4f-4805-bdeb-ad3d4fb65bca", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1dbece35-3f4f-4805-bdeb-ad3d4fb65bca", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1445.949894303017, 1692.4291044490078, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_202033", "step_number": 3, "timestamp": "2025-07-28T20:20:36.535870", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2706.1142921447754}, {"session_id": "20250728_202033", "step_number": 4, "timestamp": "2025-07-28T20:20:36.541659", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2711.9033336639404}, {"session_id": "20250728_202033", "step_number": 5, "timestamp": "2025-07-28T20:20:36.596512", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.86882209777832}, {"session_id": "20250728_202033", "step_number": 6, "timestamp": "2025-07-28T20:20:36.602373", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 55.72962760925293}, {"session_id": "20250728_203640", "step_number": 1, "timestamp": "2025-07-28T20:36:40.029814", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "30c0c695-016a-409d-bf76-c044c7d7aabc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "30c0c695-016a-409d-bf76-c044c7d7aabc", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1288.3110235594809, 1856.2688595422778, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_203640", "step_number": 2, "timestamp": "2025-07-28T20:36:40.040304", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "30c0c695-016a-409d-bf76-c044c7d7aabc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "30c0c695-016a-409d-bf76-c044c7d7aabc", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1288.3110235594809, 1856.2688595422778, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_203640", "step_number": 3, "timestamp": "2025-07-28T20:36:42.710865", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2659.424066543579}, {"session_id": "20250728_203640", "step_number": 4, "timestamp": "2025-07-28T20:36:42.717723", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2666.804313659668}, {"session_id": "20250728_203640", "step_number": 5, "timestamp": "2025-07-28T20:36:42.766063", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 43.646812438964844}, {"session_id": "20250728_203640", "step_number": 6, "timestamp": "2025-07-28T20:36:42.776196", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 53.77984046936035}, {"session_id": "20250728_204041", "step_number": 1, "timestamp": "2025-07-28T20:40:41.197520", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b6abbda7-58dd-4e7e-9197-0f2d5bf74ac8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b6abbda7-58dd-4e7e-9197-0f2d5bf74ac8", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1332.6527664479302, 1942.08999013834, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_204041", "step_number": 2, "timestamp": "2025-07-28T20:40:41.209943", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b6abbda7-58dd-4e7e-9197-0f2d5bf74ac8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b6abbda7-58dd-4e7e-9197-0f2d5bf74ac8", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1332.6527664479302, 1942.08999013834, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_204041", "step_number": 3, "timestamp": "2025-07-28T20:40:44.275888", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3055.8364391326904}, {"session_id": "20250728_204041", "step_number": 4, "timestamp": "2025-07-28T20:40:44.297120", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 3077.0676136016846}, {"session_id": "20250728_204041", "step_number": 5, "timestamp": "2025-07-28T20:40:44.348763", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.33815383911133}, {"session_id": "20250728_204041", "step_number": 6, "timestamp": "2025-07-28T20:40:44.355847", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 52.42300033569336}, {"session_id": "20250728_204306", "step_number": 1, "timestamp": "2025-07-28T20:43:06.274567", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c79184fb-3c38-49b9-8342-dcc8f02c5f5c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c79184fb-3c38-49b9-8342-dcc8f02c5f5c", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1016.7388503794873, 1588.8652041394198, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_204306", "step_number": 2, "timestamp": "2025-07-28T20:43:06.287922", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c79184fb-3c38-49b9-8342-dcc8f02c5f5c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c79184fb-3c38-49b9-8342-dcc8f02c5f5c", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1016.7388503794873, 1588.8652041394198, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_204306", "step_number": 3, "timestamp": "2025-07-28T20:43:08.398971", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2102.0379066467285}, {"session_id": "20250728_204306", "step_number": 4, "timestamp": "2025-07-28T20:43:08.405483", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2108.550548553467}, {"session_id": "20250728_204306", "step_number": 5, "timestamp": "2025-07-28T20:43:08.440464", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.999732971191406}, {"session_id": "20250728_204306", "step_number": 6, "timestamp": "2025-07-28T20:43:08.447997", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.53232955932617}, {"session_id": "20250728_205140", "step_number": 1, "timestamp": "2025-07-28T20:51:40.075797", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "30066400-c5bd-4f20-a9a9-8350e1dfc114", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "30066400-c5bd-4f20-a9a9-8350e1dfc114", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1196.7425985447567, 1856.3229194492008, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 77}}, "duration_ms": null}, {"session_id": "20250728_205140", "step_number": 2, "timestamp": "2025-07-28T20:51:40.090773", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "30066400-c5bd-4f20-a9a9-8350e1dfc114", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "30066400-c5bd-4f20-a9a9-8350e1dfc114", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1196.7425985447567, 1856.3229194492008, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_205140", "step_number": 3, "timestamp": "2025-07-28T20:51:42.743333", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2640.3708457946777}, {"session_id": "20250728_205140", "step_number": 4, "timestamp": "2025-07-28T20:51:42.750843", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2647.881031036377}, {"session_id": "20250728_205140", "step_number": 5, "timestamp": "2025-07-28T20:51:42.795497", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.62445259094238}, {"session_id": "20250728_205140", "step_number": 6, "timestamp": "2025-07-28T20:51:42.801486", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.61352729797363}, {"session_id": "20250728_205513", "step_number": 1, "timestamp": "2025-07-28T20:55:13.979557", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ebf6e7c1-4299-41ff-a1b1-cb9694cc6da7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ebf6e7c1-4299-41ff-a1b1-cb9694cc6da7", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1821.6754747839298, 1771.7761576107557, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_205513", "step_number": 2, "timestamp": "2025-07-28T20:55:13.990499", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ebf6e7c1-4299-41ff-a1b1-cb9694cc6da7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ebf6e7c1-4299-41ff-a1b1-cb9694cc6da7", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1821.6754747839298, 1771.7761576107557, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_205513", "step_number": 3, "timestamp": "2025-07-28T20:55:17.147207", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3147.237777709961}, {"session_id": "20250728_205513", "step_number": 4, "timestamp": "2025-07-28T20:55:17.154678", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 3154.7083854675293}, {"session_id": "20250728_205513", "step_number": 5, "timestamp": "2025-07-28T20:55:17.201232", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 40.44938087463379}, {"session_id": "20250728_205513", "step_number": 6, "timestamp": "2025-07-28T20:55:17.208214", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 47.430992126464844}, {"session_id": "20250728_205513", "step_number": 7, "timestamp": "2025-07-28T20:57:08.614268", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "80fe8185-97fa-4c2b-bf91-856ca12b4416", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "80fe8185-97fa-4c2b-bf91-856ca12b4416", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1821.6754747839298, 1771.7761576107557, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_205513", "step_number": 8, "timestamp": "2025-07-28T20:57:08.628225", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "80fe8185-97fa-4c2b-bf91-856ca12b4416", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "80fe8185-97fa-4c2b-bf91-856ca12b4416", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1821.6754747839298, 1771.7761576107557, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_205513", "step_number": 9, "timestamp": "2025-07-28T20:57:11.400423", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2763.5488510131836}, {"session_id": "20250728_205513", "step_number": 10, "timestamp": "2025-07-28T20:57:11.406639", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2769.7653770446777}, {"session_id": "20250728_205513", "step_number": 11, "timestamp": "2025-07-28T20:57:11.457172", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 42.85740852355957}, {"session_id": "20250728_205513", "step_number": 12, "timestamp": "2025-07-28T20:57:11.463003", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 48.68888854980469}, {"session_id": "20250728_205513", "step_number": 13, "timestamp": "2025-07-28T21:02:12.024655", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "81640058-6bf2-4068-aac3-5fc0e9dd689f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "81640058-6bf2-4068-aac3-5fc0e9dd689f", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1409.3179426571055, 1866.1891781867823, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_205513", "step_number": 14, "timestamp": "2025-07-28T21:02:12.034888", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "81640058-6bf2-4068-aac3-5fc0e9dd689f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "81640058-6bf2-4068-aac3-5fc0e9dd689f", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1409.3179426571055, 1866.1891781867823, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_205513", "step_number": 15, "timestamp": "2025-07-28T21:02:14.810526", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2766.899824142456}, {"session_id": "20250728_205513", "step_number": 16, "timestamp": "2025-07-28T21:02:14.816526", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2772.899389266968}, {"session_id": "20250728_205513", "step_number": 17, "timestamp": "2025-07-28T21:02:14.862447", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.00797462463379}, {"session_id": "20250728_205513", "step_number": 18, "timestamp": "2025-07-28T21:02:14.867938", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 43.498992919921875}, {"session_id": "20250728_210559", "step_number": 1, "timestamp": "2025-07-28T21:05:59.447280", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "74cd7252-e2c7-4ca5-aeea-6119f89dc6f9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "74cd7252-e2c7-4ca5-aeea-6119f89dc6f9", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1260.3198504137417, 1903.6202199353197, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 69}}, "duration_ms": null}, {"session_id": "20250728_210559", "step_number": 2, "timestamp": "2025-07-28T21:05:59.463332", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "74cd7252-e2c7-4ca5-aeea-6119f89dc6f9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "74cd7252-e2c7-4ca5-aeea-6119f89dc6f9", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1260.3198504137417, 1903.6202199353197, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_210559", "step_number": 3, "timestamp": "2025-07-28T21:06:02.138612", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2656.8403244018555}, {"session_id": "20250728_210559", "step_number": 4, "timestamp": "2025-07-28T21:06:02.145952", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2664.180040359497}, {"session_id": "20250728_210559", "step_number": 5, "timestamp": "2025-07-28T21:06:02.202833", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 49.57318305969238}, {"session_id": "20250728_210559", "step_number": 6, "timestamp": "2025-07-28T21:06:02.209169", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 55.90987205505371}, {"session_id": "20250728_211144", "step_number": 1, "timestamp": "2025-07-28T21:11:44.758444", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d59e9fae-d575-4546-8a7d-d8835aace9a2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d59e9fae-d575-4546-8a7d-d8835aace9a2", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1155.3736131165028, 1746.0926487677234, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_211144", "step_number": 2, "timestamp": "2025-07-28T21:11:44.771764", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d59e9fae-d575-4546-8a7d-d8835aace9a2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d59e9fae-d575-4546-8a7d-d8835aace9a2", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1155.3736131165028, 1746.0926487677234, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_211144", "step_number": 3, "timestamp": "2025-07-28T21:11:47.108394", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2329.0092945098877}, {"session_id": "20250728_211144", "step_number": 4, "timestamp": "2025-07-28T21:11:47.115358", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2335.9732627868652}, {"session_id": "20250728_211144", "step_number": 5, "timestamp": "2025-07-28T21:11:47.158237", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.207509994506836}, {"session_id": "20250728_211144", "step_number": 6, "timestamp": "2025-07-28T21:11:47.164432", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.40329360961914}, {"session_id": "20250728_211144", "step_number": 7, "timestamp": "2025-07-28T21:16:53.579652", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c955a0bc-c8e2-4df1-bab2-3a13485b79b0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c955a0bc-c8e2-4df1-bab2-3a13485b79b0", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1321.0252085098234, 1558.0261550354771, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_211144", "step_number": 8, "timestamp": "2025-07-28T21:16:53.590874", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c955a0bc-c8e2-4df1-bab2-3a13485b79b0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c955a0bc-c8e2-4df1-bab2-3a13485b79b0", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1321.0252085098234, 1558.0261550354771, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_211144", "step_number": 9, "timestamp": "2025-07-28T21:16:55.833550", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2234.532117843628}, {"session_id": "20250728_211144", "step_number": 10, "timestamp": "2025-07-28T21:16:55.840618", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2241.5995597839355}, {"session_id": "20250728_211144", "step_number": 11, "timestamp": "2025-07-28T21:16:55.883807", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.09752655029297}, {"session_id": "20250728_211144", "step_number": 12, "timestamp": "2025-07-28T21:16:55.891626", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 43.916940689086914}, {"session_id": "20250728_220917", "step_number": 1, "timestamp": "2025-07-28T22:09:17.123309", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d4c2c961-06ab-42be-8258-1e5a31a8034b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d4c2c961-06ab-42be-8258-1e5a31a8034b", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 20, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_220917", "step_number": 2, "timestamp": "2025-07-28T22:09:17.135556", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d4c2c961-06ab-42be-8258-1e5a31a8034b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d4c2c961-06ab-42be-8258-1e5a31a8034b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 20}}, "duration_ms": null}, {"session_id": "20250728_220917", "step_number": 3, "timestamp": "2025-07-28T22:09:25.599129", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 8451.331377029419}, {"session_id": "20250728_220917", "step_number": 4, "timestamp": "2025-07-28T22:09:25.605426", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 20}}, "duration_ms": 8457.628011703491}, {"session_id": "20250728_220917", "step_number": 5, "timestamp": "2025-07-28T22:09:25.701327", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 89.09940719604492}, {"session_id": "20250728_220917", "step_number": 6, "timestamp": "2025-07-28T22:09:25.708607", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 96.37975692749023}]