<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞行功能调试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: white;
            margin: 0;
            padding: 20px;
        }
        
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .debug-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .log-area {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .status-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 4px;
        }
        
        .status-label {
            font-weight: bold;
            color: #ccc;
        }
        
        .status-value {
            color: #00ff88;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🐛 飞行功能调试工具</h1>
        
        <div class="debug-section">
            <h2>1. 方法检查</h2>
            <button class="btn" id="check-methods">检查方法存在性</button>
            <button class="btn" id="check-properties">检查属性初始化</button>
            <div class="status-grid" id="method-status">
                <!-- 状态将动态生成 -->
            </div>
        </div>
        
        <div class="debug-section">
            <h2>2. 路径测试</h2>
            <button class="btn" id="create-test-path">创建测试路径</button>
            <button class="btn" id="test-interpolation">测试位置插值</button>
            <button class="btn" id="test-trail-methods">测试轨迹方法</button>
            <div class="log-area" id="path-log"></div>
        </div>
        
        <div class="debug-section">
            <h2>3. 事件系统测试</h2>
            <button class="btn" id="test-events">测试事件系统</button>
            <button class="btn" id="trigger-position-update">触发位置更新</button>
            <div class="log-area" id="event-log"></div>
        </div>
        
        <div class="debug-section">
            <h2>4. 飞行模拟测试</h2>
            <button class="btn" id="start-debug-flight">开始调试飞行</button>
            <button class="btn" id="stop-debug-flight">停止飞行</button>
            <div class="log-area" id="flight-log"></div>
        </div>
    </div>

    <script>
        // 模拟城市管理器类
        class DebugCityManager {
            constructor() {
                this.startPoint = { lng: 139.7670, lat: 35.6814 };
                this.endPoint = { lng: 139.7770, lat: 35.6914 };
                this.flightHeight = 100;
                this.safetyDistance = 20;
                this.currentPath = [];
                this.isFlying = false;
                this.flightProgress = 0;
                this.flightSpeed = 0.5;
                this.flightTrail = [];
                this.maxTrailLength = 50;
                this.eventListeners = new Map();
                
                // 创建测试路径
                this.createTestPath();
            }
            
            createTestPath() {
                this.currentPath = [
                    { lng: 139.7670, lat: 35.6814 },
                    { lng: 139.7680, lat: 35.6824 },
                    { lng: 139.7690, lat: 35.6834 },
                    { lng: 139.7700, lat: 35.6844 },
                    { lng: 139.7710, lat: 35.6854 },
                    { lng: 139.7720, lat: 35.6864 },
                    { lng: 139.7730, lat: 35.6874 },
                    { lng: 139.7740, lat: 35.6884 },
                    { lng: 139.7750, lat: 35.6894 },
                    { lng: 139.7760, lat: 35.6904 },
                    { lng: 139.7770, lat: 35.6914 }
                ];
            }
            
            on(event, callback) {
                if (!this.eventListeners.has(event)) {
                    this.eventListeners.set(event, []);
                }
                this.eventListeners.get(event).push(callback);
            }
            
            emit(event, data) {
                if (this.eventListeners.has(event)) {
                    this.eventListeners.get(event).forEach(callback => {
                        try {
                            callback(data);
                        } catch (error) {
                            console.error(`事件处理错误 ${event}:`, error);
                        }
                    });
                }
            }
            
            log(message, type = 'info') {
                console.log(`[${type.toUpperCase()}] ${message}`);
                this.emit('log', { message, level: type, timestamp: Date.now() });
            }
            
            // 从 modern-city-manager.js 复制的方法
            calculatePathLength() {
                if (this.currentPath.length < 2) return 0;

                let totalLength = 0;
                for (let i = 1; i < this.currentPath.length; i++) {
                    const prev = this.currentPath[i - 1];
                    const curr = this.currentPath[i];

                    // 使用Haversine公式计算地球表面两点间距离
                    const R = 6371; // 地球半径（公里）
                    const dLat = (curr.lat - prev.lat) * Math.PI / 180;
                    const dLng = (curr.lng - prev.lng) * Math.PI / 180;
                    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                             Math.cos(prev.lat * Math.PI / 180) * Math.cos(curr.lat * Math.PI / 180) *
                             Math.sin(dLng/2) * Math.sin(dLng/2);
                    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                    totalLength += R * c;
                }

                return totalLength;
            }
            
            interpolatePathPosition(progress) {
                // 验证路径有效性
                if (!this.currentPath || this.currentPath.length === 0) {
                    this.log('⚠️ 路径为空，无法插值计算位置', 'warning');
                    return this.startPoint || { lng: 139.7670, lat: 35.6814 };
                }

                if (this.currentPath.length === 1) {
                    return this.currentPath[0];
                }

                // 验证进度值
                progress = Math.max(0, Math.min(1, progress || 0));

                const totalLength = this.calculatePathLength();
                if (totalLength === 0) {
                    return this.currentPath[0];
                }

                const targetDistance = progress * totalLength;
                let currentDistance = 0;

                for (let i = 1; i < this.currentPath.length; i++) {
                    const prev = this.currentPath[i - 1];
                    const curr = this.currentPath[i];

                    // 验证路径点有效性
                    if (!prev || !curr || typeof prev.lng !== 'number' || typeof prev.lat !== 'number' ||
                        typeof curr.lng !== 'number' || typeof curr.lat !== 'number') {
                        this.log(`⚠️ 路径点无效: prev=${JSON.stringify(prev)}, curr=${JSON.stringify(curr)}`, 'warning');
                        continue;
                    }

                    // 计算这一段的长度
                    const R = 6371;
                    const dLat = (curr.lat - prev.lat) * Math.PI / 180;
                    const dLng = (curr.lng - prev.lng) * Math.PI / 180;
                    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                             Math.cos(prev.lat * Math.PI / 180) * Math.cos(curr.lat * Math.PI / 180) *
                             Math.sin(dLng/2) * Math.sin(dLng/2);
                    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                    const segmentLength = R * c;

                    if (currentDistance + segmentLength >= targetDistance) {
                        // 在这个线段上
                        const segmentProgress = segmentLength > 0 ? (targetDistance - currentDistance) / segmentLength : 0;

                        const interpolatedPos = {
                            lng: prev.lng + (curr.lng - prev.lng) * segmentProgress,
                            lat: prev.lat + (curr.lat - prev.lat) * segmentProgress
                        };

                        // 验证插值结果
                        if (isNaN(interpolatedPos.lng) || isNaN(interpolatedPos.lat)) {
                            this.log(`⚠️ 插值计算结果无效，返回当前点`, 'warning');
                            return curr;
                        }

                        return interpolatedPos;
                    }

                    currentDistance += segmentLength;
                }

                // 返回最后一个点
                const lastPoint = this.currentPath[this.currentPath.length - 1];
                return lastPoint || this.endPoint || { lng: 139.7670, lat: 35.6814 };
            }
            
            addToFlightTrail(position) {
                try {
                    // 验证位置有效性
                    if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number' ||
                        isNaN(position.lat) || isNaN(position.lng)) {
                        console.warn('无效的轨迹位置:', position);
                        return;
                    }

                    // 确保轨迹数组存在
                    if (!this.flightTrail) {
                        this.flightTrail = [];
                    }

                    this.flightTrail.push({
                        lat: position.lat,
                        lng: position.lng,
                        alt: position.alt || this.flightHeight,
                        timestamp: Date.now()
                    });

                    // 限制轨迹长度
                    if (this.flightTrail.length > this.maxTrailLength) {
                        this.flightTrail.shift();
                    }
                } catch (error) {
                    console.error('添加飞行轨迹点失败:', error);
                }
            }
            
            drawFlightTrail() {
                try {
                    // 模拟绘制轨迹
                    if (!this.flightTrail || this.flightTrail.length < 2) {
                        return;
                    }

                    // 验证轨迹点有效性
                    const validTrail = this.flightTrail.filter(point => 
                        point && typeof point.lng === 'number' && typeof point.lat === 'number' &&
                        !isNaN(point.lng) && !isNaN(point.lat)
                    );

                    if (validTrail.length < 2) {
                        return;
                    }

                    this.log(`绘制飞行轨迹: ${validTrail.length} 个点`, 'info');
                } catch (error) {
                    console.error('绘制飞行轨迹失败:', error);
                }
            }
            
            updateDronePosition(lngLat) {
                try {
                    // 验证坐标有效性
                    if (!lngLat || typeof lngLat.lng !== 'number' || typeof lngLat.lat !== 'number' ||
                        isNaN(lngLat.lng) || isNaN(lngLat.lat)) {
                        this.log(`⚠️ 无效的无人机坐标: ${JSON.stringify(lngLat)}`, 'warning');
                        return;
                    }

                    // 触发无人机位置更新事件（用于物体检测）
                    this.emit('dronePositionUpdate', {
                        lng: lngLat.lng,
                        lat: lngLat.lat,
                        alt: this.flightHeight,
                        timestamp: Date.now()
                    });

                } catch (error) {
                    this.log(`❌ 更新无人机位置失败: ${error.message}`, 'error');
                    console.error('更新无人机位置时出错:', error);
                }
            }
        }
        
        let debugManager;
        
        function logToArea(areaId, message) {
            const area = document.getElementById(areaId);
            if (area) {
                const timestamp = new Date().toLocaleTimeString();
                area.innerHTML += `[${timestamp}] ${message}\n`;
                area.scrollTop = area.scrollHeight;
            }
        }
        
        function updateStatus(containerId, statuses) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = statuses.map(status => `
                    <div class="status-item">
                        <div class="status-label">${status.label}</div>
                        <div class="status-value">${status.value}</div>
                    </div>
                `).join('');
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            debugManager = new DebugCityManager();
            
            // 检查方法存在性
            document.getElementById('check-methods').addEventListener('click', () => {
                const methods = [
                    'calculatePathLength',
                    'interpolatePathPosition', 
                    'addToFlightTrail',
                    'drawFlightTrail',
                    'updateDronePosition',
                    'on',
                    'emit'
                ];
                
                const statuses = methods.map(method => ({
                    label: method,
                    value: typeof debugManager[method] === 'function' ? '✅ 存在' : '❌ 缺失'
                }));
                
                updateStatus('method-status', statuses);
            });
            
            // 检查属性初始化
            document.getElementById('check-properties').addEventListener('click', () => {
                const properties = [
                    { name: 'currentPath', value: debugManager.currentPath?.length || 0 },
                    { name: 'flightTrail', value: Array.isArray(debugManager.flightTrail) ? '✅ 数组' : '❌ 无效' },
                    { name: 'startPoint', value: debugManager.startPoint ? '✅ 已设置' : '❌ 未设置' },
                    { name: 'endPoint', value: debugManager.endPoint ? '✅ 已设置' : '❌ 未设置' },
                    { name: 'eventListeners', value: debugManager.eventListeners instanceof Map ? '✅ Map' : '❌ 无效' }
                ];
                
                const statuses = properties.map(prop => ({
                    label: prop.name,
                    value: prop.value
                }));
                
                updateStatus('method-status', statuses);
            });
            
            // 创建测试路径
            document.getElementById('create-test-path').addEventListener('click', () => {
                debugManager.createTestPath();
                logToArea('path-log', `✅ 创建测试路径: ${debugManager.currentPath.length} 个点`);
                logToArea('path-log', `路径长度: ${debugManager.calculatePathLength().toFixed(4)} km`);
            });
            
            // 测试位置插值
            document.getElementById('test-interpolation').addEventListener('click', () => {
                const testProgresses = [0, 0.25, 0.5, 0.75, 1.0];
                testProgresses.forEach(progress => {
                    const pos = debugManager.interpolatePathPosition(progress);
                    logToArea('path-log', `进度 ${progress}: ${JSON.stringify(pos)}`);
                });
            });
            
            // 测试轨迹方法
            document.getElementById('test-trail-methods').addEventListener('click', () => {
                debugManager.flightTrail = [];
                
                // 添加几个测试点
                const testPoints = [
                    { lng: 139.7670, lat: 35.6814 },
                    { lng: 139.7680, lat: 35.6824 },
                    { lng: 139.7690, lat: 35.6834 }
                ];
                
                testPoints.forEach(point => {
                    debugManager.addToFlightTrail(point);
                    logToArea('path-log', `添加轨迹点: ${JSON.stringify(point)}`);
                });
                
                debugManager.drawFlightTrail();
                logToArea('path-log', `轨迹点总数: ${debugManager.flightTrail.length}`);
            });
            
            // 测试事件系统
            document.getElementById('test-events').addEventListener('click', () => {
                debugManager.on('testEvent', (data) => {
                    logToArea('event-log', `收到测试事件: ${JSON.stringify(data)}`);
                });
                
                debugManager.emit('testEvent', { message: 'Hello World', timestamp: Date.now() });
                logToArea('event-log', '✅ 事件系统测试完成');
            });
            
            // 触发位置更新
            document.getElementById('trigger-position-update').addEventListener('click', () => {
                debugManager.on('dronePositionUpdate', (position) => {
                    logToArea('event-log', `无人机位置更新: ${JSON.stringify(position)}`);
                });
                
                debugManager.updateDronePosition({ lng: 139.7670, lat: 35.6814 });
                logToArea('event-log', '✅ 位置更新事件触发完成');
            });
            
            // 开始调试飞行
            document.getElementById('start-debug-flight').addEventListener('click', () => {
                debugManager.isFlying = true;
                debugManager.flightTrail = [];
                
                const startTime = Date.now();
                const pathLength = debugManager.calculatePathLength();
                const flightDuration = (pathLength / debugManager.flightSpeed) * 1000;
                
                logToArea('flight-log', `🚁 开始调试飞行`);
                logToArea('flight-log', `路径长度: ${pathLength.toFixed(4)} km`);
                logToArea('flight-log', `预计时长: ${(flightDuration/1000).toFixed(1)} 秒`);
                
                const animate = () => {
                    if (!debugManager.isFlying) return;
                    
                    const elapsed = Date.now() - startTime;
                    debugManager.flightProgress = Math.min(elapsed / flightDuration, 1);
                    
                    if (debugManager.flightProgress >= 1) {
                        debugManager.flightProgress = 1;
                        debugManager.updateDronePosition(debugManager.endPoint);
                        debugManager.isFlying = false;
                        logToArea('flight-log', '✅ 飞行完成！');
                        return;
                    }
                    
                    const currentPos = debugManager.interpolatePathPosition(debugManager.flightProgress);
                    if (currentPos) {
                        debugManager.updateDronePosition(currentPos);
                        debugManager.addToFlightTrail(currentPos);
                        debugManager.drawFlightTrail();
                        
                        const progressPercent = (debugManager.flightProgress * 100).toFixed(1);
                        logToArea('flight-log', `飞行进度: ${progressPercent}% - ${JSON.stringify(currentPos)}`);
                    }
                    
                    setTimeout(() => requestAnimationFrame(animate), 100);
                };
                
                animate();
            });
            
            // 停止调试飞行
            document.getElementById('stop-debug-flight').addEventListener('click', () => {
                debugManager.isFlying = false;
                logToArea('flight-log', '⏹️ 飞行已停止');
            });
        });
    </script>
</body>
</html>
