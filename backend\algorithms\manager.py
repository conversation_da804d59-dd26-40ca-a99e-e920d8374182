"""
算法管理器
负责算法的注册、执行、监控和缓存管理
"""

import asyncio
import hashlib
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import logging

from .base import PathPlanningAlgorithm
from .data_structures import PathPlanningRequest, PathPlanningResponse


class AlgorithmCache:
    """算法结果缓存系统"""
    
    def __init__(self, max_size: int = 100):
        self.cache: Dict[str, Tuple[PathPlanningResponse, float]] = {}
        self.max_size = max_size
        self.access_times: Dict[str, float] = {}
    
    def _generate_cache_key(self, algorithm_name: str, request: PathPlanningRequest) -> str:
        """生成缓存键"""
        # 创建请求的哈希值
        request_data = {
            'algorithm': algorithm_name,
            'start_point': request.start_point.to_dict(),
            'end_point': request.end_point.to_dict(),
            'flight_height': request.flight_height,
            'parameters': request.parameters,
            'optimization': request.optimization
        }
        
        request_str = json.dumps(request_data, sort_keys=True)
        return hashlib.md5(request_str.encode()).hexdigest()
    
    def get(self, algorithm_name: str, request: PathPlanningRequest) -> Optional[PathPlanningResponse]:
        """获取缓存结果"""
        cache_key = self._generate_cache_key(algorithm_name, request)
        
        if cache_key in self.cache:
            response, cache_time = self.cache[cache_key]
            self.access_times[cache_key] = time.time()
            
            # 检查缓存是否过期（1小时）
            if time.time() - cache_time < 3600:
                return response
            else:
                # 删除过期缓存
                del self.cache[cache_key]
                del self.access_times[cache_key]
        
        return None
    
    def set(self, algorithm_name: str, request: PathPlanningRequest, response: PathPlanningResponse):
        """设置缓存结果"""
        cache_key = self._generate_cache_key(algorithm_name, request)
        current_time = time.time()
        
        # 如果缓存已满，删除最久未访问的项
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            del self.cache[oldest_key]
            del self.access_times[oldest_key]
        
        self.cache[cache_key] = (response, current_time)
        self.access_times[cache_key] = current_time
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hit_rate': 0.0  # TODO: 实现命中率统计
        }


class AlgorithmManager:
    """算法管理器"""
    
    def __init__(self):
        self.algorithms: Dict[str, PathPlanningAlgorithm] = {}
        self.execution_queue = asyncio.Queue()
        self.is_processing_queue = False
        self.max_concurrent_executions = 3
        self.current_executions: Dict[str, asyncio.Task] = {}
        
        # 性能监控
        self.global_stats = {
            'total_requests': 0,
            'total_execution_time': 0.0,
            'average_execution_time': 0.0,
            'algorithm_usage': defaultdict(int)
        }
        
        # 事件监听器
        self.event_listeners: Dict[str, List[callable]] = defaultdict(list)
        
        # 缓存系统
        self.cache = AlgorithmCache()
        self.cache_enabled = True
        
        # 日志记录
        self.logger = logging.getLogger(__name__)
    
    def register_algorithm(self, algorithm: PathPlanningAlgorithm) -> bool:
        """
        注册算法
        
        Args:
            algorithm: 算法实例
            
        Returns:
            bool: 注册是否成功
        """
        try:
            algorithm_name = algorithm.info.name
            
            if algorithm_name in self.algorithms:
                print(f"算法 {algorithm_name} 已存在，将被覆盖")
            
            self.algorithms[algorithm_name] = algorithm
            self.logger.info(f"✅ 算法 {algorithm_name} v{algorithm.info.version} 注册成功")
            
            # 触发事件
            self._emit_event('algorithm_registered', {
                'algorithm_name': algorithm_name,
                'algorithm_info': algorithm.get_info()
            })
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 注册算法失败: {str(e)}")
            return False
    
    def unregister_algorithm(self, algorithm_name: str) -> bool:
        """
        注销算法
        
        Args:
            algorithm_name: 算法名称
            
        Returns:
            bool: 注销是否成功
        """
        if algorithm_name in self.algorithms:
            del self.algorithms[algorithm_name]
            self.logger.info(f"算法 {algorithm_name} 已注销")
            
            # 触发事件
            self._emit_event('algorithm_unregistered', {
                'algorithm_name': algorithm_name
            })
            
            return True
        else:
            self.logger.warning(f"算法 {algorithm_name} 不存在")
            return False
    
    def list_algorithms(self) -> List[Dict[str, Any]]:
        """
        获取已注册算法列表
        
        Returns:
            List[Dict]: 算法信息列表
        """
        return [
            {
                'name': name,
                'info': algorithm.get_info()
            }
            for name, algorithm in self.algorithms.items()
        ]
    
    def get_algorithm_info(self, algorithm_name: str) -> Optional[Dict[str, Any]]:
        """
        获取算法信息
        
        Args:
            algorithm_name: 算法名称
            
        Returns:
            Optional[Dict]: 算法信息，如果不存在返回None
        """
        if algorithm_name in self.algorithms:
            return self.algorithms[algorithm_name].get_info()
        return None
    
    async def execute_algorithm(
        self, 
        algorithm_name: str, 
        request: PathPlanningRequest,
        options: Optional[Dict[str, Any]] = None
    ) -> PathPlanningResponse:
        """
        执行算法
        
        Args:
            algorithm_name: 算法名称
            request: 路径规划请求
            options: 执行选项
            
        Returns:
            PathPlanningResponse: 路径规划响应
        """
        if options is None:
            options = {}
        
        # 检查算法是否存在
        if algorithm_name not in self.algorithms:
            response = PathPlanningResponse()
            response.success = False
            response.error = f"算法 {algorithm_name} 不存在"
            return response
        
        # 检查缓存
        if self.cache_enabled and not options.get('skip_cache', False):
            cached_response = self.cache.get(algorithm_name, request)
            if cached_response:
                self.logger.info(f"使用缓存结果: {algorithm_name}")
                return cached_response
        
        # 更新全局统计
        self.global_stats['total_requests'] += 1
        self.global_stats['algorithm_usage'][algorithm_name] += 1
        
        start_time = time.time()
        
        try:
            # 获取算法实例
            algorithm = self.algorithms[algorithm_name]

            # 触发执行开始事件
            self._emit_event('algorithm_execution_started', {
                'algorithm_name': algorithm_name,
                'request_id': request.request_id
            })

            # 添加超时保护
            timeout = options.get('timeout', 30.0)  # 默认30秒超时

            try:
                # 执行算法（带超时）
                response = await asyncio.wait_for(
                    algorithm.execute(request),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                response = PathPlanningResponse()
                response.success = False
                response.error = f"算法执行超时 ({timeout}秒)"
                response.execution_time = timeout
                self.logger.warning(f"算法 {algorithm_name} 执行超时")
                return response
            
            # 更新全局统计
            execution_time = time.time() - start_time
            self.global_stats['total_execution_time'] += execution_time
            self.global_stats['average_execution_time'] = (
                self.global_stats['total_execution_time'] / 
                self.global_stats['total_requests']
            )
            
            # 缓存结果
            if self.cache_enabled and response.success:
                self.cache.set(algorithm_name, request, response)
            
            # 触发执行完成事件
            self._emit_event('algorithm_execution_completed', {
                'algorithm_name': algorithm_name,
                'request_id': request.request_id,
                'success': response.success,
                'execution_time': execution_time
            })
            
            self.logger.info(
                f"算法 {algorithm_name} 执行完成: "
                f"{'成功' if response.success else '失败'}, "
                f"耗时 {execution_time:.2f}s"
            )
            
            return response
            
        except Exception as e:
            # 创建错误响应
            response = PathPlanningResponse()
            response.success = False
            response.error = f"算法执行异常: {str(e)}"
            response.execution_time = time.time() - start_time
            
            # 触发执行错误事件
            self._emit_event('algorithm_execution_error', {
                'algorithm_name': algorithm_name,
                'request_id': request.request_id,
                'error': str(e)
            })
            
            self.logger.error(f"算法 {algorithm_name} 执行失败: {str(e)}")
            
            return response
    
    def cancel_execution(self, execution_id: str) -> bool:
        """
        取消算法执行
        
        Args:
            execution_id: 执行ID
            
        Returns:
            bool: 取消是否成功
        """
        if execution_id in self.current_executions:
            task = self.current_executions[execution_id]
            task.cancel()
            del self.current_executions[execution_id]
            return True
        return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            Dict: 性能统计数据
        """
        return {
            'global': self.global_stats.copy(),
            'algorithms': {
                name: algorithm.get_info()['stats']
                for name, algorithm in self.algorithms.items()
            },
            'cache': self.cache.get_stats()
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.global_stats = {
            'total_requests': 0,
            'total_execution_time': 0.0,
            'average_execution_time': 0.0,
            'algorithm_usage': defaultdict(int)
        }
        
        for algorithm in self.algorithms.values():
            algorithm.reset_stats()
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        self.logger.info("算法缓存已清空")
    
    def on(self, event_name: str, callback: callable):
        """
        注册事件监听器
        
        Args:
            event_name: 事件名称
            callback: 回调函数
        """
        self.event_listeners[event_name].append(callback)
    
    def off(self, event_name: str, callback: callable):
        """
        移除事件监听器
        
        Args:
            event_name: 事件名称
            callback: 回调函数
        """
        if callback in self.event_listeners[event_name]:
            self.event_listeners[event_name].remove(callback)
    
    def _emit_event(self, event_name: str, data: Dict[str, Any]):
        """
        触发事件
        
        Args:
            event_name: 事件名称
            data: 事件数据
        """
        for callback in self.event_listeners[event_name]:
            try:
                callback(data)
            except Exception as e:
                self.logger.error(f"事件回调执行失败: {str(e)}")


# 全局算法管理器实例
algorithm_manager = AlgorithmManager()

# 注意：算法注册已移至 __init__.py 中的 register_default_algorithms() 函数
# 避免重复注册，这里不再自动注册
