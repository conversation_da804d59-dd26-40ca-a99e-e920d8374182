/* 算法对比面板样式 */
.comparison-panel {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 600px;
    min-height: 200px;
    max-height: 80vh;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    z-index: 10000;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    resize: vertical;
    cursor: move;
}

.comparison-panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
    user-select: none;
}

.comparison-panel-header:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.comparison-panel.dragging {
    transition: none;
}

.comparison-panel.collapsed .comparison-panel-content {
    display: none;
}

.comparison-panel.collapsed {
    height: auto;
    min-height: auto;
}

.collapse-btn {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    margin-right: 8px;
}

.collapse-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.comparison-panel-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.close-comparison-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.close-comparison-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.comparison-panel-content {
    padding: 20px;
    max-height: calc(80vh - 80px);
    overflow-y: auto;
}

/* 对比步骤样式 */
.comparison-steps {
    margin-bottom: 20px;
}

.step-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    border-radius: 8px;
    background: #f8f9fa;
    border-left: 4px solid #e9ecef;
    transition: all 0.3s ease;
}

.step-item.pending {
    border-left-color: #6c757d;
}

.step-item.running {
    border-left-color: #007bff;
    background: #e3f2fd;
    animation: stepPulse 1.5s ease-in-out infinite;
}

.step-item.running .step-name {
    color: #1565c0 !important;  /* 深蓝色文字在浅蓝背景上 */
}

.step-item.running .step-status {
    color: #1976d2 !important;  /* 深蓝色文字在浅蓝背景上 */
}

.step-item.completed {
    border-left-color: #28a745;
    background: #e8f5e8;
}

.step-item.completed .step-name {
    color: #2e7d32 !important;  /* 深绿色文字在浅绿背景上 */
}

.step-item.completed .step-status {
    color: #388e3c !important;  /* 深绿色文字在浅绿背景上 */
}

.step-item.error {
    border-left-color: #dc3545;
    background: #ffeaea;
}

.step-item.error .step-name {
    color: #c62828 !important;  /* 深红色文字在浅红背景上 */
}

.step-item.error .step-status {
    color: #d32f2f !important;  /* 深红色文字在浅红背景上 */
}

.step-icon {
    font-size: 20px;
    margin-right: 12px;
    min-width: 24px;
}

.step-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; /* 防止flex收缩问题 */
}

.step-name {
    font-weight: 500;
    color: #ffffff !important;  /* 修复：使用白色文字 */
    margin-bottom: 2px;
    font-size: 14px;
    line-height: 1.4;
}

.step-status {
    font-size: 12px;
    color: #cccccc !important;  /* 修复：使用浅灰色文字 */
    line-height: 1.2;
}





/* 对比结果样式 */
.comparison-results {
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
}

.comparison-results h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
}

.metrics-comparison {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.metric-row {
    display: grid;
    grid-template-columns: 1fr 80px 80px 60px;
    gap: 12px;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.metric-row:last-child {
    border-bottom: none;
}

.metric-row.total {
    border-top: 2px solid #007bff;
    padding-top: 12px;
    margin-top: 8px;
    font-weight: 600;
}

.metric-name {
    font-weight: 500;
    color: #333;
}

.baseline-value {
    text-align: center;
    color: #ff6b35;
    font-weight: 500;
    font-size: 14px;
}

.improved-value {
    text-align: center;
    color: #00d4ff;
    font-weight: 500;
    font-size: 14px;
}

.improvement {
    text-align: center;
    font-weight: 600;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
}

.improvement.positive {
    background: #d4edda;
    color: #155724;
}

.improvement.negative {
    background: #f8d7da;
    color: #721c24;
}

.comparison-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
    border-radius: 8px;
    text-align: center;
}

.comparison-summary p {
    margin: 0;
    font-size: 16px;
}

.comparison-summary strong {
    font-size: 18px;
    font-weight: 700;
}

/* 物体检测面板样式 */
.detection-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    max-height: 500px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    z-index: 9999;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.detection-panel-header {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detection-panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.detection-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.toggle-detection-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.toggle-detection-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.close-detection-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.close-detection-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.detection-panel-content {
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
}

.detection-stats {
    margin-bottom: 16px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #e9ecef;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.stat-value {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.detected-objects-list {
    border-top: 1px solid #e9ecef;
    padding-top: 16px;
}

.detected-object-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.object-icon {
    font-size: 18px;
    margin-right: 12px;
    min-width: 20px;
}

.object-info {
    flex: 1;
}

.object-name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    margin-bottom: 2px;
}

.object-details {
    font-size: 12px;
    color: #666;
}

.object-cost {
    font-weight: 600;
    color: #dc3545;
    font-size: 12px;
}

/* 检测标记样式 */
.detection-marker {
    transition: transform 0.2s ease;
}

.detection-marker:hover {
    transform: scale(1.2);
}

/* 动画效果 */
@keyframes stepPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes detectionPulse {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .comparison-panel {
        width: 90%;
        max-width: 500px;
    }
    
    .detection-panel {
        width: 300px;
        right: 10px;
        top: 10px;
    }
    
    .metric-row {
        grid-template-columns: 1fr 60px 60px 50px;
        gap: 8px;
        font-size: 12px;
    }
}
