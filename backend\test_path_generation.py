#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路径生成和导出功能
验证81条路径是否真的不同
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathPlanning
from algorithms.data_structures import PathPlanningRequest

async def test_path_generation():
    """测试路径生成功能"""
    print("🧪 开始测试路径生成功能...")
    print("=" * 60)
    
    try:
        # 创建算法实例
        algorithm = ImprovedClusterBasedPathPlanning()
        
        # 创建测试请求
        request_data = {
            'startPoint': {'lng': 139.7673, 'lat': 35.6812, 'alt': 50},  # 东京站
            'endPoint': {'lng': 139.7016, 'lat': 35.6598, 'alt': 50},    # 涩谷站
            'flightHeight': 100,
            'safetyDistance': 20,
            'maxTurnAngle': 45,
            'algorithm': 'ImprovedClusterBased'
        }
        request = PathPlanningRequest(request_data)
        
        print(f"📍 起点: 东京站 ({request.start_point.lng}, {request.start_point.lat})")
        print(f"📍 终点: 涩谷站 ({request.end_point.lng}, {request.end_point.lat})")
        print()
        
        # 直接测试路径生成方法
        print("🔄 测试 _generate_optimized_path_set 方法...")
        initial_paths = await algorithm._generate_optimized_path_set(request)
        
        print()
        print("📊 路径生成结果分析:")
        print(f"   总路径数: {len(initial_paths)}")
        
        if len(initial_paths) > 0:
            # 分析方向分布
            flight_directions = [path.flight_direction for path in initial_paths]
            height_layers = [path.height_layer for path in initial_paths]
            
            unique_directions = set(flight_directions)
            unique_heights = set(height_layers)
            
            print(f"   方向种类数: {len(unique_directions)} (期望: 9)")
            print(f"   高度种类数: {len(unique_heights)} (期望: 9)")
            print(f"   方向分布: {sorted(unique_directions)}")
            print(f"   高度分布: {sorted(unique_heights)}")
            
            # 检查是否有重复的组合
            combinations = [(path.flight_direction, path.height_layer) for path in initial_paths]
            unique_combinations = set(combinations)
            
            print(f"   组合种类数: {len(unique_combinations)} (期望: 81)")
            
            if len(unique_combinations) == 81:
                print("✅ 路径组合完全不重复，符合预期")
            else:
                print("⚠️ 存在重复的路径组合")
                
                # 找出重复的组合
                from collections import Counter
                combination_counts = Counter(combinations)
                duplicates = {combo: count for combo, count in combination_counts.items() if count > 1}
                
                if duplicates:
                    print("   重复的组合:")
                    for combo, count in duplicates.items():
                        print(f"     方向{combo[0]}, 高度{combo[1]}: {count}次")
            
            # 显示前10条路径的详细信息
            print()
            print("🔍 前10条路径详细信息:")
            for i in range(min(10, len(initial_paths))):
                path = initial_paths[i]
                waypoint_count = len(path.waypoints) if path.waypoints else 0
                
                # 获取首末航点信息
                first_wp_info = "N/A"
                last_wp_info = "N/A"
                
                if path.waypoints and len(path.waypoints) > 0:
                    first_wp = path.waypoints[0]
                    last_wp = path.waypoints[-1]
                    first_wp_info = f"({first_wp.lng:.6f}, {first_wp.lat:.6f}, {first_wp.alt:.1f})"
                    last_wp_info = f"({last_wp.lng:.6f}, {last_wp.lat:.6f}, {last_wp.alt:.1f})"
                
                print(f"   路径{i}: ID={path.path_id}, 方向={path.flight_direction}, 高度={path.height_layer}")
                print(f"          航点数={waypoint_count}, 首航点={first_wp_info}")
                print(f"          末航点={last_wp_info}")
                
                # 检查是否有额外的属性
                if hasattr(path, 'path_identifier'):
                    print(f"          标识符={path.path_identifier}")
                
                print()
        
        else:
            print("❌ 没有生成任何路径")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_full_algorithm():
    """测试完整算法执行"""
    print()
    print("🧪 开始测试完整算法执行...")
    print("=" * 60)
    
    try:
        # 创建算法实例
        algorithm = ImprovedClusterBasedPathPlanning()
        
        # 创建测试请求
        request_data = {
            'startPoint': {'lng': 139.7673, 'lat': 35.6812, 'alt': 50},  # 东京站
            'endPoint': {'lng': 139.7016, 'lat': 35.6598, 'alt': 50},    # 涩谷站
            'flightHeight': 100,
            'safetyDistance': 20,
            'maxTurnAngle': 45,
            'algorithm': 'ImprovedClusterBased'
        }
        request = PathPlanningRequest(request_data)
        
        # 执行完整算法
        print("🔄 执行完整路径规划算法...")
        response = await algorithm.calculate_path(request)
        
        print(f"✅ 算法执行完成，成功: {response.success}")
        
        # 检查是否有initial_path_set
        if hasattr(algorithm, 'initial_path_set') and algorithm.initial_path_set:
            print(f"📊 算法实例中的initial_path_set: {len(algorithm.initial_path_set)} 条路径")
            
            # 验证路径差异性
            if len(algorithm.initial_path_set) >= 3:
                print("🔍 验证前3条路径:")
                for i in range(3):
                    path = algorithm.initial_path_set[i]
                    print(f"   路径{i}: 方向={path.flight_direction}, 高度={path.height_layer}")
        else:
            print("❌ 算法实例中没有initial_path_set数据")
            
    except Exception as e:
        print(f"❌ 完整算法测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚁 路径生成测试工具")
    print("=" * 60)
    
    # 运行测试
    asyncio.run(test_path_generation())
    asyncio.run(test_full_algorithm())
    
    print()
    print("🎉 测试完成！")
    print("请检查上述输出，确认路径生成是否正常。")
    print("如果看到 '✅ 路径组合完全不重复，符合预期'，说明修复成功。")
