#!/usr/bin/env python3
"""
调试API响应结构 - 查看完整的响应数据
"""

import requests
import json

def debug_api_response():
    """调试API响应结构"""
    print("🔍 调试API响应结构")
    print("=" * 60)
    
    # API端点
    url = "http://localhost:5000/api/calculate_path"
    
    # 简单的测试数据
    test_data = {
        "startPoint": {"lng": 116.3974, "lat": 39.9093, "alt": 100, "x": 0, "y": 0, "z": 100},
        "endPoint": {"lng": 116.4074, "lat": 39.9193, "alt": 100, "x": 1000, "y": 1000, "z": 100},
        "flightHeight": 120.0,
        "safetyDistance": 30.0,
        "maxTurnAngle": 90.0,
        "algorithm": "ImprovedClusterBased",
        "buildings": [
            {"id": "test_1", "x": 200, "y": 200, "radius": 25, "height": 80, "type": "residential"},
            {"id": "test_2", "x": 400, "y": 400, "radius": 30, "height": 90, "type": "commercial"}
        ],
        "protectionZones": [],
        "parameters": {"kValue": 5.0, "enablePathSwitching": True}
    }
    
    try:
        response = requests.post(url, json=test_data, headers={"Content-Type": "application/json"}, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            
            print("📊 完整响应结构:")
            print("=" * 40)
            
            # 打印顶级键
            print("🔑 顶级键:")
            for key in result.keys():
                print(f"  - {key}: {type(result[key])}")
            
            # 检查metadata
            if 'metadata' in result:
                print("\n📋 metadata 内容:")
                metadata = result['metadata']
                for key, value in metadata.items():
                    if isinstance(value, dict):
                        print(f"  - {key}: dict with {len(value)} keys")
                    elif isinstance(value, list):
                        print(f"  - {key}: list with {len(value)} items")
                    else:
                        print(f"  - {key}: {value}")
                
                # 检查detailed_calculations
                if 'detailed_calculations' in metadata:
                    print("\n🔬 detailed_calculations 内容:")
                    detailed = metadata['detailed_calculations']
                    for key, value in detailed.items():
                        if isinstance(value, dict):
                            print(f"  - {key}: dict with {len(value)} keys")
                            if key == 'terminal_output_data':
                                print("    📺 terminal_output_data 详细内容:")
                                for sub_key, sub_value in value.items():
                                    if isinstance(sub_value, dict):
                                        print(f"      - {sub_key}: dict with {len(sub_value)} keys")
                                        for sub_sub_key, sub_sub_value in sub_value.items():
                                            print(f"        - {sub_sub_key}: {sub_sub_value}")
                                    else:
                                        print(f"      - {sub_key}: {sub_value}")
                        else:
                            print(f"  - {key}: {value}")
            
            # 保存完整响应到文件以便详细查看
            with open('debug_response.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"\n💾 完整响应已保存到 debug_response.json")
            
            return True
            
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    debug_api_response()
