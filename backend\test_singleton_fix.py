#!/usr/bin/env python3
"""
测试单例模式修复效果
验证保护区管理器不再重复初始化
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_singleton_protection_zone_manager():
    """测试保护区管理器单例模式"""
    print("🧪 测试保护区管理器单例模式")
    print("=" * 50)
    
    try:
        # 1. 第一次导入和创建
        print("\n1️⃣ 第一次创建保护区管理器...")
        from protection_zones import ProtectionZoneManager
        manager1 = ProtectionZoneManager()
        print(f"   实例ID: {id(manager1)}")
        print(f"   保护区数量: {len(manager1.zones)}")
        
        # 2. 第二次创建
        print("\n2️⃣ 第二次创建保护区管理器...")
        manager2 = ProtectionZoneManager()
        print(f"   实例ID: {id(manager2)}")
        print(f"   保护区数量: {len(manager2.zones)}")
        
        # 3. 验证是否为同一实例
        print("\n3️⃣ 验证单例模式...")
        if manager1 is manager2:
            print("✅ 单例模式工作正常：两次创建返回同一实例")
        else:
            print("❌ 单例模式失败：创建了不同的实例")
        
        # 4. 测试算法模块导入
        print("\n4️⃣ 测试算法模块导入...")
        
        # 导入A*算法
        print("   导入A*算法...")
        from algorithms.astar import AStarAlgorithm
        astar = AStarAlgorithm()
        print(f"   A*算法保护区管理器ID: {id(astar.protection_zone_manager)}")
        
        # 导入改进算法
        print("   导入改进分簇算法...")
        from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathPlanning
        improved = ImprovedClusterBasedPathPlanning()
        print(f"   改进算法保护区管理器ID: {id(improved.protection_zone_manager)}")
        
        # 5. 验证所有实例是否相同
        print("\n5️⃣ 验证所有保护区管理器实例...")
        all_same = (
            manager1 is manager2 and
            manager1 is astar.protection_zone_manager and
            manager1 is improved.protection_zone_manager
        )
        
        if all_same:
            print("✅ 所有保护区管理器实例都是同一个对象")
            print("✅ 单例模式完全生效，避免了重复初始化")
        else:
            print("❌ 存在不同的保护区管理器实例")
            print(f"   manager1: {id(manager1)}")
            print(f"   manager2: {id(manager2)}")
            print(f"   astar.protection_zone_manager: {id(astar.protection_zone_manager)}")
            print(f"   improved.protection_zone_manager: {id(improved.protection_zone_manager)}")
        
        # 6. 测试保护区数据一致性
        print("\n6️⃣ 测试保护区数据一致性...")
        zone_counts = [
            len(manager1.zones),
            len(manager2.zones),
            len(astar.protection_zone_manager.zones) if astar.protection_zone_manager else 0,
            len(improved.protection_zone_manager.zones) if improved.protection_zone_manager else 0
        ]
        
        if all(count == zone_counts[0] for count in zone_counts):
            print(f"✅ 所有实例的保护区数量一致: {zone_counts[0]}个")
        else:
            print(f"❌ 保护区数量不一致: {zone_counts}")
        
        print("\n🎉 单例模式测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

def test_app_startup_simulation():
    """模拟app.py启动过程"""
    print("\n" + "=" * 50)
    print("🚀 模拟app.py启动过程")
    print("=" * 50)
    
    try:
        # 模拟算法注册过程
        print("\n📋 模拟算法注册过程...")
        
        # 1. 导入算法模块（这会触发register_default_algorithms）
        print("1. 导入algorithms模块...")
        import algorithms
        
        # 2. 导入API模块
        print("2. 导入保护区API模块...")
        from api.protection_zones import protection_zone_manager as api_manager
        
        # 3. 检查实例一致性
        print("3. 检查实例一致性...")
        from protection_zones import ProtectionZoneManager
        direct_manager = ProtectionZoneManager()
        
        if direct_manager is api_manager:
            print("✅ API模块和直接创建的管理器是同一实例")
        else:
            print("❌ API模块和直接创建的管理器不是同一实例")
        
        print("\n🎯 启动模拟完成！")
        
    except Exception as e:
        print(f"❌ 启动模拟失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

def main():
    """主函数"""
    print("🔧 保护区管理器单例模式修复验证")
    print("解决问题：app.py启动时保护区初始化被执行3次")
    
    # 测试单例模式
    test_singleton_protection_zone_manager()
    
    # 模拟app启动
    test_app_startup_simulation()
    
    print("\n" + "=" * 50)
    print("📊 修复效果总结:")
    print("✅ 使用单例模式避免重复初始化")
    print("✅ 保护区数据只初始化一次")
    print("✅ 所有算法共享同一个保护区管理器实例")
    print("✅ 减少启动时间和内存占用")
    print("=" * 50)

if __name__ == "__main__":
    main()
