#!/usr/bin/env python3
"""
函数标准化工具
自动更新函数以符合变量标准规范
"""

import re
import ast
import inspect
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from function_variable_standards import FunctionVariableStandards, FunctionVariableMapper
from algorithm_input_parameters import ALGORITHM_INPUT_PARAMETER_NAMES


class FunctionStandardizer:
    """函数标准化工具类"""
    
    def __init__(self):
        self.standards = FunctionVariableStandards()
        self.mapper = FunctionVariableMapper()
        self.standardization_log = []
    
    def standardize_function_signature(self, function_name: str, current_signature: str) -> str:
        """标准化函数签名"""
        standard = self.standards.get_function_standard(function_name)
        if not standard:
            self.log_warning(f"未找到函数 {function_name} 的标准定义")
            return current_signature
        
        # 生成标准化的参数列表
        required_params = []
        optional_params = []
        
        for param in standard.required_inputs:
            function_var = standard.input_parameters.get(param, param)
            required_params.append(function_var)
        
        for param in standard.optional_inputs:
            function_var = standard.input_parameters.get(param, param)
            optional_params.append(f"{function_var}=None")
        
        all_params = required_params + optional_params
        
        # 保留self参数（如果是方法）
        if "self" in current_signature:
            all_params.insert(0, "self")
        
        # 保留async关键字
        async_prefix = "async " if "async" in current_signature else ""
        
        standardized_signature = f"{async_prefix}def {function_name}({', '.join(all_params)})"
        
        self.log_info(f"标准化函数签名: {function_name}")
        return standardized_signature
    
    def generate_parameter_mapping_code(self, function_name: str) -> str:
        """生成参数映射代码"""
        standard = self.standards.get_function_standard(function_name)
        if not standard:
            return ""
        
        mapping_code = []
        mapping_code.append("    # 参数标准化映射")
        mapping_code.append("    from algorithm_input_parameters import ALGORITHM_INPUT_PARAMETER_NAMES")
        mapping_code.append("")
        
        # 生成输入参数映射
        for standard_param, function_var in standard.input_parameters.items():
            if standard_param in ALGORITHM_INPUT_PARAMETER_NAMES.values():
                # 找到对应的常量名
                constant_name = None
                for const_name, const_value in ALGORITHM_INPUT_PARAMETER_NAMES.items():
                    if const_value == standard_param:
                        constant_name = const_name
                        break
                
                if constant_name:
                    mapping_code.append(f"    # {standard_param} -> {function_var}")
                    mapping_code.append(f"    if '{standard_param}' in locals():")
                    mapping_code.append(f"        {function_var} = {standard_param}")
                    mapping_code.append("")
        
        return "\n".join(mapping_code)
    
    def generate_return_mapping_code(self, function_name: str) -> str:
        """生成返回值映射代码"""
        standard = self.standards.get_function_standard(function_name)
        if not standard:
            return "    return result"
        
        output_vars = list(standard.output_variables.values())
        if len(output_vars) == 1:
            return f"    return {output_vars[0]}"
        elif len(output_vars) > 1:
            return f"    return {{{', '.join([f\"'{k}': {v}\" for k, v in standard.output_variables.items()])}}}"
        else:
            return "    return result"
    
    def create_standardized_wrapper(self, function_name: str, original_function_code: str) -> str:
        """创建标准化的函数包装器"""
        standard = self.standards.get_function_standard(function_name)
        if not standard:
            return original_function_code
        
        # 生成标准化的函数签名
        required_params = [standard.input_parameters.get(param, param) 
                          for param in standard.required_inputs]
        optional_params = [f"{standard.input_parameters.get(param, param)}=None" 
                          for param in standard.optional_inputs]
        
        all_params = required_params + optional_params
        
        # 检查是否是异步函数
        is_async = "async def" in original_function_code
        async_prefix = "async " if is_async else ""
        await_prefix = "await " if is_async else ""
        
        wrapper_code = f'''
{async_prefix}def {function_name}_standardized({", ".join(all_params)}):
    """
    {standard.description}
    标准化的函数包装器，确保变量名符合规范
    
    Args:
{chr(10).join(f"        {param}: {param} 参数" for param in required_params)}
{chr(10).join(f"        {param}: {param} 参数 (可选)" for param in [p.split('=')[0] for p in optional_params])}
    
    Returns:
        标准化的返回结果
    """
    # 参数验证
    from algorithm_input_parameters import AlgorithmInputParameters
    
    # 构建标准参数字典
    standard_params = {{}}
    {chr(10).join(f"    if {param} is not None:" + chr(10) + f"        standard_params['{list(standard.input_parameters.keys())[i]}'] = {param}" 
                   for i, param in enumerate(required_params + [p.split('=')[0] for p in optional_params]))}
    
    # 验证参数
    for param_name, param_value in standard_params.items():
        if param_value is not None:
            valid, message = AlgorithmInputParameters.validate_parameter(param_name, param_value)
            if not valid:
                raise ValueError(f"参数验证失败 {{param_name}}: {{message}}")
    
    # 调用原始函数
    result = {await_prefix}{function_name}_original(**standard_params)
    
    # 标准化返回值
    if isinstance(result, dict):
        return result
    else:
        return {{"result": result}}

# 原始函数重命名
{function_name}_original = {function_name}

# 使用标准化版本替换原函数
{function_name} = {function_name}_standardized
'''
        
        return wrapper_code
    
    def analyze_function_compliance(self, file_path: str) -> Dict[str, Any]:
        """分析文件中函数的标准合规性"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {"error": f"读取文件失败: {e}"}
        
        compliance_report = {
            "file_path": file_path,
            "total_functions": 0,
            "standardized_functions": 0,
            "non_compliant_functions": [],
            "missing_standards": [],
            "recommendations": []
        }
        
        # 查找所有函数定义
        function_pattern = r'(async\s+)?def\s+(\w+)\s*\([^)]*\):'
        functions = re.findall(function_pattern, content)
        
        compliance_report["total_functions"] = len(functions)
        
        for async_prefix, func_name in functions:
            standard = self.standards.get_function_standard(func_name)
            
            if standard:
                # 检查函数是否符合标准
                function_match = re.search(rf'(async\s+)?def\s+{func_name}\s*\(([^)]*)\):', content)
                if function_match:
                    params_str = function_match.group(2)
                    
                    # 简单的参数解析
                    params = [p.strip().split('=')[0].strip() for p in params_str.split(',') if p.strip()]
                    params = [p for p in params if p not in ['self', 'cls']]
                    
                    expected_params = list(standard.input_parameters.values())
                    
                    if set(params) == set(expected_params):
                        compliance_report["standardized_functions"] += 1
                    else:
                        compliance_report["non_compliant_functions"].append({
                            "function": func_name,
                            "current_params": params,
                            "expected_params": expected_params,
                            "missing": list(set(expected_params) - set(params)),
                            "extra": list(set(params) - set(expected_params))
                        })
            else:
                compliance_report["missing_standards"].append(func_name)
        
        # 生成建议
        if compliance_report["non_compliant_functions"]:
            compliance_report["recommendations"].append("更新不符合标准的函数参数")
        
        if compliance_report["missing_standards"]:
            compliance_report["recommendations"].append("为缺少标准的函数创建标准定义")
        
        compliance_rate = (compliance_report["standardized_functions"] / 
                          compliance_report["total_functions"] * 100 
                          if compliance_report["total_functions"] > 0 else 0)
        
        compliance_report["compliance_rate"] = compliance_rate
        
        return compliance_report
    
    def generate_standardization_plan(self, file_path: str) -> Dict[str, Any]:
        """生成标准化计划"""
        compliance = self.analyze_function_compliance(file_path)
        
        plan = {
            "file_path": file_path,
            "priority_functions": [],
            "optional_functions": [],
            "new_standards_needed": [],
            "estimated_effort": "low"
        }
        
        # 按优先级分类函数
        for func_info in compliance.get("non_compliant_functions", []):
            func_name = func_info["function"]
            standard = self.standards.get_function_standard(func_name)
            
            if standard and standard.category.value == "core_algorithm":
                plan["priority_functions"].append(func_info)
            else:
                plan["optional_functions"].append(func_info)
        
        # 需要新标准的函数
        plan["new_standards_needed"] = compliance.get("missing_standards", [])
        
        # 估算工作量
        total_changes = (len(plan["priority_functions"]) + 
                        len(plan["optional_functions"]) + 
                        len(plan["new_standards_needed"]))
        
        if total_changes > 10:
            plan["estimated_effort"] = "high"
        elif total_changes > 5:
            plan["estimated_effort"] = "medium"
        else:
            plan["estimated_effort"] = "low"
        
        return plan
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.standardization_log.append(f"INFO: {message}")
    
    def log_warning(self, message: str):
        """记录警告日志"""
        self.standardization_log.append(f"WARNING: {message}")
    
    def log_error(self, message: str):
        """记录错误日志"""
        self.standardization_log.append(f"ERROR: {message}")
    
    def get_standardization_log(self) -> List[str]:
        """获取标准化日志"""
        return self.standardization_log.copy()


def analyze_project_compliance(project_root: str = "backend") -> Dict[str, Any]:
    """分析整个项目的标准合规性"""
    standardizer = FunctionStandardizer()
    project_path = Path(project_root)
    
    project_report = {
        "project_root": project_root,
        "analyzed_files": [],
        "total_functions": 0,
        "standardized_functions": 0,
        "overall_compliance_rate": 0.0,
        "priority_files": [],
        "summary": {}
    }
    
    # 分析所有Python文件
    for py_file in project_path.rglob("*.py"):
        if py_file.name.startswith("test_") or py_file.name.startswith("__"):
            continue
        
        file_compliance = standardizer.analyze_function_compliance(str(py_file))
        
        if "error" not in file_compliance:
            project_report["analyzed_files"].append(file_compliance)
            project_report["total_functions"] += file_compliance["total_functions"]
            project_report["standardized_functions"] += file_compliance["standardized_functions"]
            
            # 标记需要优先处理的文件
            if (file_compliance["total_functions"] > 0 and 
                file_compliance["compliance_rate"] < 80):
                project_report["priority_files"].append({
                    "file": str(py_file),
                    "compliance_rate": file_compliance["compliance_rate"],
                    "non_compliant_count": len(file_compliance["non_compliant_functions"])
                })
    
    # 计算总体合规率
    if project_report["total_functions"] > 0:
        project_report["overall_compliance_rate"] = (
            project_report["standardized_functions"] / 
            project_report["total_functions"] * 100
        )
    
    # 生成摘要
    project_report["summary"] = {
        "files_analyzed": len(project_report["analyzed_files"]),
        "functions_analyzed": project_report["total_functions"],
        "compliance_rate": f"{project_report['overall_compliance_rate']:.1f}%",
        "priority_files_count": len(project_report["priority_files"]),
        "recommendation": "优先标准化核心算法文件" if project_report["priority_files"] else "项目标准化程度良好"
    }
    
    return project_report


if __name__ == "__main__":
    # 测试标准化工具
    print("=== 函数标准化工具测试 ===")
    
    standardizer = FunctionStandardizer()
    
    # 分析项目合规性
    print("分析项目标准合规性...")
    project_report = analyze_project_compliance()
    
    print(f"项目摘要:")
    for key, value in project_report["summary"].items():
        print(f"  {key}: {value}")
    
    if project_report["priority_files"]:
        print(f"\n需要优先处理的文件:")
        for file_info in project_report["priority_files"][:5]:  # 显示前5个
            print(f"  {file_info['file']}: {file_info['compliance_rate']:.1f}% 合规")
    
    # 生成标准化计划
    if project_report["priority_files"]:
        priority_file = project_report["priority_files"][0]["file"]
        print(f"\n为 {priority_file} 生成标准化计划...")
        
        plan = standardizer.generate_standardization_plan(priority_file)
        print(f"  优先级函数: {len(plan['priority_functions'])}")
        print(f"  可选函数: {len(plan['optional_functions'])}")
        print(f"  预估工作量: {plan['estimated_effort']}")
    
    print(f"\n标准化日志:")
    for log_entry in standardizer.get_standardization_log():
        print(f"  {log_entry}")
