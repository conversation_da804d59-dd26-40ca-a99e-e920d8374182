"""
保护区API蓝图
提供保护区管理和碰撞代价计算的API接口
"""

from flask import Blueprint, request, jsonify
import math
import sys
import os

# 修复导入问题
try:
    from protection_zones import ProtectionZoneManager
except ImportError:
    # 添加backend目录到Python路径
    backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if backend_dir not in sys.path:
        sys.path.insert(0, backend_dir)

    try:
        from protection_zones import ProtectionZoneManager
    except ImportError:
        # 如果还是失败，创建一个简单的替代类
        class ProtectionZoneManager:
            def __init__(self):
                self.zones = []
                print("⚠️ 保护区管理器导入失败，使用简化版本")

            def calculate_path_collision_cost(self, path_points):
                return 0.0

            def get_zones_for_path(self, path_points, buffer_distance=1000):
                return []

            def to_dict(self):
                return {
                    'zones': [],
                    'statistics': {
                        'total_zones': 0,
                        'by_type': {},
                        'high_risk_zones': [],
                        'coverage_area': 0.0
                    }
                }

# 创建蓝图
protection_zones_bp = Blueprint('protection_zones', __name__)

# 全局保护区管理器实例（使用单例）
protection_zone_manager = ProtectionZoneManager()

@protection_zones_bp.route('/info', methods=['GET'])
def get_protection_zones_info():
    """
    获取保护区信息和统计数据
    """
    try:
        zone_data = protection_zone_manager.to_dict()
        
        return jsonify({
            'success': True,
            'zones': zone_data['zones'],
            'statistics': zone_data['statistics'],
            'message': '保护区信息获取成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取保护区信息失败: {str(e)}'
        }), 500

@protection_zones_bp.route('/collision-cost', methods=['POST'])
def calculate_collision_cost():
    """
    计算指定点的碰撞代价
    """
    try:
        data = request.get_json()
        
        if not data or 'lng' not in data or 'lat' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必需参数: lng, lat'
            }), 400
        
        lng = float(data['lng'])
        lat = float(data['lat'])
        
        # 计算碰撞代价
        total_cost = 0.0
        affected_zones = []
        
        for zone in protection_zone_manager.zones:
            zone_cost = zone.get_collision_cost(lng, lat)
            if zone_cost > 0:
                total_cost += zone_cost
                affected_zones.append({
                    'id': zone.id,
                    'name': zone.name,
                    'type': zone.zone_type.value,
                    'cost': zone_cost,
                    'distance': zone.get_distance_to_point(lng, lat)
                })
        
        return jsonify({
            'success': True,
            'collision_cost': total_cost,
            'affected_zones': len(affected_zones),
            'zone_details': affected_zones,
            'coordinates': {'lng': lng, 'lat': lat}
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'坐标格式错误: {str(e)}'
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'碰撞代价计算失败: {str(e)}'
        }), 500

@protection_zones_bp.route('/path-analysis', methods=['POST'])
def analyze_path():
    """
    分析路径的预估碰撞代价
    """
    try:
        data = request.get_json()
        
        if not data or 'path' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必需参数: path'
            }), 400
        
        path_points = data['path']
        
        if not isinstance(path_points, list) or len(path_points) < 2:
            return jsonify({
                'success': False,
                'error': '路径至少需要2个点'
            }), 400
        
        # 转换为元组格式
        path_tuples = []
        for point in path_points:
            if 'lng' not in point or 'lat' not in point:
                return jsonify({
                    'success': False,
                    'error': '路径点缺少lng或lat坐标'
                }), 400
            path_tuples.append((float(point['lng']), float(point['lat'])))
        
        # 计算路径长度
        path_length = 0.0
        for i in range(1, len(path_tuples)):
            prev_lng, prev_lat = path_tuples[i-1]
            curr_lng, curr_lat = path_tuples[i]
            distance = _calculate_distance(prev_lng, prev_lat, curr_lng, curr_lat)
            path_length += distance
        
        # 计算碰撞代价
        total_collision_cost = protection_zone_manager.calculate_path_collision_cost(path_tuples)
        average_collision_cost = total_collision_cost
        
        # 获取影响的保护区
        affected_zones = protection_zone_manager.get_zones_for_path(path_tuples)
        
        # 详细分析每个路径点
        point_analysis = []
        for i, (lng, lat) in enumerate(path_tuples):
            point_cost = 0.0
            point_zones = []
            
            for zone in affected_zones:
                zone_cost = zone.get_collision_cost(lng, lat)
                if zone_cost > 0:
                    point_cost += zone_cost
                    point_zones.append({
                        'name': zone.name,
                        'cost': zone_cost
                    })
            
            point_analysis.append({
                'index': i,
                'coordinates': {'lng': lng, 'lat': lat},
                'collision_cost': point_cost,
                'affected_zones': point_zones
            })
        
        return jsonify({
            'success': True,
            'total_collision_cost': total_collision_cost,
            'average_collision_cost': average_collision_cost,
            'path_length': path_length,
            'path_points': len(path_tuples),
            'affected_zones': [
                {
                    'id': zone.id,
                    'name': zone.name,
                    'type': zone.zone_type.value,
                    'risk_value': zone.risk_value,
                    'collision_cost_factor': zone.collision_cost_factor
                }
                for zone in affected_zones
            ],
            'point_analysis': point_analysis
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'数据格式错误: {str(e)}'
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'路径分析失败: {str(e)}'
        }), 500

@protection_zones_bp.route('/zones', methods=['GET'])
def list_zones():
    """
    获取所有保护区列表
    """
    try:
        zones = []
        for zone in protection_zone_manager.zones:
            zones.append({
                'id': zone.id,
                'name': zone.name,
                'type': zone.zone_type.value,
                'center': zone.center,
                'radius': zone.radius,
                'average_crash_cost': zone.average_crash_cost,
                'collision_cost_factor': zone.collision_cost_factor,
                'description': zone.description
            })
        
        return jsonify({
            'success': True,
            'zones': zones,
            'total_count': len(zones)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取保护区列表失败: {str(e)}'
        }), 500

@protection_zones_bp.route('/zones/<zone_id>', methods=['GET'])
def get_zone_details(zone_id):
    """
    获取特定保护区的详细信息
    """
    try:
        zone = None
        for z in protection_zone_manager.zones:
            if z.id == zone_id:
                zone = z
                break
        
        if not zone:
            return jsonify({
                'success': False,
                'error': f'保护区 {zone_id} 不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'zone': {
                'id': zone.id,
                'name': zone.name,
                'type': zone.zone_type.value,
                'center': zone.center,
                'radius': zone.radius,
                'average_crash_cost': zone.average_crash_cost,
                'collision_cost_factor': zone.collision_cost_factor,
                'description': zone.description
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取保护区详情失败: {str(e)}'
        }), 500

def _calculate_distance(lng1, lat1, lng2, lat2):
    """计算两点间距离（米）"""
    R = 6371000  # 地球半径（米）
    dLat = math.radians(lat2 - lat1)
    dLng = math.radians(lng2 - lng1)
    a = (math.sin(dLat/2) * math.sin(dLat/2) +
         math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) *
         math.sin(dLng/2) * math.sin(dLng/2))
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    return R * c
