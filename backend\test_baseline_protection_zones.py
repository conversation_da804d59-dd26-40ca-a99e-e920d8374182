#!/usr/bin/env python3
"""
测试基准算法保护区计算修复
验证基准算法是否能正确使用前端保护区系统
"""

def test_baseline_protection_zones():
    """测试基准算法保护区计算"""
    
    print("🔍 测试基准算法保护区计算修复")
    print("=" * 60)
    
    try:
        # 1. 测试前端保护区系统
        print("📋 1. 测试前端保护区系统")
        
        from protection_zones import ProtectionZoneManager
        protection_manager = ProtectionZoneManager()
        
        print(f"   前端保护区总数: {len(protection_manager.zones)}")
        
        # 2. 模拟基准算法的路径点
        print(f"\n🛤️ 2. 模拟基准算法的路径点")
        
        from algorithms.data_structures import Point3D
        
        # 创建模拟的基准算法路径点（经过东京站和涩谷）
        mock_waypoints = [
            Point3D(lng=139.7673, lat=35.6812, alt=120, x=0, y=0, z=120),  # 东京站
            Point3D(lng=139.7016, lat=35.6598, alt=120, x=1000, y=1000, z=120),  # 涩谷
        ]
        
        print(f"   模拟路径点数: {len(mock_waypoints)}")
        for i, wp in enumerate(mock_waypoints):
            print(f"     点{i+1}: ({wp.lng:.4f}, {wp.lat:.4f})")
        
        # 3. 测试基准算法的保护区检测
        print(f"\n🔍 3. 测试基准算法的保护区检测")
        
        # 获取路径相关的保护区
        path_points = [(wp.lng, wp.lat) for wp in mock_waypoints]
        relevant_zones = protection_manager.get_zones_for_path(path_points, buffer_distance=500)
        
        print(f"   检测到相关保护区: {len(relevant_zones)} 个")
        for zone in relevant_zones:
            print(f"     - {zone.name} (ID: {zone.id}, 类型: {zone.zone_type.value}, 半径: {zone.radius}m)")
        
        # 4. 测试碰撞代价计算
        print(f"\n💰 4. 测试碰撞代价计算")
        
        total_collision_cost = 0.0
        collision_cost_breakdown = {}
        
        for waypoint in mock_waypoints:
            waypoint_cost = 0.0
            print(f"   航点 ({waypoint.lng:.4f}, {waypoint.lat:.4f}):")
            
            for zone in relevant_zones:
                # 使用前端保护区的碰撞代价计算方法
                zone_cost = zone.get_collision_cost(waypoint.lng, waypoint.lat)
                waypoint_cost += zone_cost
                
                if zone_cost > 0:
                    print(f"     - {zone.name}: {zone_cost:.4f}")
                    
                    # 累计到总体统计
                    if zone.id not in collision_cost_breakdown:
                        collision_cost_breakdown[zone.id] = {
                            'zone_name': zone.name,
                            'zone_type': zone.zone_type.value,
                            'total_cost': 0.0,
                            'average_crash_cost': zone.average_crash_cost
                        }
                    collision_cost_breakdown[zone.id]['total_cost'] += zone_cost
            
            total_collision_cost += waypoint_cost
            print(f"     航点总代价: {waypoint_cost:.4f}")
        
        print(f"   总碰撞代价: {total_collision_cost:.4f}")
        print(f"   活跃保护区数: {len(collision_cost_breakdown)}")
        
        # 5. 测试基准算法API的保护区计算方法
        print(f"\n🤖 5. 测试基准算法API的保护区计算方法")
        
        from algorithm_comparison_api import AlgorithmComparisonAPI
        api = AlgorithmComparisonAPI()
        
        # 模拟基准算法的碰撞代价计算
        try:
            calculated_cost = api._estimate_collision_cost(mock_waypoints)
            print(f"   基准算法API计算的碰撞代价: {calculated_cost:.4f}")
            
            # 检查是否有保护区信息
            if hasattr(api, 'baseline_protection_zones_info') and api.baseline_protection_zones_info:
                zones_info = api.baseline_protection_zones_info
                print(f"   API保存的保护区信息:")
                print(f"     - 总保护区数: {zones_info['total_zones']}")
                print(f"     - 活跃保护区数: {zones_info['active_zones']}")
                print(f"     - 活跃保护区ID: {list(zones_info['collision_cost_breakdown'].keys())}")
                
                # 对比手动计算和API计算的结果
                api_active_zones = len(zones_info['collision_cost_breakdown'])
                manual_active_zones = len(collision_cost_breakdown)
                
                print(f"   结果对比:")
                print(f"     - 手动计算活跃保护区: {manual_active_zones}")
                print(f"     - API计算活跃保护区: {api_active_zones}")
                print(f"     - 手动计算总代价: {total_collision_cost:.4f}")
                print(f"     - API计算总代价: {calculated_cost:.4f}")
                
                if abs(total_collision_cost - calculated_cost) < 0.01 and manual_active_zones == api_active_zones:
                    print(f"   ✅ 计算结果一致")
                else:
                    print(f"   ⚠️ 计算结果不一致")
            else:
                print(f"   ⚠️ API未保存保护区信息")
                
        except Exception as e:
            print(f"   ❌ 基准算法API计算失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. 测试前端ID格式
        print(f"\n🎯 6. 测试前端ID格式")
        
        active_zone_ids = list(collision_cost_breakdown.keys())
        print(f"   活跃保护区ID: {active_zone_ids}")
        
        # 模拟前端接收处理
        def simulate_frontend_processing(zone_ids):
            print(f"   前端接收到的ID: {zone_ids}")
            
            # 标准化处理（移除可能的前缀）
            normalized_ids = [id.replace('frontend_', '') for id in zone_ids]
            print(f"   标准化后的ID: {normalized_ids}")
            
            # 检查是否与前端保护区ID匹配
            frontend_zone_ids = [zone.id for zone in protection_manager.zones]
            matched_zones = [id for id in normalized_ids if id in frontend_zone_ids]
            
            print(f"   匹配的保护区: {matched_zones}")
            return matched_zones
        
        matched_zones = simulate_frontend_processing(active_zone_ids)
        
        if len(matched_zones) == len(active_zone_ids):
            print(f"   ✅ 所有活跃保护区都能在前端正确匹配")
        else:
            print(f"   ⚠️ 部分保护区无法匹配")
        
        # 7. 生成测试报告
        print(f"\n📊 7. 测试报告")
        
        test_results = {
            'relevant_zones_count': len(relevant_zones),
            'active_zones_count': len(collision_cost_breakdown),
            'total_collision_cost': total_collision_cost,
            'api_calculation_success': hasattr(api, 'baseline_protection_zones_info'),
            'frontend_matching_success': len(matched_zones) == len(active_zone_ids),
            'active_zone_names': [collision_cost_breakdown[zone_id]['zone_name'] for zone_id in active_zone_ids]
        }
        
        print(f"   检测到相关保护区: {test_results['relevant_zones_count']} 个")
        print(f"   参与运算的保护区: {test_results['active_zones_count']} 个")
        print(f"   总碰撞代价: {test_results['total_collision_cost']:.4f}")
        print(f"   API计算成功: {'✅' if test_results['api_calculation_success'] else '❌'}")
        print(f"   前端匹配成功: {'✅' if test_results['frontend_matching_success'] else '❌'}")
        print(f"   参与运算的保护区: {', '.join(test_results['active_zone_names'])}")
        
        # 8. 验证修复效果
        print(f"\n✅ 8. 修复效果验证")
        
        if test_results['active_zones_count'] > 0:
            print(f"   ✅ 基准算法成功检测到保护区")
            print(f"   ✅ 碰撞代价计算正常工作")
            print(f"   ✅ 保护区信息可以传递到前端")
            
            if test_results['api_calculation_success']:
                print(f"   ✅ API保护区计算方法工作正常")
            else:
                print(f"   ⚠️ API保护区计算方法需要进一步调试")
                
            if test_results['frontend_matching_success']:
                print(f"   ✅ 前端ID匹配工作正常")
            else:
                print(f"   ⚠️ 前端ID匹配需要进一步调试")
        else:
            print(f"   ❌ 基准算法未检测到任何保护区")
            print(f"   ❌ 需要检查路径点或保护区配置")
        
        return test_results
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_baseline_protection_zones()
    if result:
        print(f"\n🎉 基准算法保护区计算测试完成")
        if result['active_zones_count'] > 0 and result['api_calculation_success']:
            print(f"✅ 修复成功！基准算法现在能正确使用前端保护区系统")
            print(f"✅ 保护区参与运算状态应该能在前端正确显示")
        else:
            print(f"⚠️ 部分功能仍需调试")
    else:
        print(f"\n❌ 基准算法保护区计算测试失败")
