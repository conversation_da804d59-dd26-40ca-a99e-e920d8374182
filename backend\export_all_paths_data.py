#!/usr/bin/env python3
"""
导出所有81条路径的详细数据
从已完成的算法对比结果中获取数据，而不是重新计算
"""

import json
import math
from datetime import datetime
from typing import Dict, List, Any, Optional

def get_latest_calculation_data() -> Optional[Dict[str, Any]]:
    """
    从算法对比API获取最新的计算数据
    包括改进算法的结果和81条路径数据
    """
    try:
        from algorithm_comparison_api import get_latest_improved_result, get_latest_baseline_result
        improved_result = get_latest_improved_result()
        baseline_result = get_latest_baseline_result()

        # 🔧 添加详细调试信息
        print(f"🔍 调试信息:")
        print(f"   improved_result 类型: {type(improved_result)}")
        print(f"   improved_result 是否为空: {improved_result is None}")
        if improved_result:
            print(f"   improved_result 键: {list(improved_result.keys()) if isinstance(improved_result, dict) else 'Not a dict'}")

        print(f"   baseline_result 类型: {type(baseline_result)}")
        print(f"   baseline_result 是否为空: {baseline_result is None}")
        if baseline_result:
            print(f"   baseline_result 键: {list(baseline_result.keys()) if isinstance(baseline_result, dict) else 'Not a dict'}")
            print(f"   baseline_result 算法: {baseline_result.get('algorithm', 'N/A')}")

        if not improved_result:
            print("❌ 没有找到最新的改进算法结果")
            return None

        if not improved_result.get('initial_path_set'):
            print("❌ 改进算法结果中没有81条路径数据")
            return None

        print(f"✅ 获取到改进算法结果，包含 {len(improved_result['initial_path_set'])} 条路径")

        if baseline_result:
            print(f"✅ 获取到基准算法结果: {baseline_result.get('algorithm', 'Unknown')}")
        else:
            print("⚠️ 没有找到基准算法结果")
            print("   这可能是因为：")
            print("   1. 没有运行算法对比")
            print("   2. 基准算法执行失败")
            print("   3. 数据没有正确保存")

        return {
            'improved_result': improved_result,
            'baseline_result': baseline_result
        }

    except ImportError:
        print("❌ 无法导入算法对比API")
        return None
    except Exception as e:
        print(f"❌ 获取计算数据失败: {e}")
        return None

def extract_request_info_from_result(improved_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    从改进算法结果中提取请求信息
    """
    # 尝试从路径数据中推断起点终点
    path_data = improved_result.get('path', [])

    if path_data and len(path_data) >= 2:
        start_point = path_data[0]
        end_point = path_data[-1]

        return {
            'start_point': {
                'lng': start_point.get('lng', 0),
                'lat': start_point.get('lat', 0),
                'alt': start_point.get('alt', 50)
            },
            'end_point': {
                'lng': end_point.get('lng', 0),
                'lat': end_point.get('lat', 0),
                'alt': end_point.get('alt', 50)
            },
            'algorithm': improved_result.get('algorithm', '改进分簇算法'),
            'timestamp': improved_result.get('timestamp', datetime.now().timestamp())
        }
    else:
        # 如果无法从路径数据推断，返回默认值
        return {
            'start_point': {'lng': 0, 'lat': 0, 'alt': 50},
            'end_point': {'lng': 0, 'lat': 0, 'alt': 50},
            'algorithm': improved_result.get('algorithm', '改进分簇算法'),
            'timestamp': improved_result.get('timestamp', datetime.now().timestamp())
        }

def calculate_detailed_metrics(path_data: Dict[str, Any], baseline_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    计算详细的路径指标，包括四个指标、参考值、权重等
    """
    # 基础指标
    path_length = path_data.get('path_length', 0.0)
    turning_cost = path_data.get('turning_cost', 0.0)
    risk_value = path_data.get('risk_value', 0.0)
    collision_cost = path_data.get('collision_cost', 0.0)
    final_cost = path_data.get('final_cost', 0.0)

    # 🔧 新增：簇ID信息
    cluster_3x3_ids = path_data.get('cluster_3x3_ids', '')
    cluster_4x4_ids = path_data.get('cluster_4x4_ids', '')
    cluster_id = path_data.get('cluster_id', '')
    cluster_type = path_data.get('cluster_type', '')

    # 🔧 新增：保护区信息
    protection_zones_info = extract_protection_zones_info(path_data)

    # 固定权重（按照算法设计）
    alpha = 0.5   # 风险权重
    beta = 0.4    # 碰撞权重
    gamma = 0.05  # 长度权重
    delta = 0.05  # 转向权重

    # 参考值计算
    # 风险参考值：通常是所有路径风险的平均值，这里使用默认值
    risk_reference = 100.0
    # 碰撞代价参考值：基于保护区平均碰撞代价
    collision_reference = 50.0
    # 转向成本参考值：基于航点数量计算
    waypoint_count = path_data.get('waypoint_count', 25)
    turning_reference = 0.3 * max(waypoint_count - 2, 0) * math.radians(45.0)  # 45度转弧度
    # 长度参考值：曼哈顿距离
    length_reference = path_length * 1.5  # 简化估算

    # 归一化值计算
    risk_normalized = risk_value / max(risk_reference, 1.0)
    collision_normalized = collision_cost / max(collision_reference, 1.0)
    length_normalized = path_length / max(length_reference, 1.0)
    turning_normalized = turning_cost / max(turning_reference, 1.0)

    # 加权项计算
    risk_term = alpha * risk_normalized
    collision_term = beta * collision_normalized
    length_term = gamma * length_normalized
    orient_term = delta * turning_normalized

    # 计算贡献百分比
    total_weighted = risk_term + collision_term + length_term + orient_term
    if total_weighted > 0:
        risk_percent = (risk_term / total_weighted) * 100
        collision_percent = (collision_term / total_weighted) * 100
        length_percent = (length_term / total_weighted) * 100
        turning_percent = (orient_term / total_weighted) * 100
    else:
        risk_percent = collision_percent = length_percent = turning_percent = 0.0

    # 实际碰撞代价（如果有的话）
    actual_collision_cost = path_data.get('actual_collision_cost', collision_cost)

    return {
        # 原始指标
        'path_length': path_length,
        'turning_cost': turning_cost,
        'risk_value': risk_value,
        'collision_cost': collision_cost,
        'final_cost': final_cost,
        'actual_collision_cost': actual_collision_cost,

        # 权重
        'weight_alpha': alpha,
        'weight_beta': beta,
        'weight_gamma': gamma,
        'weight_delta': delta,

        # 参考值
        'risk_reference': risk_reference,
        'collision_reference': collision_reference,
        'turning_reference': turning_reference,
        'length_reference': length_reference,

        # 归一化值
        'risk_normalized': risk_normalized,
        'collision_normalized': collision_normalized,
        'length_normalized': length_normalized,
        'turning_normalized': turning_normalized,

        # 加权项
        'risk_term': risk_term,
        'collision_term': collision_term,
        'length_term': length_term,
        'orient_term': orient_term,

        # 贡献百分比
        'risk_percent': risk_percent,
        'collision_percent': collision_percent,
        'length_percent': length_percent,
        'turning_percent': turning_percent,

        # 🔧 新增：簇ID信息
        'cluster_3x3_ids': cluster_3x3_ids,
        'cluster_4x4_ids': cluster_4x4_ids,
        'cluster_id': cluster_id,
        'cluster_type': cluster_type,

        # 🔧 新增：保护区信息
        **protection_zones_info,

        # 基准对比（如果有基准数据）
        'baseline_comparison': calculate_baseline_comparison(path_data, baseline_data) if baseline_data else None
    }

def extract_protection_zones_info(path_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    提取路径经过的保护区信息
    """
    # 🔧 修复：从多个可能的字段中提取保护区信息
    protection_zones = path_data.get('protection_zones', [])

    # 🔧 新增：检查protectionZonesInfo字段（前端格式）
    protection_zones_info = path_data.get('protectionZonesInfo', {})
    if protection_zones_info and not protection_zones:
        # 从前端格式的保护区信息中提取
        collision_breakdown = protection_zones_info.get('collision_cost_breakdown', {})
        if collision_breakdown:
            # 转换为标准格式
            protection_zones = []
            for zone_id, zone_data in collision_breakdown.items():
                protection_zones.append({
                    'id': zone_id,
                    'name': zone_data.get('zone_name', zone_id),
                    'type': zone_data.get('zone_type', 'unknown'),
                    'cost': zone_data.get('total_cost', 0.0),
                    'average_crash_cost': zone_data.get('average_crash_cost', 0.0),
                    'waypoints_affected': zone_data.get('waypoints_affected', [])
                })

    # 🔧 修复：检查raw_data.metadata中的保护区信息（正确的位置）
    raw_data = path_data.get('raw_data', {})
    if raw_data and not protection_zones:
        raw_metadata = raw_data.get('metadata', {})
        if raw_metadata:
            metadata_zones = raw_metadata.get('protection_zones', {})
            if metadata_zones:
                collision_breakdown = metadata_zones.get('collision_cost_breakdown', {})
                if collision_breakdown:
                    protection_zones = []
                    for zone_id, zone_data in collision_breakdown.items():
                        protection_zones.append({
                            'id': zone_id,
                            'name': zone_data.get('zone_name', zone_id),
                            'type': zone_data.get('zone_type', 'unknown'),
                            'cost': zone_data.get('total_cost', 0.0),
                            'average_crash_cost': zone_data.get('average_crash_cost', 0.0),
                            'waypoints_affected': zone_data.get('waypoints_affected', [])
                        })

    # 🔧 备用：检查直接metadata中的保护区信息（兼容性）
    metadata = path_data.get('metadata', {})
    if metadata and not protection_zones:
        metadata_zones = metadata.get('protection_zones', {})
        if metadata_zones:
            collision_breakdown = metadata_zones.get('collision_cost_breakdown', {})
            if collision_breakdown:
                protection_zones = []
                for zone_id, zone_data in collision_breakdown.items():
                    protection_zones.append({
                        'id': zone_id,
                        'name': zone_data.get('zone_name', zone_id),
                        'type': zone_data.get('zone_type', 'unknown'),
                        'cost': zone_data.get('total_cost', 0.0),
                        'average_crash_cost': zone_data.get('average_crash_cost', 0.0),
                        'waypoints_affected': zone_data.get('waypoints_affected', [])
                    })

    if not protection_zones:
        return {
            'protection_zones_count': 0,
            'protection_zones_list': '',
            'protection_zones_types': '',
            'protection_zones_costs': '',
            'total_protection_zone_cost': 0.0,
            'protection_zone_details': ''
        }

    # 提取保护区详细信息
    zone_names = []
    zone_types = []
    zone_costs = []
    zone_details = []
    total_cost = 0.0

    for zone in protection_zones:
        if isinstance(zone, dict):
            zone_name = zone.get('name', zone.get('id', 'Unknown'))
            zone_type = zone.get('type', zone.get('zone_type', 'Unknown'))  # 🔧 修复：优先使用type字段
            zone_cost = zone.get('average_crash_cost', 0.0)

            zone_names.append(zone_name)
            zone_types.append(zone_type)
            zone_costs.append(str(zone_cost))
            zone_details.append(f"{zone_name}({zone_type}:{zone_cost})")
            total_cost += zone_cost

    return {
        'protection_zones_count': len(protection_zones),
        'protection_zones_list': '; '.join(zone_names),
        'protection_zones_types': '; '.join(zone_types),
        'protection_zones_costs': '; '.join(zone_costs),
        'total_protection_zone_cost': total_cost,
        'protection_zone_details': '; '.join(zone_details)
    }

def calculate_baseline_comparison(path_data: Dict[str, Any], baseline_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    计算与基准算法的对比数据
    """
    baseline_length = baseline_data.get('path_length', 0.0)
    baseline_turning = baseline_data.get('turning_cost', 0.0)
    baseline_risk = baseline_data.get('risk_value', 0.0)
    baseline_collision = baseline_data.get('collision_cost', 0.0)
    baseline_final = baseline_data.get('final_cost', 0.0)

    path_length = path_data.get('path_length', 0.0)
    path_turning = path_data.get('turning_cost', 0.0)
    path_risk = path_data.get('risk_value', 0.0)
    path_collision = path_data.get('collision_cost', 0.0)
    path_final = path_data.get('final_cost', 0.0)

    # 计算改进百分比（负值表示改进）
    def calc_improvement(improved_val, baseline_val):
        if baseline_val == 0:
            return 0.0
        return ((improved_val - baseline_val) / baseline_val) * 100

    return {
        'baseline_path_length': baseline_length,
        'baseline_turning_cost': baseline_turning,
        'baseline_risk_value': baseline_risk,
        'baseline_collision_cost': baseline_collision,
        'baseline_final_cost': baseline_final,
        'length_improvement_percent': calc_improvement(path_length, baseline_length),
        'turning_improvement_percent': calc_improvement(path_turning, baseline_turning),
        'risk_improvement_percent': calc_improvement(path_risk, baseline_risk),
        'collision_improvement_percent': calc_improvement(path_collision, baseline_collision),
        'final_cost_improvement_percent': calc_improvement(path_final, baseline_final)
    }

def export_all_paths_data():
    """
    导出所有81条路径的详细数据
    从已完成的算法对比结果中获取数据，包括基准路线对比
    """

    print("🚀 开始导出所有81条路径数据...")

    try:
        # 从算法对比API获取最新的计算结果
        calculation_data = get_latest_calculation_data()

        if not calculation_data:
            print("❌ 没有找到算法计算结果，请先运行算法对比")
            return None

        improved_result = calculation_data['improved_result']
        baseline_result = calculation_data['baseline_result']

        # 提取请求信息
        request_info = extract_request_info_from_result(improved_result)

        print(f"📍 起点: ({request_info['start_point']['lng']:.6f}, {request_info['start_point']['lat']:.6f})")
        print(f"📍 终点: ({request_info['end_point']['lng']:.6f}, {request_info['end_point']['lat']:.6f})")
        print(f"� 算法: {request_info['algorithm']}")

        # 获取81条路径数据
        paths_data = improved_result['initial_path_set']
        print(f"📊 找到 {len(paths_data)} 条路径")

        # 获取基准算法数据
        if baseline_result:
            print(f"📊 找到基准算法数据: {baseline_result.get('algorithm', 'A*基准算法')}")
        else:
            print("⚠️ 没有基准算法数据，将跳过基准对比")
        
        # 导出所有路径的详细数据
        all_paths_export = []

        # 找到选中的路径ID
        selected_path_id = improved_result.get('selected_path_id')

        for i, path_data in enumerate(paths_data):
            # 基本路径信息 - 从字典数据中获取
            path_info = {
                'path_id': path_data.get('path_id', i),
                'flight_direction': path_data.get('flight_direction', 'unknown'),
                'height_layer': path_data.get('height_layer', 'unknown'),
                'waypoints_count': path_data.get('waypoint_count', 0),
                'is_selected': 1 if path_data.get('path_id') == selected_path_id else 0
            }

            # 🔧 计算详细指标，包括四个指标、参考值、权重等
            detailed_metrics = calculate_detailed_metrics(path_data, baseline_result)

            # 合并所有数据
            path_export = {
                **path_info,
                **detailed_metrics,  # 包含所有详细指标
                'raw_data': path_data  # 保留原始数据供参考
            }

            all_paths_export.append(path_export)

        # 🔧 添加基准路径数据（如果有的话）
        if baseline_result:
            baseline_metrics = calculate_detailed_metrics(baseline_result)
            baseline_path_export = {
                'path_id': 'BASELINE_A*',
                'flight_direction': 'OPTIMAL',
                'height_layer': 'SINGLE',
                'waypoints_count': baseline_result.get('waypoint_count', 0),
                'is_selected': 0,  # 基准路径不是选中路径
                'cluster_3x3_ids': 'N/A',
                'cluster_4x4_ids': 'N/A',
                'cluster_id': 'BASELINE',
                'cluster_type': 'BASELINE',
                **baseline_metrics,
                'raw_data': baseline_result
            }
            # 🔧 将基准路径插入到列表开头
            all_paths_export.insert(0, baseline_path_export)
        
        # 添加选中路径信息 - 从改进算法结果中获取
        selected_path_info = None
        if selected_path_id is not None:
            for i, path_data in enumerate(paths_data):
                if path_data.get('path_id') == selected_path_id:
                    selected_path_info = {
                        'selected_path_index': i + 1,
                        'selected_path_id': selected_path_id,
                        'selection_reason': 'lowest_final_cost_in_best_cluster'
                    }
                    break

        # 🔧 详细统计信息，包括所有指标
        collision_costs = [p['collision_cost'] for p in all_paths_export]
        final_costs = [p['final_cost'] for p in all_paths_export]
        path_lengths = [p['path_length'] for p in all_paths_export]
        turning_costs = [p['turning_cost'] for p in all_paths_export]
        risk_values = [p['risk_value'] for p in all_paths_export]
        actual_collision_costs = [p['actual_collision_cost'] for p in all_paths_export]

        # 权重统计（所有路径应该使用相同的权重）
        weights_info = {
            'alpha': all_paths_export[0]['weight_alpha'] if all_paths_export else 0.5,
            'beta': all_paths_export[0]['weight_beta'] if all_paths_export else 0.4,
            'gamma': all_paths_export[0]['weight_gamma'] if all_paths_export else 0.05,
            'delta': all_paths_export[0]['weight_delta'] if all_paths_export else 0.05
        }

        # 参考值统计
        reference_values = {
            'risk_reference': all_paths_export[0]['risk_reference'] if all_paths_export else 100.0,
            'collision_reference': all_paths_export[0]['collision_reference'] if all_paths_export else 50.0,
            'turning_reference': all_paths_export[0]['turning_reference'] if all_paths_export else 30.0,
            'length_reference': all_paths_export[0]['length_reference'] if all_paths_export else 1500.0
        }

        statistics = {
            'total_paths': len(all_paths_export),
            'weights': weights_info,
            'reference_values': reference_values,
            'collision_cost_stats': {
                'min': min(collision_costs) if collision_costs else 0,
                'max': max(collision_costs) if collision_costs else 0,
                'avg': sum(collision_costs) / len(collision_costs) if collision_costs else 0
            },
            'final_cost_stats': {
                'min': min(final_costs) if final_costs else 0,
                'max': max(final_costs) if final_costs else 0,
                'avg': sum(final_costs) / len(final_costs) if final_costs else 0
            },
            'path_length_stats': {
                'min': min(path_lengths) if path_lengths else 0,
                'max': max(path_lengths) if path_lengths else 0,
                'avg': sum(path_lengths) / len(path_lengths) if path_lengths else 0
            },
            'turning_cost_stats': {
                'min': min(turning_costs) if turning_costs else 0,
                'max': max(turning_costs) if turning_costs else 0,
                'avg': sum(turning_costs) / len(turning_costs) if turning_costs else 0
            },
            'risk_value_stats': {
                'min': min(risk_values) if risk_values else 0,
                'max': max(risk_values) if risk_values else 0,
                'avg': sum(risk_values) / len(risk_values) if risk_values else 0
            },
            'actual_collision_cost_stats': {
                'min': min(actual_collision_costs) if actual_collision_costs else 0,
                'max': max(actual_collision_costs) if actual_collision_costs else 0,
                'avg': sum(actual_collision_costs) / len(actual_collision_costs) if actual_collision_costs else 0
            }
        }
        
        # 完整导出数据
        export_data = {
            'metadata': {
                'export_time': datetime.now().isoformat(),
                'start_point': request_info['start_point'],
                'end_point': request_info['end_point'],
                'algorithm': request_info['algorithm'],
                'calculation_timestamp': request_info['timestamp'],
                'has_baseline_data': baseline_result is not None
            },
            'statistics': statistics,
            'selected_path': selected_path_info,
            'all_paths': all_paths_export,
            'improved_algorithm_result': {
                'path_length': improved_result.get('path_length', 0),
                'turning_cost': improved_result.get('turning_cost', 0),
                'risk_value': improved_result.get('risk_value', 0),
                'collision_cost': improved_result.get('collision_cost', 0),
                'final_cost': improved_result.get('final_cost', 0),
                'execution_time': improved_result.get('execution_time', 0),
                'waypoint_count': improved_result.get('waypoint_count', 0)
            },
            'baseline_algorithm_result': baseline_result if baseline_result else {
                'note': '没有基准算法数据，请先运行算法对比'
            }
        }
        
        # 保存到文件
        filename = f"all_81_paths_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"\n💾 所有路径数据已保存到 {filename}")

        # 输出简化的文本格式供快速查看
        baseline_count = 1 if baseline_result else 0
        improved_count = len(all_paths_export) - baseline_count

        print(f"\n📋 所有{len(all_paths_export)}条路径数据摘要:")
        print(f"   包含: {baseline_count}条基准路径 + {improved_count}条改进算法路径")
        print("=" * 150)
        print(f"{'路径ID':<12} {'方向':<8} {'高度':<6} {'路径长度':<10} {'转向成本':<10} {'风险值':<8} {'碰撞代价':<10} {'最终代价':<10} {'航点数':<6} {'选中':<4}")
        print("-" * 150)

        for path_data in all_paths_export:
            selected_mark = "✓" if path_data['is_selected'] else ""
            path_id_str = str(path_data['path_id'])
            if path_id_str == 'BASELINE_A*':
                path_id_str = "🎯基准A*"

            print(f"{path_id_str:<12} "
                  f"{path_data['flight_direction']:<8} "
                  f"{path_data['height_layer']:<6} "
                  f"{path_data['path_length']:<10.2f} "
                  f"{path_data['turning_cost']:<10.4f} "
                  f"{path_data['risk_value']:<8.4f} "
                  f"{path_data['collision_cost']:<10.4f} "
                  f"{path_data['final_cost']:<10.6f} "
                  f"{path_data['waypoints_count']:<6} "
                  f"{selected_mark:<4}")

        print("-" * 150)
        print(f"📊 统计信息:")
        print(f"  路径长度: {statistics['path_length_stats']['min']:.2f} ~ {statistics['path_length_stats']['max']:.2f} (平均: {statistics['path_length_stats']['avg']:.2f})")
        print(f"  转向成本: {statistics['turning_cost_stats']['min']:.4f} ~ {statistics['turning_cost_stats']['max']:.4f} (平均: {statistics['turning_cost_stats']['avg']:.4f})")
        print(f"  风险值:   {statistics['risk_value_stats']['min']:.4f} ~ {statistics['risk_value_stats']['max']:.4f} (平均: {statistics['risk_value_stats']['avg']:.4f})")
        print(f"  碰撞代价: {statistics['collision_cost_stats']['min']:.4f} ~ {statistics['collision_cost_stats']['max']:.4f} (平均: {statistics['collision_cost_stats']['avg']:.4f})")
        print(f"  实际碰撞: {statistics['actual_collision_cost_stats']['min']:.4f} ~ {statistics['actual_collision_cost_stats']['max']:.4f} (平均: {statistics['actual_collision_cost_stats']['avg']:.4f})")
        print(f"  最终代价: {statistics['final_cost_stats']['min']:.6f} ~ {statistics['final_cost_stats']['max']:.6f} (平均: {statistics['final_cost_stats']['avg']:.6f})")

        print(f"\n🔧 权重信息:")
        print(f"  α(风险): {statistics['weights']['alpha']}, β(碰撞): {statistics['weights']['beta']}")
        print(f"  γ(长度): {statistics['weights']['gamma']}, δ(转向): {statistics['weights']['delta']}")

        print(f"\n📏 参考值:")
        print(f"  风险参考值: {statistics['reference_values']['risk_reference']:.2f}")
        print(f"  碰撞参考值: {statistics['reference_values']['collision_reference']:.2f}")
        print(f"  转向参考值: {statistics['reference_values']['turning_reference']:.4f}")
        print(f"  长度参考值: {statistics['reference_values']['length_reference']:.2f}")

        if baseline_result:
            print(f"\n🎯 基准算法对比:")
            print(f"  基准算法: {baseline_result.get('algorithm', 'A*基准算法')}")
            print(f"  基准路径长度: {baseline_result.get('path_length', 0):.2f}米")
            print(f"  基准最终代价: {baseline_result.get('final_cost', 0):.6f}")

        if selected_path_info:
            print(f"\n✅ 选中路径: ID {selected_path_info['selected_path_id']}")

        print(f"\n✅ 数据导出完成！文件: {filename}")
        print(f"📊 包含数据: {len(all_paths_export)}条路径 = {'1条基准路径 + ' if baseline_result else ''}{improved_count}条改进算法路径")
        return export_data

    except Exception as e:
        print(f"❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_algorithm_data_availability():
    """测试算法数据可用性"""
    print("🧪 测试算法数据可用性...")

    try:
        from algorithm_comparison_api import get_latest_improved_result, get_latest_baseline_result

        improved = get_latest_improved_result()
        baseline = get_latest_baseline_result()

        print(f"📊 改进算法数据: {'✅ 可用' if improved else '❌ 不可用'}")
        if improved:
            print(f"   - 类型: {type(improved)}")
            print(f"   - 包含81条路径: {'✅ 是' if improved.get('initial_path_set') else '❌ 否'}")
            if improved.get('initial_path_set'):
                print(f"   - 路径数量: {len(improved['initial_path_set'])}")

        print(f"📊 基准算法数据: {'✅ 可用' if baseline else '❌ 不可用'}")
        if baseline:
            print(f"   - 类型: {type(baseline)}")
            print(f"   - 算法名称: {baseline.get('algorithm', 'N/A')}")
            print(f"   - 执行成功: {baseline.get('success', 'N/A')}")
            print(f"   - 路径长度: {baseline.get('path_length', 'N/A')}")
            print(f"   - 最终代价: {baseline.get('final_cost', 'N/A')}")
            if 'error' in baseline:
                print(f"   - 错误信息: {baseline.get('error', 'N/A')}")

        return improved is not None, baseline is not None

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, False

def test_baseline_algorithm_directly():
    """直接测试基准算法是否能正常工作"""
    print("\n🧪 直接测试基准算法...")

    try:
        from algorithms.astar import AStarAlgorithm
        from algorithms.data_structures import PathPlanningRequest

        # 创建基准算法实例
        baseline_algorithm = AStarAlgorithm()
        print("✅ 基准算法实例创建成功")

        # 创建简单的测试请求
        test_request_data = {
            'startPoint': {'longitude': 139.767296, 'latitude': 35.678924, 'height': 100},
            'endPoint': {'longitude': 139.759127, 'latitude': 35.689760, 'height': 100},
            'algorithm': 'AStar',
            'parameters': {'gridSize': 50, 'maxIterations': 1000}
        }

        request = PathPlanningRequest(test_request_data)
        print("✅ 测试请求创建成功")

        # 同步测试基准算法
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            print("🔄 开始执行基准算法...")
            response = loop.run_until_complete(baseline_algorithm.calculate_path(request))
            print(f"✅ 基准算法执行完成")
            print(f"   - 成功: {response.success}")
            print(f"   - 路径点数: {len(response.path) if response.path else 0}")
            if hasattr(response, 'error_message'):
                print(f"   - 错误信息: {response.error_message}")

            return response.success

        finally:
            loop.close()

    except Exception as e:
        print(f"❌ 基准算法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 先测试数据可用性
    has_improved, has_baseline = test_algorithm_data_availability()

    if not has_improved:
        print("\n⚠️ 没有改进算法数据，请先运行算法对比")
        exit(1)

    if not has_baseline:
        print("\n⚠️ 没有基准算法数据，测试基准算法是否能正常工作...")
        baseline_works = test_baseline_algorithm_directly()
        if baseline_works:
            print("✅ 基准算法本身工作正常，问题可能在算法对比流程中")
        else:
            print("❌ 基准算法本身存在问题")
        print("\n继续导出改进算法数据...")

    # 执行导出
    result = export_all_paths_data()
    if result:
        print("🎉 导出成功！")
    else:
        print("❌ 导出失败！")
