#!/usr/bin/env python3
"""
快速分析项目中需要标准化的函数
"""

import os
import re
from pathlib import Path

def analyze_functions_in_file(file_path):
    """分析文件中的函数"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        return []
    
    # 查找函数定义
    function_pattern = r'(async\s+)?def\s+(\w+)\s*\([^)]*\):'
    functions = re.findall(function_pattern, content)
    
    return [func_name for _, func_name in functions]

def main():
    """主分析函数"""
    print("=== 项目函数分析 ===")
    
    # 需要重点关注的核心函数
    core_functions = [
        'calculate_path',
        'calculate_final_cost', 
        'generate_initial_path_set',
        'assign_paths_to_clusters',
        'calculate_gradient_field',
        'should_switch_path',
        'smooth_path',
        'simulate_detection_at_waypoint'
    ]
    
    # 分析核心算法文件
    core_files = [
        'algorithms/improved_cluster_pathfinding.py',
        'path_smoother.py',
        'object_detection_simulator.py',
        'algorithm_comparison_api.py'
    ]
    
    found_functions = {}
    
    for file_path in core_files:
        if os.path.exists(file_path):
            functions = analyze_functions_in_file(file_path)
            found_functions[file_path] = functions
            
            print(f"\n📁 {file_path}:")
            print(f"   总函数数: {len(functions)}")
            
            # 检查核心函数
            core_found = [f for f in functions if f in core_functions]
            if core_found:
                print(f"   核心函数: {core_found}")
            
            # 显示前几个函数
            if functions:
                print(f"   函数列表: {functions[:5]}{'...' if len(functions) > 5 else ''}")
        else:
            print(f"\n❌ 文件不存在: {file_path}")
    
    # 统计核心函数覆盖情况
    all_found = []
    for functions in found_functions.values():
        all_found.extend(functions)
    
    print(f"\n📊 核心函数覆盖情况:")
    for core_func in core_functions:
        status = "✅" if core_func in all_found else "❌"
        print(f"   {status} {core_func}")
    
    # 需要标准化的建议
    print(f"\n🔧 标准化建议:")
    print(f"   1. 优先标准化核心算法函数的参数名")
    print(f"   2. 确保所有函数使用标准的输入参数名")
    print(f"   3. 统一返回值格式")
    print(f"   4. 添加参数验证")

if __name__ == "__main__":
    main()
