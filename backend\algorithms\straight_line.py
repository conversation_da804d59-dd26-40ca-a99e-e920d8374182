"""
直线路径算法
最简单的路径规划算法，生成起点到终点的直线路径
"""

import time
import numpy as np
from typing import List

from .base import PathPlanningAlgorithm, AlgorithmInfo, AlgorithmParameter
from .data_structures import PathPlanningRequest, PathPlanningResponse, PathPoint


class StraightLineAlgorithm(PathPlanningAlgorithm):
    """直线路径算法实现"""
    
    def __init__(self):
        super().__init__()
        
        # 设置算法信息
        self.info = AlgorithmInfo(
            name="StraightLine",
            version="1.0.0",
            description="直线路径算法，适用于无障碍环境的快速路径规划",
            author="System",
            category="basic",
            supported_optimizations=["distance", "time"],
            optional_parameters=[
                AlgorithmParameter(
                    name="pointCount",
                    type="number",
                    description="路径点数量",
                    default_value=20,
                    validation=lambda x: isinstance(x, (int, float)) and 2 <= x <= 1000
                ),
                AlgorithmParameter(
                    name="smoothing",
                    type="boolean",
                    description="是否启用路径平滑",
                    default_value=True
                ),
                AlgorithmParameter(
                    name="speedProfile",
                    type="string",
                    description="速度配置文件",
                    default_value="constant",
                    validation=lambda x: x in ["constant", "accelerate", "cruise"]
                )
            ]
        )
    
    async def calculate_path(self, request: PathPlanningRequest) -> PathPlanningResponse:
        """
        计算直线路径
        
        Args:
            request: 路径规划请求
            
        Returns:
            PathPlanningResponse: 路径规划响应
        """
        response = PathPlanningResponse()
        
        try:
            # 获取参数
            point_count = int(request.parameters.get('pointCount', 20))
            smoothing = request.parameters.get('smoothing', True)
            speed_profile = request.parameters.get('speedProfile', 'constant')
            
            # 生成直线路径点
            path_points = self._generate_straight_path(
                request.start_point,
                request.end_point,
                request.flight_height,
                point_count
            )
            
            # 应用平滑处理
            if smoothing:
                path_points = self._smooth_path(path_points)
            
            # 设置速度和航向
            path_points = self._set_speed_and_heading(path_points, speed_profile, request.max_speed)
            
            # 设置时间戳
            path_points = self._set_timestamps(path_points)
            
            # 设置响应数据
            response.success = True
            response.path = path_points
            response.calculate_statistics()
            
            # 质量评估
            response.quality = {
                'risk_score': 10,  # 直线路径风险较低
                'smoothness': 90 if smoothing else 70,
                'efficiency': 95,  # 直线最高效
                'safety_margin': request.safety_distance,
                'complexity': 'low'
            }
            
            # 调试信息
            response.debug_info = {
                'algorithm_version': self.info.version,
                'iteration_count': 1,
                'convergence_time': 0.0,
                'optimization_steps': ['直线路径生成', '平滑处理' if smoothing else '无平滑处理'],
                'warnings': [],
                'suggestions': []
            }
            
            # 检查路径质量并添加建议
            self._add_quality_suggestions(response, request)
            
            return response
            
        except Exception as e:
            response.success = False
            response.error = f"直线算法执行失败: {str(e)}"
            response.debug_info['warnings'].append(str(e))
            return response
    
    def _generate_straight_path(
        self, 
        start_point, 
        end_point, 
        flight_height: float, 
        point_count: int
    ) -> List[PathPoint]:
        """
        生成直线路径点
        
        Args:
            start_point: 起点
            end_point: 终点
            flight_height: 飞行高度
            point_count: 路径点数量
            
        Returns:
            List[PathPoint]: 路径点列表
        """
        path_points = []
        
        # 使用地理坐标进行插值
        start_lng = start_point.lng if start_point.lng != 0 else start_point.x
        start_lat = start_point.lat if start_point.lat != 0 else start_point.y
        end_lng = end_point.lng if end_point.lng != 0 else end_point.x
        end_lat = end_point.lat if end_point.lat != 0 else end_point.y
        
        for i in range(point_count):
            t = i / (point_count - 1) if point_count > 1 else 0
            
            # 线性插值
            lng = start_lng + t * (end_lng - start_lng)
            lat = start_lat + t * (end_lat - start_lat)
            alt = flight_height
            
            # 创建路径点
            path_point = PathPoint(
                lng=lng,
                lat=lat,
                alt=alt,
                timestamp=0,  # 稍后设置
                speed=0.0,    # 稍后设置
                heading=0.0,  # 稍后设置
                action='fly'
            )
            
            path_points.append(path_point)
        
        # 设置起点和终点的动作
        if path_points:
            path_points[0].action = 'takeoff'
            path_points[-1].action = 'land'
        
        return path_points
    
    def _smooth_path(self, path_points: List[PathPoint]) -> List[PathPoint]:
        """
        路径平滑处理
        
        Args:
            path_points: 原始路径点
            
        Returns:
            List[PathPoint]: 平滑后的路径点
        """
        if len(path_points) < 3:
            return path_points
        
        smoothed_points = [path_points[0]]  # 保持起点不变
        
        # 对中间点进行平滑
        for i in range(1, len(path_points) - 1):
            prev_point = path_points[i - 1]
            curr_point = path_points[i]
            next_point = path_points[i + 1]
            
            # 简单的移动平均平滑
            smoothed_lng = (prev_point.lng + curr_point.lng + next_point.lng) / 3
            smoothed_lat = (prev_point.lat + curr_point.lat + next_point.lat) / 3
            smoothed_alt = (prev_point.alt + curr_point.alt + next_point.alt) / 3
            
            smoothed_point = PathPoint(
                lng=smoothed_lng,
                lat=smoothed_lat,
                alt=smoothed_alt,
                timestamp=curr_point.timestamp,
                speed=curr_point.speed,
                heading=curr_point.heading,
                action=curr_point.action
            )
            
            smoothed_points.append(smoothed_point)
        
        smoothed_points.append(path_points[-1])  # 保持终点不变
        
        return smoothed_points
    
    def _set_speed_and_heading(
        self, 
        path_points: List[PathPoint], 
        speed_profile: str, 
        max_speed: float
    ) -> List[PathPoint]:
        """
        设置速度和航向
        
        Args:
            path_points: 路径点列表
            speed_profile: 速度配置文件
            max_speed: 最大速度
            
        Returns:
            List[PathPoint]: 设置速度和航向后的路径点
        """
        if len(path_points) < 2:
            return path_points
        
        for i in range(len(path_points)):
            # 设置速度
            if speed_profile == "constant":
                path_points[i].speed = max_speed
            elif speed_profile == "accelerate":
                # 加速度配置：起点慢，终点快
                t = i / (len(path_points) - 1) if len(path_points) > 1 else 0
                path_points[i].speed = max_speed * 0.3 + max_speed * 0.7 * t
            elif speed_profile == "cruise":
                # 巡航配置：起点慢，中间快，终点慢
                t = i / (len(path_points) - 1) if len(path_points) > 1 else 0
                if t < 0.2:
                    path_points[i].speed = max_speed * 0.3 + max_speed * 0.7 * (t / 0.2)
                elif t > 0.8:
                    path_points[i].speed = max_speed * (1 - (t - 0.8) / 0.2 * 0.7)
                else:
                    path_points[i].speed = max_speed
            
            # 设置航向
            if i < len(path_points) - 1:
                next_point = path_points[i + 1]
                curr_point = path_points[i]
                
                # 计算航向角
                delta_lng = next_point.lng - curr_point.lng
                delta_lat = next_point.lat - curr_point.lat
                
                if delta_lng != 0 or delta_lat != 0:
                    heading = np.degrees(np.arctan2(delta_lng, delta_lat))
                    if heading < 0:
                        heading += 360
                    path_points[i].heading = heading
                else:
                    path_points[i].heading = 0.0
            else:
                # 最后一个点使用前一个点的航向
                if i > 0:
                    path_points[i].heading = path_points[i - 1].heading
                else:
                    path_points[i].heading = 0.0
        
        return path_points
    
    def _set_timestamps(self, path_points: List[PathPoint]) -> List[PathPoint]:
        """
        设置时间戳
        
        Args:
            path_points: 路径点列表
            
        Returns:
            List[PathPoint]: 设置时间戳后的路径点
        """
        if not path_points:
            return path_points
        
        current_time = int(time.time() * 1000)  # 毫秒时间戳
        
        path_points[0].timestamp = current_time
        
        for i in range(1, len(path_points)):
            prev_point = path_points[i - 1]
            curr_point = path_points[i]
            
            # 计算段距离
            distance = self._calculate_distance(prev_point, curr_point)
            
            # 计算飞行时间
            avg_speed = (prev_point.speed + curr_point.speed) / 2
            if avg_speed > 0:
                flight_time = distance / avg_speed  # 秒
            else:
                flight_time = 1.0  # 默认1秒
            
            # 设置时间戳
            curr_point.timestamp = prev_point.timestamp + int(flight_time * 1000)
        
        return path_points
    
    def _calculate_distance(self, point1: PathPoint, point2: PathPoint) -> float:
        """计算两个路径点之间的距离"""
        # 使用Haversine公式计算地理距离
        R = 6371000  # 地球半径(米)
        
        lat1_rad = np.radians(point1.lat)
        lat2_rad = np.radians(point2.lat)
        delta_lat = np.radians(point2.lat - point1.lat)
        delta_lon = np.radians(point2.lng - point1.lng)
        
        a = (np.sin(delta_lat / 2)**2 +
             np.cos(lat1_rad) * np.cos(lat2_rad) *
             np.sin(delta_lon / 2)**2)
        c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))
        
        horizontal_distance = R * c
        vertical_distance = abs(point2.alt - point1.alt)
        
        return np.sqrt(horizontal_distance**2 + vertical_distance**2)
    
    def _add_quality_suggestions(self, response: PathPlanningResponse, request: PathPlanningRequest):
        """添加质量建议"""
        suggestions = []
        
        # 检查路径长度
        if response.path_length > 10000:  # 超过10km
            suggestions.append("路径较长，建议考虑中途降落充电")
        
        # 检查飞行高度
        if request.flight_height < 50:
            suggestions.append("飞行高度较低，注意避开地面障碍物")
        elif request.flight_height > 300:
            suggestions.append("飞行高度较高，注意空域管制要求")
        
        # 检查天气条件
        if hasattr(request, 'weather') and request.weather.wind_speed > 10:
            suggestions.append("风速较大，建议降低飞行速度或调整路径")
        
        response.debug_info['suggestions'] = suggestions
