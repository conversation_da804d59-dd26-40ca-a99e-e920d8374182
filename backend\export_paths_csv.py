#!/usr/bin/env python3
"""
将81条路径数据导出为CSV格式
"""

import json
import csv
import os

def export_paths_to_csv():
    """将路径数据导出为CSV"""
    
    # 读取JSON数据
    json_file = 'all_81_paths_data.json'
    if not os.path.exists(json_file):
        print(f"❌ 找不到文件: {json_file}")
        print("请先运行 export_all_paths_data.py 生成数据")
        return
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 准备CSV数据
    csv_data = []
    
    # 添加表头
    headers = [
        'path_id',
        'path_index',
        'flight_direction',
        'height_layer',
        'cluster_id',
        'cluster_type',
        'waypoints_count',
        'path_length',
        'turning_cost',
        'risk_value',
        'collision_cost',
        'final_cost',
        'weight_alpha',
        'weight_beta',
        'weight_gamma',
        'weight_delta',
        'risk_normalized',
        'collision_normalized',
        'length_normalized',
        'turning_normalized',
        'risk_term',
        'collision_term',
        'length_term',
        'orient_term',
        'risk_percent',
        'collision_percent',
        'length_percent',
        'turning_percent',
        'is_selected'
    ]
    
    # 🔧 修复：获取选中路径ID，使用最终代价最低的路径
    selected_path_id = None
    min_final_cost = float('inf')

    # 找到最终代价最低的路径作为选中路径
    if 'all_paths' in data:
        for path_data in data['all_paths']:
            final_cost = path_data.get('metrics', {}).get('final_cost', float('inf'))
            if final_cost < min_final_cost:
                min_final_cost = final_cost
                selected_path_id = path_data.get('path_id')

    # 备用方案：从selected_path字段获取
    if selected_path_id is None and 'selected_path' in data and data['selected_path']:
        selected_path_id = data['selected_path']['selected_path_id']

    print(f"🎯 CSV导出 - 选中路径ID: {selected_path_id}, 最终代价: {min_final_cost:.6f}")
    
    # 处理每条路径数据
    for path_data in data['all_paths']:
        row = []
        
        # 基本信息
        row.append(path_data['path_id'])
        row.append(path_data['path_index'])
        row.append(path_data['flight_direction'])
        row.append(path_data['height_layer'])

        # 🔧 添加簇信息
        row.append(path_data.get('cluster_id', 'unknown'))
        row.append(path_data.get('cluster_type', 'unknown'))

        row.append(path_data['waypoints_count'])
        
        # 原始指标
        metrics = path_data['metrics']
        row.append(round(metrics['path_length'], 2))
        row.append(round(metrics['turning_cost'], 4))
        row.append(round(metrics['risk_value'], 4))
        row.append(round(metrics['collision_cost'], 4))
        row.append(round(metrics['final_cost'], 6))
        
        # 权重
        weights = path_data['cost_breakdown']['weights']
        row.append(round(weights['alpha'], 6))
        row.append(round(weights['beta'], 6))
        row.append(round(weights['gamma'], 6))
        row.append(round(weights['delta'], 6))
        
        # 归一化值
        normalized = path_data['cost_breakdown']['normalized_values']
        row.append(round(normalized['risk_normalized'], 6))
        row.append(round(normalized['collision_normalized'], 6))
        row.append(round(normalized['length_normalized'], 6))
        row.append(round(normalized['turning_normalized'], 6))
        
        # 权重项
        terms = path_data['cost_breakdown']['weighted_terms']
        row.append(round(terms['risk_term'], 6))
        row.append(round(terms['collision_term'], 6))
        row.append(round(terms['length_term'], 6))
        row.append(round(terms['orient_term'], 6))
        
        # 贡献百分比
        contributions = path_data['cost_breakdown']['contributions']
        row.append(round(contributions['risk_percent'], 2))
        row.append(round(contributions['collision_percent'], 2))
        row.append(round(contributions['length_percent'], 2))
        row.append(round(contributions['turning_percent'], 2))
        
        # 是否被选中
        row.append(1 if path_data['path_id'] == selected_path_id else 0)
        
        csv_data.append(row)
    
    # 写入CSV文件
    csv_file = 'all_81_paths_data.csv'
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(headers)
        writer.writerows(csv_data)
    
    print(f"✅ CSV数据已导出到: {csv_file}")
    print(f"📊 包含 {len(csv_data)} 条路径数据")
    print(f"📋 包含 {len(headers)} 个字段")
    
    # 显示统计信息
    print(f"\n📈 数据统计:")
    print(f"   - 碰撞代价范围: {data['statistics']['collision_cost_stats']['min']:.4f} ~ {data['statistics']['collision_cost_stats']['max']:.4f}")
    print(f"   - 最终代价范围: {data['statistics']['final_cost_stats']['min']:.6f} ~ {data['statistics']['final_cost_stats']['max']:.6f}")
    print(f"   - 选中路径ID: {selected_path_id}")
    
    # 显示权重信息
    weights_used = data['statistics']['weights_used']
    print(f"\n⚖️ 使用的权重:")
    print(f"   - α (风险权重): {weights_used['alpha']:.6f}")
    print(f"   - β (碰撞权重): {weights_used['beta']:.6f}")
    print(f"   - γ (长度权重): {weights_used['gamma']:.6f}")
    print(f"   - δ (转向权重): {weights_used['delta']:.6f}")
    
    # 显示前几行数据预览
    print(f"\n📋 CSV数据预览 (前5行):")
    print("路径ID | 方向 | 高度 | 路径长度 | 转向成本 | 碰撞代价 | 最终代价 | 碰撞占比% | 选中")
    print("-" * 80)
    
    for i, row in enumerate(csv_data[:5]):
        path_id = row[0]
        direction = row[2]
        height = row[3]
        length = row[5]
        turning = row[6]
        collision = row[8]
        final = row[9]
        collision_pct = row[22]
        selected = "✓" if row[26] else ""
        
        print(f"{path_id:6} | {direction:4} | {height:4} | {length:8.2f} | {turning:8.4f} | {collision:8.4f} | {final:8.6f} | {collision_pct:7.2f} | {selected:2}")
    
    return csv_file

def create_summary_csv():
    """创建简化的汇总CSV"""
    
    # 读取JSON数据
    json_file = 'all_81_paths_data.json'
    if not os.path.exists(json_file):
        print(f"❌ 找不到文件: {json_file}")
        return
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 简化的CSV数据
    summary_headers = [
        'path_id',
        'direction',
        'height',
        'path_length',
        'turning_cost',
        'risk_value',
        'collision_cost',
        'final_cost',
        'collision_percent',
        'selected',
        'rank_by_collision',
        'rank_by_final'
    ]
    
    # 获取选中路径ID
    selected_path_id = None
    if 'selected_path' in data and data['selected_path']:
        selected_path_id = data['selected_path']['selected_path_id']
    
    # 准备数据并排序
    paths_for_ranking = []
    for path_data in data['all_paths']:
        paths_for_ranking.append({
            'path_id': path_data['path_id'],
            'collision_cost': path_data['metrics']['collision_cost'],
            'final_cost': path_data['metrics']['final_cost'],
            'data': path_data
        })
    
    # 按碰撞代价和最终代价排序获得排名
    collision_sorted = sorted(paths_for_ranking, key=lambda x: x['collision_cost'])
    final_sorted = sorted(paths_for_ranking, key=lambda x: x['final_cost'])
    
    collision_ranks = {path['path_id']: i+1 for i, path in enumerate(collision_sorted)}
    final_ranks = {path['path_id']: i+1 for i, path in enumerate(final_sorted)}
    
    # 生成汇总数据
    summary_data = []
    for path_data in data['all_paths']:
        path_id = path_data['path_id']
        metrics = path_data['metrics']
        contributions = path_data['cost_breakdown']['contributions']
        
        row = [
            path_id,
            path_data['flight_direction'],
            path_data['height_layer'],
            round(metrics['path_length'], 2),
            round(metrics['turning_cost'], 4),
            round(metrics['risk_value'], 4),
            round(metrics['collision_cost'], 4),
            round(metrics['final_cost'], 6),
            round(contributions['collision_percent'], 2),
            1 if path_id == selected_path_id else 0,
            collision_ranks[path_id],
            final_ranks[path_id]
        ]
        summary_data.append(row)
    
    # 写入汇总CSV
    summary_file = 'paths_summary.csv'
    with open(summary_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(summary_headers)
        writer.writerows(summary_data)
    
    print(f"✅ 汇总CSV已导出到: {summary_file}")
    return summary_file

if __name__ == "__main__":
    print("🚀 开始导出CSV数据...")
    
    # 导出完整CSV
    full_csv = export_paths_to_csv()
    
    print("\n" + "="*60)
    
    # 导出汇总CSV
    summary_csv = create_summary_csv()
    
    print(f"\n💾 导出完成!")
    print(f"   - 完整数据: {full_csv}")
    print(f"   - 汇总数据: {summary_csv}")
    print(f"\n📝 您现在可以使用Excel或其他工具打开这些CSV文件进行分析和调参!")
