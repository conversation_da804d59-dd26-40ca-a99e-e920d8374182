/**
 * Python算法客户端
 * 专门用于调用Python后端的路径规划算法
 */

class PythonAlgorithmClient {
    constructor() {
        this.baseUrl = 'http://localhost:5000';
        this.timeout = 30000; // 30秒超时
        this.availableAlgorithms = [];
    }

    /**
     * 初始化客户端
     */
    async initialize() {
        try {
            // 检查后端健康状态
            const health = await this.checkHealth();
            if (!health.success) {
                throw new Error('Python后端服务不可用');
            }

            // 获取可用算法列表
            const algorithms = await this.getAlgorithmList();
            this.availableAlgorithms = algorithms.algorithms || [];

            console.log('🐍 Python算法客户端初始化成功');
            console.log(`📊 可用算法: ${this.availableAlgorithms.map(a => a.name).join(', ')}`);
            
            return true;
        } catch (error) {
            console.error('❌ Python算法客户端初始化失败:', error);
            return false;
        }
    }

    /**
     * 检查后端健康状态
     */
    async checkHealth() {
        try {
            const response = await fetch(`${this.baseUrl}/api/health`, {
                method: 'GET',
                timeout: 5000
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取算法列表
     */
    async getAlgorithmList() {
        try {
            const response = await fetch(`${this.baseUrl}/api/pathfinding/algorithms`, {
                method: 'GET',
                timeout: 5000
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            throw new Error(`获取算法列表失败: ${error.message}`);
        }
    }

    /**
     * 执行路径规划
     */
    async calculatePath(requestData) {
        try {
            console.log('🚀 发送路径规划请求到Python后端...');
            console.log('📍 请求数据:', {
                algorithm: requestData.algorithm,
                startPoint: requestData.startPoint,
                endPoint: requestData.endPoint,
                flightHeight: requestData.flightHeight,
                parameters: requestData.parameters
            });

            const response = await fetch(`${this.baseUrl}/api/pathfinding/calculate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData),
                timeout: this.timeout
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }

            const result = await response.json();
            
            console.log('✅ Python后端响应:', {
                success: result.success,
                pathLength: result.pathLength || result.path_length,
                pathPoints: result.path ? result.path.length : 0,
                error: result.error
            });

            return result;
        } catch (error) {
            console.error('❌ Python后端调用失败:', error);
            throw error;
        }
    }

    /**
     * 获取算法信息
     */
    getAlgorithmInfo(algorithmName) {
        return this.availableAlgorithms.find(alg => alg.name === algorithmName);
    }

    /**
     * 检查算法是否可用
     */
    isAlgorithmAvailable(algorithmName) {
        return this.availableAlgorithms.some(alg => alg.name === algorithmName);
    }

    /**
     * 获取算法的默认参数
     */
    getDefaultParameters(algorithmName) {
        const algorithmInfo = this.getAlgorithmInfo(algorithmName);
        if (!algorithmInfo) {
            return {};
        }

        const defaults = {};
        
        // 处理必需参数
        if (algorithmInfo.info && algorithmInfo.info.required_parameters) {
            algorithmInfo.info.required_parameters.forEach(param => {
                defaults[param.name] = param.default_value;
            });
        }

        // 处理可选参数
        if (algorithmInfo.info && algorithmInfo.info.optional_parameters) {
            algorithmInfo.info.optional_parameters.forEach(param => {
                defaults[param.name] = param.default_value;
            });
        }

        return defaults;
    }

    /**
     * 验证请求数据
     */
    validateRequest(requestData) {
        const errors = [];

        // 基本验证
        if (!requestData.startPoint || !requestData.endPoint) {
            errors.push('起点和终点不能为空');
        }

        if (!requestData.algorithm) {
            errors.push('必须指定算法');
        }

        if (requestData.algorithm && !this.isAlgorithmAvailable(requestData.algorithm)) {
            errors.push(`算法 ${requestData.algorithm} 不可用`);
        }

        if (!requestData.flightHeight || requestData.flightHeight < 30 || requestData.flightHeight > 500) {
            errors.push('飞行高度必须在30-500米之间');
        }

        // 坐标验证
        if (requestData.startPoint) {
            if (Math.abs(requestData.startPoint.lng) > 180 || Math.abs(requestData.startPoint.lat) > 90) {
                errors.push('起点坐标无效');
            }
        }

        if (requestData.endPoint) {
            if (Math.abs(requestData.endPoint.lng) > 180 || Math.abs(requestData.endPoint.lat) > 90) {
                errors.push('终点坐标无效');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 格式化路径数据
     */
    formatPathData(pathData) {
        console.log('🔍 formatPathData 输入数据:', pathData);

        if (!pathData || !pathData.path) {
            console.log('🔍 formatPathData: 无效的路径数据');
            return [];
        }

        const formattedPath = pathData.path.map((point, index) => {
            console.log(`🔍 formatPathData 处理点 ${index}:`, point);

            // 处理不同的路径点格式
            let lng, lat, alt;

            if (point.lng !== undefined && point.lat !== undefined) {
                // 标准格式: {lng, lat, alt}
                lng = point.lng;
                lat = point.lat;
                alt = point.alt || point.height || 120;
            } else if (point.longitude !== undefined && point.latitude !== undefined) {
                // 备用格式: {longitude, latitude, height}
                lng = point.longitude;
                lat = point.latitude;
                alt = point.height || point.alt || 120;
            } else if (Array.isArray(point) && point.length >= 2) {
                // 数组格式: [lng, lat, alt]
                lng = point[0];
                lat = point[1];
                alt = point[2] || 120;
            } else {
                console.warn('🔍 formatPathData: 无法识别的点格式:', point);
                return null;
            }

            const formattedPoint = {
                lng: lng,
                lat: lat,
                alt: alt,
                speed: point.speed || 15,
                heading: point.heading || 0,
                timestamp: point.timestamp || 0
            };

            console.log(`🔍 formatPathData 格式化后的点 ${index}:`, formattedPoint);
            return formattedPoint;
        }).filter(point => point !== null);

        console.log('🔍 formatPathData 最终结果:', formattedPath);
        return formattedPath;
    }

    /**
     * 获取支持的算法列表（用于UI显示）
     */
    getSupportedAlgorithms() {
        return [
            {
                value: 'StraightLine',
                label: '直线算法 - 快速规划',
                description: '最简单的路径规划算法，适用于无障碍环境'
            },
            {
                value: 'AStar',
                label: 'A*算法 - 最优路径',
                description: '经典的最优路径搜索算法，适用于复杂环境'
            },
            {
                value: 'RRT',
                label: 'RRT算法 - 快速探索',
                description: '快速随机树算法，适用于复杂环境的快速探索'
            },
            {
                value: 'ImprovedClusterBased',
                label: '⭐ 改进分簇算法 - 专业版',
                description: '基于分簇的高级路径规划算法，支持多路径生成和动态换路',
                featured: true
            }
        ];
    }
}

// 全局实例
window.pythonAlgorithmClient = new PythonAlgorithmClient();
