/**
 * 交通密度管理器
 * 用于在地图上生成和管理人车密度点，支持算法的碰撞代价计算
 */
class TrafficDensityManager {
    constructor(map, cityManager) {
        this.map = map;
        this.cityManager = cityManager;
        
        // 密度配置
        this.densityConfig = {
            enabled: false,
            vehicleDensity: 0.5,     // 车辆密度 (0-1)
            pedestrianDensity: 0.3,  // 行人密度 (0-1)
            gridSize: 100,           // 网格大小(米)
            maxVehiclesPerPoint: 10, // 每个点最大车辆数
            maxPedestriansPerPoint: 30, // 每个点最大行人数
            visualizationEnabled: true // 是否显示可视化
        };
        
        // 数据存储
        this.trafficPoints = new Map(); // 存储所有交通点
        this.visualMarkers = [];        // 可视化标记
        
        // 地图边界
        this.mapBounds = null;
        
        this.log("交通密度管理器初始化完成", "info");
    }

    /**
     * 设置密度配置
     */
    setDensityConfig(config) {
        this.densityConfig = { ...this.densityConfig, ...config };
        this.log(`密度配置已更新: 车辆密度=${this.densityConfig.vehicleDensity}, 行人密度=${this.densityConfig.pedestrianDensity}`, "info");
        
        if (this.densityConfig.enabled) {
            this.regenerateTrafficPoints();
        }
    }

    /**
     * 启用/禁用交通密度系统
     */
    setEnabled(enabled) {
        this.densityConfig.enabled = enabled;
        
        if (enabled) {
            this.generateTrafficPoints();
            if (this.densityConfig.visualizationEnabled) {
                this.showVisualization();
            }
        } else {
            this.clearTrafficPoints();
            this.hideVisualization();
        }
        
        this.log(`交通密度系统${enabled ? '已启用' : '已禁用'}`, "info");
    }

    /**
     * 生成交通密度点
     */
    generateTrafficPoints() {
        this.clearTrafficPoints();
        
        // 获取当前地图视野范围
        this.updateMapBounds();
        
        if (!this.mapBounds) {
            this.log("无法获取地图边界，跳过交通点生成", "warning");
            return;
        }

        const { north, south, east, west } = this.mapBounds;
        const gridSize = this.densityConfig.gridSize;
        
        // 计算网格数量
        const latStep = this.metersToLatitude(gridSize);
        const lngStep = this.metersToLongitude(gridSize, (north + south) / 2);
        
        let pointId = 0;
        let totalVehicles = 0;
        let totalPedestrians = 0;
        
        // 按网格生成交通点
        for (let lat = south; lat < north; lat += latStep) {
            for (let lng = west; lng < east; lng += lngStep) {
                // 根据密度配置决定是否在此位置生成交通点
                if (Math.random() < this.densityConfig.vehicleDensity || 
                    Math.random() < this.densityConfig.pedestrianDensity) {
                    
                    const trafficPoint = this.createTrafficPoint(pointId++, lng, lat);
                    this.trafficPoints.set(trafficPoint.id, trafficPoint);
                    
                    totalVehicles += trafficPoint.vehicles;
                    totalPedestrians += trafficPoint.pedestrians;
                }
            }
        }
        
        this.log(`生成交通密度点完成: ${this.trafficPoints.size}个点, ${totalVehicles}辆车, ${totalPedestrians}个行人`, "info");
    }

    /**
     * 创建单个交通点
     */
    createTrafficPoint(id, lng, lat) {
        // 根据位置和密度配置生成车辆和行人数量
        const vehicleCount = this.calculateVehicleCount(lng, lat);
        const pedestrianCount = this.calculatePedestrianCount(lng, lat);
        
        return {
            id: id,
            lng: lng,
            lat: lat,
            x: lng, // 用于算法计算
            y: lat, // 用于算法计算
            vehicles: vehicleCount,
            pedestrians: pedestrianCount,
            type: 'traffic_point',
            timestamp: Date.now()
        };
    }

    /**
     * 计算指定位置的车辆数量
     */
    calculateVehicleCount(lng, lat) {
        // 基于位置的车辆密度变化
        // 市中心区域车辆密度更高
        const centerLng = 139.7682; // 东京市中心经度
        const centerLat = 35.6784;  // 东京市中心纬度
        
        const distance = this.calculateDistance(lng, lat, centerLng, centerLat);
        const distanceFactor = Math.max(0.1, 1.0 - distance / 5000); // 5km范围内密度递减
        
        const baseDensity = this.densityConfig.vehicleDensity;
        const adjustedDensity = baseDensity * distanceFactor;
        
        // 随机生成车辆数量
        const maxVehicles = this.densityConfig.maxVehiclesPerPoint;
        return Math.floor(Math.random() * maxVehicles * adjustedDensity) + 1;
    }

    /**
     * 计算指定位置的行人数量
     */
    calculatePedestrianCount(lng, lat) {
        // 基于位置的行人密度变化
        const centerLng = 139.7682;
        const centerLat = 35.6784;
        
        const distance = this.calculateDistance(lng, lat, centerLng, centerLat);
        const distanceFactor = Math.max(0.2, 1.0 - distance / 3000); // 3km范围内密度递减
        
        const baseDensity = this.densityConfig.pedestrianDensity;
        const adjustedDensity = baseDensity * distanceFactor;
        
        // 随机生成行人数量
        const maxPedestrians = this.densityConfig.maxPedestriansPerPoint;
        return Math.floor(Math.random() * maxPedestrians * adjustedDensity) + 1;
    }

    /**
     * 获取指定航点范围内的交通数据
     */
    getTrafficDataInRange(lng, lat, radiusMeters = 30) {
        const trafficData = {
            vehicles: 0,
            pedestrians: 0,
            points: []
        };
        
        for (const point of this.trafficPoints.values()) {
            const distance = this.calculateDistance(lng, lat, point.lng, point.lat);
            
            if (distance <= radiusMeters) {
                trafficData.vehicles += point.vehicles;
                trafficData.pedestrians += point.pedestrians;
                trafficData.points.push({
                    ...point,
                    distance: distance
                });
            }
        }
        
        return trafficData;
    }

    /**
     * 获取所有交通点数据（用于传递给后端算法）
     */
    getAllTrafficData() {
        return Array.from(this.trafficPoints.values());
    }

    /**
     * 显示可视化
     */
    showVisualization() {
        this.hideVisualization(); // 先清除现有标记
        
        for (const point of this.trafficPoints.values()) {
            const marker = this.createVisualizationMarker(point);
            this.visualMarkers.push(marker);
        }
        
        this.log(`显示${this.visualMarkers.length}个交通密度可视化标记`, "info");
    }

    /**
     * 创建可视化标记
     */
    createVisualizationMarker(point) {
        // 根据车辆和行人数量确定标记颜色和大小
        const totalTraffic = point.vehicles + point.pedestrians;
        const intensity = Math.min(1.0, totalTraffic / 20); // 归一化强度
        
        // 颜色从绿色(低密度)到红色(高密度)
        const red = Math.floor(255 * intensity);
        const green = Math.floor(255 * (1 - intensity));
        const color = `rgb(${red}, ${green}, 0)`;
        
        // 创建HTML标记
        const el = document.createElement('div');
        el.className = 'traffic-density-marker';
        el.style.cssText = `
            width: ${8 + intensity * 12}px;
            height: ${8 + intensity * 12}px;
            background-color: ${color};
            border: 2px solid white;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        `;
        
        // 添加点击事件显示详细信息
        el.addEventListener('click', () => {
            this.showTrafficPointDetails(point);
        });
        
        // 创建Mapbox标记
        const marker = new mapboxgl.Marker(el)
            .setLngLat([point.lng, point.lat])
            .addTo(this.map);
        
        return marker;
    }

    /**
     * 显示交通点详细信息
     */
    showTrafficPointDetails(point) {
        const popup = new mapboxgl.Popup()
            .setLngLat([point.lng, point.lat])
            .setHTML(`
                <div class="traffic-point-popup">
                    <h4>交通密度点 #${point.id}</h4>
                    <p><strong>位置:</strong> (${point.lng.toFixed(6)}, ${point.lat.toFixed(6)})</p>
                    <p><strong>车辆数量:</strong> ${point.vehicles} 辆</p>
                    <p><strong>行人数量:</strong> ${point.pedestrians} 人</p>
                    <p><strong>总交通量:</strong> ${point.vehicles + point.pedestrians}</p>
                </div>
            `)
            .addTo(this.map);
    }

    /**
     * 隐藏可视化
     */
    hideVisualization() {
        this.visualMarkers.forEach(marker => marker.remove());
        this.visualMarkers = [];
    }

    /**
     * 清除所有交通点
     */
    clearTrafficPoints() {
        this.trafficPoints.clear();
        this.hideVisualization();
    }

    /**
     * 重新生成交通点
     */
    regenerateTrafficPoints() {
        if (this.densityConfig.enabled) {
            this.generateTrafficPoints();
            if (this.densityConfig.visualizationEnabled) {
                this.showVisualization();
            }
        }
    }

    /**
     * 更新地图边界
     */
    updateMapBounds() {
        const bounds = this.map.getBounds();
        this.mapBounds = {
            north: bounds.getNorth(),
            south: bounds.getSouth(),
            east: bounds.getEast(),
            west: bounds.getWest()
        };
    }

    /**
     * 计算两点间距离(米)
     */
    calculateDistance(lng1, lat1, lng2, lat2) {
        const R = 6371000; // 地球半径(米)
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    /**
     * 米转纬度
     */
    metersToLatitude(meters) {
        return meters / 111320; // 1度纬度约等于111320米
    }

    /**
     * 米转经度
     */
    metersToLongitude(meters, latitude) {
        return meters / (111320 * Math.cos(latitude * Math.PI / 180));
    }

    /**
     * 日志输出
     */
    log(message, level = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[${timestamp}] 🚗 ${message}`;
        
        if (this.cityManager && this.cityManager.log) {
            this.cityManager.log(logMessage, level);
        } else {
            console.log(logMessage);
        }
    }
}
