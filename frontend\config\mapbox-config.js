/**
 * Mapbox配置文件
 * 请在这里设置您的Mapbox访问令牌
 */

// Mapbox配置
const MAPBOX_CONFIG = {
    // ✅ Mapbox访问令牌已配置
    accessToken: 'pk.eyJ1IjoiZHhlbTV6IiwiYSI6ImNtZGNkYmFjdDEya3EybnE5ZHlvaWpheGoifQ.oYUO-ir5mtrqplJqfo1_Bg',
    
    // 默认地图样式
    defaultStyle: 'mapbox://styles/mapbox/dark-v10',
    
    // 可用的地图样式
    availableStyles: [
        {
            id: 'dark',
            name: '深色主题',
            url: 'mapbox://styles/mapbox/dark-v10'
        },
        {
            id: 'light',
            name: '浅色主题', 
            url: 'mapbox://styles/mapbox/light-v10'
        },
        {
            id: 'streets',
            name: '街道地图',
            url: 'mapbox://styles/mapbox/streets-v11'
        },
        {
            id: 'satellite',
            name: '卫星地图',
            url: 'mapbox://styles/mapbox/satellite-v9'
        },
        {
            id: 'outdoors',
            name: '户外地图',
            url: 'mapbox://styles/mapbox/outdoors-v11'
        }
    ],
    
    // 默认视图设置
    defaultView: {
        center: [139.7670, 35.6814], // 东京
        zoom: 15,
        pitch: 60,
        bearing: -17.6
    },
    
    // 3D建筑设置
    buildings: {
        minZoom: 15,
        opacity: 0.8,
        colors: {
            default: '#74b9ff',
            hover: '#ff6b6b',
            selected: '#00ff88'
        }
    }
};

// 城市预设位置
const CITY_PRESETS = {
    tokyo: {
        name: '东京23区 (皇居中心)',
        center: [139.7670, 35.6814],
        zoom: 15,
        pitch: 60,
        bearing: -17.6,
        bounds: [[139.5619, 35.5322], [139.9187, 35.8986]]
    },
    // 东京23区详细配置
    shinjuku: {
        name: '新宿区 (都政府)',
        center: [139.7036, 35.6938],
        zoom: 16,
        pitch: 60,
        bearing: 0,
        bounds: [[139.6786, 35.6738], [139.7286, 35.7138]]
    },
    shibuya: {
        name: '渋谷区 (商业中心)',
        center: [139.7016, 35.6580],
        zoom: 16,
        pitch: 60,
        bearing: -30,
        bounds: [[139.6766, 35.6380], [139.7266, 35.6780]]
    },
    minato: {
        name: '港区 (东京塔)',
        center: [139.7514, 35.6585],
        zoom: 16,
        pitch: 60,
        bearing: 45,
        bounds: [[139.7284, 35.6284], [139.7744, 35.6886]]
    },
    chiyoda: {
        name: '千代田区 (皇居)',
        center: [139.7538, 35.6938],
        zoom: 16,
        pitch: 60,
        bearing: -15,
        bounds: [[139.7284, 35.6740], [139.7792, 35.7136]]
    },
    chuo: {
        name: '中央区 (银座)',
        center: [139.7714, 35.6735],
        zoom: 16,
        pitch: 60,
        bearing: 30,
        bounds: [[139.7570, 35.6580], [139.7858, 35.6890]]
    },
    taito: {
        name: '台东区 (浅草)',
        center: [139.7786, 35.7120],
        zoom: 16,
        pitch: 60,
        bearing: -45,
        bounds: [[139.7636, 35.6970], [139.7936, 35.7270]]
    },
    beijing: {
        name: '北京, 中国',
        center: [116.4074, 39.9042],
        zoom: 15,
        pitch: 45,
        bearing: 0
    },
    shanghai: {
        name: '上海, 中国',
        center: [121.4737, 31.2304],
        zoom: 15,
        pitch: 60,
        bearing: -30
    },
    newyork: {
        name: '纽约, 美国',
        center: [-74.0060, 40.7128],
        zoom: 16,
        pitch: 60,
        bearing: 45
    },
    london: {
        name: '伦敦, 英国',
        center: [-0.1276, 51.5074],
        zoom: 15,
        pitch: 45,
        bearing: 0
    },
    paris: {
        name: '巴黎, 法国',
        center: [2.3522, 48.8566],
        zoom: 15,
        pitch: 60,
        bearing: -15
    },
    hongkong: {
        name: '香港',
        center: [114.1694, 22.3193],
        zoom: 16,
        pitch: 60,
        bearing: 30
    },
    singapore: {
        name: '新加坡',
        center: [103.8198, 1.3521],
        zoom: 15,
        pitch: 60,
        bearing: 0
    },
    dubai: {
        name: '迪拜, 阿联酋',
        center: [55.2708, 25.2048],
        zoom: 15,
        pitch: 60,
        bearing: -45
    },
    sydney: {
        name: '悉尼, 澳大利亚',
        center: [151.2093, -33.8688],
        zoom: 15,
        pitch: 60,
        bearing: 15
    }
};

// OSM数据配置
const OSM_CONFIG = {
    // Overpass API端点
    overpassEndpoints: [
        'https://overpass-api.de/api/interpreter',
        'https://overpass.kumi.systems/api/interpreter',
        'https://overpass.openstreetmap.ru/api/interpreter'
    ],
    
    // 请求配置
    timeout: 25,
    maxRetries: 3,
    retryDelay: 1000,
    cacheTimeout: 5 * 60 * 1000, // 5分钟
    
    // 数据类型配置
    dataTypes: {
        buildings: {
            enabled: true,
            minZoom: 16,
            query: 'way["building"]'
        },
        roads: {
            enabled: false,
            minZoom: 15,
            query: 'way["highway"~"^(primary|secondary|tertiary|residential|trunk|motorway)$"]'
        },
        pois: {
            enabled: false,
            minZoom: 17,
            query: 'node["amenity"]'
        },
        natural: {
            enabled: false,
            minZoom: 14,
            query: 'way["natural"]'
        }
    }
};

// 性能配置
const PERFORMANCE_CONFIG = {
    // 渲染配置
    maxBuildings: 1000,
    lodLevels: 3,
    cullingDistance: 5000,
    
    // 缓存配置
    maxCacheSize: 100,
    cacheCleanupInterval: 60000, // 1分钟
    
    // 动画配置
    animationFrameRate: 60,
    transitionDuration: 1000
};

// 导出配置
if (typeof window !== 'undefined') {
    window.MAPBOX_CONFIG = MAPBOX_CONFIG;
    window.CITY_PRESETS = CITY_PRESETS;
    window.OSM_CONFIG = OSM_CONFIG;
    window.PERFORMANCE_CONFIG = PERFORMANCE_CONFIG;
}

// Node.js环境导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MAPBOX_CONFIG,
        CITY_PRESETS,
        OSM_CONFIG,
        PERFORMANCE_CONFIG
    };
}
