/**
 * OpenStreetMap数据加载器
 * 使用Overpass API获取全球建筑和地理数据
 */

class OSMDataLoader {
    constructor(options = {}) {
        this.options = {
            overpassUrl: options.overpassUrl || 'https://overpass-api.de/api/interpreter',
            timeout: options.timeout || 25,
            maxRetries: options.maxRetries || 3,
            retryDelay: options.retryDelay || 1000,
            onDataLoaded: options.onDataLoaded || (() => {}),
            onError: options.onError || (() => {}),
            onProgress: options.onProgress || (() => {}),
            ...options
        };
        
        // 缓存系统
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
        
        // 请求管理
        this.activeRequests = new Map();
        this.requestQueue = [];
        this.maxConcurrentRequests = 2;
        
        this.log('OSMDataLoader初始化完成', 'info');
    }
    
    /**
     * 加载指定边界的数据
     */
    async loadData(bounds, options = {}) {
        try {
            const bbox = this.boundsToOverpassBbox(bounds);
            const cacheKey = this.getBboxCacheKey(bbox);
            
            // 检查缓存
            const cachedData = this.getFromCache(cacheKey);
            if (cachedData) {
                this.log('使用缓存数据', 'info');
                this.options.onDataLoaded(cachedData, bounds);
                return cachedData;
            }
            
            // 检查是否已有相同请求
            if (this.activeRequests.has(cacheKey)) {
                this.log('请求已在进行中，等待结果...', 'info');
                return await this.activeRequests.get(cacheKey);
            }
            
            // 创建新请求
            const requestPromise = this.executeRequest(bbox, bounds, options);
            this.activeRequests.set(cacheKey, requestPromise);
            
            try {
                const result = await requestPromise;
                return result;
            } finally {
                this.activeRequests.delete(cacheKey);
            }
            
        } catch (error) {
            this.log(`数据加载失败: ${error.message}`, 'error');
            this.options.onError(error);
            throw error;
        }
    }
    
    /**
     * 执行实际的数据请求
     */
    async executeRequest(bbox, bounds, options = {}) {
        const query = this.buildOverpassQuery(bbox, options);
        
        this.log(`发送Overpass查询: ${bbox.join(',')}`, 'info');
        this.options.onProgress({ stage: 'requesting', bbox });
        
        let lastError;
        
        for (let attempt = 1; attempt <= this.options.maxRetries; attempt++) {
            try {
                const data = await this.fetchOverpassData(query);
                const geojson = this.osmToGeoJSON(data);
                
                // 缓存结果
                const cacheKey = this.getBboxCacheKey(bbox);
                this.setCache(cacheKey, geojson);
                
                this.log(`数据加载成功: ${geojson.features.length} 个要素`, 'success');
                this.options.onDataLoaded(geojson, bounds);
                
                return geojson;
                
            } catch (error) {
                lastError = error;
                this.log(`请求失败 (尝试 ${attempt}/${this.options.maxRetries}): ${error.message}`, 'warning');
                
                if (attempt < this.options.maxRetries) {
                    await this.delay(this.options.retryDelay * attempt);
                }
            }
        }
        
        throw lastError;
    }
    
    /**
     * 构建Overpass查询
     */
    buildOverpassQuery(bbox, options = {}) {
        const [south, west, north, east] = bbox;
        
        // 基础查询 - 建筑物和主要道路
        let query = `[out:json][timeout:${this.options.timeout}];\n(\n`;
        
        // 建筑物
        if (options.includeBuildings !== false) {
            query += `  way["building"](${south},${west},${north},${east});\n`;
            query += `  relation["building"](${south},${west},${north},${east});\n`;
        }
        
        // 道路
        if (options.includeRoads !== false) {
            query += `  way["highway"~"^(primary|secondary|tertiary|residential|trunk|motorway)$"](${south},${west},${north},${east});\n`;
        }
        
        // 兴趣点
        if (options.includePOIs) {
            query += `  node["amenity"](${south},${west},${north},${east});\n`;
            query += `  node["shop"](${south},${west},${north},${east});\n`;
        }
        
        // 自然要素
        if (options.includeNatural) {
            query += `  way["natural"](${south},${west},${north},${east});\n`;
            query += `  way["landuse"](${south},${west},${north},${east});\n`;
        }
        
        query += ');\nout geom;';
        
        return query;
    }
    
    /**
     * 获取Overpass数据
     */
    async fetchOverpassData(query) {
        const response = await fetch(this.options.overpassUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `data=${encodeURIComponent(query)}`
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.remark && data.remark.includes('timeout')) {
            throw new Error('Overpass API超时');
        }
        
        return data;
    }
    
    /**
     * 将OSM数据转换为GeoJSON
     */
    osmToGeoJSON(osmData) {
        const geojson = {
            type: 'FeatureCollection',
            features: []
        };
        
        // 处理节点
        const nodes = new Map();
        if (osmData.elements) {
            osmData.elements.forEach(element => {
                if (element.type === 'node') {
                    nodes.set(element.id, element);
                }
            });
            
            // 处理路径和关系
            osmData.elements.forEach(element => {
                if (element.type === 'way') {
                    const feature = this.wayToGeoJSON(element, nodes);
                    if (feature) {
                        geojson.features.push(feature);
                    }
                } else if (element.type === 'relation') {
                    const feature = this.relationToGeoJSON(element, nodes);
                    if (feature) {
                        geojson.features.push(feature);
                    }
                } else if (element.type === 'node' && element.tags) {
                    const feature = this.nodeToGeoJSON(element);
                    if (feature) {
                        geojson.features.push(feature);
                    }
                }
            });
        }
        
        return geojson;
    }
    
    /**
     * 将OSM way转换为GeoJSON
     */
    wayToGeoJSON(way, nodes) {
        if (!way.geometry || way.geometry.length === 0) {
            return null;
        }
        
        const coordinates = way.geometry.map(coord => [coord.lon, coord.lat]);
        
        // 判断是否为多边形（建筑物）
        const isPolygon = way.tags && way.tags.building && 
                         coordinates.length > 3 && 
                         coordinates[0][0] === coordinates[coordinates.length - 1][0] &&
                         coordinates[0][1] === coordinates[coordinates.length - 1][1];
        
        return {
            type: 'Feature',
            id: way.id,
            properties: {
                ...way.tags,
                osm_id: way.id,
                osm_type: 'way'
            },
            geometry: {
                type: isPolygon ? 'Polygon' : 'LineString',
                coordinates: isPolygon ? [coordinates] : coordinates
            }
        };
    }
    
    /**
     * 将OSM relation转换为GeoJSON
     */
    relationToGeoJSON(relation, nodes) {
        // 简化处理 - 这里可以根据需要扩展
        return {
            type: 'Feature',
            id: relation.id,
            properties: {
                ...relation.tags,
                osm_id: relation.id,
                osm_type: 'relation'
            },
            geometry: {
                type: 'Point',
                coordinates: [0, 0] // 占位符
            }
        };
    }
    
    /**
     * 将OSM node转换为GeoJSON
     */
    nodeToGeoJSON(node) {
        return {
            type: 'Feature',
            id: node.id,
            properties: {
                ...node.tags,
                osm_id: node.id,
                osm_type: 'node'
            },
            geometry: {
                type: 'Point',
                coordinates: [node.lon, node.lat]
            }
        };
    }
    
    /**
     * 边界转换为Overpass bbox格式
     */
    boundsToOverpassBbox(bounds) {
        const ne = bounds.getNorthEast();
        const sw = bounds.getSouthWest();
        return [sw.lat, sw.lng, ne.lat, ne.lng]; // south, west, north, east
    }
    
    /**
     * 获取bbox的缓存键
     */
    getBboxCacheKey(bbox) {
        return bbox.map(coord => coord.toFixed(4)).join(',');
    }
    
    /**
     * 缓存管理
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        
        if (cached) {
            this.cache.delete(key);
        }
        
        return null;
    }
    
    setCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
        
        // 清理过期缓存
        this.cleanExpiredCache();
    }
    
    cleanExpiredCache() {
        const now = Date.now();
        for (const [key, cached] of this.cache.entries()) {
            if (now - cached.timestamp >= this.cacheTimeout) {
                this.cache.delete(key);
            }
        }
    }
    
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 日志
     */
    log(message, level = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[OSM] [${timestamp}] ${message}`;
        
        switch (level) {
            case 'error':
                console.error(logMessage);
                break;
            case 'warning':
                console.warn(logMessage);
                break;
            case 'success':
                console.log(`✅ ${logMessage}`);
                break;
            default:
                console.log(logMessage);
        }
    }
    
    /**
     * 清理资源
     */
    destroy() {
        this.cache.clear();
        this.activeRequests.clear();
        this.requestQueue = [];
        this.log('OSMDataLoader已销毁', 'info');
    }
}

// 导出
window.OSMDataLoader = OSMDataLoader;
