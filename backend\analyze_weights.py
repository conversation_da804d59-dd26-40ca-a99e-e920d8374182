#!/usr/bin/env python3
"""
权重和代价分析工具
分析为什么选择了碰撞代价最高的路径
"""

import math
import json

def calculate_fixed_weights() -> dict:
    """计算固定权重（按照您的要求）"""
    alpha = 0.5   # 风险权重
    beta = 0.4    # 碰撞权重
    gamma = 0.05  # 长度权重
    delta = 0.05  # 转向权重

    return {
        'alpha': alpha,
        'beta': beta,
        'gamma': gamma,
        'delta': delta,
        'sum': alpha + beta + gamma + delta
    }

def analyze_final_cost(path_length, turning_cost, risk_value, collision_cost, 
                      risk_reference=100.0, collision_reference=50.0, 
                      turning_reference=1000.0, risk_density=0.1):
    """分析最终代价的组成"""
    
    # 计算权重（使用固定权重）
    weights = calculate_fixed_weights()
    alpha = weights['alpha']
    beta = weights['beta']
    gamma = weights['gamma']
    delta = weights['delta']
    
    # 计算曼哈顿距离估算
    length_manhattan = path_length * 1.5
    
    # 计算各项代价
    risk_term = alpha * (risk_value / max(risk_reference, 1.0))
    collision_term = beta * (collision_cost / max(collision_reference, 1.0))
    length_term = gamma * (path_length / max(length_manhattan, 1.0))
    orient_term = delta * (turning_cost / max(turning_reference, 1.0))
    
    final_cost = risk_term + collision_term + length_term + orient_term
    
    return {
        'weights': weights,
        'terms': {
            'risk_term': risk_term,
            'collision_term': collision_term,
            'length_term': length_term,
            'orient_term': orient_term
        },
        'final_cost': final_cost,
        'breakdown': {
            'risk_contribution': risk_term / final_cost * 100,
            'collision_contribution': collision_term / final_cost * 100,
            'length_contribution': length_term / final_cost * 100,
            'turning_contribution': orient_term / final_cost * 100
        }
    }

def main():
    print("🔍 权重和代价分析")
    print("=" * 50)
    
    # 从测试结果分析选中路径
    selected_path = {
        'path_length': 6350.58,
        'turning_cost': 904.7517,
        'risk_value': 1.5925,
        'collision_cost': 29.3439
    }
    
    print(f"📊 选中路径数据:")
    print(f"   - 路径长度: {selected_path['path_length']:.2f}m")
    print(f"   - 转向成本: {selected_path['turning_cost']:.4f}")
    print(f"   - 风险值: {selected_path['risk_value']:.4f}")
    print(f"   - 碰撞代价: {selected_path['collision_cost']:.4f}")
    
    # 分析不同风险密度下的权重
    print(f"\n🎯 权重分析:")
    print("   使用固定权重（不依赖风险密度）:")
    weights = calculate_fixed_weights()
    print(f"   固定权重: α={weights['alpha']:.3f}, β={weights['beta']:.3f}, γ={weights['gamma']:.3f}, δ={weights['delta']:.3f}")
    
    # 使用实际数据分析最终代价
    print(f"\n💰 最终代价分析 (风险密度=0.1):")
    analysis = analyze_final_cost(**selected_path)
    
    print(f"   权重: α={analysis['weights']['alpha']:.3f}, β={analysis['weights']['beta']:.3f}, γ={analysis['weights']['gamma']:.3f}, δ={analysis['weights']['delta']:.3f}")
    print(f"   各项代价:")
    print(f"     - 风险项: {analysis['terms']['risk_term']:.6f} ({analysis['breakdown']['risk_contribution']:.1f}%)")
    print(f"     - 碰撞项: {analysis['terms']['collision_term']:.6f} ({analysis['breakdown']['collision_contribution']:.1f}%)")
    print(f"     - 长度项: {analysis['terms']['length_term']:.6f} ({analysis['breakdown']['length_contribution']:.1f}%)")
    print(f"     - 转向项: {analysis['terms']['orient_term']:.6f} ({analysis['breakdown']['turning_contribution']:.1f}%)")
    print(f"   最终代价: {analysis['final_cost']:.6f}")
    
    # 模拟一个碰撞代价更低的路径
    print(f"\n🔄 对比分析 - 如果碰撞代价降低到最低值:")
    low_collision_path = selected_path.copy()
    low_collision_path['collision_cost'] = 17.8254  # 最低碰撞代价
    
    analysis_low = analyze_final_cost(**low_collision_path)
    print(f"   碰撞项: {analysis_low['terms']['collision_term']:.6f} ({analysis_low['breakdown']['collision_contribution']:.1f}%)")
    print(f"   最终代价: {analysis_low['final_cost']:.6f}")
    print(f"   代价差异: {analysis['final_cost'] - analysis_low['final_cost']:.6f}")
    
    # 分析转向成本的影响
    print(f"\n🔄 转向成本影响分析:")
    for turning_factor in [0.1, 0.5, 1.0, 2.0]:
        test_path = selected_path.copy()
        test_path['turning_cost'] = selected_path['turning_cost'] * turning_factor
        
        analysis_turn = analyze_final_cost(**test_path)
        print(f"   转向成本 × {turning_factor}: 最终代价 = {analysis_turn['final_cost']:.6f}, 转向占比 = {analysis_turn['breakdown']['turning_contribution']:.1f}%")
    
    # 保存分析结果
    result = {
        'selected_path': selected_path,
        'analysis': analysis,
        'low_collision_analysis': analysis_low,
        'weight_analysis': {
            'type': 'fixed_weights',
            'weights': calculate_fixed_weights()
        }
    }
    
    with open('weight_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 分析结果已保存到 weight_analysis_results.json")

if __name__ == "__main__":
    main()
