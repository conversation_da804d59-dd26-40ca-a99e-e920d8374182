#!/usr/bin/env python3
"""
测试保护区显示问题
验证保护区信息是否正确传递到前端
"""

def test_protection_zone_display():
    """测试保护区显示"""
    
    print("🔍 测试保护区显示问题")
    print("=" * 60)
    
    try:
        # 1. 测试前端保护区系统
        print("📋 1. 测试前端保护区系统")
        
        from protection_zones import ProtectionZoneManager
        protection_manager = ProtectionZoneManager()
        
        print(f"   前端保护区总数: {len(protection_manager.zones)}")
        
        # 2. 模拟一条经过东京站的路径
        print(f"\n🛤️ 2. 模拟经过东京站的路径")
        
        # 东京站坐标：(139.7673, 35.6812)
        test_path_points = [
            (139.7670, 35.6810),  # 东京站附近起点
            (139.7673, 35.6812),  # 东京站中心
            (139.7676, 35.6814),  # 东京站附近终点
        ]
        
        print(f"   测试路径点数: {len(test_path_points)}")
        for i, (lng, lat) in enumerate(test_path_points):
            print(f"     点{i+1}: ({lng:.4f}, {lat:.4f})")
        
        # 3. 检测相关保护区
        print(f"\n🔍 3. 检测相关保护区")
        
        relevant_zones = protection_manager.get_zones_for_path(test_path_points, buffer_distance=500)
        
        print(f"   检测到相关保护区: {len(relevant_zones)} 个")
        for zone in relevant_zones:
            print(f"     - {zone.name} (ID: {zone.id}, 类型: {zone.zone_type.value}, 半径: {zone.radius}m)")
        
        # 4. 计算碰撞代价
        print(f"\n💰 4. 计算碰撞代价")
        
        collision_cost_breakdown = {}
        total_cost = 0.0
        
        for zone in relevant_zones:
            zone_total_cost = 0.0
            for lng, lat in test_path_points:
                cost = zone.get_collision_cost(lng, lat)
                zone_total_cost += cost
            
            if zone_total_cost > 0:
                collision_cost_breakdown[zone.id] = {
                    'zone_name': zone.name,
                    'zone_type': zone.zone_type.value,
                    'total_cost': zone_total_cost,
                    'average_crash_cost': zone.average_crash_cost
                }
                total_cost += zone_total_cost
                
                print(f"   {zone.name} (ID: {zone.id}): 代价 {zone_total_cost:.4f}")
        
        print(f"   总碰撞代价: {total_cost:.4f}")
        print(f"   活跃保护区数: {len(collision_cost_breakdown)}")
        
        # 5. 模拟改进算法响应格式
        print(f"\n📤 5. 模拟改进算法响应格式")
        
        # 改进算法的响应格式
        improved_response = {
            'success': True,
            'protectionZonesInfo': {
                'collision_cost_breakdown': collision_cost_breakdown,
                'total_zones': len(relevant_zones),
                'total_estimated_cost': total_cost
            }
        }
        
        # 提取活跃保护区ID
        improved_active_ids = list(collision_cost_breakdown.keys())
        print(f"   改进算法活跃保护区ID: {improved_active_ids}")
        
        # 6. 模拟基准算法响应格式
        print(f"\n⭐ 6. 模拟基准算法响应格式")
        
        # 基准算法的响应格式
        baseline_response = {
            'success': True,
            'metadata': {
                'protection_zones': {
                    'collision_cost_breakdown': collision_cost_breakdown,
                    'active_zones': len(collision_cost_breakdown),
                    'active_zone_ids': list(collision_cost_breakdown.keys())
                }
            }
        }
        
        baseline_active_ids = baseline_response['metadata']['protection_zones']['active_zone_ids']
        print(f"   基准算法活跃保护区ID: {baseline_active_ids}")
        
        # 7. 测试前端ID处理逻辑
        print(f"\n🖥️ 7. 测试前端ID处理逻辑")
        
        def simulate_frontend_update(active_zone_ids, algorithm_name):
            print(f"   {algorithm_name}:")
            print(f"     接收到的ID: {active_zone_ids}")
            
            # 前端的标准化处理
            normalized_ids = [id.replace('frontend_', '') for id in active_zone_ids]
            print(f"     标准化后的ID: {normalized_ids}")
            
            # 模拟前端保护区元素
            frontend_zones = [
                'tokyo_station', 'shibuya_crossing', 'ginza_district', 
                'shinjuku_station', 'metro_exit'
            ]
            
            # 检查匹配情况
            matched_zones = []
            for zone_id in frontend_zones:
                is_active = zone_id in normalized_ids or zone_id in active_zone_ids
                status = "✓ 参与运算" if is_active else "○ 未参与运算"
                print(f"     {zone_id}: {status}")
                if is_active:
                    matched_zones.append(zone_id)
            
            return matched_zones
        
        # 测试改进算法
        improved_matched = simulate_frontend_update(improved_active_ids, "改进算法")
        
        # 测试基准算法
        baseline_matched = simulate_frontend_update(baseline_active_ids, "基准算法")
        
        # 8. 验证结果
        print(f"\n✅ 8. 验证结果")
        
        print(f"   检测到相关保护区: {len(relevant_zones)} 个")
        print(f"   参与运算的保护区: {len(collision_cost_breakdown)} 个")
        print(f"   改进算法匹配的保护区: {len(improved_matched)} 个")
        print(f"   基准算法匹配的保护区: {len(baseline_matched)} 个")
        
        if len(collision_cost_breakdown) > 0:
            print(f"   ✅ 保护区检测正常")
            print(f"   参与运算的保护区: {', '.join([info['zone_name'] for info in collision_cost_breakdown.values()])}")
            
            if len(improved_matched) == len(collision_cost_breakdown):
                print(f"   ✅ 改进算法前端匹配正常")
            else:
                print(f"   ⚠️ 改进算法前端匹配有问题")
            
            if len(baseline_matched) == len(collision_cost_breakdown):
                print(f"   ✅ 基准算法前端匹配正常")
            else:
                print(f"   ⚠️ 基准算法前端匹配有问题")
        else:
            print(f"   ❌ 没有检测到任何保护区参与运算")
            print(f"   可能原因：路径点不在任何保护区范围内")
        
        # 9. 生成调试信息
        print(f"\n🔧 9. 调试信息")
        
        print(f"   如果前端显示'○ 未参与运算'，可能的原因：")
        print(f"   1. updateProtectionZoneStatus 方法没有被调用")
        print(f"   2. 传递给方法的 activeZoneIds 是空数组")
        print(f"   3. ID格式不匹配（已修复）")
        print(f"   4. 后端没有正确生成保护区信息")
        
        print(f"\n   检查步骤：")
        print(f"   1. 在浏览器控制台查看是否有'🛡️ 更新保护区状态'的日志")
        print(f"   2. 检查传递的activeZoneIds是否包含期望的保护区ID")
        print(f"   3. 检查后端是否输出了保护区检测日志")
        
        return {
            'relevant_zones': len(relevant_zones),
            'active_zones': len(collision_cost_breakdown),
            'improved_matched': len(improved_matched),
            'baseline_matched': len(baseline_matched),
            'active_zone_names': [info['zone_name'] for info in collision_cost_breakdown.values()]
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_protection_zone_display()
    if result:
        print(f"\n🎉 保护区显示测试完成")
        print(f"   相关保护区: {result['relevant_zones']} 个")
        print(f"   活跃保护区: {result['active_zones']} 个")
        print(f"   改进算法匹配: {result['improved_matched']} 个")
        print(f"   基准算法匹配: {result['baseline_matched']} 个")
        
        if result['active_zones'] > 0:
            print(f"   参与运算的保护区: {', '.join(result['active_zone_names'])}")
            print(f"\n💡 如果前端仍显示'○ 未参与运算'，请检查：")
            print(f"   1. 浏览器控制台的保护区更新日志")
            print(f"   2. 后端是否正确传递了保护区信息")
            print(f"   3. 前端是否正确调用了updateProtectionZoneStatus方法")
        else:
            print(f"\n⚠️ 没有检测到活跃保护区，这可能是正常的")
            print(f"   如果路径不经过任何保护区，显示'○ 未参与运算'是正确的")
    else:
        print(f"\n❌ 保护区显示测试失败")
