[{"session_id": "20250730_193618", "step_number": 1, "timestamp": "2025-07-30T19:36:18.280067", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b23acd1b-bc59-4cb5-9dcd-f3f67c13bce0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b23acd1b-bc59-4cb5-9dcd-f3f67c13bce0", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250730_193618", "step_number": 2, "timestamp": "2025-07-30T19:36:18.286296", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b23acd1b-bc59-4cb5-9dcd-f3f67c13bce0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b23acd1b-bc59-4cb5-9dcd-f3f67c13bce0", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_193618", "step_number": 3, "timestamp": "2025-07-30T19:36:21.675993", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3384.1683864593506}, {"session_id": "20250730_193618", "step_number": 4, "timestamp": "2025-07-30T19:36:21.676990", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3385.1654529571533}, {"session_id": "20250730_193618", "step_number": 5, "timestamp": "2025-07-30T19:36:21.735975", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 57.98745155334473}, {"session_id": "20250730_193618", "step_number": 6, "timestamp": "2025-07-30T19:36:21.736972", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 58.985233306884766}, {"session_id": "20250730_195718", "step_number": 1, "timestamp": "2025-07-30T19:57:18.102506", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "eec02993-efa2-4a71-b568-704640fcb105", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "eec02993-efa2-4a71-b568-704640fcb105", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250730_195718", "step_number": 2, "timestamp": "2025-07-30T19:57:18.108485", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "eec02993-efa2-4a71-b568-704640fcb105", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "eec02993-efa2-4a71-b568-704640fcb105", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_195718", "step_number": 3, "timestamp": "2025-07-30T19:57:20.889982", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2778.4218788146973}, {"session_id": "20250730_195718", "step_number": 4, "timestamp": "2025-07-30T19:57:20.891083", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2779.522657394409}, {"session_id": "20250730_195718", "step_number": 5, "timestamp": "2025-07-30T19:57:20.926931", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.848379135131836}, {"session_id": "20250730_195718", "step_number": 6, "timestamp": "2025-07-30T19:57:20.927949", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.8654727935791}, {"session_id": "20250730_200913", "step_number": 1, "timestamp": "2025-07-30T20:09:13.580722", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8ba87af9-fd9e-4e1b-b51f-20113bda7f7a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8ba87af9-fd9e-4e1b-b51f-20113bda7f7a", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 105}}, "duration_ms": null}, {"session_id": "20250730_200913", "step_number": 2, "timestamp": "2025-07-30T20:09:13.595101", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8ba87af9-fd9e-4e1b-b51f-20113bda7f7a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8ba87af9-fd9e-4e1b-b51f-20113bda7f7a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_200913", "step_number": 3, "timestamp": "2025-07-30T20:09:16.666745", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3060.0454807281494}, {"session_id": "20250730_200913", "step_number": 4, "timestamp": "2025-07-30T20:09:16.667753", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3061.05375289917}, {"session_id": "20250730_200913", "step_number": 5, "timestamp": "2025-07-30T20:09:16.711447", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 42.68932342529297}, {"session_id": "20250730_200913", "step_number": 6, "timestamp": "2025-07-30T20:09:16.712461", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 43.70379447937012}, {"session_id": "20250730_201544", "step_number": 1, "timestamp": "2025-07-30T20:15:44.929347", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e3a6b957-4020-4e60-be6f-9dd50c0ac638", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e3a6b957-4020-4e60-be6f-9dd50c0ac638", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 104}}, "duration_ms": null}, {"session_id": "20250730_201544", "step_number": 2, "timestamp": "2025-07-30T20:15:44.937879", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e3a6b957-4020-4e60-be6f-9dd50c0ac638", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e3a6b957-4020-4e60-be6f-9dd50c0ac638", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_201544", "step_number": 3, "timestamp": "2025-07-30T20:15:48.121962", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3175.0638484954834}, {"session_id": "20250730_201544", "step_number": 4, "timestamp": "2025-07-30T20:15:48.123959", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3177.061080932617}, {"session_id": "20250730_201544", "step_number": 5, "timestamp": "2025-07-30T20:15:48.162970", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.00654411315918}, {"session_id": "20250730_201544", "step_number": 6, "timestamp": "2025-07-30T20:15:48.163976", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.01243209838867}, {"session_id": "20250730_203043", "step_number": 1, "timestamp": "2025-07-30T20:30:43.709902", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e369c59e-9d9b-429f-9b31-f8a77fb25c24", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e369c59e-9d9b-429f-9b31-f8a77fb25c24", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250730_203043", "step_number": 2, "timestamp": "2025-07-30T20:30:43.715615", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e369c59e-9d9b-429f-9b31-f8a77fb25c24", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e369c59e-9d9b-429f-9b31-f8a77fb25c24", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_203043", "step_number": 3, "timestamp": "2025-07-30T20:30:47.146188", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 3425.586223602295}, {"session_id": "20250730_203043", "step_number": 4, "timestamp": "2025-07-30T20:30:47.147201", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 3426.5995025634766}, {"session_id": "20250730_203043", "step_number": 5, "timestamp": "2025-07-30T20:30:47.167975", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.771099090576172}, {"session_id": "20250730_203043", "step_number": 6, "timestamp": "2025-07-30T20:30:47.169968", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.764516830444336}, {"session_id": "20250730_203811", "step_number": 1, "timestamp": "2025-07-30T20:38:11.783409", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "70632d2d-cad6-431f-91c1-e31cbc769de4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "70632d2d-cad6-431f-91c1-e31cbc769de4", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 2, "timestamp": "2025-07-30T20:38:11.790612", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "70632d2d-cad6-431f-91c1-e31cbc769de4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "70632d2d-cad6-431f-91c1-e31cbc769de4", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 3, "timestamp": "2025-07-30T20:38:11.834719", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 4, "timestamp": "2025-07-30T20:44:30.250017", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "dbeb1f21-b82c-4f26-800b-c5c8742efae3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "dbeb1f21-b82c-4f26-800b-c5c8742efae3", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 5, "timestamp": "2025-07-30T20:44:30.255498", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "dbeb1f21-b82c-4f26-800b-c5c8742efae3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "dbeb1f21-b82c-4f26-800b-c5c8742efae3", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 6, "timestamp": "2025-07-30T20:44:30.276507", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 7, "timestamp": "2025-07-30T20:46:14.634022", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "dd6ddf1c-e88f-4aba-9af0-ff9e09e8b469", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "dd6ddf1c-e88f-4aba-9af0-ff9e09e8b469", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 8, "timestamp": "2025-07-30T20:46:14.638671", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "dd6ddf1c-e88f-4aba-9af0-ff9e09e8b469", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "dd6ddf1c-e88f-4aba-9af0-ff9e09e8b469", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 9, "timestamp": "2025-07-30T20:46:14.666538", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 10, "timestamp": "2025-07-30T20:54:43.321705", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "83bde634-3d09-4986-8cdc-0ca4e1dadd9d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "83bde634-3d09-4986-8cdc-0ca4e1dadd9d", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 11, "timestamp": "2025-07-30T20:54:43.329695", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "83bde634-3d09-4986-8cdc-0ca4e1dadd9d", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "83bde634-3d09-4986-8cdc-0ca4e1dadd9d", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 12, "timestamp": "2025-07-30T20:54:43.354750", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 13, "timestamp": "2025-07-30T21:02:37.855292", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b4292365-c0e7-43ba-b6ec-46d296775585", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b4292365-c0e7-43ba-b6ec-46d296775585", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 109}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 14, "timestamp": "2025-07-30T21:02:37.866301", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b4292365-c0e7-43ba-b6ec-46d296775585", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b4292365-c0e7-43ba-b6ec-46d296775585", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 15, "timestamp": "2025-07-30T21:02:37.899109", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 16, "timestamp": "2025-07-30T21:08:54.800983", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "717582d2-5c5d-413b-b686-88d58b9da2be", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "717582d2-5c5d-413b-b686-88d58b9da2be", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 77}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 17, "timestamp": "2025-07-30T21:08:54.812474", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "717582d2-5c5d-413b-b686-88d58b9da2be", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "717582d2-5c5d-413b-b686-88d58b9da2be", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 18, "timestamp": "2025-07-30T21:08:54.858490", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 19, "timestamp": "2025-07-30T21:09:14.092586", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "395c5e06-edb1-49b2-8604-001f8dab17a9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "395c5e06-edb1-49b2-8604-001f8dab17a9", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 83}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 20, "timestamp": "2025-07-30T21:09:14.101611", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "395c5e06-edb1-49b2-8604-001f8dab17a9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "395c5e06-edb1-49b2-8604-001f8dab17a9", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 21, "timestamp": "2025-07-30T21:09:14.131519", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 22, "timestamp": "2025-07-30T21:10:02.403548", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "66b50758-851c-4d24-b839-f27e7739a0f8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "66b50758-851c-4d24-b839-f27e7739a0f8", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 82}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 23, "timestamp": "2025-07-30T21:10:02.412736", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "66b50758-851c-4d24-b839-f27e7739a0f8", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "66b50758-851c-4d24-b839-f27e7739a0f8", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 24, "timestamp": "2025-07-30T21:10:02.447661", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 25, "timestamp": "2025-07-30T21:11:01.751861", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "305bf083-a3f8-4781-a983-3bf051f97be1", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "305bf083-a3f8-4781-a983-3bf051f97be1", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 98}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 26, "timestamp": "2025-07-30T21:11:01.760310", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "305bf083-a3f8-4781-a983-3bf051f97be1", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "305bf083-a3f8-4781-a983-3bf051f97be1", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_203811", "step_number": 27, "timestamp": "2025-07-30T21:11:01.794327", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 1, "timestamp": "2025-07-30T21:11:47.979027", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a0ef7f08-953f-4165-8377-726a1a51abe7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a0ef7f08-953f-4165-8377-726a1a51abe7", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 70}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 2, "timestamp": "2025-07-30T21:11:47.998724", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a0ef7f08-953f-4165-8377-726a1a51abe7", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a0ef7f08-953f-4165-8377-726a1a51abe7", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 3, "timestamp": "2025-07-30T21:11:48.030578", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 4, "timestamp": "2025-07-30T21:12:18.256470", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ddf98c90-b60c-436d-a572-41f4056321cf", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ddf98c90-b60c-436d-a572-41f4056321cf", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 93}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 5, "timestamp": "2025-07-30T21:12:18.266945", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ddf98c90-b60c-436d-a572-41f4056321cf", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ddf98c90-b60c-436d-a572-41f4056321cf", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 6, "timestamp": "2025-07-30T21:12:18.319446", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 7, "timestamp": "2025-07-30T21:12:42.780912", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3b0c3b1a-d6bb-413b-8b7f-c2ae937ae3be", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3b0c3b1a-d6bb-413b-8b7f-c2ae937ae3be", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 79}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 8, "timestamp": "2025-07-30T21:12:42.793602", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3b0c3b1a-d6bb-413b-8b7f-c2ae937ae3be", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3b0c3b1a-d6bb-413b-8b7f-c2ae937ae3be", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 9, "timestamp": "2025-07-30T21:12:42.840220", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 10, "timestamp": "2025-07-30T21:13:16.860846", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "92a621af-37ad-454e-adab-33b50940f753", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "92a621af-37ad-454e-adab-33b50940f753", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 103}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 11, "timestamp": "2025-07-30T21:13:16.873665", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "92a621af-37ad-454e-adab-33b50940f753", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "92a621af-37ad-454e-adab-33b50940f753", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 12, "timestamp": "2025-07-30T21:13:16.907858", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 13, "timestamp": "2025-07-30T21:14:03.199472", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1187e5e8-f3a4-47b0-aa97-83c0cbad1a83", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1187e5e8-f3a4-47b0-aa97-83c0cbad1a83", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 66}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 14, "timestamp": "2025-07-30T21:14:03.210120", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1187e5e8-f3a4-47b0-aa97-83c0cbad1a83", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1187e5e8-f3a4-47b0-aa97-83c0cbad1a83", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 15, "timestamp": "2025-07-30T21:14:03.248994", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 16, "timestamp": "2025-07-30T21:14:29.981220", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bc6224d6-1136-4329-9c61-53760bc36f15", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bc6224d6-1136-4329-9c61-53760bc36f15", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 91}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 17, "timestamp": "2025-07-30T21:14:30.001194", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bc6224d6-1136-4329-9c61-53760bc36f15", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bc6224d6-1136-4329-9c61-53760bc36f15", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 18, "timestamp": "2025-07-30T21:14:30.028410", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 19, "timestamp": "2025-07-30T21:15:27.741939", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "2b62b42d-045a-479d-bfcb-6ee3d0c46a90", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "2b62b42d-045a-479d-bfcb-6ee3d0c46a90", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30, "buildings_count": 67}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 20, "timestamp": "2025-07-30T21:15:27.752064", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "2b62b42d-045a-479d-bfcb-6ee3d0c46a90", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "2b62b42d-045a-479d-bfcb-6ee3d0c46a90", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 21, "timestamp": "2025-07-30T21:15:27.774029", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 22, "timestamp": "2025-07-30T21:20:38.247416", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "27b8b248-0ecf-4eed-98d3-c0ed0c5e8baf", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "27b8b248-0ecf-4eed-98d3-c0ed0c5e8baf", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30, "buildings_count": 92}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 23, "timestamp": "2025-07-30T21:20:38.257039", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "27b8b248-0ecf-4eed-98d3-c0ed0c5e8baf", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "27b8b248-0ecf-4eed-98d3-c0ed0c5e8baf", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 24, "timestamp": "2025-07-30T21:20:38.280640", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 25, "timestamp": "2025-07-30T21:21:58.291878", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "935dbf50-e21a-46af-a4dc-4dbb86785dfa", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "935dbf50-e21a-46af-a4dc-4dbb86785dfa", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30, "buildings_count": 71}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 26, "timestamp": "2025-07-30T21:21:58.300685", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "935dbf50-e21a-46af-a4dc-4dbb86785dfa", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "935dbf50-e21a-46af-a4dc-4dbb86785dfa", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250730_211147", "step_number": 27, "timestamp": "2025-07-30T21:21:58.353061", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: NameError: name 'verbose_logging' is not defined", "algorithm": null, "request_id": null, "details": {"error_type": "NameError", "error_message": "name 'verbose_logging' is not defined", "context": "初始路径集生成"}, "duration_ms": null}, {"session_id": "20250730_212401", "step_number": 1, "timestamp": "2025-07-30T21:24:01.997762", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5675def1-05ed-42a3-85db-cc25ee58ef75", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5675def1-05ed-42a3-85db-cc25ee58ef75", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 84}}, "duration_ms": null}, {"session_id": "20250730_212401", "step_number": 2, "timestamp": "2025-07-30T21:24:02.005638", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5675def1-05ed-42a3-85db-cc25ee58ef75", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5675def1-05ed-42a3-85db-cc25ee58ef75", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250730_212401", "step_number": 3, "timestamp": "2025-07-30T21:24:04.513528", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2497.213125228882}, {"session_id": "20250730_212401", "step_number": 4, "timestamp": "2025-07-30T21:24:04.515520", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2499.204635620117}, {"session_id": "20250730_212401", "step_number": 5, "timestamp": "2025-07-30T21:24:04.535582", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.515897750854492}, {"session_id": "20250730_212401", "step_number": 6, "timestamp": "2025-07-30T21:24:04.538110", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.043373107910156}]