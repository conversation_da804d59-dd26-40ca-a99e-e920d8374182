#!/usr/bin/env python3
"""
简单路径导出器
提供基本的路径数据导出功能，避免ModuleNotFoundError
"""

import json
import csv
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

def export_comparison_data(comparison_data: Dict[str, Any]) -> Optional[str]:
    """
    导出算法对比数据到CSV文件
    
    Args:
        comparison_data: 算法对比数据
        
    Returns:
        str: 导出的文件路径，失败时返回None
    """
    try:
        print("📊 开始导出算法对比数据...")
        
        # 创建导出目录
        export_dir = "csv"
        os.makedirs(export_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"algorithm_comparison_{timestamp}.csv"
        filepath = os.path.join(export_dir, filename)
        
        # 提取数据
        improved_data = comparison_data.get('improved', {})
        baseline_data = comparison_data.get('baseline', {})
        
        # 准备CSV数据
        csv_data = []
        
        # 添加改进算法数据
        if improved_data:
            csv_data.append({
                'algorithm': 'Improved',
                'path_length': improved_data.get('path_length', 0),
                'turning_cost': improved_data.get('turning_cost', 0),
                'risk_value': improved_data.get('risk_value', 0),
                'collision_cost': improved_data.get('collision_cost', 0),
                'final_cost': improved_data.get('final_cost', 0),
                'execution_time': improved_data.get('execution_time', 0),
                'waypoint_count': improved_data.get('waypoint_count', 0),
                'success': improved_data.get('success', False)
            })
        
        # 添加基准算法数据
        if baseline_data:
            csv_data.append({
                'algorithm': 'Baseline',
                'path_length': baseline_data.get('path_length', 0),
                'turning_cost': baseline_data.get('turning_cost', 0),
                'risk_value': baseline_data.get('risk_value', 0),
                'collision_cost': baseline_data.get('collision_cost', 0),
                'final_cost': baseline_data.get('final_cost', 0),
                'execution_time': baseline_data.get('execution_time', 0),
                'waypoint_count': baseline_data.get('waypoint_count', 0),
                'success': baseline_data.get('success', False)
            })
        
        # 写入CSV文件
        if csv_data:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['algorithm', 'path_length', 'turning_cost', 'risk_value', 
                             'collision_cost', 'final_cost', 'execution_time', 
                             'waypoint_count', 'success']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for row in csv_data:
                    writer.writerow(row)
            
            print(f"✅ 算法对比数据已导出到: {filepath}")
            return filepath
        else:
            print("⚠️ 没有可导出的数据")
            return None
            
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return None

def export_path_data(path_data: List[Dict[str, Any]], filename_prefix: str = "path_data") -> Optional[str]:
    """
    导出路径数据到CSV文件
    
    Args:
        path_data: 路径数据列表
        filename_prefix: 文件名前缀
        
    Returns:
        str: 导出的文件路径，失败时返回None
    """
    try:
        print(f"📊 开始导出路径数据，共{len(path_data)}条路径...")
        
        # 创建导出目录
        export_dir = "csv"
        os.makedirs(export_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{filename_prefix}_{timestamp}.csv"
        filepath = os.path.join(export_dir, filename)
        
        # 写入CSV文件
        if path_data:
            # 获取所有可能的字段
            all_fields = set()
            for path in path_data:
                all_fields.update(path.keys())
            
            fieldnames = sorted(list(all_fields))
            
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for path in path_data:
                    writer.writerow(path)
            
            print(f"✅ 路径数据已导出到: {filepath}")
            return filepath
        else:
            print("⚠️ 没有可导出的路径数据")
            return None
            
    except Exception as e:
        print(f"❌ 路径数据导出失败: {e}")
        return None

def export_waypoints_data(waypoints: List[Dict[str, Any]], filename_prefix: str = "waypoints") -> Optional[str]:
    """
    导出航点数据到CSV文件
    
    Args:
        waypoints: 航点数据列表
        filename_prefix: 文件名前缀
        
    Returns:
        str: 导出的文件路径，失败时返回None
    """
    try:
        print(f"📊 开始导出航点数据，共{len(waypoints)}个航点...")
        
        # 创建导出目录
        export_dir = "csv"
        os.makedirs(export_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{filename_prefix}_{timestamp}.csv"
        filepath = os.path.join(export_dir, filename)
        
        # 写入CSV文件
        if waypoints:
            fieldnames = ['index', 'lng', 'lat', 'alt', 'path_id']
            
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for i, waypoint in enumerate(waypoints):
                    row = {
                        'index': i,
                        'lng': waypoint.get('lng', 0),
                        'lat': waypoint.get('lat', 0),
                        'alt': waypoint.get('alt', 0),
                        'path_id': waypoint.get('path_id', f'waypoint_{i}')
                    }
                    writer.writerow(row)
            
            print(f"✅ 航点数据已导出到: {filepath}")
            return filepath
        else:
            print("⚠️ 没有可导出的航点数据")
            return None
            
    except Exception as e:
        print(f"❌ 航点数据导出失败: {e}")
        return None

# 为了向后兼容，提供一些别名
def export_data(data: Dict[str, Any]) -> Optional[str]:
    """导出数据的通用接口"""
    return export_comparison_data(data)

def main():
    """测试函数"""
    print("🧪 简单路径导出器测试")
    
    # 测试数据
    test_data = {
        'improved': {
            'path_length': 1500.5,
            'turning_cost': 0.8,
            'risk_value': 12.3,
            'collision_cost': 45.6,
            'final_cost': 0.25,
            'execution_time': 2.1,
            'waypoint_count': 25,
            'success': True
        },
        'baseline': {
            'path_length': 1800.2,
            'turning_cost': 1.2,
            'risk_value': 8.7,
            'collision_cost': 52.1,
            'final_cost': 0.31,
            'execution_time': 1.5,
            'waypoint_count': 20,
            'success': True
        }
    }
    
    # 测试导出
    result = export_comparison_data(test_data)
    if result:
        print(f"✅ 测试成功，文件已保存到: {result}")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
