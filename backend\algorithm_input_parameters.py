#!/usr/bin/env python3
"""
算法输入参数规范定义
定义所有算法组件需要的输入参数，变量名不允许变动
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum


class ParameterType(Enum):
    """参数类型枚举"""
    REQUIRED = "required"      # 必需参数
    OPTIONAL = "optional"      # 可选参数
    COMPUTED = "computed"      # 计算得出的参数


@dataclass
class ParameterDefinition:
    """参数定义"""
    name: str                          # 参数名称（不可变）
    type: type                         # 参数类型
    parameter_type: ParameterType      # 参数分类
    default_value: Any = None          # 默认值
    description: str = ""              # 参数描述
    unit: str = ""                     # 单位
    valid_range: Optional[tuple] = None # 有效范围
    example: Any = None                # 示例值


class AlgorithmInputParameters:
    """算法输入参数规范类 - 所有变量名不允许变动"""
    
    # ==================== 基础路径规划参数 ====================
    PATH_PLANNING_PARAMS = {
        # 起点参数（必需）
        "start_point": ParameterDefinition(
            name="start_point",
            type=dict,
            parameter_type=ParameterType.REQUIRED,
            description="起始点坐标",
            example={"lng": 116.404, "lat": 39.915, "alt": 1.0, "x": 0, "y": 0, "z": 1.0}
        ),
        
        # 终点参数（必需）
        "end_point": ParameterDefinition(
            name="end_point", 
            type=dict,
            parameter_type=ParameterType.REQUIRED,
            description="目标点坐标",
            example={"lng": 116.504, "lat": 39.815, "alt": 1.0, "x": 1000, "y": 800, "z": 1.0}
        ),
        
        # 飞行高度（必需）
        "flight_height": ParameterDefinition(
            name="flight_height",
            type=float,
            parameter_type=ParameterType.REQUIRED,
            default_value=70.0,  # 修改默认飞行高度为70米（适合城市环境）
            description="飞行高度",
            unit="m",
            valid_range=(10.0, 500.0),
            example=70.0  # 修改示例值为70米
        ),
        
        # 安全距离（可选）
        "safety_distance": ParameterDefinition(
            name="safety_distance",
            type=float,
            parameter_type=ParameterType.OPTIONAL,
            default_value=30.0,
            description="安全距离",
            unit="m",
            valid_range=(5.0, 100.0),
            example=30.0
        ),
        
        # 最大转向角度（可选）
        "max_turn_angle": ParameterDefinition(
            name="max_turn_angle",
            type=float,
            parameter_type=ParameterType.OPTIONAL,
            default_value=90.0,
            description="最大转向角度",
            unit="degree",
            valid_range=(30.0, 180.0),
            example=90.0
        )
    }
    
    # ==================== 改进算法专用参数 ====================
    IMPROVED_ALGORITHM_PARAMS = {
        # 风险边缘距离（公式5-7使用）
        "risk_edge_distance": ParameterDefinition(
            name="risk_edge_distance",
            type=float,
            parameter_type=ParameterType.OPTIONAL,
            default_value=50.0,
            description="风险边缘距离",
            unit="m",
            valid_range=(10.0, 200.0),
            example=50.0
        ),
        
        # k值（公式15动态权重使用）
        "k_value": ParameterDefinition(
            name="k_value",
            type=float,
            parameter_type=ParameterType.OPTIONAL,
            default_value=5.0,
            description="指数变化速率控制参数",
            unit="",
            valid_range=(1.0, 10.0),
            example=5.0
        ),
        
        # 是否启用路径换路
        "enable_path_switching": ParameterDefinition(
            name="enable_path_switching",
            type=bool,
            parameter_type=ParameterType.OPTIONAL,
            default_value=True,
            description="是否启用动态换路",
            example=True
        ),
        
        # 碰撞阈值（换路触发条件）
        "collision_threshold": ParameterDefinition(
            name="collision_threshold",
            type=float,
            parameter_type=ParameterType.OPTIONAL,
            default_value=20.0,
            description="碰撞代价异常阈值",
            unit="%",
            valid_range=(10.0, 50.0),
            example=20.0
        ),
        
        # 连续检查航点数
        "consecutive_waypoints_check": ParameterDefinition(
            name="consecutive_waypoints_check",
            type=int,
            parameter_type=ParameterType.OPTIONAL,
            default_value=5,
            description="连续检查的航点数量",
            unit="个",
            valid_range=(3, 10),
            example=5
        )
    }
    
    # ==================== 环境数据参数 ====================
    ENVIRONMENT_PARAMS = {
        # 建筑物数据
        "buildings": ParameterDefinition(
            name="buildings",
            type=list,
            parameter_type=ParameterType.OPTIONAL,
            default_value=[],
            description="建筑物障碍物列表",
            example=[{"x": 200, "y": 150, "height": 80, "radius": 30}]
        ),
        
        # 保护区数据
        "protection_zones": ParameterDefinition(
            name="protection_zones",
            type=list,
            parameter_type=ParameterType.OPTIONAL,
            default_value=[],
            description="保护区列表",
            example=[{
                "zone_id": "zone_1",
                "zone_type": "vehicle",
                "polygon_points": [],
                "collision_cost_density": 15.0
            }]
        ),
        
        # 天气条件
        "weather_condition": ParameterDefinition(
            name="weather_condition",
            type=str,
            parameter_type=ParameterType.OPTIONAL,
            default_value="clear",
            description="天气条件",
            example="clear"
        ),
        
        # 时间条件
        "time_of_day": ParameterDefinition(
            name="time_of_day",
            type=str,
            parameter_type=ParameterType.OPTIONAL,
            default_value="day",
            description="时间条件",
            example="day"
        ),
        
        # 能见度
        "visibility": ParameterDefinition(
            name="visibility",
            type=float,
            parameter_type=ParameterType.OPTIONAL,
            default_value=1.0,
            description="能见度",
            unit="",
            valid_range=(0.1, 1.0),
            example=0.9
        )
    }
    
    # ==================== A*算法参数 ====================
    ASTAR_PARAMS = {
        # 最大迭代次数
        "max_iterations": ParameterDefinition(
            name="max_iterations",
            type=int,
            parameter_type=ParameterType.OPTIONAL,
            default_value=1000,
            description="A*算法最大迭代次数",
            unit="次",
            valid_range=(100, 10000),
            example=1000
        ),
        
        # 步长
        "step_size": ParameterDefinition(
            name="step_size",
            type=float,
            parameter_type=ParameterType.OPTIONAL,
            default_value=10.0,
            description="A*算法步长",
            unit="m",
            valid_range=(1.0, 50.0),
            example=10.0
        ),
        
        # 启发式权重
        "heuristic_weight": ParameterDefinition(
            name="heuristic_weight",
            type=float,
            parameter_type=ParameterType.OPTIONAL,
            default_value=1.0,
            description="启发式函数权重",
            unit="",
            valid_range=(0.1, 5.0),
            example=1.0
        )
    }
    
    # ==================== 路径平滑参数 ====================
    SMOOTHING_PARAMS = {
        # 平滑因子
        "smoothing_factor": ParameterDefinition(
            name="smoothing_factor",
            type=float,
            parameter_type=ParameterType.OPTIONAL,
            default_value=0.3,
            description="路径平滑因子",
            unit="",
            valid_range=(0.1, 1.0),
            example=0.3
        ),
        
        # 插值点数
        "interpolation_points": ParameterDefinition(
            name="interpolation_points",
            type=int,
            parameter_type=ParameterType.OPTIONAL,
            default_value=50,
            description="插值点数量",
            unit="个",
            valid_range=(20, 200),
            example=50
        ),
        
        # 最大曲率
        "max_curvature": ParameterDefinition(
            name="max_curvature",
            type=float,
            parameter_type=ParameterType.OPTIONAL,
            default_value=0.1,
            description="最大允许曲率",
            unit="1/m",
            valid_range=(0.01, 1.0),
            example=0.1
        )
    }
    
    @classmethod
    def get_all_parameters(cls) -> Dict[str, ParameterDefinition]:
        """获取所有参数定义"""
        all_params = {}
        all_params.update(cls.PATH_PLANNING_PARAMS)
        all_params.update(cls.IMPROVED_ALGORITHM_PARAMS)
        all_params.update(cls.ENVIRONMENT_PARAMS)
        all_params.update(cls.ASTAR_PARAMS)
        all_params.update(cls.SMOOTHING_PARAMS)
        return all_params
    
    @classmethod
    def get_required_parameters(cls) -> Dict[str, ParameterDefinition]:
        """获取必需参数"""
        all_params = cls.get_all_parameters()
        return {name: param for name, param in all_params.items() 
                if param.parameter_type == ParameterType.REQUIRED}
    
    @classmethod
    def get_optional_parameters(cls) -> Dict[str, ParameterDefinition]:
        """获取可选参数"""
        all_params = cls.get_all_parameters()
        return {name: param for name, param in all_params.items() 
                if param.parameter_type == ParameterType.OPTIONAL}
    
    @classmethod
    def validate_parameter(cls, name: str, value: Any) -> tuple[bool, str]:
        """验证参数值"""
        all_params = cls.get_all_parameters()
        
        if name not in all_params:
            return False, f"未知参数: {name}"
        
        param_def = all_params[name]
        
        # 类型检查
        if not isinstance(value, param_def.type):
            return False, f"参数 {name} 类型错误，期望 {param_def.type.__name__}，实际 {type(value).__name__}"
        
        # 范围检查
        if param_def.valid_range and isinstance(value, (int, float)):
            min_val, max_val = param_def.valid_range
            if not (min_val <= value <= max_val):
                return False, f"参数 {name} 超出有效范围 [{min_val}, {max_val}]"
        
        return True, "参数验证通过"
    
    @classmethod
    def get_default_values(cls) -> Dict[str, Any]:
        """获取所有参数的默认值"""
        all_params = cls.get_all_parameters()
        return {name: param.default_value for name, param in all_params.items() 
                if param.default_value is not None}
    
    @classmethod
    def get_parameter_info(cls, name: str) -> Optional[ParameterDefinition]:
        """获取指定参数的详细信息"""
        all_params = cls.get_all_parameters()
        return all_params.get(name)


# 导出常量 - 这些变量名绝对不允许变动
ALGORITHM_INPUT_PARAMETER_NAMES = {
    # 基础参数
    "START_POINT": "start_point",
    "END_POINT": "end_point", 
    "FLIGHT_HEIGHT": "flight_height",
    "SAFETY_DISTANCE": "safety_distance",
    "MAX_TURN_ANGLE": "max_turn_angle",
    
    # 改进算法参数
    "RISK_EDGE_DISTANCE": "risk_edge_distance",
    "K_VALUE": "k_value",
    "ENABLE_PATH_SWITCHING": "enable_path_switching",
    "COLLISION_THRESHOLD": "collision_threshold",
    "CONSECUTIVE_WAYPOINTS_CHECK": "consecutive_waypoints_check",
    
    # 环境参数
    "BUILDINGS": "buildings",
    "PROTECTION_ZONES": "protection_zones",
    "WEATHER_CONDITION": "weather_condition",
    "TIME_OF_DAY": "time_of_day",
    "VISIBILITY": "visibility",
    
    # A*参数
    "MAX_ITERATIONS": "max_iterations",
    "STEP_SIZE": "step_size",
    "HEURISTIC_WEIGHT": "heuristic_weight",
    
    # 平滑参数
    "SMOOTHING_FACTOR": "smoothing_factor",
    "INTERPOLATION_POINTS": "interpolation_points",
    "MAX_CURVATURE": "max_curvature"
}


if __name__ == "__main__":
    # 测试参数定义
    params = AlgorithmInputParameters()
    
    print("=== 算法输入参数规范 ===")
    print(f"总参数数量: {len(params.get_all_parameters())}")
    print(f"必需参数数量: {len(params.get_required_parameters())}")
    print(f"可选参数数量: {len(params.get_optional_parameters())}")
    
    print("\n=== 必需参数列表 ===")
    for name, param in params.get_required_parameters().items():
        print(f"  {name}: {param.description}")
    
    print("\n=== 参数验证测试 ===")
    test_cases = [
        ("flight_height", 100.0),
        ("flight_height", "invalid"),
        ("k_value", 5.0),
        ("k_value", 15.0)  # 超出范围
    ]
    
    for param_name, value in test_cases:
        valid, message = params.validate_parameter(param_name, value)
        print(f"  {param_name}={value}: {'✅' if valid else '❌'} {message}")
