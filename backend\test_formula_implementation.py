#!/usr/bin/env python3
"""
测试公式实现的正确性
验证各指标计算是否按公式正确执行
"""

import math
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from algorithms.improved_cluster_pathfinding import CostCalculator, ImprovedPathPoint
from algorithms.data_structures import Point3D
from protection_zones import ProtectionZoneManager

def test_manhattan_distance():
    """测试曼哈顿距离计算 - 公式2"""
    print("🔍 测试曼哈顿距离计算（公式2）")
    print("=" * 50)
    
    config = {'maxTurnAngle': 90, 'riskEdgeDistance': 50, 'kValue': 5}
    calculator = CostCalculator(config)
    
    # 创建测试点
    start_point = ImprovedPathPoint(x=0, y=0, z=100)
    end_point = ImprovedPathPoint(x=100, y=200, z=150)
    
    # 计算曼哈顿距离
    manhattan_distance = calculator.calculate_manhattan_length(start_point, end_point)
    
    # 验证公式：Length_manhattan = 3 * (|x_end - x_start| + |y_end - y_start| + |z_end - z_start|)
    expected = 3.0 * (abs(100-0) + abs(200-0) + abs(150-100))
    expected = 3.0 * (100 + 200 + 50)
    expected = 3.0 * 350
    expected = 1050.0
    
    print(f"起点: ({start_point.x}, {start_point.y}, {start_point.z})")
    print(f"终点: ({end_point.x}, {end_point.y}, {end_point.z})")
    print(f"计算结果: {manhattan_distance}")
    print(f"预期结果: {expected}")
    print(f"测试结果: {'✅ 通过' if abs(manhattan_distance - expected) < 0.001 else '❌ 失败'}")
    print()

def test_turning_cost_reference():
    """测试转向成本参考值计算 - 公式4"""
    print("🔍 测试转向成本参考值计算（公式4）")
    print("=" * 50)
    
    config = {'maxTurnAngle': 90, 'riskEdgeDistance': 50, 'kValue': 5}
    calculator = CostCalculator(config)
    
    # 测试不同航点数量
    test_cases = [
        (5, "5个航点"),
        (10, "10个航点"),
        (20, "20个航点")
    ]
    
    for n_waypoints, description in test_cases:
        result = calculator.calculate_turning_cost_reference(n_waypoints)
        
        # 验证公式：OrientAdjustCost_reference = 0.3 * (n - 2) * Δθ_max
        # 其中Δθ_max = 45度 = π/4 弧度
        delta_theta_max = math.radians(45.0)
        expected = 0.3 * (n_waypoints - 2) * delta_theta_max
        
        print(f"{description}:")
        print(f"  计算结果: {result:.6f}")
        print(f"  预期结果: {expected:.6f}")
        print(f"  测试结果: {'✅ 通过' if abs(result - expected) < 0.000001 else '❌ 失败'}")
    print()

def test_fixed_weights():
    """测试固定权重设置"""
    print("🔍 测试固定权重设置")
    print("=" * 50)
    
    config = {'maxTurnAngle': 90, 'riskEdgeDistance': 50, 'kValue': 5}
    calculator = CostCalculator(config)
    
    weights = calculator.calculate_fixed_weights()
    
    # 验证权重值
    expected_weights = {
        'alpha': 0.5,   # 风险权重
        'beta': 0.4,    # 碰撞权重  
        'gamma': 0.05,  # 长度权重
        'delta': 0.05   # 转向权重
    }
    
    print("权重测试:")
    all_passed = True
    for key, expected_value in expected_weights.items():
        actual_value = weights[key]
        passed = abs(actual_value - expected_value) < 0.000001
        all_passed = all_passed and passed
        print(f"  {key}: {actual_value} (预期: {expected_value}) {'✅' if passed else '❌'}")
    
    # 验证权重归一化
    weight_sum = weights['alpha'] + weights['beta'] + weights['gamma'] + weights['delta']
    sum_passed = abs(weight_sum - 1.0) < 0.000001
    all_passed = all_passed and sum_passed
    
    print(f"  权重总和: {weight_sum} (预期: 1.0) {'✅' if sum_passed else '❌'}")
    print(f"总体测试结果: {'✅ 通过' if all_passed else '❌ 失败'}")
    print()

def test_protection_zones():
    """测试保护区设计"""
    print("🔍 测试保护区设计")
    print("=" * 50)
    
    # 初始化保护区管理器
    protection_manager = ProtectionZoneManager()
    
    zones = protection_manager.zones
    print(f"保护区总数: {len(zones)}")
    
    if len(zones) >= 9:  # 确保有足够的保护区
        print("✅ 保护区数量充足")
    else:
        print("❌ 保护区数量不足")
    
    # 计算碰撞代价统计
    total_collision_cost = sum(zone.collision_cost_factor for zone in zones)
    avg_collision_cost = total_collision_cost / len(zones) if zones else 0
    
    print(f"总碰撞代价因子: {total_collision_cost:.2f}")
    print(f"平均碰撞代价因子: {avg_collision_cost:.2f}")
    
    # 测试碰撞代价参考值计算
    config = {'maxTurnAngle': 90, 'riskEdgeDistance': 50, 'kValue': 5}
    calculator = CostCalculator(config)
    
    # 创建模拟保护区数据
    from algorithms.improved_cluster_pathfinding import LegacyProtectionZone
    from algorithms.data_structures import Point3D
    
    legacy_zones = []
    for zone in zones[:5]:  # 使用前5个保护区进行测试
        legacy_zone = LegacyProtectionZone(
            zone_id=zone.id,
            zone_type=zone.zone_type.value,
            polygon_points=[
                Point3D(zone.center[0]-0.001, zone.center[1]-0.001, 0),
                Point3D(zone.center[0]+0.001, zone.center[1]-0.001, 0),
                Point3D(zone.center[0]+0.001, zone.center[1]+0.001, 0),
                Point3D(zone.center[0]-0.001, zone.center[1]+0.001, 0)
            ],
            collision_cost_density=zone.collision_cost_factor
        )
        legacy_zones.append(legacy_zone)
    
    # 测试碰撞代价参考值计算 - 🔧 更新：去掉航点数量参数
    reference_value = calculator.calculate_collision_cost_reference(legacy_zones)

    # 验证公式：RoadCrashCost_reference = (∑AverageCrashCost / ProtectzoneNum) * 300 (去掉n)
    total_avg_cost = sum(zone.collision_cost_density for zone in legacy_zones)
    expected_reference = (total_avg_cost / len(legacy_zones)) * 300

    print(f"碰撞代价参考值计算（更新公式）:")
    print(f"  计算结果: {reference_value:.4f}")
    print(f"  预期结果: {expected_reference:.4f}")
    print(f"  测试结果: {'✅ 通过' if abs(reference_value - expected_reference) < 0.001 else '❌ 失败'}")
    print(f"  说明: 已去掉航点数量n的影响")
    print()

def main():
    """主测试函数"""
    print("🚀 开始测试公式实现的正确性")
    print("=" * 60)
    print()
    
    try:
        # 测试各个公式实现
        test_manhattan_distance()
        test_turning_cost_reference()
        test_fixed_weights()
        test_protection_zones()
        
        print("🎉 所有测试完成！")
        print("请检查上述测试结果，确保所有公式都按要求正确实现。")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
