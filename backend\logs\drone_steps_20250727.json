[{"session_id": "20250727_073823", "step_number": 1, "timestamp": "2025-07-27T07:38:23.543793", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: TestAlgorithm", "algorithm": "TestAlgorithm", "request_id": "test_001", "details": {"algorithm_name": "TestAlgorithm", "request_id": "test_001", "parameters": {"start_point": "(0, 0, 100)", "end_point": "(1000, 1000, 100)", "flight_height": 100}}, "duration_ms": null}, {"session_id": "20250727_073823", "step_number": 2, "timestamp": "2025-07-27T07:38:23.652502", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": "TestAlgorithm", "request_id": "test_001", "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本"}}, "duration_ms": 150.5}, {"session_id": "20250727_073823", "step_number": 3, "timestamp": "2025-07-27T07:38:23.760365", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": "TestAlgorithm", "request_id": "test_001", "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 75.2}, {"session_id": "20250727_073823", "step_number": 4, "timestamp": "2025-07-27T07:38:23.853475", "step_type": "代价计算", "level": "INFO", "message": "路径最终代价计算完成: 0.234567", "algorithm": "TestAlgorithm", "request_id": "test_001", "details": {"calculation_type": "路径最终代价", "cost_value": 0.234567}, "duration_ms": 25.8}, {"session_id": "20250727_073823", "step_number": 5, "timestamp": "2025-07-27T07:38:23.963646", "step_type": "路径切换", "level": "INFO", "message": "路径切换: Path_1_1 -> Path_2_3", "algorithm": "TestAlgorithm", "request_id": "test_001", "details": {"from_path": "Path_1_1", "to_path": "Path_2_3", "reason": "检测到局部最优陷阱"}, "duration_ms": 45.3}, {"session_id": "20250727_073823", "step_number": 6, "timestamp": "2025-07-27T07:38:23.964644", "step_type": "API请求", "level": "INFO", "message": "POST /api/pathplanning/calculate", "algorithm": "TestAlgorithm", "request_id": "test_001", "details": {"endpoint": "/api/pathplanning/calculate", "method": "POST", "parameters": {"algorithm": "ImprovedClusterBased"}}, "duration_ms": null}, {"session_id": "20250727_073823", "step_number": 7, "timestamp": "2025-07-27T07:38:24.072388", "step_type": "API响应", "level": "INFO", "message": "/api/pathplanning/calculate 响应: 200", "algorithm": "TestAlgorithm", "request_id": "test_001", "details": {"endpoint": "/api/pathplanning/calculate", "status_code": 200, "response_size": 1024}, "duration_ms": 320.5}, {"session_id": "20250727_073823", "step_number": 8, "timestamp": "2025-07-27T07:38:24.074707", "step_type": "错误处理", "level": "ERROR", "message": "发生错误: ValueError: 这是一个测试错误", "algorithm": "TestAlgorithm", "request_id": "test_001", "details": {"error_type": "ValueError", "error_message": "这是一个测试错误", "traceback": "Traceback (most recent call last):\n  File \"E:\\编程\\无人机建模python版723\\backend\\test_logger.py\", line 80, in test_logger\n    raise ValueError(\"这是一个测试错误\")\nValueError: 这是一个测试错误\n", "context": "测试错误处理"}, "duration_ms": null}, {"session_id": "20250727_073823", "step_number": 9, "timestamp": "2025-07-27T07:38:24.074707", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: TestAlgorithm", "algorithm": "TestAlgorithm", "request_id": "test_001", "details": {"algorithm_name": "TestAlgorithm", "request_id": "test_001", "success": true, "result": {"path_points_count": 15, "total_time_ms": 620.3, "algorithm_name": "TestAlgorithm"}}, "duration_ms": null}, {"session_id": "20250727_081552", "step_number": 1, "timestamp": "2025-07-27T08:15:52.998569", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: TestAlgorithm_Direct", "algorithm": "TestAlgorithm_Direct", "request_id": "test_direct_001", "details": {"algorithm_name": "TestAlgorithm_Direct", "request_id": "test_direct_001", "parameters": {"start_point": "(0, 0, 100)", "end_point": "(1000, 1000, 100)", "flight_height": 120}}, "duration_ms": null}, {"session_id": "20250727_081552", "step_number": 2, "timestamp": "2025-07-27T08:15:52.999571", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": "TestAlgorithm_Direct", "request_id": "test_direct_001", "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "直接测试", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 150.5}, {"session_id": "20250727_081552", "step_number": 3, "timestamp": "2025-07-27T08:15:53.000583", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": "TestAlgorithm_Direct", "request_id": "test_direct_001", "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 75.2}, {"session_id": "20250727_081552", "step_number": 4, "timestamp": "2025-07-27T08:15:53.000583", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: TestAlgorithm_Direct", "algorithm": "TestAlgorithm_Direct", "request_id": "test_direct_001", "details": {"algorithm_name": "TestAlgorithm_Direct", "request_id": "test_direct_001", "success": true, "result": {"path_points_count": 80, "total_time_ms": 5000, "algorithm_name": "TestAlgorithm_Direct"}}, "duration_ms": null}, {"session_id": "20250727_100650", "step_number": 1, "timestamp": "2025-07-27T10:06:50.367027", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8d9e1ecf-3fed-46fa-bb48-46355ee20042", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8d9e1ecf-3fed-46fa-bb48-46355ee20042", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-716.8670488895516, 1821.310546062591, 1)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_100650", "step_number": 2, "timestamp": "2025-07-27T10:06:50.372127", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "8d9e1ecf-3fed-46fa-bb48-46355ee20042", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "8d9e1ecf-3fed-46fa-bb48-46355ee20042", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-716.8670488895516, 1821.310546062591, 1)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_100650", "step_number": 3, "timestamp": "2025-07-27T10:06:50.639489", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 263.64827156066895}, {"session_id": "20250727_100650", "step_number": 4, "timestamp": "2025-07-27T10:06:50.639489", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 263.64827156066895}, {"session_id": "20250727_100650", "step_number": 5, "timestamp": "2025-07-27T10:06:50.652789", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.82866096496582}, {"session_id": "20250727_100650", "step_number": 6, "timestamp": "2025-07-27T10:06:50.654834", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.87476921081543}, {"session_id": "20250727_102735", "step_number": 1, "timestamp": "2025-07-27T10:27:35.810419", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "eaba29fe-c327-478b-be5a-c18fdb44682a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "eaba29fe-c327-478b-be5a-c18fdb44682a", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-460.8420308055823, 1037.3278526686502, 1)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_102735", "step_number": 2, "timestamp": "2025-07-27T10:27:35.814406", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "eaba29fe-c327-478b-be5a-c18fdb44682a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "eaba29fe-c327-478b-be5a-c18fdb44682a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-460.8420308055823, 1037.3278526686502, 1)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_102735", "step_number": 3, "timestamp": "2025-07-27T10:27:36.092641", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 276.19147300720215}, {"session_id": "20250727_102735", "step_number": 4, "timestamp": "2025-07-27T10:27:36.092641", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 276.19147300720215}, {"session_id": "20250727_102735", "step_number": 5, "timestamp": "2025-07-27T10:27:36.104241", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.089874267578125}, {"session_id": "20250727_102735", "step_number": 6, "timestamp": "2025-07-27T10:27:36.106310", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.159109115600586}, {"session_id": "20250727_104606", "step_number": 1, "timestamp": "2025-07-27T10:46:06.762187", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "41fca714-042e-48e4-879b-ea6d08ae4173", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "41fca714-042e-48e4-879b-ea6d08ae4173", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-801.1061550134896, 1586.68724745007, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 70}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 2, "timestamp": "2025-07-27T10:46:06.774078", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "41fca714-042e-48e4-879b-ea6d08ae4173", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "41fca714-042e-48e4-879b-ea6d08ae4173", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-801.1061550134896, 1586.68724745007, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 3, "timestamp": "2025-07-27T10:46:07.153363", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 371.43492698669434}, {"session_id": "20250727_104606", "step_number": 4, "timestamp": "2025-07-27T10:46:07.155404", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 373.4755516052246}, {"session_id": "20250727_104606", "step_number": 5, "timestamp": "2025-07-27T10:46:07.169759", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 14.355182647705078}, {"session_id": "20250727_104606", "step_number": 6, "timestamp": "2025-07-27T10:46:07.172951", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.547130584716797}, {"session_id": "20250727_104606", "step_number": 7, "timestamp": "2025-07-27T10:51:57.468416", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6bf1f12e-3daa-471e-bf67-50ba026a3a83", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6bf1f12e-3daa-471e-bf67-50ba026a3a83", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-861.0559339176679, 1253.2750414422453, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 8, "timestamp": "2025-07-27T10:51:57.489882", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6bf1f12e-3daa-471e-bf67-50ba026a3a83", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6bf1f12e-3daa-471e-bf67-50ba026a3a83", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-861.0559339176679, 1253.2750414422453, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 9, "timestamp": "2025-07-27T10:51:58.143178", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 642.5669193267822}, {"session_id": "20250727_104606", "step_number": 10, "timestamp": "2025-07-27T10:51:58.148165", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 647.5539207458496}, {"session_id": "20250727_104606", "step_number": 11, "timestamp": "2025-07-27T10:51:58.180147", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 27.382850646972656}, {"session_id": "20250727_104606", "step_number": 12, "timestamp": "2025-07-27T10:51:58.184709", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.945466995239258}, {"session_id": "20250727_104606", "step_number": 13, "timestamp": "2025-07-27T10:52:18.300116", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "581dd478-a928-49e2-97f9-b14a0d45fc24", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "581dd478-a928-49e2-97f9-b14a0d45fc24", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-861.0559339176679, 1253.2750414422453, 1)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 14, "timestamp": "2025-07-27T10:52:18.302390", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "581dd478-a928-49e2-97f9-b14a0d45fc24", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "581dd478-a928-49e2-97f9-b14a0d45fc24", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-861.0559339176679, 1253.2750414422453, 1)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 15, "timestamp": "2025-07-27T10:52:18.702426", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 396.52276039123535}, {"session_id": "20250727_104606", "step_number": 16, "timestamp": "2025-07-27T10:52:18.704430", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 398.5261917114258}, {"session_id": "20250727_104606", "step_number": 17, "timestamp": "2025-07-27T10:52:18.714502", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.076595306396484}, {"session_id": "20250727_104606", "step_number": 18, "timestamp": "2025-07-27T10:52:18.717496", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.07113265991211}, {"session_id": "20250727_104606", "step_number": 19, "timestamp": "2025-07-27T10:58:19.365101", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a8bb0fe0-7fd6-4c4b-8500-ab32ba0b162a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a8bb0fe0-7fd6-4c4b-8500-ab32ba0b162a", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-866.0142973496, 1333.088179770532, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 20, "timestamp": "2025-07-27T10:58:19.373974", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a8bb0fe0-7fd6-4c4b-8500-ab32ba0b162a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a8bb0fe0-7fd6-4c4b-8500-ab32ba0b162a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-866.0142973496, 1333.088179770532, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 21, "timestamp": "2025-07-27T10:58:19.820222", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 438.51494789123535}, {"session_id": "20250727_104606", "step_number": 22, "timestamp": "2025-07-27T10:58:19.822217", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 440.5093193054199}, {"session_id": "20250727_104606", "step_number": 23, "timestamp": "2025-07-27T10:58:19.841017", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.80779457092285}, {"session_id": "20250727_104606", "step_number": 24, "timestamp": "2025-07-27T10:58:19.843011", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.801450729370117}, {"session_id": "20250727_104606", "step_number": 25, "timestamp": "2025-07-27T11:01:15.518972", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "09a1e330-c738-4b0d-845b-03818cdd9588", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "09a1e330-c738-4b0d-845b-03818cdd9588", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-858.2462159135207, 1294.1751472843532, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 26, "timestamp": "2025-07-27T11:01:15.526429", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "09a1e330-c738-4b0d-845b-03818cdd9588", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "09a1e330-c738-4b0d-845b-03818cdd9588", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-858.2462159135207, 1294.1751472843532, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 27, "timestamp": "2025-07-27T11:01:15.878579", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 348.1626510620117}, {"session_id": "20250727_104606", "step_number": 28, "timestamp": "2025-07-27T11:01:15.880634", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 350.2182960510254}, {"session_id": "20250727_104606", "step_number": 29, "timestamp": "2025-07-27T11:01:15.894623", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.93380355834961}, {"session_id": "20250727_104606", "step_number": 30, "timestamp": "2025-07-27T11:01:15.896644", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.954877853393555}, {"session_id": "20250727_104606", "step_number": 31, "timestamp": "2025-07-27T11:04:04.230343", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e3798782-6b7f-4f6a-9270-7cf4494df8d4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e3798782-6b7f-4f6a-9270-7cf4494df8d4", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-938.2162774666062, 1342.76559330822, 1)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 32, "timestamp": "2025-07-27T11:04:04.235944", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e3798782-6b7f-4f6a-9270-7cf4494df8d4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e3798782-6b7f-4f6a-9270-7cf4494df8d4", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-938.2162774666062, 1342.76559330822, 1)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 33, "timestamp": "2025-07-27T11:04:04.982487", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 739.2373085021973}, {"session_id": "20250727_104606", "step_number": 34, "timestamp": "2025-07-27T11:04:04.985506", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 742.255687713623}, {"session_id": "20250727_104606", "step_number": 35, "timestamp": "2025-07-27T11:04:05.003944", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.041589736938477}, {"session_id": "20250727_104606", "step_number": 36, "timestamp": "2025-07-27T11:04:05.006948", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.045902252197266}, {"session_id": "20250727_104606", "step_number": 37, "timestamp": "2025-07-27T11:05:34.101843", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b302ad62-602c-4605-9279-23e42146a2f4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b302ad62-602c-4605-9279-23e42146a2f4", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-938.2162774666062, 1342.76559330822, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 38, "timestamp": "2025-07-27T11:05:34.110516", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b302ad62-602c-4605-9279-23e42146a2f4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b302ad62-602c-4605-9279-23e42146a2f4", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-938.2162774666062, 1342.76559330822, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_104606", "step_number": 39, "timestamp": "2025-07-27T11:05:34.496212", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 377.38490104675293}, {"session_id": "20250727_104606", "step_number": 40, "timestamp": "2025-07-27T11:05:34.498783", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 379.9562454223633}, {"session_id": "20250727_104606", "step_number": 41, "timestamp": "2025-07-27T11:05:34.520612", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.72031593322754}, {"session_id": "20250727_104606", "step_number": 42, "timestamp": "2025-07-27T11:05:34.522607", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.715641021728516}, {"session_id": "20250727_111414", "step_number": 1, "timestamp": "2025-07-27T11:14:14.999015", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e3b6dc66-5c0d-499c-9a05-f7b134552b81", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e3b6dc66-5c0d-499c-9a05-f7b134552b81", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-686.3223607555799, 1100.9124155776092, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_111414", "step_number": 2, "timestamp": "2025-07-27T11:14:15.009567", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e3b6dc66-5c0d-499c-9a05-f7b134552b81", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e3b6dc66-5c0d-499c-9a05-f7b134552b81", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-686.3223607555799, 1100.9124155776092, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_111414", "step_number": 3, "timestamp": "2025-07-27T11:14:15.394649", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 377.4752616882324}, {"session_id": "20250727_111414", "step_number": 4, "timestamp": "2025-07-27T11:14:15.397897", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 380.723237991333}, {"session_id": "20250727_111414", "step_number": 5, "timestamp": "2025-07-27T11:14:15.412157", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.749505996704102}, {"session_id": "20250727_111414", "step_number": 6, "timestamp": "2025-07-27T11:14:15.415704", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.295982360839844}, {"session_id": "20250727_121831", "step_number": 1, "timestamp": "2025-07-27T12:18:31.446153", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "195ff0c0-72ff-4b36-aa3f-c14df3252a87", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "195ff0c0-72ff-4b36-aa3f-c14df3252a87", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-877.0977463982723, 917.4820000006107, 120)", "flight_height": 120, "safety_distance": 15, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_121831", "step_number": 2, "timestamp": "2025-07-27T12:18:31.451649", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "195ff0c0-72ff-4b36-aa3f-c14df3252a87", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "195ff0c0-72ff-4b36-aa3f-c14df3252a87", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-877.0977463982723, 917.4820000006107, 120)", "flight_height": 120, "safety_distance": 15}}, "duration_ms": null}, {"session_id": "20250727_121831", "step_number": 3, "timestamp": "2025-07-27T12:18:32.961619", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1505.4771900177002}, {"session_id": "20250727_121831", "step_number": 4, "timestamp": "2025-07-27T12:18:32.964787", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 15}}, "duration_ms": 1508.6452960968018}, {"session_id": "20250727_121831", "step_number": 5, "timestamp": "2025-07-27T12:18:32.976274", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 8.967161178588867}, {"session_id": "20250727_121831", "step_number": 6, "timestamp": "2025-07-27T12:18:32.979376", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.068748474121094}, {"session_id": "20250727_122306", "step_number": 1, "timestamp": "2025-07-27T12:23:06.074346", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "2fb285a5-e2b9-4d70-aa4b-a771da7bd667", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "2fb285a5-e2b9-4d70-aa4b-a771da7bd667", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-877.0977463982723, 917.4820000006107, 120)", "flight_height": 120, "safety_distance": 15, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_122306", "step_number": 2, "timestamp": "2025-07-27T12:23:06.080454", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "2fb285a5-e2b9-4d70-aa4b-a771da7bd667", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "2fb285a5-e2b9-4d70-aa4b-a771da7bd667", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-877.0977463982723, 917.4820000006107, 120)", "flight_height": 120, "safety_distance": 15}}, "duration_ms": null}, {"session_id": "20250727_122306", "step_number": 3, "timestamp": "2025-07-27T12:23:07.730499", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1645.8497047424316}, {"session_id": "20250727_122306", "step_number": 4, "timestamp": "2025-07-27T12:23:07.733999", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 15}}, "duration_ms": 1649.3499279022217}, {"session_id": "20250727_122306", "step_number": 5, "timestamp": "2025-07-27T12:23:07.745496", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.504556655883789}, {"session_id": "20250727_122306", "step_number": 6, "timestamp": "2025-07-27T12:23:07.748486", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 12.495279312133789}, {"session_id": "20250727_123221", "step_number": 1, "timestamp": "2025-07-27T12:32:21.442818", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e7f48fb2-e2f8-4fcb-a669-18c6b4783714", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e7f48fb2-e2f8-4fcb-a669-18c6b4783714", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1019.42654092368, 1131.3042742767468, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 85}}, "duration_ms": null}, {"session_id": "20250727_123221", "step_number": 2, "timestamp": "2025-07-27T12:32:21.462160", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e7f48fb2-e2f8-4fcb-a669-18c6b4783714", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e7f48fb2-e2f8-4fcb-a669-18c6b4783714", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1019.42654092368, 1131.3042742767468, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_123221", "step_number": 3, "timestamp": "2025-07-27T12:32:24.108984", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2633.711576461792}, {"session_id": "20250727_123221", "step_number": 4, "timestamp": "2025-07-27T12:32:24.112527", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2637.2549533843994}, {"session_id": "20250727_123221", "step_number": 5, "timestamp": "2025-07-27T12:32:24.127002", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.287689208984375}, {"session_id": "20250727_123221", "step_number": 6, "timestamp": "2025-07-27T12:32:24.131010", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.295267105102539}, {"session_id": "20250727_125901", "step_number": 1, "timestamp": "2025-07-27T12:59:01.484177", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5205cf04-dc63-48da-8158-1bce7ad28178", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5205cf04-dc63-48da-8158-1bce7ad28178", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-877.0977463982723, 917.4820000006107, 120)", "flight_height": 120, "safety_distance": 15, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_125901", "step_number": 2, "timestamp": "2025-07-27T12:59:01.489597", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5205cf04-dc63-48da-8158-1bce7ad28178", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5205cf04-dc63-48da-8158-1bce7ad28178", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-877.0977463982723, 917.4820000006107, 120)", "flight_height": 120, "safety_distance": 15}}, "duration_ms": null}, {"session_id": "20250727_125901", "step_number": 3, "timestamp": "2025-07-27T12:59:03.030791", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1536.6368293762207}, {"session_id": "20250727_125901", "step_number": 4, "timestamp": "2025-07-27T12:59:03.034342", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 15}}, "duration_ms": 1540.1878356933594}, {"session_id": "20250727_125901", "step_number": 5, "timestamp": "2025-07-27T12:59:03.045765", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 7.144689559936523}, {"session_id": "20250727_125901", "step_number": 6, "timestamp": "2025-07-27T12:59:03.049349", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.728597640991211}, {"session_id": "20250727_125901", "step_number": 7, "timestamp": "2025-07-27T13:05:45.255298", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c0f535e9-37c7-4caa-8e93-5746fa69760b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c0f535e9-37c7-4caa-8e93-5746fa69760b", "parameters": {"start_point": "(0.0, 0.0, 1)", "end_point": "(-1352.7779724567304, 1931.8800342221707, 1)", "flight_height": 120, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250727_125901", "step_number": 8, "timestamp": "2025-07-27T13:05:45.259285", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c0f535e9-37c7-4caa-8e93-5746fa69760b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c0f535e9-37c7-4caa-8e93-5746fa69760b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 1)", "end_point": "(-1352.7779724567304, 1931.8800342221707, 1)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_125901", "step_number": 9, "timestamp": "2025-07-27T13:05:47.590656", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2328.3133506774902}, {"session_id": "20250727_125901", "step_number": 10, "timestamp": "2025-07-27T13:05:47.593151", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2330.808162689209}, {"session_id": "20250727_125901", "step_number": 11, "timestamp": "2025-07-27T13:05:47.609633", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.484716415405273}, {"session_id": "20250727_125901", "step_number": 12, "timestamp": "2025-07-27T13:05:47.611636", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.4876708984375}, {"session_id": "20250727_125901", "step_number": 13, "timestamp": "2025-07-27T13:06:31.046173", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "af600a36-0804-4e32-b17a-61231e6bef84", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "af600a36-0804-4e32-b17a-61231e6bef84", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1352.7779724567304, 1931.8800342221707, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_125901", "step_number": 14, "timestamp": "2025-07-27T13:06:31.053722", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "af600a36-0804-4e32-b17a-61231e6bef84", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "af600a36-0804-4e32-b17a-61231e6bef84", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1352.7779724567304, 1931.8800342221707, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_125901", "step_number": 15, "timestamp": "2025-07-27T13:06:33.376697", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2318.422794342041}, {"session_id": "20250727_125901", "step_number": 16, "timestamp": "2025-07-27T13:06:33.378700", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2320.4259872436523}, {"session_id": "20250727_125901", "step_number": 17, "timestamp": "2025-07-27T13:06:33.395631", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.940095901489258}, {"session_id": "20250727_125901", "step_number": 18, "timestamp": "2025-07-27T13:06:33.398621", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.930341720581055}, {"session_id": "20250727_134758", "step_number": 1, "timestamp": "2025-07-27T13:47:58.929793", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "868b5a5f-f338-4e94-9362-cba9b2cfdf01", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "868b5a5f-f338-4e94-9362-cba9b2cfdf01", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1081.7815211871311, 1501.4010221319543, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 2, "timestamp": "2025-07-27T13:47:58.938806", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "868b5a5f-f338-4e94-9362-cba9b2cfdf01", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "868b5a5f-f338-4e94-9362-cba9b2cfdf01", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1081.7815211871311, 1501.4010221319543, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 3, "timestamp": "2025-07-27T13:48:00.726575", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1782.2198867797852}, {"session_id": "20250727_134758", "step_number": 4, "timestamp": "2025-07-27T13:48:00.728727", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1784.3716144561768}, {"session_id": "20250727_134758", "step_number": 5, "timestamp": "2025-07-27T13:48:00.742619", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.518789291381836}, {"session_id": "20250727_134758", "step_number": 6, "timestamp": "2025-07-27T13:48:00.745498", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.397455215454102}, {"session_id": "20250727_134758", "step_number": 7, "timestamp": "2025-07-27T15:11:58.996868", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "da117d9c-0e6b-47ad-ab40-10e172ef9e6a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "da117d9c-0e6b-47ad-ab40-10e172ef9e6a", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-894.3394754030667, 1180.836087292263, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 8, "timestamp": "2025-07-27T15:11:59.007897", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "da117d9c-0e6b-47ad-ab40-10e172ef9e6a", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "da117d9c-0e6b-47ad-ab40-10e172ef9e6a", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-894.3394754030667, 1180.836087292263, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 9, "timestamp": "2025-07-27T15:12:00.453406", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1440.9897327423096}, {"session_id": "20250727_134758", "step_number": 10, "timestamp": "2025-07-27T15:12:00.457500", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1445.0836181640625}, {"session_id": "20250727_134758", "step_number": 11, "timestamp": "2025-07-27T15:12:00.469544", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.035587310791016}, {"session_id": "20250727_134758", "step_number": 12, "timestamp": "2025-07-27T15:12:00.474218", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.710260391235352}, {"session_id": "20250727_134758", "step_number": 13, "timestamp": "2025-07-27T15:16:11.068169", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "96dfd766-45db-4518-9aa7-5b42fe84e30e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "96dfd766-45db-4518-9aa7-5b42fe84e30e", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-894.3394754030667, 1180.836087292263, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 14, "timestamp": "2025-07-27T15:16:11.074257", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "96dfd766-45db-4518-9aa7-5b42fe84e30e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "96dfd766-45db-4518-9aa7-5b42fe84e30e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-894.3394754030667, 1180.836087292263, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 15, "timestamp": "2025-07-27T15:16:12.666544", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1583.688735961914}, {"session_id": "20250727_134758", "step_number": 16, "timestamp": "2025-07-27T15:16:12.671083", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1588.2279872894287}, {"session_id": "20250727_134758", "step_number": 17, "timestamp": "2025-07-27T15:16:12.684062", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.989738464355469}, {"session_id": "20250727_134758", "step_number": 18, "timestamp": "2025-07-27T15:16:12.688565", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 14.492988586425781}, {"session_id": "20250727_134758", "step_number": 19, "timestamp": "2025-07-27T15:30:12.617201", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7842ddf3-f803-458e-a59e-791044f66324", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7842ddf3-f803-458e-a59e-791044f66324", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1246.9695069343352, 1711.8094022359585, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 20, "timestamp": "2025-07-27T15:30:12.626948", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7842ddf3-f803-458e-a59e-791044f66324", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7842ddf3-f803-458e-a59e-791044f66324", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1246.9695069343352, 1711.8094022359585, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 21, "timestamp": "2025-07-27T15:30:14.932878", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2297.186851501465}, {"session_id": "20250727_134758", "step_number": 22, "timestamp": "2025-07-27T15:30:14.935986", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2300.294876098633}, {"session_id": "20250727_134758", "step_number": 23, "timestamp": "2025-07-27T15:30:14.951731", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.98267936706543}, {"session_id": "20250727_134758", "step_number": 24, "timestamp": "2025-07-27T15:30:14.955587", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.838384628295898}, {"session_id": "20250727_134758", "step_number": 25, "timestamp": "2025-07-27T15:47:39.561835", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bd488dcd-f4f9-45c4-9787-3be7894f7d90", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bd488dcd-f4f9-45c4-9787-3be7894f7d90", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-821.0872181616296, 1236.4200379011547, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 26, "timestamp": "2025-07-27T15:47:39.571022", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "bd488dcd-f4f9-45c4-9787-3be7894f7d90", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "bd488dcd-f4f9-45c4-9787-3be7894f7d90", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-821.0872181616296, 1236.4200379011547, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 27, "timestamp": "2025-07-27T15:47:41.103576", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1521.0754871368408}, {"session_id": "20250727_134758", "step_number": 28, "timestamp": "2025-07-27T15:47:41.109692", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1527.1904468536377}, {"session_id": "20250727_134758", "step_number": 29, "timestamp": "2025-07-27T15:47:41.123758", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 10.326862335205078}, {"session_id": "20250727_134758", "step_number": 30, "timestamp": "2025-07-27T15:47:41.126864", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.432979583740234}, {"session_id": "20250727_134758", "step_number": 31, "timestamp": "2025-07-27T16:03:09.539495", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "68d7595f-3219-44f2-b8dc-8dd91a5e5e21", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "68d7595f-3219-44f2-b8dc-8dd91a5e5e21", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1165.2353558902546, 1335.4406476191796, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 32, "timestamp": "2025-07-27T16:03:09.548804", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "68d7595f-3219-44f2-b8dc-8dd91a5e5e21", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "68d7595f-3219-44f2-b8dc-8dd91a5e5e21", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1165.2353558902546, 1335.4406476191796, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 33, "timestamp": "2025-07-27T16:03:11.366427", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1811.664342880249}, {"session_id": "20250727_134758", "step_number": 34, "timestamp": "2025-07-27T16:03:11.372060", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1817.2969818115234}, {"session_id": "20250727_134758", "step_number": 35, "timestamp": "2025-07-27T16:03:11.391137", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 14.899015426635742}, {"session_id": "20250727_134758", "step_number": 36, "timestamp": "2025-07-27T16:03:11.398330", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.092580795288086}, {"session_id": "20250727_134758", "step_number": 37, "timestamp": "2025-07-27T16:07:50.264828", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e2f8a3d0-df18-4776-b90e-edf816aefd93", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e2f8a3d0-df18-4776-b90e-edf816aefd93", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1165.2353558902546, 1335.4406476191796, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 38, "timestamp": "2025-07-27T16:07:50.273699", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e2f8a3d0-df18-4776-b90e-edf816aefd93", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e2f8a3d0-df18-4776-b90e-edf816aefd93", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1165.2353558902546, 1335.4406476191796, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 39, "timestamp": "2025-07-27T16:07:52.121989", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1841.2818908691406}, {"session_id": "20250727_134758", "step_number": 40, "timestamp": "2025-07-27T16:07:52.126484", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1845.7765579223633}, {"session_id": "20250727_134758", "step_number": 41, "timestamp": "2025-07-27T16:07:52.142061", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.57379150390625}, {"session_id": "20250727_134758", "step_number": 42, "timestamp": "2025-07-27T16:07:52.146571", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.08443260192871}, {"session_id": "20250727_134758", "step_number": 43, "timestamp": "2025-07-27T16:12:52.117455", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d499e1a3-52d0-42a3-aeab-6df2b2c9313c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d499e1a3-52d0-42a3-aeab-6df2b2c9313c", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1131.2236101825283, 1254.486319444185, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 44, "timestamp": "2025-07-27T16:12:52.127922", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "d499e1a3-52d0-42a3-aeab-6df2b2c9313c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "d499e1a3-52d0-42a3-aeab-6df2b2c9313c", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1131.2236101825283, 1254.486319444185, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_134758", "step_number": 45, "timestamp": "2025-07-27T16:12:54.433822", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2296.815872192383}, {"session_id": "20250727_134758", "step_number": 46, "timestamp": "2025-07-27T16:12:54.437812", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 2300.804853439331}, {"session_id": "20250727_134758", "step_number": 47, "timestamp": "2025-07-27T16:12:54.457308", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.966083526611328}, {"session_id": "20250727_134758", "step_number": 48, "timestamp": "2025-07-27T16:12:54.465124", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.78168296813965}, {"session_id": "20250727_161906", "step_number": 1, "timestamp": "2025-07-27T16:19:06.836861", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c1ba0a44-086a-476a-84fb-24e27ab9ccaa", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c1ba0a44-086a-476a-84fb-24e27ab9ccaa", "parameters": {"start_point": "(0.0, 0.0, 120)", "end_point": "(-1109.5171447475138, 1217.2300968204513, 120)", "flight_height": 120, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_161906", "step_number": 2, "timestamp": "2025-07-27T16:19:06.846189", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c1ba0a44-086a-476a-84fb-24e27ab9ccaa", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c1ba0a44-086a-476a-84fb-24e27ab9ccaa", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 120)", "end_point": "(-1109.5171447475138, 1217.2300968204513, 120)", "flight_height": 120, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_161906", "step_number": 3, "timestamp": "2025-07-27T16:19:08.477704", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1622.0049858093262}, {"session_id": "20250727_161906", "step_number": 4, "timestamp": "2025-07-27T16:19:08.481862", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 120, "safety_distance": 30}}, "duration_ms": 1626.1632442474365}, {"session_id": "20250727_161906", "step_number": 5, "timestamp": "2025-07-27T16:19:08.498689", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.843204498291016}, {"session_id": "20250727_161906", "step_number": 6, "timestamp": "2025-07-27T16:19:08.503675", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.82877540588379}, {"session_id": "20250727_223713", "step_number": 1, "timestamp": "2025-07-27T22:37:13.410137", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c842c24c-c1da-40be-bf2f-7169bf727958", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c842c24c-c1da-40be-bf2f-7169bf727958", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1050.9249301829468, 1618.399961501039, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250727_223713", "step_number": 2, "timestamp": "2025-07-27T22:37:13.419548", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c842c24c-c1da-40be-bf2f-7169bf727958", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c842c24c-c1da-40be-bf2f-7169bf727958", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1050.9249301829468, 1618.399961501039, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_223713", "step_number": 3, "timestamp": "2025-07-27T22:37:15.507757", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2079.0135860443115}, {"session_id": "20250727_223713", "step_number": 4, "timestamp": "2025-07-27T22:37:15.512291", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2083.547353744507}, {"session_id": "20250727_223713", "step_number": 5, "timestamp": "2025-07-27T22:37:15.527781", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 11.49439811706543}, {"session_id": "20250727_223713", "step_number": 6, "timestamp": "2025-07-27T22:37:15.532445", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.1590576171875}, {"session_id": "20250727_224723", "step_number": 1, "timestamp": "2025-07-27T22:47:23.848542", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "282448d0-b92a-4dc6-8cfa-02840d41f6fb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "282448d0-b92a-4dc6-8cfa-02840d41f6fb", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-770.2524173101299, 1241.5512690318862, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 77}}, "duration_ms": null}, {"session_id": "20250727_224723", "step_number": 2, "timestamp": "2025-07-27T22:47:23.858843", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "282448d0-b92a-4dc6-8cfa-02840d41f6fb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "282448d0-b92a-4dc6-8cfa-02840d41f6fb", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-770.2524173101299, 1241.5512690318862, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_224723", "step_number": 3, "timestamp": "2025-07-27T22:47:25.343105", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1475.9547710418701}, {"session_id": "20250727_224723", "step_number": 4, "timestamp": "2025-07-27T22:47:25.347687", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1480.5374145507812}, {"session_id": "20250727_224723", "step_number": 5, "timestamp": "2025-07-27T22:47:25.362086", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 9.414196014404297}, {"session_id": "20250727_224723", "step_number": 6, "timestamp": "2025-07-27T22:47:25.365921", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.248920440673828}, {"session_id": "20250727_225429", "step_number": 1, "timestamp": "2025-07-27T22:54:29.964543", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "20480be6-8b4d-473e-a2c7-06d8fcd48b66", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "20480be6-8b4d-473e-a2c7-06d8fcd48b66", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-911.3353096844792, 1337.7858089497488, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 81}}, "duration_ms": null}, {"session_id": "20250727_225429", "step_number": 2, "timestamp": "2025-07-27T22:54:29.985042", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "20480be6-8b4d-473e-a2c7-06d8fcd48b66", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "20480be6-8b4d-473e-a2c7-06d8fcd48b66", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-911.3353096844792, 1337.7858089497488, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250727_225429", "step_number": 3, "timestamp": "2025-07-27T22:54:32.251656", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2245.5384731292725}, {"session_id": "20250727_225429", "step_number": 4, "timestamp": "2025-07-27T22:54:32.258791", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2252.673864364624}, {"session_id": "20250727_225429", "step_number": 5, "timestamp": "2025-07-27T22:54:32.280305", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.604257583618164}, {"session_id": "20250727_225429", "step_number": 6, "timestamp": "2025-07-27T22:54:32.287085", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.384166717529297}]