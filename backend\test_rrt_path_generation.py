#!/usr/bin/env python3
"""
测试RRT路径生成功能
验证改进算法中的RRT路径生成接口是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_rrt_path_generation():
    """测试RRT路径生成功能"""
    print("🚀 开始测试RRT路径生成功能...")
    
    try:
        # 1. 导入必要的模块
        print("\n1️⃣ 导入算法模块...")
        from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathPlanning
        from algorithms.astar import AStarAlgorithm
        from algorithms.rrt import RRTAlgorithm
        from algorithms.data_structures import PathPlanningRequest
        print("✅ 算法模块导入成功")
        
        # 2. 创建算法实例
        print("\n2️⃣ 创建算法实例...")
        improved_algorithm = ImprovedClusterBasedPathPlanning()
        astar_algorithm = AStarAlgorithm()
        
        # 设置A*算法实例
        improved_algorithm.set_astar_algorithm(astar_algorithm)
        print("✅ 算法实例创建成功")
        
        # 3. 测试A*路径生成（默认）
        print("\n3️⃣ 测试A*路径生成（默认）...")
        astar_request_data = {
            'startPoint': {'lng': 139.768494, 'lat': 35.677535, 'alt': 100.0},
            'endPoint': {'lng': 139.751541, 'lat': 35.692594, 'alt': 100.0},
            'algorithm': 'ImprovedClusterPathfinding',
            'flightHeight': 100.0,
            'parameters': {
                'pathGenerationAlgorithm': 'astar',  # 明确指定使用A*
                'maxTurnAngle': 90.0,
                'riskEdgeDistance': 50.0,
                'kValue': 5.0,
                'enablePathSwitching': True
            }
        }
        
        astar_request = PathPlanningRequest(astar_request_data)
        print(f"   请求参数: pathGenerationAlgorithm = {astar_request.parameters.get('pathGenerationAlgorithm')}")
        
        astar_response = await improved_algorithm.execute(astar_request)
        
        if astar_response.success:
            print(f"✅ A*路径生成成功:")
            print(f"   路径点数: {len(astar_response.path)}")
            print(f"   执行时间: {astar_response.execution_time:.2f}秒")
            print(f"   基准算法: {astar_response.metadata.get('baseline_algorithm', 'N/A')}")
        else:
            print(f"❌ A*路径生成失败: {astar_response.error}")
        
        # 4. 测试RRT路径生成
        print("\n4️⃣ 测试RRT路径生成...")
        rrt_request_data = {
            'startPoint': {'lng': 139.768494, 'lat': 35.677535, 'alt': 100.0},
            'endPoint': {'lng': 139.751541, 'lat': 35.692594, 'alt': 100.0},
            'algorithm': 'ImprovedClusterPathfinding',
            'flightHeight': 100.0,
            'parameters': {
                'pathGenerationAlgorithm': 'rrt',  # 指定使用RRT
                'maxTurnAngle': 90.0,
                'riskEdgeDistance': 50.0,
                'kValue': 5.0,
                'enablePathSwitching': True
            }
        }
        
        rrt_request = PathPlanningRequest(rrt_request_data)
        print(f"   请求参数: pathGenerationAlgorithm = {rrt_request.parameters.get('pathGenerationAlgorithm')}")
        
        rrt_response = await improved_algorithm.execute(rrt_request)
        
        if rrt_response.success:
            print(f"✅ RRT路径生成成功:")
            print(f"   路径点数: {len(rrt_response.path)}")
            print(f"   执行时间: {rrt_response.execution_time:.2f}秒")
            print(f"   基准算法: {rrt_response.metadata.get('baseline_algorithm', 'N/A')}")
        else:
            print(f"❌ RRT路径生成失败: {rrt_response.error}")
        
        # 5. 对比结果
        print("\n5️⃣ 对比结果...")
        if astar_response.success and rrt_response.success:
            print("📊 A* vs RRT 对比:")
            print(f"   A*路径点数: {len(astar_response.path)} | RRT路径点数: {len(rrt_response.path)}")
            print(f"   A*执行时间: {astar_response.execution_time:.2f}s | RRT执行时间: {rrt_response.execution_time:.2f}s")
            
            # 检查基准算法是否不同
            astar_baseline = astar_response.metadata.get('baseline_algorithm', 'A*')
            rrt_baseline = rrt_response.metadata.get('baseline_algorithm', 'RRT')
            print(f"   A*基准算法: {astar_baseline} | RRT基准算法: {rrt_baseline}")
            
            if astar_baseline != rrt_baseline:
                print("✅ 基准算法正确区分，避免了重复计算")
            else:
                print("⚠️ 基准算法可能仍有重复计算问题")
        
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 RRT路径生成功能测试")
    print("=" * 60)
    
    # 运行异步测试
    asyncio.run(test_rrt_path_generation())

if __name__ == "__main__":
    main()
