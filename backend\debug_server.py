#!/usr/bin/env python3
"""
调试服务器 - 用于诊断Python环境和算法系统问题
"""

import sys
import os
import traceback

print("=" * 60)
print("🔍 Python算法系统诊断工具")
print("=" * 60)

# 1. 检查Python版本
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")
print(f"工作目录: {os.getcwd()}")

# 2. 检查基础模块
print("\n📦 检查基础模块...")
try:
    import json
    print("✅ json模块正常")
except ImportError as e:
    print(f"❌ json模块失败: {e}")

try:
    import asyncio
    print("✅ asyncio模块正常")
except ImportError as e:
    print(f"❌ asyncio模块失败: {e}")

# 3. 检查Flask
print("\n🌐 检查Flask...")
try:
    from flask import Flask, jsonify, request
    from flask_cors import CORS
    print("✅ Flask和CORS模块正常")
except ImportError as e:
    print(f"❌ Flask模块失败: {e}")
    print("请运行: pip install flask flask-cors")

# 4. 检查NumPy
print("\n🔢 检查NumPy...")
try:
    import numpy as np
    print(f"✅ NumPy版本: {np.__version__}")
except ImportError as e:
    print(f"❌ NumPy失败: {e}")
    print("请运行: pip install numpy")

# 5. 检查算法模块
print("\n🧠 检查算法模块...")
try:
    # 添加当前目录到路径
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    from algorithms.base import PathPlanningAlgorithm
    print("✅ 算法基类导入成功")
    
    from algorithms.data_structures import PathPlanningRequest, PathPlanningResponse
    print("✅ 数据结构导入成功")
    
    from algorithms.manager import algorithm_manager
    print("✅ 算法管理器导入成功")
    
    # 检查已注册的算法
    algorithms = algorithm_manager.list_algorithms()
    print(f"✅ 已注册算法数量: {len(algorithms)}")
    for alg in algorithms:
        print(f"   - {alg['name']}: {alg['info']['description']}")
        
except Exception as e:
    print(f"❌ 算法模块失败: {e}")
    traceback.print_exc()

# 6. 创建简单的Flask应用测试
print("\n🚀 创建测试服务器...")
try:
    app = Flask(__name__)
    CORS(app)
    
    @app.route('/test')
    def test():
        return jsonify({
            'status': 'ok',
            'message': 'Python后端测试成功',
            'python_version': sys.version
        })
    
    @app.route('/api/pathfinding/health')
    def health():
        try:
            from algorithms import algorithm_manager
            algorithms = algorithm_manager.list_algorithms()
            
            return jsonify({
                'success': True,
                'status': 'healthy',
                'message': '路径规划服务运行正常',
                'available_algorithms': len(algorithms),
                'algorithms': [alg['name'] for alg in algorithms]
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'status': 'unhealthy',
                'error': str(e)
            }), 500
    
    @app.route('/api/pathfinding/test-algorithm')
    def test_algorithm():
        try:
            from algorithms import algorithm_manager, PathPlanningRequest
            
            # 创建测试请求
            test_data = {
                'startPoint': {'lng': 139.767, 'lat': 35.681, 'alt': 0},
                'endPoint': {'lng': 139.777, 'lat': 35.691, 'alt': 0},
                'flightHeight': 100,
                'algorithm': 'StraightLine',
                'parameters': {'pointCount': 10}
            }
            
            request_obj = PathPlanningRequest(test_data)
            
            # 执行算法
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                response = loop.run_until_complete(
                    algorithm_manager.execute_algorithm('StraightLine', request_obj)
                )
            finally:
                loop.close()
            
            return jsonify({
                'success': True,
                'test_result': {
                    'algorithm_success': response.success,
                    'path_length': len(response.path) if response.path else 0,
                    'execution_time': response.execution_time,
                    'error': response.error if not response.success else None
                }
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    print("✅ Flask应用创建成功")
    print("\n" + "=" * 60)
    print("🎯 诊断完成！")
    print("如果所有检查都通过，服务器将启动在 http://localhost:5000")
    print("测试地址:")
    print("  - http://localhost:5000/test")
    print("  - http://localhost:5000/api/pathfinding/health")
    print("  - http://localhost:5000/api/pathfinding/test-algorithm")
    print("=" * 60)
    
    # 启动服务器
    app.run(host='0.0.0.0', port=5000, debug=True)
    
except Exception as e:
    print(f"❌ Flask应用创建失败: {e}")
    traceback.print_exc()
    
print("\n🔧 如果有错误，请根据上面的提示安装缺失的依赖")
print("常用命令:")
print("  pip install flask flask-cors numpy scipy")
