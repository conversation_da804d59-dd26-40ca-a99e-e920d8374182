/**
 * 算法对比管理器
 * 实现需求中的自动对比流程：先原始算法后改进算法，并生成对比图表
 */
class AlgorithmComparisonManager {
    constructor(cityManager) {
        this.cityManager = cityManager;
        this.isComparing = false;
        this.currentComparison = null;
        
        // 对比结果存储
        this.baselineResult = null;
        this.improvedResult = null;
        
        // 状态管理
        this.comparisonSteps = [
            { id: 'baseline', name: '基准算法路径生成', status: 'pending' },
            { id: 'baseline_analysis', name: '基准算法指标统计', status: 'pending' },
            { id: 'improved', name: '改进算法路径生成', status: 'pending' },
            { id: 'improved_flight', name: '改进算法飞行模拟', status: 'pending' },
            { id: 'comparison', name: '生成对比图表', status: 'pending' }
        ];
        
        this.init();
    }
    
    init() {
        this.createComparisonPanel();
        this.bindEvents();
    }
    
    /**
     * 创建对比面板UI
     */
    createComparisonPanel() {
        // 🔧 修复：使用HTML中已有的对比面板，不创建新的
        this.log('📊 使用HTML中的对比面板', 'info');

        // 立即初始化步骤显示
        setTimeout(() => {
            this.updateStepsDisplay();
        }, 100);
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        const closeBtn = document.getElementById('close-comparison-btn');
        const collapseBtn = document.getElementById('collapse-comparison-btn');
        const header = document.getElementById('comparison-panel-header');
        const panel = document.getElementById('algorithm-comparison-panel');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideComparisonPanel();
            });
        }

        if (collapseBtn) {
            collapseBtn.addEventListener('click', () => {
                this.toggleCollapse();
            });
        }

        // 添加拖拽功能
        if (header && panel) {
            this.makeDraggable(panel, header);
        }
    }

    /**
     * 切换折叠状态
     */
    toggleCollapse() {
        const panel = document.getElementById('algorithm-comparison-panel');
        const collapseBtn = document.getElementById('collapse-comparison-btn');

        if (panel && collapseBtn) {
            panel.classList.toggle('collapsed');
            collapseBtn.textContent = panel.classList.contains('collapsed') ? '+' : '−';
        }
    }

    /**
     * 使面板可拖拽
     */
    makeDraggable(panel, header) {
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        header.addEventListener('mousedown', (e) => {
            if (e.target.tagName === 'BUTTON') return; // 不在按钮上拖拽

            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                panel.classList.add('dragging');
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                // 限制拖拽范围
                const maxX = window.innerWidth - panel.offsetWidth;
                const maxY = window.innerHeight - panel.offsetHeight;

                xOffset = Math.max(0, Math.min(maxX, xOffset));
                yOffset = Math.max(0, Math.min(maxY, yOffset));

                panel.style.transform = `translate(${xOffset}px, ${yOffset}px)`;
                panel.style.left = '0';
                panel.style.top = '0';
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                panel.classList.remove('dragging');
            }
        });
    }

    /**
     * 检查Python客户端是否可用
     */
    checkPythonClient() {
        console.log('🔍 开始检查Python客户端...');
        console.log('🔍 cityManager:', this.cityManager);
        console.log('🔍 cityManager.pythonClient:', this.cityManager.pythonClient);

        if (!this.cityManager.pythonClient) {
            console.error('❌ Python客户端未初始化');
            throw new Error('Python算法客户端未初始化。请确保：\n1. 后端服务正在运行\n2. 页面已完全加载\n3. 网络连接正常');
        }

        console.log('🔍 pythonClient类型:', typeof this.cityManager.pythonClient);
        console.log('🔍 pythonClient方法:', Object.getOwnPropertyNames(this.cityManager.pythonClient));
        console.log('🔍 calculatePath方法类型:', typeof this.cityManager.pythonClient.calculatePath);

        if (typeof this.cityManager.pythonClient.calculatePath !== 'function') {
            console.error('❌ calculatePath方法不可用');
            throw new Error('Python客户端的calculatePath方法不可用，请检查客户端版本');
        }

        this.log('✅ Python客户端检查通过', 'info');
    }

    /**
     * 开始算法对比流程
     */
    async startComparison() {
        if (this.isComparing) {
            this.log('对比流程正在进行中...', 'warning');
            return;
        }

        if (!this.cityManager.startPoint || !this.cityManager.endPoint) {
            this.log('请先设置起点和终点', 'error');
            return;
        }

        // 🔧 修复：在开始时就显示对比面板和步骤
        this.showComparisonPanel();
        this.updateStepsDisplay();

        // 预先检查Python客户端
        try {
            this.checkPythonClient();
        } catch (error) {
            this.log(`❌ 预检查失败: ${error.message}`, 'error');
            return;
        }

        this.isComparing = true;
        this.resetComparisonState();
        this.showComparisonPanel();
        
        try {
            this.log('🚀 开始算法对比流程...', 'info');

            // 🔧 修复：使用统一算法对比API，确保基准算法结果保存到后端
            await this.runUnifiedComparison();

            this.log('✅ 算法对比流程完成！', 'success');

        } catch (error) {
            this.log(`❌ 对比流程失败: ${error.message}`, 'error');
            console.error('算法对比错误:', error);
        } finally {
            this.isComparing = false;
        }
    }

    /**
     * 运行统一算法对比（使用后端API）
     */
    async runUnifiedComparison() {
        this.log('🔄 使用后端统一算法对比API...', 'info');

        try {
            // 获取建筑数据
            const buildingData = await this.cityManager.getBuildingDataForPathPlanning();

            // 构建请求数据
            const requestData = {
                startPoint: {
                    lng: this.cityManager.startPoint.lng,
                    lat: this.cityManager.startPoint.lat,
                    alt: this.cityManager.startPoint.alt || 1.0  // 起飞高度1米
                },
                endPoint: {
                    lng: this.cityManager.endPoint.lng,
                    lat: this.cityManager.endPoint.lat,
                    alt: this.cityManager.endPoint.alt || 1.0  // 降落高度1米
                },
                buildings: buildingData,
                parameters: {
                    flightHeight: 70,  // 修改巡航高度为70米（适合城市环境）
                    safetyDistance: 30,
                    gridSize: 10,
                    maxTurnAngle: 90,
                    riskEdgeDistance: 50
                }
            };

            this.log('📡 调用后端算法对比API...', 'info');

            // 调用后端算法对比API
            const response = await fetch('/api/algorithm_comparison', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const comparisonResult = await response.json();

            if (!comparisonResult.success) {
                throw new Error(comparisonResult.error || '算法对比失败');
            }

            this.log('✅ 后端算法对比完成', 'success');

            // 处理对比结果
            this.processUnifiedComparisonResult(comparisonResult);

        } catch (error) {
            console.error('统一算法对比失败:', error);
            this.log(`❌ 统一算法对比失败: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * 处理统一算法对比结果
     */
    processUnifiedComparisonResult(result) {
        this.log('📊 处理算法对比结果...', 'info');

        // 🔧 修复：只保存数据，不立即标记步骤完成
        // 设置改进算法结果
        if (result.improved_algorithm) {
            this.improvedResult = {
                algorithm: '改进分簇算法',
                path: result.improved_algorithm.path || [],
                metrics: {
                    pathLength: result.improved_algorithm.path_length || 0,
                    turningCost: result.improved_algorithm.turning_cost || 0,
                    riskValue: result.improved_algorithm.risk_value || 0,
                    collisionCost: result.improved_algorithm.collision_cost || 0,
                    finalCost: result.improved_algorithm.final_cost || 0,
                    executionTime: result.improved_algorithm.execution_time || 0
                },
                rawResponse: result.improved_algorithm
            };
            this.log('✅ 改进算法数据已保存', 'success');
        }

        // 设置基准算法结果
        if (result.baseline_algorithm) {
            this.baselineResult = {
                algorithm: 'A*',
                path: result.baseline_algorithm.path || [],
                metrics: {
                    pathLength: result.baseline_algorithm.path_length || 0,
                    turningCost: result.baseline_algorithm.turning_cost || 0,
                    riskValue: result.baseline_algorithm.risk_value || 0,
                    collisionCost: result.baseline_algorithm.collision_cost || 0,
                    finalCost: result.baseline_algorithm.final_cost || 0,
                    executionTime: result.baseline_algorithm.execution_time || 0
                },
                rawResponse: result.baseline_algorithm
            };
            this.log('✅ 基准算法数据已保存', 'success');
        }

        // 🔧 修复：按正确顺序执行5步流程
        this.executeSequentialSteps();
    }

    /**
     * 按正确顺序执行5步对比流程
     */
    async executeSequentialSteps() {
        try {
            this.log('🔄 开始按顺序执行5步对比流程...', 'info');
            console.log('🔧 executeSequentialSteps: 开始按顺序执行');
            console.log('🔧 当前时间:', new Date().toLocaleTimeString());

            // 步骤1：基准算法路径生成
            console.log('🔧 准备执行步骤1：基准算法路径生成');
            await this.executeStep1();
            console.log('🔧 步骤1完成，当前时间:', new Date().toLocaleTimeString());

            // 步骤2：基准算法指标统计
            console.log('🔧 准备执行步骤2：基准算法指标统计');
            await this.executeStep2();
            console.log('🔧 步骤2完成，当前时间:', new Date().toLocaleTimeString());

            // 步骤3：改进算法路径生成
            console.log('🔧 准备执行步骤3：改进算法路径生成');
            await this.executeStep3();
            console.log('🔧 步骤3完成，当前时间:', new Date().toLocaleTimeString());

            // 步骤4：改进算法飞行模拟
            console.log('🔧 准备执行步骤4：改进算法飞行模拟');
            await this.executeStep4();
            console.log('🔧 步骤4完成，当前时间:', new Date().toLocaleTimeString());

            // 步骤5：生成对比图表
            console.log('🔧 准备执行步骤5：生成对比图表');
            await this.executeStep5();
            console.log('🔧 步骤5完成，当前时间:', new Date().toLocaleTimeString());

            this.log('✅ 5步对比流程按顺序执行完成', 'success');

        } catch (error) {
            this.log(`❌ 对比流程执行失败: ${error.message}`, 'error');
            console.error('对比流程执行错误:', error);
        }
    }

    /**
     * 步骤1：基准算法路径生成
     */
    async executeStep1() {
        if (!this.baselineResult) {
            console.log('⚠️ 跳过步骤1：基准算法数据不存在');
            return;
        }

        this.updateStepStatus('baseline', 'running');
        this.log('🔄 步骤1：基准算法路径生成...', 'info');

        // 清除所有现有路径，从干净状态开始
        if (this.cityManager && typeof this.cityManager.clearPathVisualization === 'function') {
            this.cityManager.clearPathVisualization();
        }

        // 等待一下让用户看到步骤开始
        await this.delay(500);

        // 显示基准路径
        this.displayBaselinePath();

        this.updateStepStatus('baseline', 'completed');
        this.log('✅ 步骤1：基准算法路径生成完成', 'success');

        // 等待一下再进行下一步
        await this.delay(1000);
    }

    /**
     * 步骤2：基准算法指标统计
     */
    async executeStep2() {
        if (!this.baselineResult) {
            console.log('⚠️ 跳过步骤2：基准算法数据不存在');
            return;
        }

        await this.analyzeBaselineResults();
    }

    /**
     * 步骤3：改进算法路径生成
     */
    async executeStep3() {
        if (!this.improvedResult) {
            console.log('⚠️ 跳过步骤3：改进算法数据不存在');
            return;
        }

        this.updateStepStatus('improved', 'running');
        this.log('🔄 步骤3：改进算法路径生成...', 'info');

        // 等待一下让用户看到步骤开始
        await this.delay(500);

        // 显示改进路径
        this.displayImprovedPath();

        this.updateStepStatus('improved', 'completed');
        this.log('✅ 步骤3：改进算法路径生成完成', 'success');

        // 等待一下再进行下一步
        await this.delay(1000);
    }

    /**
     * 步骤4：改进算法飞行模拟
     */
    async executeStep4() {
        if (!this.improvedResult) {
            console.log('⚠️ 跳过步骤4：改进算法数据不存在');
            return;
        }

        await this.simulateImprovedFlight();
    }

    /**
     * 步骤5：生成对比图表
     */
    async executeStep5() {
        await this.generateComparisonChart();
    }

    /**
     * 运行基准算法（A*）
     */
    async runBaselineAlgorithm() {
        this.updateStepStatus('baseline', 'running');
        this.log('📊 正在运行基准算法（A*）...', 'info');

        try {
            // 获取建筑数据
            const buildingData = await this.cityManager.getBuildingDataForPathPlanning();
            
            // 创建A*算法请求
            const requestData = {
                algorithm: 'AStar',
                startPoint: {
                    lng: this.cityManager.startPoint.lng,
                    lat: this.cityManager.startPoint.lat,
                    alt: this.cityManager.flightHeight,
                    x: this.cityManager.startPoint.x || 0,
                    y: this.cityManager.startPoint.y || 0,
                    z: this.cityManager.flightHeight
                },
                endPoint: {
                    lng: this.cityManager.endPoint.lng,
                    lat: this.cityManager.endPoint.lat,
                    alt: this.cityManager.flightHeight,
                    x: this.cityManager.endPoint.x || 100,
                    y: this.cityManager.endPoint.y || 100,
                    z: this.cityManager.flightHeight
                },
                // 🔧 修复：统一基准算法和改进算法的参数
                flightHeight: 70,  // 修改统一巡航高度为70米（适合城市环境）
                safetyDistance: 30, // 统一安全距离
                buildings: buildingData || [],
                parameters: {
                    flightHeight: 70,  // 修改统一巡航高度为70米（适合城市环境）
                    safetyDistance: 30, // 统一安全距离
                    maxTurnAngle: 90,   // 统一最大转向角
                    riskEdgeDistance: 50, // 统一风险边缘距离
                    // A*算法专用参数
                    gridSize: 10,       // 🔧 恢复合理的网格大小，保持A*算法精度
                    heuristicWeight: 1.0,
                    maxIterations: 10000,
                    allowDiagonal: true,
                    smoothPath: true    // 🔧 确保路径平滑
                }
            };
            
            // 调用Python后端
            const response = await this.cityManager.pythonClient.calculatePath(requestData);

            console.log('🔍 A*算法后端完整响应:', response);
            console.log('🔍 响应中的所有字段:', Object.keys(response || {}));

            if (response && response.success) {
                console.log('🎯 基准算法完成回调被执行');
                console.log('🎯 基准算法响应结构:', response);
                console.log('🎯 检查metadata:', response.metadata);
                console.log('🎯 检查metadata.protection_zones:', response.metadata ? response.metadata.protection_zones : 'metadata不存在');

                const extractedMetrics = this.extractMetrics(response);
                console.log('🔍 提取的指标:', extractedMetrics);

                this.baselineResult = {
                    algorithm: 'A*',
                    path: this.cityManager.pythonClient.formatPathData(response),
                    metrics: extractedMetrics,
                    rawResponse: response
                };

                console.log('🔍 baselineResult.metrics:', this.baselineResult.metrics);
                console.log('🔍 后端路径长度:', this.baselineResult.metrics.pathLength);
                console.log('🔍 后端转向成本:', this.baselineResult.metrics.turningCost);
                console.log('🔍 后端风险值:', this.baselineResult.metrics.riskValue);
                console.log('🔍 后端碰撞代价:', this.baselineResult.metrics.collisionCost);
                console.log('🔍 后端最终代价:', this.baselineResult.metrics.finalCost);
                console.log('🔍 路径点数:', this.baselineResult.path.length);

                // 处理保护区信息
                if (this.baselineResult.rawResponse && this.baselineResult.rawResponse.metadata &&
                    this.baselineResult.rawResponse.metadata.protection_zones) {
                    const protectionInfo = this.baselineResult.rawResponse.metadata.protection_zones;
                    console.log('🛡️ 基准算法保护区信息:', protectionInfo);

                    // 更新前端地图上的保护区状态
                    if (this.cityManager && typeof this.cityManager.updateProtectionZoneStatus === 'function') {
                        console.log('🛡️ 调用updateProtectionZoneStatus（基准算法），传递ID:', protectionInfo.active_zone_ids);
                        this.cityManager.updateProtectionZoneStatus(protectionInfo.active_zone_ids);
                        console.log('🛡️ updateProtectionZoneStatus调用完成（基准算法）');
                    } else {
                        console.error('❌ cityManager或updateProtectionZoneStatus方法不可用（基准算法）');
                    }

                    this.log(`🛡️ 基准算法使用了 ${protectionInfo.active_zones} 个保护区`, 'info');
                } else {
                    console.log('⚠️ 基准算法响应中没有保护区信息');
                }

                // 显示基准算法路径（不需要动态模拟）
                this.displayBaselinePath();

                this.updateStepStatus('baseline', 'completed');
                console.log('✅ 后端A*算法成功！');
                console.log('✅ 后端baselineResult:', this.baselineResult);
                console.log('✅ 后端rawResponse存在:', !!this.baselineResult.rawResponse);
                this.log(`✅ 基准算法完成，路径点数: ${this.baselineResult.path.length}`, 'success');

                // 后端A*算法成功，直接返回，不执行前端回退
                return;

            } else {
                throw new Error(response ? response.error : '基准算法执行失败');
            }
            
        } catch (error) {
            this.log(`❌ 后端A*算法失败: ${error.message}`, 'error');
            console.error('后端A*算法详细错误:', error);
            this.updateStepStatus('baseline', 'error');
            throw new Error(`基准算法执行失败: ${error.message}`);
        }
    }
    
    // 🚫 前端A*算法已删除，所有算法由后端执行


    // 🚫 内置A*算法已删除，所有算法由后端执行

    // 🚫 所有前端指标计算已删除，只使用后端计算结果
    // 🚫 calculateFrontendMetrics 方法已删除




    // 🚫 所有前端计算方法已删除，只使用后端计算结果

    /**
     * 统计基准算法指标
     */
    async analyzeBaselineResults() {
        this.updateStepStatus('baseline_analysis', 'running');
        this.log('📈 正在统计基准算法指标...', 'info');
        
        try {
            // 等待一下让用户看到路径
            await this.delay(1000);
            
            // 🚫 完全禁用前端重新计算，只使用后端数据
            console.log('🔍 analyzeBaselineResults: 开始处理基准算法指标');
            console.log('🔍 baselineResult.rawResponse存在:', !!this.baselineResult.rawResponse);
            console.log('🔍 baselineResult.metrics存在:', !!this.baselineResult.metrics);
            console.log('🔍 baselineResult.metrics内容:', this.baselineResult.metrics);

            // 直接使用后端计算的指标，绝不重新计算
            this.baselineResult.detailedMetrics = this.baselineResult.metrics;
            console.log('✅ 直接使用后端指标作为detailedMetrics:', this.baselineResult.detailedMetrics);
            
            // 显示基准算法指标
            this.displayBaselineMetrics();
            
            this.updateStepStatus('baseline_analysis', 'completed');
            this.log('✅ 基准算法指标统计完成', 'success');
            
        } catch (error) {
            this.updateStepStatus('baseline_analysis', 'error');
            throw new Error(`基准算法指标统计失败: ${error.message}`);
        }
    }
    
    /**
     * 运行改进算法
     */
    async runImprovedAlgorithm() {
        this.updateStepStatus('improved', 'running');
        this.log('🚀 正在运行改进算法...', 'info');

        try {
            // 获取建筑数据
            const buildingData = await this.cityManager.getBuildingDataForPathPlanning();
            
            // 创建改进算法请求
            const requestData = {
                algorithm: 'ImprovedClusterBased',
                startPoint: {
                    lng: this.cityManager.startPoint.lng,
                    lat: this.cityManager.startPoint.lat,
                    alt: this.cityManager.flightHeight,
                    x: this.cityManager.startPoint.x || 0,
                    y: this.cityManager.startPoint.y || 0,
                    z: this.cityManager.flightHeight
                },
                endPoint: {
                    lng: this.cityManager.endPoint.lng,
                    lat: this.cityManager.endPoint.lat,
                    alt: this.cityManager.flightHeight,
                    x: this.cityManager.endPoint.x || 100,
                    y: this.cityManager.endPoint.y || 100,
                    z: this.cityManager.flightHeight
                },
                flightHeight: this.cityManager.flightHeight,
                safetyDistance: this.cityManager.safetyDistance,
                buildings: buildingData || [],
                parameters: {
                    flightHeight: this.cityManager.flightHeight,
                    safetyDistance: this.cityManager.safetyDistance,
                    maxTurnAngle: 90,
                    riskEdgeDistance: 50,
                    kValue: 5,
                    enablePathSwitching: true
                }
            };
            
            // 调用Python后端
            const response = await this.cityManager.pythonClient.calculatePath(requestData);
            
            if (response && response.success) {
                console.log('🎯 改进算法完成回调被执行');
                console.log('🎯 改进算法响应结构:', response);
                console.log('🎯 检查protectionZonesInfo:', response.protectionZonesInfo);

                this.improvedResult = {
                    algorithm: '改进分簇算法',
                    path: this.cityManager.pythonClient.formatPathData(response),
                    metrics: this.extractMetrics(response),
                    rawResponse: response
                };

                // 显示改进算法路径
                this.displayImprovedPath();

                // 🔧 修复：处理改进算法的保护区信息
                console.log('🔍 检查改进算法保护区信息...');
                console.log('🔍 response.protectionZonesInfo存在:', !!response.protectionZonesInfo);

                if (response.protectionZonesInfo) {
                    console.log('🔍 protectionZonesInfo内容:', response.protectionZonesInfo);
                    console.log('🔍 collision_cost_breakdown存在:', !!response.protectionZonesInfo.collision_cost_breakdown);
                    console.log('🔍 collision_cost_breakdown内容:', response.protectionZonesInfo.collision_cost_breakdown);

                    if (response.protectionZonesInfo.collision_cost_breakdown) {
                        const activeZoneIds = Object.keys(response.protectionZonesInfo.collision_cost_breakdown);
                        console.log('🛡️ 改进算法保护区信息:', response.protectionZonesInfo);
                        console.log('🛡️ 活跃保护区ID:', activeZoneIds);
                        console.log('🛡️ 活跃保护区数量:', activeZoneIds.length);

                        // 更新前端地图上的保护区状态
                        if (this.cityManager && typeof this.cityManager.updateProtectionZoneStatus === 'function') {
                            console.log('🛡️ 调用updateProtectionZoneStatus，传递ID:', activeZoneIds);
                            this.cityManager.updateProtectionZoneStatus(activeZoneIds);
                            console.log('🛡️ updateProtectionZoneStatus调用完成');
                        } else {
                            console.error('❌ cityManager或updateProtectionZoneStatus方法不可用');
                        }

                        this.log(`🛡️ 改进算法使用了 ${activeZoneIds.length} 个保护区`, 'info');
                    } else {
                        console.log('⚠️ collision_cost_breakdown为空或不存在');
                    }
                } else {
                    console.log('⚠️ 改进算法响应中没有protectionZonesInfo');
                }

                this.updateStepStatus('improved', 'completed');
                this.log(`✅ 改进算法完成，路径点数: ${this.improvedResult.path.length}`, 'success');

            } else {
                throw new Error(response ? response.error : '改进算法执行失败');
            }
            
        } catch (error) {
            this.updateStepStatus('improved', 'error');
            throw new Error(`改进算法执行失败: ${error.message}`);
        }
    }
    
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 模拟改进算法飞行
     */
    async simulateImprovedFlight() {
        this.updateStepStatus('improved_flight', 'running');
        this.log('✈️ 开始改进算法飞行模拟...', 'info');

        try {
            // 设置当前路径为改进算法路径
            this.cityManager.currentPath = this.improvedResult.path;

            // 启动飞行模拟
            await this.cityManager.startFlight();

            // 等待飞行完成
            await this.waitForFlightCompletion();

            // 🚫 不再计算改进算法指标，直接使用后端数据
            this.improvedResult.detailedMetrics = this.improvedResult.metrics;

            this.updateStepStatus('improved_flight', 'completed');
            this.log('✅ 改进算法飞行模拟完成', 'success');

        } catch (error) {
            this.updateStepStatus('improved_flight', 'error');
            throw new Error(`改进算法飞行模拟失败: ${error.message}`);
        }
    }

    /**
     * 等待飞行完成
     */
    async waitForFlightCompletion() {
        return new Promise((resolve) => {
            const checkFlight = () => {
                if (!this.cityManager.isFlying) {
                    resolve();
                } else {
                    setTimeout(checkFlight, 500);
                }
            };
            checkFlight();
        });
    }

    /**
     * 生成对比图表
     */
    async generateComparisonChart() {
        this.updateStepStatus('comparison', 'running');
        this.log('📊 正在生成对比图表...', 'info');

        try {
            // 🚫 绝不重新计算基准算法指标，只使用后端数据
            if (!this.baselineResult.detailedMetrics) {
                console.log('⚠️ baselineResult.detailedMetrics 不存在，使用后端metrics');
                this.baselineResult.detailedMetrics = this.baselineResult.metrics || {};
            }
            console.log('✅ generateComparisonChart: 基准算法指标:', this.baselineResult.detailedMetrics);
            if (!this.improvedResult.detailedMetrics) {
                this.improvedResult.detailedMetrics = this.improvedResult.metrics || {};
            }

            // 安全获取指标数据，提供默认值
            const getMetricValue = (result, metric) => {
                const value = result.detailedMetrics?.[metric] || result.metrics?.[metric] || 0;
                return isNaN(value) ? 0 : Number(value);
            };

            // 准备对比数据
            const comparisonData = {
                improved: {
                    pathLength: getMetricValue(this.improvedResult, 'pathLength'),
                    turningCost: getMetricValue(this.improvedResult, 'turningCost'),
                    riskValue: getMetricValue(this.improvedResult, 'riskValue'),
                    collisionCost: getMetricValue(this.improvedResult, 'collisionCost'),
                    finalCost: getMetricValue(this.improvedResult, 'finalCost')
                },
                baseline: {
                    pathLength: getMetricValue(this.baselineResult, 'pathLength'),
                    turningCost: getMetricValue(this.baselineResult, 'turningCost'),
                    riskValue: getMetricValue(this.baselineResult, 'riskValue'),
                    collisionCost: getMetricValue(this.baselineResult, 'collisionCost'),
                    finalCost: getMetricValue(this.baselineResult, 'finalCost')
                }
            };

            // 验证数据有效性
            this.log(`📊 基准算法指标: 路径长度=${comparisonData.baseline.pathLength.toFixed(2)}m, 转向成本=${comparisonData.baseline.turningCost.toFixed(2)}, 风险值=${comparisonData.baseline.riskValue.toFixed(2)}, 碰撞代价=${comparisonData.baseline.collisionCost.toFixed(2)}, 最终代价=${comparisonData.baseline.finalCost.toFixed(2)}`, 'info');
            this.log(`📊 改进算法指标: 路径长度=${comparisonData.improved.pathLength.toFixed(2)}m, 转向成本=${comparisonData.improved.turningCost.toFixed(2)}, 风险值=${comparisonData.improved.riskValue.toFixed(2)}, 碰撞代价=${comparisonData.improved.collisionCost.toFixed(2)}, 最终代价=${comparisonData.improved.finalCost.toFixed(2)}`, 'info');

            // 将对比数据传递给现代化面板管理器
            if (this.cityManager && this.cityManager.panelManager) {
                this.cityManager.panelManager.setComparisonData(comparisonData.improved, comparisonData.baseline);
                this.log('✅ 对比数据已传递给面板管理器', 'info');
            }

            // 启用对比图表按钮
            const comparisonChartBtn = document.getElementById('show-comparison-chart-btn');
            if (comparisonChartBtn) {
                comparisonChartBtn.disabled = false;
                this.log('✅ 对比图表按钮已启用', 'info');
            }

            // 显示对比图表（兼容旧版本）
            if (typeof showAlgorithmComparison === 'function') {
                showAlgorithmComparison(comparisonData.improved, comparisonData.baseline);
            }

            // 显示详细对比结果
            this.displayComparisonResults(comparisonData);

            this.updateStepStatus('comparison', 'completed');
            this.log('✅ 对比图表生成完成', 'success');

            // 延迟3秒后清除高度显示，让用户看到完成状态
            setTimeout(() => {
                if (this.cityManager && typeof this.cityManager.clearAltitudeDisplay === 'function') {
                    this.cityManager.clearAltitudeDisplay();
                }
            }, 3000);

        } catch (error) {
            this.updateStepStatus('comparison', 'error');
            throw new Error(`对比图表生成失败: ${error.message}`);
        }
    }

    /**
     * 显示基准算法路径
     */
    displayBaselinePath() {
        // 🔧 修复：在对比模式下不清除现有路径，而是添加基准路径图层
        this.addBaselinePathLayer();

        this.log('🟠 基准算法路径已显示（橙色）', 'info');
    }

    /**
     * 显示改进算法路径
     */
    displayImprovedPath() {
        // 不清除基准路径，而是添加新的改进路径图层
        this.cityManager.currentPath = this.improvedResult.path;

        // 添加改进算法路径图层（蓝色）
        this.addImprovedPathLayer();

        this.log('🔵 改进算法路径已显示（蓝色）', 'info');
    }

    /**
     * 添加改进算法路径图层
     */
    addImprovedPathLayer() {
        console.log('🔧 addImprovedPathLayer 调用');
        console.log('   地图存在:', !!this.cityManager.map);
        console.log('   改进路径存在:', !!this.improvedResult.path);
        console.log('   改进路径长度:', this.improvedResult.path ? this.improvedResult.path.length : 0);

        if (!this.cityManager.map || !this.improvedResult.path) return;

        const coordinates = this.improvedResult.path.map(point => [point.lng, point.lat]);
        console.log('   坐标数组长度:', coordinates.length);
        console.log('   前3个坐标:', coordinates.slice(0, 3));

        // 移除旧的改进路径图层
        if (this.cityManager.map.getSource('improved-path')) {
            this.cityManager.map.removeLayer('improved-path-layer');
            this.cityManager.map.removeSource('improved-path');
        }

        // 添加改进路径数据源
        this.cityManager.map.addSource('improved-path', {
            type: 'geojson',
            data: {
                type: 'Feature',
                properties: {},
                geometry: {
                    type: 'LineString',
                    coordinates: coordinates
                }
            }
        });

        // 添加改进路径图层
        this.cityManager.map.addLayer({
            id: 'improved-path-layer',
            type: 'line',
            source: 'improved-path',
            paint: {
                'line-color': '#00d4ff',
                'line-width': 6,
                'line-opacity': 0.8
            }
        });
    }

    /**
     * 添加基准算法路径图层
     */
    addBaselinePathLayer() {
        console.log('🔧 addBaselinePathLayer 调用');
        console.log('   地图存在:', !!this.cityManager.map);
        console.log('   基准路径存在:', !!this.baselineResult.path);
        console.log('   基准路径长度:', this.baselineResult.path ? this.baselineResult.path.length : 0);

        if (!this.cityManager.map || !this.baselineResult.path) return;

        const coordinates = this.baselineResult.path.map(point => [point.lng, point.lat]);
        console.log('   坐标数组长度:', coordinates.length);
        console.log('   前3个坐标:', coordinates.slice(0, 3));

        // 移除旧的基准路径图层
        if (this.cityManager.map.getSource('baseline-path')) {
            this.cityManager.map.removeLayer('baseline-path-layer');
            this.cityManager.map.removeSource('baseline-path');
        }

        // 添加基准路径数据源
        this.cityManager.map.addSource('baseline-path', {
            type: 'geojson',
            data: {
                type: 'Feature',
                properties: {},
                geometry: {
                    type: 'LineString',
                    coordinates: coordinates
                }
            }
        });

        // 添加基准路径图层（橙色/红色）
        this.cityManager.map.addLayer({
            id: 'baseline-path-layer',
            type: 'line',
            source: 'baseline-path',
            paint: {
                'line-color': '#ff6b35',  // 橙色
                'line-width': 6,
                'line-opacity': 0.8
            }
        });
    }

    /**
     * 显示基准算法指标
     */
    displayBaselineMetrics() {
        const metrics = this.baselineResult.detailedMetrics;
        console.log('🔍 displayBaselineMetrics - detailedMetrics:', metrics);
        console.log('🔍 displayBaselineMetrics - baselineResult:', this.baselineResult);
        console.log('🔍 displayBaselineMetrics - 显示的路径长度:', metrics.pathLength);
        console.log('🔍 displayBaselineMetrics - 显示的转向成本:', metrics.turningCost);
        console.log('🔍 displayBaselineMetrics - 显示的风险值:', metrics.riskValue);
        console.log('🔍 displayBaselineMetrics - 显示的碰撞代价:', metrics.collisionCost);
        console.log('🔍 displayBaselineMetrics - 显示的最终代价:', metrics.finalCost);

        this.log(`📊 基准算法指标:`, 'info');
        this.log(`   路径长度: ${metrics.pathLength.toFixed(2)}m`, 'info');
        this.log(`   转向成本: ${metrics.turningCost.toFixed(2)}`, 'info');
        this.log(`   风险值: ${metrics.riskValue.toFixed(2)}`, 'info');
        this.log(`   碰撞代价: ${metrics.collisionCost.toFixed(2)}`, 'info');
        this.log(`   最终代价: ${metrics.finalCost.toFixed(2)}`, 'info');
    }

    /**
     * 提取指标数据
     */
    extractMetrics(response) {
        console.log('🔍 extractMetrics 输入响应:', response);
        
        // 详细记录响应中的关键字段
        console.log('🔍 响应中的pathLength:', response.pathLength);
        console.log('🔍 响应中的path_length:', response.path_length);
        console.log('🔍 响应中的turningCost:', response.turningCost);
        console.log('🔍 响应中的turning_cost:', response.turning_cost);
        console.log('🔍 响应中的riskValue:', response.riskValue);
        console.log('🔍 响应中的risk_value:', response.risk_value);
        console.log('🔍 响应中的collisionCost:', response.collisionCost);
        console.log('🔍 响应中的collision_cost:', response.collision_cost);
        console.log('🔍 响应中的finalCost:', response.finalCost);
        console.log('🔍 响应中的final_cost:', response.final_cost);
        console.log('🔍 响应的所有字段:', Object.keys(response));
        
        // 检查metadata中是否包含指标
        if (response.metadata && typeof response.metadata === 'object') {
            console.log('🔍 响应中包含metadata:', response.metadata);
            
            // 检查metadata.metrics
            if (response.metadata.metrics) {
                console.log('🔍 metadata中包含metrics:', response.metadata.metrics);
            }
            
            // 检查metadata.calculations (可能包含详细计算过程)
            if (response.metadata.calculations) {
                console.log('🔍 metadata中包含calculations:', response.metadata.calculations);
                console.log('🔍 metadata.calculations中的finalCost:', 
                    response.metadata.calculations.finalCost || response.metadata.calculations.final_cost);
            }
        }
        
        // 统一提取逻辑，按优先级顺序尝试不同位置
        const getMetricValue = (metricName) => {
            // 1. 直接从响应根级别获取 (驼峰命名)
            if (response[metricName] !== undefined && response[metricName] !== null) {
                console.log(`🔍 从响应根级别获取 ${metricName}:`, response[metricName]);
                return response[metricName];
            }
            
            // 2. 直接从响应根级别获取 (下划线命名)
            const snakeCase = metricName.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
            if (response[snakeCase] !== undefined && response[snakeCase] !== null) {
                console.log(`🔍 从响应根级别获取 ${snakeCase}:`, response[snakeCase]);
                return response[snakeCase];
            }
            
            // 3. 从metadata.metrics获取
            if (response.metadata && response.metadata.metrics && 
                response.metadata.metrics[metricName] !== undefined && 
                response.metadata.metrics[metricName] !== null) {
                console.log(`🔍 从metadata.metrics获取 ${metricName}:`, response.metadata.metrics[metricName]);
                return response.metadata.metrics[metricName];
            }
            
            // 4. 从metadata.calculations获取
            if (response.metadata && response.metadata.calculations) {
                // 尝试驼峰命名
                if (response.metadata.calculations[metricName] !== undefined && 
                    response.metadata.calculations[metricName] !== null) {
                    console.log(`🔍 从metadata.calculations获取 ${metricName}:`, 
                        response.metadata.calculations[metricName]);
                    return response.metadata.calculations[metricName];
                }
                
                // 尝试下划线命名
                if (response.metadata.calculations[snakeCase] !== undefined && 
                    response.metadata.calculations[snakeCase] !== null) {
                    console.log(`🔍 从metadata.calculations获取 ${snakeCase}:`, 
                        response.metadata.calculations[snakeCase]);
                    return response.metadata.calculations[snakeCase];
                }
            }
            
            // 5. 从metadata直接获取
            if (response.metadata && 
                response.metadata[metricName] !== undefined && 
                response.metadata[metricName] !== null) {
                console.log(`🔍 从metadata直接获取 ${metricName}:`, response.metadata[metricName]);
                return response.metadata[metricName];
            }
            
            console.log(`⚠️ 未找到指标 ${metricName}，返回0`);
            return 0;
        };
        
        // 使用新的提取逻辑获取所有指标
        const metrics = {
            pathLength: getMetricValue('pathLength'),
            turningCost: getMetricValue('turningCost'),
            riskValue: getMetricValue('riskValue'),
            collisionCost: getMetricValue('collisionCost'),
            finalCost: getMetricValue('finalCost'),
            executionTime: getMetricValue('executionTime')
        };

        // 检查finalCost是否为0，这可能表示提取失败
        if (metrics.finalCost === 0 && response.algorithm === 'AStar') {
            console.warn('⚠️ A*算法的finalCost为0，这可能是错误的。尝试从其他位置获取或重新计算...');
            
            // 尝试从其他可能的字段名获取
            const possibleFinalCostFields = [
                'total_cost', 'totalCost', 'path_cost', 'pathCost', 
                'final_score', 'finalScore', 'cost'
            ];
            
            for (const field of possibleFinalCostFields) {
                if (response[field] !== undefined && response[field] !== null) {
                    console.log(`🔍 从字段 ${field} 获取finalCost:`, response[field]);
                    metrics.finalCost = response[field];
                    break;
                }
                
                if (response.metadata && response.metadata[field] !== undefined && 
                    response.metadata[field] !== null) {
                    console.log(`🔍 从metadata.${field} 获取finalCost:`, response.metadata[field]);
                    metrics.finalCost = response.metadata[field];
                    break;
                }
            }
        }

        console.log('🔍 extractMetrics 最终输出指标:', metrics);
        return metrics;
    }

    /**
     * 计算详细指标
     */
    calculateDetailedMetrics(result) {
        console.log('🔍 calculateDetailedMetrics 被调用，输入结果:', result);

        // 🚫 完全禁用前端计算，只使用后端数据
        if (result.metrics) {
            console.log('✅ 直接使用后端指标，不进行任何前端计算');
            return result.metrics;
        }

        // 如果没有后端指标，返回空指标（不进行前端计算）
        console.log('⚠️ 没有后端指标，返回空指标');
        return {
            pathLength: 0,
            turningCost: 0,
            riskValue: 0,
            collisionCost: 0,
            finalCost: 0
        };
    }

    // 🚫 所有路径计算方法已删除，只使用后端计算结果

    /**
     * 显示对比结果
     */
    displayComparisonResults(comparisonData) {
        const resultsDiv = document.getElementById('comparison-results');
        if (!resultsDiv) return;

        const improved = comparisonData.improved;
        const baseline = comparisonData.baseline;

        // 确保所有值都是有效数字
        const validateNumber = (value) => {
            if (value === undefined || value === null || isNaN(value)) return 0;
            return Number(value);
        };

        // 安全地获取改进值
        const getImprovement = (baseValue, improvedValue) => {
            baseValue = validateNumber(baseValue);
            improvedValue = validateNumber(improvedValue);
            
            // 避免除以零
            if (baseValue === 0) {
                // 如果基准值为0但改进值不为0，对于"越小越好"的指标
                return improvedValue === 0 ? 0 : (improvedValue > 0 ? -100 : 100);
            }
            
            // 所有指标都是"越小越好"的逻辑
            return ((baseValue - improvedValue) / baseValue * 100);
        };

        // 计算改进百分比
        const improvements = {
            pathLength: getImprovement(baseline.pathLength, improved.pathLength),
            turningCost: getImprovement(baseline.turningCost, improved.turningCost),
            riskValue: getImprovement(baseline.riskValue, improved.riskValue),
            collisionCost: getImprovement(baseline.collisionCost, improved.collisionCost),
            finalCost: getImprovement(baseline.finalCost, improved.finalCost)
        };

        // 记录计算的改进百分比
        console.log('🔍 计算的改进百分比:', improvements);

        resultsDiv.innerHTML = `
            <h4>📊 算法对比结果</h4>
            <div class="metrics-comparison">
                <div class="metric-row">
                    <span class="metric-name">路径长度</span>
                    <span class="baseline-value">${validateNumber(baseline.pathLength).toFixed(1)}m</span>
                    <span class="improved-value">${validateNumber(improved.pathLength).toFixed(1)}m</span>
                    <span class="improvement ${improvements.pathLength > 0 ? 'positive' : 'negative'}">
                        ${improvements.pathLength.toFixed(1)}%
                    </span>
                </div>
                <div class="metric-row">
                    <span class="metric-name">转向成本</span>
                    <span class="baseline-value">${validateNumber(baseline.turningCost).toFixed(2)}</span>
                    <span class="improved-value">${validateNumber(improved.turningCost).toFixed(2)}</span>
                    <span class="improvement ${improvements.turningCost > 0 ? 'positive' : 'negative'}">
                        ${improvements.turningCost.toFixed(1)}%
                    </span>
                </div>
                <div class="metric-row">
                    <span class="metric-name">风险值</span>
                    <span class="baseline-value">${validateNumber(baseline.riskValue).toFixed(2)}</span>
                    <span class="improved-value">${validateNumber(improved.riskValue).toFixed(2)}</span>
                    <span class="improvement ${improvements.riskValue > 0 ? 'positive' : 'negative'}">
                        ${improvements.riskValue.toFixed(1)}%
                    </span>
                </div>
                <div class="metric-row">
                    <span class="metric-name">碰撞代价</span>
                    <span class="baseline-value">${validateNumber(baseline.collisionCost).toFixed(2)}</span>
                    <span class="improved-value">${validateNumber(improved.collisionCost).toFixed(2)}</span>
                    <span class="improvement ${improvements.collisionCost > 0 ? 'positive' : 'negative'}">
                        ${improvements.collisionCost.toFixed(1)}%
                    </span>
                </div>
                <div class="metric-row total">
                    <span class="metric-name">最终代价</span>
                    <span class="baseline-value">${validateNumber(baseline.finalCost).toFixed(2)}</span>
                    <span class="improved-value">${validateNumber(improved.finalCost).toFixed(2)}</span>
                    <span class="improvement ${improvements.finalCost > 0 ? 'positive' : 'negative'}">
                        ${improvements.finalCost.toFixed(1)}%
                    </span>
                </div>
            </div>
            <div class="comparison-summary">
                <p>改进算法相比基准算法总体改进: <strong>${improvements.finalCost.toFixed(1)}%</strong></p>
            </div>
        `;

        resultsDiv.style.display = 'block';
    }

    /**
     * 更新步骤显示
     */
    updateStepsDisplay() {
        const stepsDiv = document.getElementById('comparison-steps');
        if (!stepsDiv) {
            console.warn('⚠️ comparison-steps 元素未找到');
            return;
        }

        console.log('🔄 更新步骤显示:', this.comparisonSteps);
        console.log('🔄 步骤数组长度:', this.comparisonSteps.length);

        const stepsHTML = this.comparisonSteps.map((step, index) => {
            console.log(`🔍 处理步骤 ${index}:`, step);
            const stepName = step.name || '未知步骤';
            const statusText = this.getStatusText(step.status);
            const icon = this.getStepIcon(step.status);

            console.log(`📝 生成步骤 ${step.id}: "${stepName}" - ${statusText}`);
            console.log(`📝 步骤名字长度:`, stepName.length);
            console.log(`📝 步骤详情:`, step);

            return `
                <div class="step-item ${step.status}" id="step-${step.id}">
                    <div class="step-icon">
                        ${icon}
                    </div>
                    <div class="step-content">
                        <span class="step-name" style="color: #ffffff !important; font-weight: 500 !important; font-size: 14px !important; display: block !important; visibility: visible !important; opacity: 1 !important; -webkit-text-fill-color: #ffffff !important;">${stepName}</span>
                        <span class="step-status" style="color: #cccccc !important; font-size: 12px !important; display: block !important; visibility: visible !important; opacity: 1 !important; -webkit-text-fill-color: #cccccc !important;">${statusText}</span>
                    </div>
                </div>
            `;
        }).join('');

        stepsDiv.innerHTML = stepsHTML;
        console.log('✅ 步骤显示更新完成');


    }

    /**
     * 显示对比面板
     */
    showComparisonPanel() {
        const panel = document.getElementById('comparison-chart-panel');
        if (panel) {
            panel.style.display = 'block';
            this.log('📊 对比面板已显示', 'info');
        } else {
            console.error('❌ 找不到对比面板元素');
        }
    }

    /**
     * 测试步骤显示（调试用）
     */
    testStepsDisplay() {
        console.log('🧪 测试步骤显示');
        console.log('🧪 comparisonSteps:', this.comparisonSteps);

        // 检查所有相关DOM元素
        const panel = document.getElementById('comparison-chart-panel');
        const container = document.getElementById('comparison-steps-container');
        const stepsDiv = document.getElementById('comparison-steps');

        console.log('🧪 对比面板:', panel);
        console.log('🧪 步骤容器:', container);
        console.log('🧪 步骤显示区:', stepsDiv);

        if (panel) {
            console.log('🧪 面板显示状态:', panel.style.display);
            console.log('🧪 面板内容:', panel.innerHTML.substring(0, 200) + '...');
        }

        this.updateStepsDisplay();

        if (stepsDiv) {
            console.log('🧪 步骤容器HTML:', stepsDiv.innerHTML);
        }
    }

    /**
     * 更新步骤状态
     */
    updateStepStatus(stepId, status) {
        const step = this.comparisonSteps.find(s => s.id === stepId);
        if (step) {
            step.status = status;
            this.updateStepsDisplay();
        }
    }

    /**
     * 获取步骤图标
     */
    getStepIcon(status) {
        switch (status) {
            case 'pending': return '⏳';
            case 'running': return '🔄';
            case 'completed': return '✅';
            case 'error': return '❌';
            default: return '⏳';
        }
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        switch (status) {
            case 'pending': return '等待中';
            case 'running': return '进行中';
            case 'completed': return '已完成';
            case 'error': return '失败';
            default: return '等待中';
        }
    }

    /**
     * 重置对比状态
     */
    resetComparisonState() {
        this.baselineResult = null;
        this.improvedResult = null;
        this.comparisonSteps.forEach(step => {
            step.status = 'pending';
        });
        this.updateStepsDisplay();

        // 隐藏结果面板
        const resultsDiv = document.getElementById('comparison-results');
        if (resultsDiv) {
            resultsDiv.style.display = 'none';
        }
    }

    /**
     * 显示对比面板
     */
    showComparisonPanel() {
        const panel = document.getElementById('algorithm-comparison-panel');
        if (panel) {
            panel.style.display = 'block';
        }
    }

    /**
     * 隐藏对比面板
     */
    hideComparisonPanel() {
        const panel = document.getElementById('algorithm-comparison-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * 日志输出
     */
    log(message, type = 'info') {
        if (this.cityManager && this.cityManager.log) {
            this.cityManager.log(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}
