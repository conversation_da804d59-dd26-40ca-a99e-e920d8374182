#!/usr/bin/env python3
"""
测试完整的CSV导出功能
包含起点坐标、终点坐标、飞行高度、簇ID等新增字段
"""

import os
import sys
import json
import glob

try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False

def test_json_data_structure():
    """检查JSON数据结构"""
    print("🔍 检查JSON数据结构...")
    
    json_files = glob.glob('json/all_81_paths_data_*.json')
    if not json_files:
        print("❌ 没有找到JSON文件")
        return False, None

    latest_json = max(json_files, key=os.path.getctime)
    print(f"✅ 找到JSON文件: {latest_json}")
    
    try:
        with open(latest_json, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查metadata
        metadata = data.get('metadata', {})
        start_point = metadata.get('start_point', {})
        end_point = metadata.get('end_point', {})
        
        print(f"✅ 起点坐标: 经度{start_point.get('lng', 0):.6f}, 纬度{start_point.get('lat', 0):.6f}, 高度{start_point.get('alt', 0):.2f}")
        print(f"✅ 终点坐标: 经度{end_point.get('lng', 0):.6f}, 纬度{end_point.get('lat', 0):.6f}, 高度{end_point.get('alt', 0):.2f}")
        
        # 检查路径数据
        all_paths = data.get('all_paths', [])
        if not all_paths:
            print("❌ 没有路径数据")
            return False, None
        
        print(f"✅ 路径数量: {len(all_paths)}")
        
        # 检查基准路径
        baseline_found = False
        selected_path_id = data.get('selected_path', {}).get('selected_path_id')
        
        for path in all_paths[:5]:  # 检查前5条路径
            path_id = path.get('path_id')
            if path_id == 'BASELINE_A*':
                baseline_found = True
                print(f"✅ 找到基准路径: {path_id}")
            else:
                cluster_id = path.get('cluster_id', '')
                height_layer = path.get('height_layer', '')
                flight_direction = path.get('flight_direction', '')
                print(f"✅ 改进路径: ID={path_id}, 方向={flight_direction}, 高度={height_layer}, 簇={cluster_id}")
        
        if not baseline_found:
            print("⚠️ 未找到基准路径")
        
        print(f"✅ 选中路径ID: {selected_path_id}")
        
        return True, latest_json
        
    except Exception as e:
        print(f"❌ 读取JSON文件失败: {e}")
        return False, None

def test_api_export():
    """测试API导出功能"""
    print("\n🌐 测试API导出...")

    if not HAS_REQUESTS:
        print("⚠️ requests模块未安装，跳过API测试")
        return True

    try:
        response = requests.post('http://localhost:5000/api/export_calculated_paths', 
                               json={'test': True})
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ API导出成功")
                print(f"   文件名: {data.get('filename')}")
                print(f"   路径数: {data.get('total_paths')}")
                print(f"   源JSON: {data.get('source_json')}")
                
                # 检查生成的CSV文件
                csv_path = data.get('filepath')
                if csv_path and os.path.exists(csv_path):
                    print(f"✅ CSV文件已创建: {csv_path}")
                    
                    # 读取并检查内容
                    with open(csv_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    if len(lines) > 0:
                        header = lines[0].strip()
                        print(f"\n📋 CSV表头:")
                        print(f"   {header}")
                        
                        # 检查新增字段
                        required_fields = ['起点经度', '起点纬度', '起点高度', '终点经度', '终点纬度', '终点高度',
                                         '飞行高度', '簇ID', '保护区数量', '保护区列表', '保护区类型']
                        missing_fields = []
                        for field in required_fields:
                            if field not in header:
                                missing_fields.append(field)
                        
                        if missing_fields:
                            print(f"❌ 缺少字段: {missing_fields}")
                            return False
                        else:
                            print("✅ 所有必需字段都存在")
                    
                    if len(lines) > 1:
                        print(f"\n📋 数据示例 (前3行):")
                        for i, line in enumerate(lines[:3]):
                            if '🎯' in line or '✓' in line:
                                print(f"   {i+1}: {line.strip()[:100]}... ⭐")
                            else:
                                print(f"   {i+1}: {line.strip()[:100]}...")
                    
                    print(f"\n📊 CSV文件统计:")
                    print(f"   总行数: {len(lines)}")
                    print(f"   数据行数: {len(lines) - 1}")
                    
                    return True
                else:
                    print(f"❌ CSV文件未创建: {csv_path}")
                    return False
            else:
                print(f"❌ API返回失败: {data.get('error')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_csv_content_validation():
    """验证CSV内容的正确性"""
    print("\n📋 验证CSV内容...")
    
    # 查找最新的CSV文件
    csv_files = glob.glob('csv/路径数据_简洁格式_*.csv')
    if not csv_files:
        print("❌ 没有找到CSV文件")
        return False
    
    latest_csv = max(csv_files, key=os.path.getctime)
    print(f"✅ 找到CSV文件: {latest_csv}")
    
    try:
        with open(latest_csv, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) < 2:
            print("❌ CSV文件内容不足")
            return False
        
        # 解析表头
        header = lines[0].strip().split(',')
        print(f"📋 字段数量: {len(header)}")
        
        # 检查数据行
        data_lines = lines[1:]
        baseline_count = 0
        selected_count = 0
        cluster_types = set()
        
        for line in data_lines:
            fields = line.strip().split(',')
            if len(fields) >= len(header):
                path_id = fields[0]
                cluster_id = fields[10] if len(fields) > 10 else ''  # 簇ID字段
                selected = fields[-1]  # 选中字段
                
                if '基准' in path_id or 'BASELINE' in path_id:
                    baseline_count += 1
                
                if '✓' in selected:
                    selected_count += 1
                
                if cluster_id and cluster_id != '基准算法':
                    cluster_types.add(cluster_id)
        
        print(f"✅ 基准路径数量: {baseline_count}")
        print(f"✅ 选中路径数量: {selected_count}")
        print(f"✅ 簇类型数量: {len(cluster_types)}")
        
        if len(cluster_types) > 0:
            print(f"📊 簇类型示例: {list(cluster_types)[:5]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证CSV内容失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 完整CSV导出功能测试")
    print("=" * 60)
    
    # 检查JSON数据结构
    json_ok, json_file = test_json_data_structure()
    
    if not json_ok:
        print("\n❌ 测试终止：JSON数据检查失败")
        return False
    
    # 测试API导出
    api_ok = test_api_export()
    
    # 验证CSV内容
    content_ok = test_csv_content_validation()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   JSON数据:   {'✅ 通过' if json_ok else '❌ 失败'}")
    print(f"   API导出:    {'✅ 通过' if api_ok else '❌ 失败'}")
    print(f"   CSV内容:    {'✅ 通过' if content_ok else '❌ 失败'}")
    
    if json_ok and api_ok and content_ok:
        print("\n🎉 所有测试通过！完整CSV导出功能正常工作")
        print("\n📝 新增字段说明:")
        print("   • 起点经度/纬度/高度: 路径起始点的三维坐标")
        print("   • 终点经度/纬度/高度: 路径终止点的三维坐标")
        print("   • 飞行高度: 路径的实际飞行高度（米）")
        print("   • 簇ID: 路径所属的分簇标识符")
        print("\n🎯 使用说明:")
        print("   1. 在前端点击 '📊 导出81条路径数据' 按钮")
        print("   2. 系统会自动生成包含完整信息的CSV文件")
        print("   3. 文件保存在 csv/ 目录下")
        return True
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
