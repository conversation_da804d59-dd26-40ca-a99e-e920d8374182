#!/usr/bin/env python3
"""
按照日志格式导出路径数据
完全匹配日志中显示的格式和内容
"""

import json
import csv
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

def export_log_format_data() -> Optional[Dict[str, Any]]:
    """
    按照日志格式导出路径数据
    
    Returns:
        导出结果字典，包含文件路径和统计信息
    """
    try:
        print("🔄 开始按日志格式导出路径数据...")
        
        # 获取路径数据
        from export_all_paths_data import export_all_paths_data
        result = export_all_paths_data()
        
        if not result or not result.get('all_paths'):
            print("❌ 没有找到已计算的路径数据")
            return None
        
        # 创建导出目录
        os.makedirs('csv', exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y-%m-%dT%H-%M-%S')
        filename = f'路径数据_日志格式_{timestamp}.csv'
        filepath = os.path.join('csv', filename)
        
        # 准备数据
        csv_data = []
        
        # 添加基准路径（如果存在）
        if result.get('baseline_path'):
            baseline = result['baseline_path']
            csv_data.append({
                'path_id': '🎯基准A*',
                'direction': 'OPTIMAL',
                'height': 'SINGLE',
                'path_length': round(baseline.get('path_length', 0), 2),
                'turning_cost': round(baseline.get('turning_cost', 0), 4),
                'risk_value': round(baseline.get('risk_value', 0), 4),
                'collision_cost': round(baseline.get('collision_cost', 0), 4),
                'final_cost': round(baseline.get('final_cost', 0), 6),
                'waypoint_count': baseline.get('waypoint_count', 0),
                'selected': ''
            })
        
        # 获取选中路径ID
        selected_path_id = result.get('selected_path', {}).get('selected_path_id')
        
        # 添加改进算法路径，按最终代价排序
        all_paths = result['all_paths']
        
        # 按最终代价排序
        sorted_paths = sorted(all_paths, key=lambda x: x.get('metrics', {}).get('final_cost', float('inf')))
        
        for path_data in sorted_paths:
            is_selected = '✓' if path_data.get('path_id') == selected_path_id else ''
            metrics = path_data.get('metrics', {})
            
            csv_data.append({
                'path_id': path_data.get('path_id', ''),
                'direction': path_data.get('flight_direction', ''),
                'height': path_data.get('height_layer', ''),
                'path_length': round(metrics.get('path_length', 0), 2),
                'turning_cost': round(metrics.get('turning_cost', 0), 4),
                'risk_value': round(metrics.get('risk_value', 0), 4),
                'collision_cost': round(metrics.get('collision_cost', 0), 4),
                'final_cost': round(metrics.get('final_cost', 0), 6),
                'waypoint_count': path_data.get('waypoints_count', 0),
                'selected': is_selected
            })
        
        # 写入CSV文件
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['path_id', 'direction', 'height', 'path_length', 'turning_cost', 
                         'risk_value', 'collision_cost', 'final_cost', 'waypoint_count', 'selected']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            # 写入表头（中文）
            writer.writerow({
                'path_id': '路径ID',
                'direction': '方向',
                'height': '高度',
                'path_length': '路径长度',
                'turning_cost': '转向成本',
                'risk_value': '风险值',
                'collision_cost': '碰撞代价',
                'final_cost': '最终代价',
                'waypoint_count': '航点数',
                'selected': '选中'
            })
            
            # 写入数据
            writer.writerows(csv_data)
        
        # 计算统计信息
        path_lengths = [row['path_length'] for row in csv_data[1:]]  # 排除基准路径
        turning_costs = [row['turning_cost'] for row in csv_data[1:]]
        risk_values = [row['risk_value'] for row in csv_data[1:]]
        collision_costs = [row['collision_cost'] for row in csv_data[1:]]
        final_costs = [row['final_cost'] for row in csv_data[1:]]
        
        stats = {
            'total_paths': len(csv_data),
            'improved_paths': len(csv_data) - 1,
            'path_length_range': f"{min(path_lengths):.2f} ~ {max(path_lengths):.2f}" if path_lengths else "N/A",
            'turning_cost_range': f"{min(turning_costs):.4f} ~ {max(turning_costs):.4f}" if turning_costs else "N/A",
            'risk_value_range': f"{min(risk_values):.4f} ~ {max(risk_values):.4f}" if risk_values else "N/A",
            'collision_cost_range': f"{min(collision_costs):.4f} ~ {max(collision_costs):.4f}" if collision_costs else "N/A",
            'final_cost_range': f"{min(final_costs):.6f} ~ {max(final_costs):.6f}" if final_costs else "N/A"
        }
        
        print(f"✅ 日志格式数据导出成功: {filepath}")
        print(f"📊 统计信息:")
        print(f"   总路径数: {stats['total_paths']}")
        print(f"   改进路径数: {stats['improved_paths']}")
        print(f"   路径长度范围: {stats['path_length_range']}")
        print(f"   转向成本范围: {stats['turning_cost_range']}")
        print(f"   风险值范围: {stats['risk_value_range']}")
        print(f"   碰撞代价范围: {stats['collision_cost_range']}")
        print(f"   最终代价范围: {stats['final_cost_range']}")
        
        return {
            'success': True,
            'filepath': filepath,
            'filename': filename,
            'stats': stats,
            'export_time': datetime.now().isoformat()
        }
        
    except Exception as e:
        print(f"❌ 日志格式导出失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_log_format_summary(data: List[Dict[str, Any]]) -> str:
    """
    生成类似日志的摘要文本
    
    Args:
        data: 路径数据列表
        
    Returns:
        格式化的摘要文本
    """
    if not data:
        return "没有可用的路径数据"
    
    # 分离基准路径和改进路径
    baseline_data = None
    improved_data = []
    
    for row in data:
        if row['path_id'] == '🎯基准A*':
            baseline_data = row
        else:
            improved_data.append(row)
    
    # 生成摘要
    summary_lines = []
    summary_lines.append("📋 所有路径数据摘要:")
    
    if baseline_data:
        summary_lines.append(f"   包含: 1条基准路径 + {len(improved_data)}条改进算法路径")
    else:
        summary_lines.append(f"   包含: {len(improved_data)}条改进算法路径")
    
    summary_lines.append("=" * 150)
    summary_lines.append(f"{'路径ID':<15} {'方向':<8} {'高度':<8} {'路径长度':<12} {'转向成本':<12} {'风险值':<12} {'碰撞代价':<12} {'最终代价':<12} {'航点数':<8} {'选中':<4}")
    summary_lines.append("-" * 150)
    
    # 添加基准路径
    if baseline_data:
        summary_lines.append(
            f"{baseline_data['path_id']:<15} "
            f"{baseline_data['direction']:<8} "
            f"{baseline_data['height']:<8} "
            f"{baseline_data['path_length']:<12.2f} "
            f"{baseline_data['turning_cost']:<12.4f} "
            f"{baseline_data['risk_value']:<12.4f} "
            f"{baseline_data['collision_cost']:<12.4f} "
            f"{baseline_data['final_cost']:<12.6f} "
            f"{baseline_data['waypoint_count']:<8} "
            f"{baseline_data['selected']:<4}"
        )
    
    # 添加改进路径
    for row in improved_data:
        summary_lines.append(
            f"{row['path_id']:<15} "
            f"{row['direction']:<8} "
            f"{row['height']:<8} "
            f"{row['path_length']:<12.2f} "
            f"{row['turning_cost']:<12.4f} "
            f"{row['risk_value']:<12.4f} "
            f"{row['collision_cost']:<12.4f} "
            f"{row['final_cost']:<12.6f} "
            f"{row['waypoint_count']:<8} "
            f"{row['selected']:<4}"
        )
    
    summary_lines.append("-" * 150)
    
    # 添加统计信息
    if improved_data:
        path_lengths = [row['path_length'] for row in improved_data]
        turning_costs = [row['turning_cost'] for row in improved_data]
        risk_values = [row['risk_value'] for row in improved_data]
        collision_costs = [row['collision_cost'] for row in improved_data]
        final_costs = [row['final_cost'] for row in improved_data]
        
        summary_lines.append("📊 统计信息:")
        summary_lines.append(f"  路径长度: {min(path_lengths):.2f} ~ {max(path_lengths):.2f} (平均: {sum(path_lengths)/len(path_lengths):.2f})")
        summary_lines.append(f"  转向成本: {min(turning_costs):.4f} ~ {max(turning_costs):.4f} (平均: {sum(turning_costs)/len(turning_costs):.4f})")
        summary_lines.append(f"  风险值:   {min(risk_values):.4f} ~ {max(risk_values):.4f} (平均: {sum(risk_values)/len(risk_values):.4f})")
        summary_lines.append(f"  碰撞代价: {min(collision_costs):.4f} ~ {max(collision_costs):.4f} (平均: {sum(collision_costs)/len(collision_costs):.4f})")
        summary_lines.append(f"  最终代价: {min(final_costs):.6f} ~ {max(final_costs):.6f} (平均: {sum(final_costs)/len(final_costs):.6f})")
    
    return "\n".join(summary_lines)

if __name__ == "__main__":
    result = export_log_format_data()
    if result:
        print(f"\n💾 导出完成!")
        print(f"   文件路径: {result['filepath']}")
        print(f"   文件名: {result['filename']}")
        print(f"   导出时间: {result['export_time']}")
    else:
        print("❌ 导出失败")
