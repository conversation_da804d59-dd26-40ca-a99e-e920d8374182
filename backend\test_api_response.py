#!/usr/bin/env python3
"""
测试API响应
"""

import sys
import os
import json
import glob
from datetime import datetime

def test_api_logic():
    """测试API逻辑"""
    print("🧪 测试API逻辑...")
    
    try:
        # 模拟app.py中的导出逻辑
        
        # 查找最新的JSON文件（从json目录）
        json_files = glob.glob('json/all_81_paths_data_*.json')
        if not json_files:
            print("❌ 没有找到路径数据文件")
            return False
        
        # 使用最新的文件
        latest_json = max(json_files, key=os.path.getctime)
        print(f"📂 使用JSON文件: {latest_json}")
        
        # 读取JSON数据
        with open(latest_json, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data.get('all_paths'):
            print("❌ JSON文件中没有路径数据")
            return False
        
        # 创建CSV文件名
        timestamp = datetime.now().strftime('%Y-%m-%dT%H-%M-%S')
        filename = f'路径数据_完整版_{timestamp}.csv'
        filepath = os.path.join('csv', filename)
        
        print(f"📄 生成的文件名: {filename}")
        print(f"📁 生成的文件路径: {filepath}")
        print(f"📂 源JSON文件名: {os.path.basename(latest_json)}")
        
        # 模拟API返回的数据
        api_response = {
            'success': True,
            'message': '路径数据导出成功',
            'filepath': filepath,
            'filename': filename,
            'total_paths': len(data['all_paths']),
            'source_json': os.path.basename(latest_json),
            'export_time': datetime.now().isoformat()
        }
        
        print(f"\n📊 模拟API返回数据:")
        for key, value in api_response.items():
            print(f"   {key}: {value}")
        
        # 检查字段是否为空
        if not api_response['filename']:
            print("❌ filename字段为空")
        if not api_response['source_json']:
            print("❌ source_json字段为空")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_api():
    """测试实际的API调用"""
    print("\n🌐 测试实际的API调用...")
    
    try:
        import requests
        
        # 调用实际的API
        url = "http://127.0.0.1:5000/api/export_calculated_paths"
        data = {
            "timestamp": datetime.now().isoformat(),
            "request_type": "complete_csv_export"
        }
        
        print(f"📡 发送请求到: {url}")
        print(f"📦 请求数据: {data}")
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功!")
            print(f"📊 API返回数据:")
            for key, value in result.items():
                print(f"   {key}: {value}")
            
            # 检查关键字段
            if not result.get('filename'):
                print("❌ API返回的filename字段为空")
            if not result.get('source_json'):
                print("❌ API返回的source_json字段为空")
            
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('error', '未知错误')}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 API响应测试")
    print("=" * 60)
    
    # 测试API逻辑
    logic_ok = test_api_logic()
    
    # 测试实际API调用
    api_ok = test_actual_api()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   API逻辑:    {'✅ 通过' if logic_ok else '❌ 失败'}")
    print(f"   API调用:    {'✅ 通过' if api_ok else '❌ 失败'}")
    
    if logic_ok and api_ok:
        print("\n🎉 API测试通过！")
        return True
    else:
        print("\n❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
