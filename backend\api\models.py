"""
数学模型API模块
提供路径长度、转向成本、风险模型、碰撞代价等计算服务
"""
from flask import Blueprint, request, jsonify
import numpy as np

# 创建蓝图
models_bp = Blueprint('models', __name__)

@models_bp.route('/calculate_path_metrics', methods=['POST'])
def calculate_path_metrics():
    """
    计算路径的所有指标
    """
    try:
        data = request.get_json()
        path = data.get('path')  # 路径点列表 [[x,y,z], ...]
        
        if not path or len(path) < 2:
            return jsonify({'error': '路径数据无效，至少需要2个点'}), 400
        
        # TODO: 调用实际的数学模型计算函数
        # 当前返回模拟计算结果
        
        # 计算路径长度
        length = calculate_mock_length(path)
        
        # 计算转向成本
        orientation_cost = calculate_mock_orientation_cost(path)
        
        # 计算风险值
        risk_sum = calculate_mock_risk(path)
        
        # 计算碰撞代价
        crash_cost = calculate_mock_crash_cost(path)
        
        # 计算最终代价
        final_cost = calculate_mock_final_cost(length, orientation_cost, risk_sum, crash_cost)
        
        return jsonify({
            'success': True,
            'metrics': {
                'path_length': length,
                'orientation_cost': orientation_cost,
                'risk_sum': risk_sum,
                'crash_cost': crash_cost,
                'final_cost': final_cost
            },
            'path_points': len(path),
            'message': '路径指标计算完成（模拟数据）'
        })
        
    except Exception as e:
        return jsonify({'error': f'路径指标计算失败: {str(e)}'}), 500

@models_bp.route('/calculate_length', methods=['POST'])
def calculate_path_length():
    """
    计算路径长度
    """
    try:
        data = request.get_json()
        path = data.get('path')
        
        if not path or len(path) < 2:
            return jsonify({'error': '路径数据无效'}), 400
        
        length = calculate_mock_length(path)
        
        return jsonify({
            'success': True,
            'path_length': length,
            'formula': 'Length = Σ(|x_{i+1}-x_i| + |y_{i+1}-y_i| + |z_{i+1}-z_i|) - 曼哈顿距离'
        })
        
    except Exception as e:
        return jsonify({'error': f'路径长度计算失败: {str(e)}'}), 500

@models_bp.route('/calculate_risk', methods=['POST'])
def calculate_path_risk():
    """
    计算路径风险值
    """
    try:
        data = request.get_json()
        path = data.get('path')
        
        if not path:
            return jsonify({'error': '路径数据无效'}), 400
        
        risk_sum = calculate_mock_risk(path)
        risk_density = risk_sum / calculate_mock_length(path) if path else 0
        
        return jsonify({
            'success': True,
            'risk_sum': risk_sum,
            'risk_density': risk_density,
            'formula': 'RiskSum = Σ PointRisk(x_i, y_i, z_i)'
        })
        
    except Exception as e:
        return jsonify({'error': f'风险计算失败: {str(e)}'}), 500

@models_bp.route('/optimize_path_set', methods=['POST'])
def optimize_path_set():
    """
    对路径集进行优化和排序
    """
    try:
        data = request.get_json()
        path_set = data.get('path_set')
        
        if not path_set:
            return jsonify({'error': '路径集数据无效'}), 400
        
        # TODO: 实现路径集优化算法
        # 当前进行模拟排序
        
        optimized_paths = []
        for i, path_data in enumerate(path_set):
            path = path_data.get('waypoints', [])
            if len(path) >= 2:
                final_cost = calculate_mock_final_cost_for_path(path)
                optimized_paths.append({
                    'path_id': path_data.get('path_id', f'Path_{i}'),
                    'waypoints': path,
                    'final_cost': final_cost,
                    'ranking': 0  # 将在排序后设置
                })
        
        # 按最终代价排序
        optimized_paths.sort(key=lambda x: x['final_cost'])
        
        # 设置排名
        for i, path in enumerate(optimized_paths):
            path['ranking'] = i + 1
        
        return jsonify({
            'success': True,
            'optimized_path_count': len(optimized_paths),
            'best_path': optimized_paths[0] if optimized_paths else None,
            'all_paths': optimized_paths,
            'message': '路径集优化完成（模拟数据）'
        })
        
    except Exception as e:
        return jsonify({'error': f'路径集优化失败: {str(e)}'}), 500

# 模拟计算函数（后续将被实际算法替换）

def calculate_mock_length(path):
    """模拟路径长度计算 - 使用曼哈顿距离"""
    total_length = 0
    for i in range(len(path) - 1):
        p1 = np.array(path[i])
        p2 = np.array(path[i + 1])
        # 使用曼哈顿距离：|x2-x1| + |y2-y1| + |z2-z1|
        manhattan_distance = np.sum(np.abs(p2 - p1))
        total_length += manhattan_distance
    return float(total_length)

def calculate_mock_orientation_cost(path):
    """模拟转向成本计算"""
    if len(path) < 3:
        return 0.0
    
    total_cost = 0
    for i in range(1, len(path) - 1):
        # 计算转向角度（简化版本）
        v1 = np.array(path[i]) - np.array(path[i-1])
        v2 = np.array(path[i+1]) - np.array(path[i])
        
        # 避免零向量
        if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            cos_angle = np.clip(cos_angle, -1, 1)
            angle = np.arccos(cos_angle)
            total_cost += angle
    
    return float(total_cost)

def calculate_mock_risk(path):
    """模拟风险值计算"""
    # 简单模拟：基于路径点的高度和位置
    risk_sum = 0
    for point in path:
        x, y, z = point
        # 模拟建筑物风险（高度越低风险越高）
        height_risk = max(0, 100 - z) / 100
        risk_sum += height_risk * np.random.uniform(0.1, 1.0)
    
    return float(risk_sum)

def calculate_mock_crash_cost(path):
    """模拟碰撞代价计算"""
    # 简单模拟：基于路径密度
    crash_cost = len(path) * np.random.uniform(0.5, 2.0)
    return float(crash_cost)

def calculate_mock_final_cost(length, orientation_cost, risk_sum, crash_cost):
    """模拟最终代价计算"""
    # 简化的目标函数
    alpha, beta, gamma, delta = 0.6, 0.3, 0.7, 0.2
    
    # 归一化参考值（模拟）
    length_ref = 1000
    orientation_ref = 10
    risk_ref = 50
    crash_ref = 100
    
    final_cost = (alpha * risk_sum / risk_ref + 
                  beta * crash_cost / crash_ref + 
                  gamma * length / length_ref + 
                  delta * orientation_cost / orientation_ref)
    
    return float(final_cost)

def calculate_mock_final_cost_for_path(path):
    """为单个路径计算模拟最终代价"""
    length = calculate_mock_length(path)
    orientation_cost = calculate_mock_orientation_cost(path)
    risk_sum = calculate_mock_risk(path)
    crash_cost = calculate_mock_crash_cost(path)
    
    return calculate_mock_final_cost(length, orientation_cost, risk_sum, crash_cost)
