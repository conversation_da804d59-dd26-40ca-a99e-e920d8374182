/**
 * 物体识别可视化管理器
 * 实现"经过即识别"功能，在无人机飞行过程中显示识别到的物体
 */
class ObjectDetectionVisualizer {
    constructor(cityManager) {
        this.cityManager = cityManager;
        this.detectedObjects = new Map();
        this.detectionMarkers = [];
        this.isDetectionEnabled = true;
        this.detectionRadius = 30; // 30米检测半径

        // 检测间隔控制（200米）
        this.detectionInterval = 200.0; // 200米间隔
        this.lastDetectionPosition = null;
        this.totalFlightDistance = 0;

        // 物体类型配置
        this.objectTypes = {
            'bicycle': { name: '二轮车辆', color: '#ff6b35', cost: 15.0, icon: '🚲' },
            'pedestrian': { name: '行人', color: '#00ff88', cost: 10.0, icon: '🚶' },
            'vehicle': { name: '三轮以上车辆', color: '#ff4444', cost: 8.0, icon: '🚗' }
        };

        this.init();
    }
    
    init() {
        this.createDetectionPanel();
        this.bindEvents();
    }
    
    /**
     * 创建检测面板UI
     */
    createDetectionPanel() {
        // 检查是否已存在面板
        if (document.getElementById('object-detection-panel')) {
            return;
        }
        
        const panel = document.createElement('div');
        panel.id = 'object-detection-panel';
        panel.className = 'detection-panel';
        panel.style.display = 'none';
        
        panel.innerHTML = `
            <div class="detection-panel-header">
                <h3>👁️ 物体识别</h3>
                <div class="detection-controls">
                    <button class="toggle-detection-btn" id="toggle-detection-btn">
                        <span id="detection-status">启用</span>
                    </button>
                    <button class="close-detection-btn" id="close-detection-btn">×</button>
                </div>
            </div>
            <div class="detection-panel-content">
                <div class="detection-stats">
                    <div class="stat-item">
                        <span class="stat-label">检测间隔:</span>
                        <span class="stat-value">${this.detectionInterval}m</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">检测半径:</span>
                        <span class="stat-value">${this.detectionRadius}m</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">已识别物体:</span>
                        <span class="stat-value" id="detected-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总碰撞代价:</span>
                        <span class="stat-value" id="total-collision-cost">0.0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">距上次检测:</span>
                        <span class="stat-value" id="distance-since-last">0m</span>
                    </div>
                </div>
                <div class="detected-objects-list" id="detected-objects-list">
                    <!-- 检测到的物体列表 -->
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        const toggleBtn = document.getElementById('toggle-detection-btn');
        const closeBtn = document.getElementById('close-detection-btn');
        
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.toggleDetection();
            });
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideDetectionPanel();
            });
        }
        
        // 监听无人机位置变化
        if (this.cityManager && typeof this.cityManager.on === 'function') {
            this.cityManager.on('dronePositionUpdate', (position) => {
                try {
                    if (this.isDetectionEnabled && position) {
                        this.performDetection(position);
                        // 更新距离显示
                        this.updateDetectionDisplay(position);
                    }
                } catch (error) {
                    console.error('物体检测处理错误:', error);
                }
            });
        } else {
            console.warn('城市管理器事件系统不可用');
        }
    }
    
    /**
     * 开始物体检测
     */
    startDetection() {
        this.isDetectionEnabled = true;
        this.lastDetectionPosition = null; // 重置检测位置
        this.totalFlightDistance = 0; // 重置飞行距离
        this.showDetectionPanel();
        this.clearDetectedObjects();
        this.log('🔍 物体识别系统已启动（200米间隔检测）', 'info');
    }
    
    /**
     * 停止物体检测
     */
    stopDetection() {
        this.isDetectionEnabled = false;
        this.clearDetectionMarkers();
        this.log('⏹️ 物体识别系统已停止', 'info');
    }
    
    /**
     * 切换检测状态
     */
    toggleDetection() {
        if (this.isDetectionEnabled) {
            this.stopDetection();
            document.getElementById('detection-status').textContent = '禁用';
        } else {
            this.startDetection();
            document.getElementById('detection-status').textContent = '启用';
        }
    }
    
    /**
     * 执行物体检测（每隔200米检测一次）
     */
    performDetection(dronePosition) {
        if (!this.isDetectionEnabled || !dronePosition) return;

        // 检查是否需要进行检测
        const shouldDetect = this.shouldPerformDetection(dronePosition);

        if (shouldDetect) {
            console.log(`🔍 执行人车检测 - 距离上次检测: ${this.getDistanceFromLastDetection(dronePosition).toFixed(1)}米`);

            // 模拟物体检测（在实际应用中这里会调用真实的检测API）
            const detectedObjects = this.simulateObjectDetection(dronePosition);

            // 处理检测结果
            detectedObjects.forEach(obj => {
                this.addDetectedObject(obj);
            });

            // 更新最后检测位置
            this.lastDetectionPosition = { ...dronePosition };

            // 在地图上显示检测区域（可选）
            this.showDetectionZone(dronePosition);

            // 更新显示
            this.updateDetectionDisplay();
        }
    }

    /**
     * 判断是否应该执行检测
     */
    shouldPerformDetection(currentPosition) {
        // 第一次检测
        if (!this.lastDetectionPosition) {
            return true;
        }

        // 计算距离上次检测的距离
        const distance = this.getDistanceFromLastDetection(currentPosition);

        // 如果距离超过200米，则进行检测
        return distance >= this.detectionInterval;
    }

    /**
     * 计算距离上次检测位置的距离
     */
    getDistanceFromLastDetection(currentPosition) {
        if (!this.lastDetectionPosition) {
            return 0;
        }

        return this.calculateDistance(this.lastDetectionPosition, currentPosition);
    }

    /**
     * 计算两点间距离（米）
     */
    calculateDistance(pos1, pos2) {
        const R = 6371000; // 地球半径(米)
        const lat1Rad = pos1.lat * Math.PI / 180;
        const lat2Rad = pos2.lat * Math.PI / 180;
        const deltaLat = (pos2.lat - pos1.lat) * Math.PI / 180;
        const deltaLng = (pos2.lng - pos1.lng) * Math.PI / 180;

        const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                  Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                  Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }

    /**
     * 显示检测区域
     */
    showDetectionZone(position) {
        if (!this.cityManager.map) return;

        // 创建检测区域圆圈
        const detectionZone = {
            type: 'Feature',
            geometry: {
                type: 'Point',
                coordinates: [position.lng, position.lat]
            }
        };

        // 添加或更新检测区域数据源
        if (this.cityManager.map.getSource('detection-zone')) {
            this.cityManager.map.getSource('detection-zone').setData(detectionZone);
        } else {
            this.cityManager.map.addSource('detection-zone', {
                type: 'geojson',
                data: detectionZone
            });

            // 添加检测区域图层
            this.cityManager.map.addLayer({
                id: 'detection-zone-circle',
                type: 'circle',
                source: 'detection-zone',
                paint: {
                    'circle-radius': 15,
                    'circle-color': '#00ff88',
                    'circle-opacity': 0.3,
                    'circle-stroke-width': 2,
                    'circle-stroke-color': '#00ff88',
                    'circle-stroke-opacity': 0.8
                }
            });
        }

        // 3秒后移除检测区域显示
        setTimeout(() => {
            if (this.cityManager.map.getLayer('detection-zone-circle')) {
                this.cityManager.map.removeLayer('detection-zone-circle');
                this.cityManager.map.removeSource('detection-zone');
            }
        }, 3000);
    }
    
    /**
     * 模拟物体检测
     */
    simulateObjectDetection(dronePosition) {
        const objects = [];
        const objectCount = Math.floor(Math.random() * 4); // 0-3个物体
        
        for (let i = 0; i < objectCount; i++) {
            // 随机生成物体类型
            const types = Object.keys(this.objectTypes);
            const type = types[Math.floor(Math.random() * types.length)];
            
            // 在检测半径内随机生成位置
            const angle = Math.random() * 2 * Math.PI;
            const distance = Math.random() * this.detectionRadius;
            
            // 计算物体位置（简化的坐标转换）
            const offsetLng = (distance * Math.cos(angle)) / 111320; // 大约转换为经度
            const offsetLat = (distance * Math.sin(angle)) / 110540; // 大约转换为纬度
            
            const object = {
                id: `obj_${Date.now()}_${i}`,
                type: type,
                position: {
                    lng: dronePosition.lng + offsetLng,
                    lat: dronePosition.lat + offsetLat,
                    alt: 0
                },
                distance: distance,
                confidence: 0.7 + Math.random() * 0.3, // 0.7-1.0的置信度
                detectionTime: Date.now(),
                dronePosition: { ...dronePosition }
            };
            
            objects.push(object);
        }
        
        return objects;
    }
    
    /**
     * 添加检测到的物体
     */
    addDetectedObject(object) {
        // 避免重复添加相同位置的物体
        const existingKey = `${object.position.lng.toFixed(6)}_${object.position.lat.toFixed(6)}`;
        if (this.detectedObjects.has(existingKey)) {
            return;
        }
        
        this.detectedObjects.set(object.id, object);
        
        // 在地图上添加标记
        this.addDetectionMarker(object);
        
        // 记录日志
        const objectConfig = this.objectTypes[object.type];
        this.log(`🎯 检测到${objectConfig.name}，距离${object.distance.toFixed(1)}m，置信度${(object.confidence * 100).toFixed(1)}%`, 'info');
    }
    
    /**
     * 在地图上添加检测标记
     */
    addDetectionMarker(object) {
        if (!this.cityManager.map) return;
        
        const objectConfig = this.objectTypes[object.type];
        
        // 创建标记元素
        const el = document.createElement('div');
        el.className = 'detection-marker';
        el.innerHTML = objectConfig.icon;
        el.style.cssText = `
            width: 24px;
            height: 24px;
            background-color: ${objectConfig.color};
            border: 2px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            animation: detectionPulse 2s ease-in-out;
        `;
        
        // 添加点击事件
        el.addEventListener('click', () => {
            this.showObjectDetails(object);
        });
        
        // 创建Mapbox标记
        const marker = new mapboxgl.Marker(el)
            .setLngLat([object.position.lng, object.position.lat])
            .addTo(this.cityManager.map);
        
        // 存储标记引用
        this.detectionMarkers.push({
            marker: marker,
            object: object,
            element: el
        });
        
        // 5秒后自动移除标记
        setTimeout(() => {
            this.removeDetectionMarker(object.id);
        }, 5000);
    }
    
    /**
     * 移除检测标记
     */
    removeDetectionMarker(objectId) {
        const markerIndex = this.detectionMarkers.findIndex(m => m.object.id === objectId);
        if (markerIndex !== -1) {
            const markerData = this.detectionMarkers[markerIndex];
            markerData.marker.remove();
            this.detectionMarkers.splice(markerIndex, 1);
        }
    }
    
    /**
     * 显示物体详情
     */
    showObjectDetails(object) {
        const objectConfig = this.objectTypes[object.type];
        const details = `
            物体类型: ${objectConfig.name}
            检测距离: ${object.distance.toFixed(1)}m
            置信度: ${(object.confidence * 100).toFixed(1)}%
            碰撞代价: ${objectConfig.cost}
            检测时间: ${new Date(object.detectionTime).toLocaleTimeString()}
        `;
        
        // 创建简单的提示框
        alert(details);
    }
    
    /**
     * 更新检测显示
     */
    updateDetectionDisplay(currentPosition = null) {
        const countElement = document.getElementById('detected-count');
        const costElement = document.getElementById('total-collision-cost');
        const distanceElement = document.getElementById('distance-since-last');
        const listElement = document.getElementById('detected-objects-list');

        if (countElement) {
            countElement.textContent = this.detectedObjects.size;
        }

        if (costElement) {
            const totalCost = Array.from(this.detectedObjects.values())
                .reduce((sum, obj) => sum + this.objectTypes[obj.type].cost, 0);
            costElement.textContent = totalCost.toFixed(1);
        }

        if (distanceElement && currentPosition) {
            const distance = this.getDistanceFromLastDetection(currentPosition);
            distanceElement.textContent = `${distance.toFixed(0)}m`;
        }
        
        if (listElement) {
            const objectsArray = Array.from(this.detectedObjects.values())
                .sort((a, b) => b.detectionTime - a.detectionTime)
                .slice(0, 10); // 只显示最近的10个
            
            listElement.innerHTML = objectsArray.map(obj => {
                const config = this.objectTypes[obj.type];
                return `
                    <div class="detected-object-item">
                        <span class="object-icon">${config.icon}</span>
                        <div class="object-info">
                            <div class="object-name">${config.name}</div>
                            <div class="object-details">
                                ${obj.distance.toFixed(1)}m | ${(obj.confidence * 100).toFixed(0)}%
                            </div>
                        </div>
                        <span class="object-cost">${config.cost}</span>
                    </div>
                `;
            }).join('');
        }
    }
    
    /**
     * 清除检测到的物体
     */
    clearDetectedObjects() {
        this.detectedObjects.clear();
        this.updateDetectionDisplay();
    }
    
    /**
     * 清除检测标记
     */
    clearDetectionMarkers() {
        this.detectionMarkers.forEach(markerData => {
            markerData.marker.remove();
        });
        this.detectionMarkers = [];
    }
    
    /**
     * 显示检测面板
     */
    showDetectionPanel() {
        const panel = document.getElementById('object-detection-panel');
        if (panel) {
            panel.style.display = 'block';
        }
    }
    
    /**
     * 隐藏检测面板
     */
    hideDetectionPanel() {
        const panel = document.getElementById('object-detection-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }
    
    /**
     * 日志输出
     */
    log(message, type = 'info') {
        if (this.cityManager && this.cityManager.log) {
            this.cityManager.log(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}
