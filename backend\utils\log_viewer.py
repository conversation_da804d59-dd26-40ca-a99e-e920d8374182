"""
日志查看器工具
用于查看和分析后端日志
"""

import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path


class LogViewer:
    """日志查看器"""

    def __init__(self, log_dir: str = "logs"):
        # 如果当前在backend目录下，使用相对路径
        if Path.cwd().name == "backend" and Path("logs").exists():
            self.log_dir = Path("logs")
        else:
            self.log_dir = Path(log_dir)
        
    def get_available_dates(self) -> List[str]:
        """获取可用的日志日期"""
        dates = []
        for file in self.log_dir.glob("drone_steps_*.json"):
            date_str = file.stem.split("_")[-1]
            if len(date_str) == 8:  # YYYYMMDD
                dates.append(date_str)
        return sorted(dates, reverse=True)
    
    def load_steps_by_date(self, date: str) -> List[Dict[str, Any]]:
        """根据日期加载步骤日志"""
        step_file = self.log_dir / f"drone_steps_{date}.json"
        if not step_file.exists():
            return []
        
        try:
            with open(step_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载日志失败: {e}")
            return []
    
    def get_steps_by_session(self, session_id: str, date: str = None) -> List[Dict[str, Any]]:
        """根据会话ID获取步骤"""
        if date:
            all_steps = self.load_steps_by_date(date)
        else:
            # 搜索所有日期
            all_steps = []
            for date in self.get_available_dates():
                all_steps.extend(self.load_steps_by_date(date))
        
        return [step for step in all_steps if step.get("session_id") == session_id]
    
    def get_steps_by_algorithm(self, algorithm_name: str, date: str = None) -> List[Dict[str, Any]]:
        """根据算法名称获取步骤"""
        if date:
            all_steps = self.load_steps_by_date(date)
        else:
            all_steps = []
            for date in self.get_available_dates():
                all_steps.extend(self.load_steps_by_date(date))
        
        return [step for step in all_steps if step.get("algorithm") == algorithm_name]
    
    def get_error_steps(self, date: str = None) -> List[Dict[str, Any]]:
        """获取错误步骤"""
        if date:
            all_steps = self.load_steps_by_date(date)
        else:
            all_steps = []
            for date in self.get_available_dates():
                all_steps.extend(self.load_steps_by_date(date))
        
        return [step for step in all_steps if step.get("level") == "ERROR"]
    
    def get_recent_sessions(self, hours: int = 24) -> List[str]:
        """获取最近的会话ID"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        sessions = set()
        
        for date in self.get_available_dates():
            steps = self.load_steps_by_date(date)
            for step in steps:
                timestamp_str = step.get("timestamp", "")
                try:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    if timestamp >= cutoff_time:
                        sessions.add(step.get("session_id"))
                except:
                    continue
        
        return sorted(list(sessions), reverse=True)
    
    def analyze_session_performance(self, session_id: str) -> Dict[str, Any]:
        """分析会话性能"""
        steps = self.get_steps_by_session(session_id)
        if not steps:
            return {"error": "会话未找到"}
        
        analysis = {
            "session_id": session_id,
            "total_steps": len(steps),
            "start_time": steps[0].get("timestamp") if steps else None,
            "end_time": steps[-1].get("timestamp") if steps else None,
            "total_duration": 0,
            "step_types": {},
            "algorithms": {},
            "errors": [],
            "performance_metrics": {}
        }
        
        # 计算总耗时
        if analysis["start_time"] and analysis["end_time"]:
            try:
                start = datetime.fromisoformat(analysis["start_time"].replace('Z', '+00:00'))
                end = datetime.fromisoformat(analysis["end_time"].replace('Z', '+00:00'))
                analysis["total_duration"] = (end - start).total_seconds() * 1000
            except:
                pass
        
        # 分析步骤类型
        for step in steps:
            step_type = step.get("step_type", "未知")
            analysis["step_types"][step_type] = analysis["step_types"].get(step_type, 0) + 1
            
            # 收集算法信息
            algorithm = step.get("algorithm")
            if algorithm:
                if algorithm not in analysis["algorithms"]:
                    analysis["algorithms"][algorithm] = {
                        "steps": 0,
                        "duration": 0,
                        "errors": 0
                    }
                analysis["algorithms"][algorithm]["steps"] += 1
                
                duration = step.get("duration_ms", 0)
                if duration:
                    analysis["algorithms"][algorithm]["duration"] += duration
            
            # 收集错误
            if step.get("level") == "ERROR":
                analysis["errors"].append({
                    "step_number": step.get("step_number"),
                    "message": step.get("message"),
                    "timestamp": step.get("timestamp"),
                    "details": step.get("details", {})
                })
                
                if algorithm:
                    analysis["algorithms"][algorithm]["errors"] += 1
        
        # 性能指标
        durations = [step.get("duration_ms", 0) for step in steps if step.get("duration_ms")]
        if durations:
            analysis["performance_metrics"] = {
                "avg_step_duration": sum(durations) / len(durations),
                "max_step_duration": max(durations),
                "min_step_duration": min(durations),
                "total_measured_duration": sum(durations)
            }
        
        return analysis
    
    def print_session_summary(self, session_id: str):
        """打印会话摘要"""
        analysis = self.analyze_session_performance(session_id)
        
        if "error" in analysis:
            print(f"❌ {analysis['error']}")
            return
        
        print(f"\n📊 会话分析报告: {session_id}")
        print("=" * 60)
        
        print(f"🕐 开始时间: {analysis['start_time']}")
        print(f"🕐 结束时间: {analysis['end_time']}")
        print(f"⏱️  总耗时: {analysis['total_duration']:.2f}ms")
        print(f"📝 总步骤数: {analysis['total_steps']}")
        
        if analysis['errors']:
            print(f"❌ 错误数量: {len(analysis['errors'])}")
        else:
            print("✅ 无错误")
        
        print("\n📋 步骤类型分布:")
        for step_type, count in analysis['step_types'].items():
            print(f"   {step_type}: {count}")
        
        print("\n🔧 算法执行情况:")
        for algorithm, info in analysis['algorithms'].items():
            print(f"   {algorithm}:")
            print(f"     步骤数: {info['steps']}")
            print(f"     耗时: {info['duration']:.2f}ms")
            print(f"     错误: {info['errors']}")
        
        if analysis['performance_metrics']:
            metrics = analysis['performance_metrics']
            print("\n⚡ 性能指标:")
            print(f"   平均步骤耗时: {metrics['avg_step_duration']:.2f}ms")
            print(f"   最长步骤耗时: {metrics['max_step_duration']:.2f}ms")
            print(f"   最短步骤耗时: {metrics['min_step_duration']:.2f}ms")
        
        if analysis['errors']:
            print("\n🚨 错误详情:")
            for error in analysis['errors']:
                print(f"   步骤{error['step_number']}: {error['message']}")
    
    def print_recent_activity(self, hours: int = 24):
        """打印最近活动"""
        sessions = self.get_recent_sessions(hours)
        
        print(f"\n📈 最近 {hours} 小时的活动")
        print("=" * 40)
        
        if not sessions:
            print("无活动记录")
            return
        
        for session in sessions[:10]:  # 只显示最近10个会话
            steps = self.get_steps_by_session(session)
            if steps:
                start_time = steps[0].get("timestamp", "")
                algorithms = set(step.get("algorithm") for step in steps if step.get("algorithm"))
                error_count = len([s for s in steps if s.get("level") == "ERROR"])
                
                status = "❌" if error_count > 0 else "✅"
                print(f"{status} {session} | {start_time[:19]} | {len(steps)}步骤 | {len(algorithms)}算法")


def main():
    """命令行工具主函数"""
    import sys
    
    viewer = LogViewer()
    
    if len(sys.argv) < 2:
        print("用法:")
        print("  python log_viewer.py recent [hours]     - 查看最近活动")
        print("  python log_viewer.py session <id>       - 查看会话详情")
        print("  python log_viewer.py errors [date]      - 查看错误日志")
        print("  python log_viewer.py dates              - 查看可用日期")
        return
    
    command = sys.argv[1]
    
    if command == "recent":
        hours = int(sys.argv[2]) if len(sys.argv) > 2 else 24
        viewer.print_recent_activity(hours)
    
    elif command == "session":
        if len(sys.argv) < 3:
            print("请提供会话ID")
            return
        session_id = sys.argv[2]
        viewer.print_session_summary(session_id)
    
    elif command == "errors":
        date = sys.argv[2] if len(sys.argv) > 2 else None
        errors = viewer.get_error_steps(date)
        
        print(f"\n🚨 错误日志 {'(' + date + ')' if date else '(所有日期)'}")
        print("=" * 40)
        
        for error in errors[-20:]:  # 最近20个错误
            print(f"[{error.get('timestamp', '')[:19]}] {error.get('message', '')}")
    
    elif command == "dates":
        dates = viewer.get_available_dates()
        print("\n📅 可用日志日期:")
        for date in dates:
            formatted_date = f"{date[:4]}-{date[4:6]}-{date[6:8]}"
            print(f"   {formatted_date} ({date})")
    
    else:
        print(f"未知命令: {command}")


if __name__ == "__main__":
    main()
