<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试构造函数</title>
    
    <!-- Mapbox GL JS -->
    <script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>
    <link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
        }
        
        #map {
            width: 100%;
            height: 400px;
            border: 1px solid #444;
        }
        
        .debug-info {
            margin: 20px 0;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 8px;
        }
        
        .btn {
            background: #00d4ff;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .log {
            background: #000;
            color: #00ff88;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🐛 ModernCityManager构造函数调试</h1>
    
    <div class="debug-info">
        <h3>调试步骤</h3>
        <p>逐步测试ModernCityManager的构造函数，找出调用栈溢出的原因</p>
        
        <button class="btn" onclick="step1()">步骤1: 加载配置</button>
        <button class="btn" onclick="step2()">步骤2: 测试构造函数</button>
        <button class="btn" onclick="step3()">步骤3: 测试初始化</button>
        <button class="btn" onclick="clearLog()">清除日志</button>
    </div>
    
    <div id="map"></div>
    
    <div class="log" id="log"></div>

    <!-- 配置文件 -->
    <script src="config/mapbox-config.js"></script>
    
    <script>
        let cityManager = null;
        let step = 0;
        
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function step1() {
            try {
                log('🚀 步骤1: 检查配置和依赖...');
                
                // 检查Mapbox配置
                if (typeof MAPBOX_CONFIG === 'undefined') {
                    throw new Error('MAPBOX_CONFIG未定义');
                }
                log('✅ MAPBOX_CONFIG已加载');
                
                // 检查Mapbox GL JS
                if (typeof mapboxgl === 'undefined') {
                    throw new Error('Mapbox GL JS未加载');
                }
                log('✅ Mapbox GL JS已加载');
                
                // 检查容器
                const container = document.getElementById('map');
                if (!container) {
                    throw new Error('地图容器未找到');
                }
                log('✅ 地图容器存在');
                
                log('✅ 步骤1完成，可以进行步骤2');
                step = 1;
                
            } catch (error) {
                log(`❌ 步骤1失败: ${error.message}`, 'error');
            }
        }
        
        function step2() {
            if (step < 1) {
                log('❌ 请先完成步骤1', 'error');
                return;
            }
            
            try {
                log('🚀 步骤2: 测试ModernCityManager构造函数...');
                
                // 清除现有实例
                if (cityManager) {
                    cityManager = null;
                    log('🗑️ 清除现有实例');
                }
                
                // 创建最小化的构造函数测试
                log('📝 开始构造函数执行...');
                
                // 使用try-catch包装每个步骤
                try {
                    log('   - 设置containerId...');
                    const containerId = 'map';
                    
                    log('   - 设置options...');
                    const options = {
                        mapboxToken: MAPBOX_CONFIG.accessToken,
                        initialLocation: [139.7670, 35.6814],
                        initialZoom: 15,
                        style: 'mapbox://styles/mapbox/dark-v10'
                    };
                    
                    log('   - 开始实例化...');
                    
                    // 这里我们不直接实例化，而是手动执行构造函数的每个步骤
                    const testObj = {};
                    
                    log('   - 设置基本属性...');
                    testObj.containerId = containerId;
                    testObj.container = null;
                    
                    log('   - 设置配置选项...');
                    testObj.options = {
                        mapboxToken: options.mapboxToken || 'your-mapbox-token',
                        initialLocation: options.initialLocation || [139.7670, 35.6814],
                        initialZoom: options.initialZoom || 15,
                        style: options.style || 'mapbox://styles/mapbox/dark-v10',
                        enable3D: options.enable3D !== false,
                        enableBuildings: options.enableBuildings !== false,
                        enableOSMData: options.enableOSMData !== false,
                        ...options
                    };
                    
                    log('   - 设置核心组件...');
                    testObj.map = null;
                    testObj.scene = null;
                    testObj.camera = null;
                    testObj.renderer = null;
                    
                    log('   - 设置数据管理...');
                    testObj.buildingData = new Map();
                    testObj.osmCache = new Map();
                    testObj.loadedTiles = new Set();
                    
                    log('   - 设置路径规划...');
                    testObj.drone = null;
                    testObj.currentPath = [];
                    testObj.pathHistory = [];
                    testObj.isFlying = false;
                    testObj.flightSpeed = 50;
                    
                    log('   - 设置事件系统...');
                    testObj.eventListeners = new Map();
                    
                    log('   - 设置路径规划器...');
                    testObj.pathPlanner = null; // 不在构造函数中初始化
                    
                    log('✅ 构造函数模拟执行成功！');
                    
                    // 保存测试对象
                    cityManager = testObj;
                    step = 2;
                    
                } catch (innerError) {
                    log(`❌ 构造函数内部错误: ${innerError.message}`, 'error');
                    console.error('构造函数内部错误:', innerError);
                }
                
            } catch (error) {
                log(`❌ 步骤2失败: ${error.message}`, 'error');
                console.error('步骤2错误:', error);
            }
        }
        
        function step3() {
            if (step < 2) {
                log('❌ 请先完成步骤2', 'error');
                return;
            }
            
            try {
                log('🚀 步骤3: 测试初始化方法...');
                
                if (!cityManager) {
                    throw new Error('cityManager对象不存在');
                }
                
                // 添加log方法
                cityManager.log = function(message, level = 'info') {
                    log(`[CityManager] ${message}`, level);
                };
                
                // 添加简化的初始化方法
                cityManager.simpleInit = function() {
                    try {
                        this.log('开始简化初始化...', 'info');
                        
                        // 检查容器
                        this.container = document.getElementById(this.containerId);
                        if (!this.container) {
                            throw new Error(`容器元素 #${this.containerId} 未找到`);
                        }
                        this.log('✅ 容器检查完成');
                        
                        // 设置Mapbox访问令牌
                        mapboxgl.accessToken = this.options.mapboxToken;
                        this.log('✅ Mapbox访问令牌设置完成');
                        
                        // 创建地图实例
                        this.map = new mapboxgl.Map({
                            container: this.containerId,
                            style: this.options.style,
                            center: this.options.initialLocation,
                            zoom: this.options.initialZoom,
                            pitch: 60,
                            bearing: -17.6
                        });
                        
                        this.log('✅ 地图实例创建完成');
                        
                        // 地图加载完成事件
                        this.map.on('load', () => {
                            this.log('✅ 地图加载完成！');
                        });
                        
                        this.log('✅ 简化初始化完成');
                        
                    } catch (error) {
                        this.log(`❌ 简化初始化失败: ${error.message}`, 'error');
                        throw error;
                    }
                };
                
                // 执行简化初始化
                cityManager.simpleInit();
                
                log('✅ 步骤3完成！ModernCityManager基础功能正常');
                step = 3;
                
            } catch (error) {
                log(`❌ 步骤3失败: ${error.message}`, 'error');
                console.error('步骤3错误:', error);
            }
        }
        
        // 页面加载完成后自动开始
        document.addEventListener('DOMContentLoaded', () => {
            log('📋 ModernCityManager构造函数调试页面已加载');
            log('💡 点击"步骤1"开始调试');
        });
    </script>
</body>
</html>
