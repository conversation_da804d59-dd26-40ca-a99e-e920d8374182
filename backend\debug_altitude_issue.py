#!/usr/bin/env python3
"""
调试高度问题
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from algorithms.data_structures import PathPlanningRequest, Point3D

async def debug_altitude_issue():
    """调试高度问题"""
    print("🔍 开始调试高度问题...")
    
    # 1. 测试请求数据创建
    request_data = {
        'startPoint': {
            'lng': 139.7673,
            'lat': 35.6812,
            'alt': 1.0  # 起飞高度1米
        },
        'endPoint': {
            'lng': 139.7016,
            'lat': 35.6598,
            'alt': 1.0  # 降落高度1米
        },
        'flightHeight': 100.0,  # 巡航高度100米
        'safetyDistance': 30.0,
        'algorithm': 'ImprovedClusterBased',
        'parameters': {
            'flightHeight': 100.0,
            'safetyDistance': 30.0,
            'maxTurnAngle': 90,
            'riskEdgeDistance': 50
        }
    }
    
    print(f"📋 原始请求数据:")
    print(f"   起点: lng={request_data['startPoint']['lng']}, lat={request_data['startPoint']['lat']}, alt={request_data['startPoint']['alt']}")
    print(f"   终点: lng={request_data['endPoint']['lng']}, lat={request_data['endPoint']['lat']}, alt={request_data['endPoint']['alt']}")
    print(f"   飞行高度: {request_data['flightHeight']}")
    
    # 2. 创建请求对象
    request = PathPlanningRequest(request_data)
    
    print(f"\n📋 请求对象:")
    print(f"   起点: lng={request.start_point.lng}, lat={request.start_point.lat}, alt={request.start_point.alt}")
    print(f"   终点: lng={request.end_point.lng}, lat={request.end_point.lat}, alt={request.end_point.alt}")
    print(f"   飞行高度: {request.flight_height}")
    
    # 3. 测试算法处理
    from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathfinding
    algorithm = ImprovedClusterBasedPathfinding()
    
    print(f"\n🔄 开始算法处理...")
    
    # 4. 执行算法（只生成一条路径用于调试）
    try:
        response = await algorithm.calculate_path(request)
        
        print(f"\n📊 算法响应:")
        print(f"   路径数量: {len(response.paths) if response.paths else 0}")
        
        if response.paths and len(response.paths) > 0:
            first_path = response.paths[0]
            print(f"   第一条路径航点数: {len(first_path.waypoints) if first_path.waypoints else 0}")
            
            if first_path.waypoints and len(first_path.waypoints) > 0:
                start_waypoint = first_path.waypoints[0]
                end_waypoint = first_path.waypoints[-1]
                
                print(f"   起始航点: lng={start_waypoint.lng:.6f}, lat={start_waypoint.lat:.6f}, alt={start_waypoint.alt:.1f}")
                print(f"   结束航点: lng={end_waypoint.lng:.6f}, lat={end_waypoint.lat:.6f}, alt={end_waypoint.alt:.1f}")
                
                # 检查中间航点的高度
                if len(first_path.waypoints) > 2:
                    mid_waypoint = first_path.waypoints[len(first_path.waypoints)//2]
                    print(f"   中间航点: lng={mid_waypoint.lng:.6f}, lat={mid_waypoint.lat:.6f}, alt={mid_waypoint.alt:.1f}")
        
        # 5. 检查元数据中的起终点高度
        if hasattr(response, 'metadata') and response.metadata:
            print(f"\n📋 响应元数据:")
            for key, value in response.metadata.items():
                if 'alt' in key.lower() or 'height' in key.lower():
                    print(f"   {key}: {value}")
        
        print(f"\n✅ 调试完成！")
        
        # 6. 分析问题
        if response.paths and len(response.paths) > 0 and first_path.waypoints:
            start_alt = first_path.waypoints[0].alt
            end_alt = first_path.waypoints[-1].alt
            
            print(f"\n🔍 问题分析:")
            print(f"   期望起点高度: 1.0米")
            print(f"   实际起点高度: {start_alt:.1f}米")
            print(f"   起点高度正确: {'✅' if abs(start_alt - 1.0) < 0.1 else '❌'}")
            
            print(f"   期望终点高度: 1.0米")
            print(f"   实际终点高度: {end_alt:.1f}米")
            print(f"   终点高度正确: {'✅' if abs(end_alt - 1.0) < 0.1 else '❌'}")
            
            if abs(start_alt - 1.0) > 0.1 or abs(end_alt - 1.0) > 0.1:
                print(f"\n❌ 发现高度问题！")
                print(f"   可能原因:")
                print(f"   1. 算法内部覆盖了起终点高度")
                print(f"   2. 安全高度限制强制提升了高度")
                print(f"   3. 高度变化逻辑影响了起终点")
            else:
                print(f"\n✅ 高度设置正确！")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(debug_altitude_issue())
    if success:
        print("\n✅ 调试完成！")
    else:
        print("\n❌ 调试失败！")
        sys.exit(1)
