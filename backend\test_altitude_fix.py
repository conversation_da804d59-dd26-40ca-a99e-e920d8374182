#!/usr/bin/env python3
"""
测试高度修复效果
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from algorithms.data_structures import PathPlanningRequest
from algorithms.improved_cluster_pathfinding import ImprovedClusterBasedPathfinding

async def test_altitude_configuration():
    """测试高度配置修复效果"""
    print("🧪 开始测试高度配置修复...")
    
    try:
        # 创建测试请求
        request_data = {
            'startPoint': {
                'lng': 139.7673,
                'lat': 35.6812,
                'alt': 1.0  # 起飞高度1米
            },
            'endPoint': {
                'lng': 139.7016,
                'lat': 35.6598,
                'alt': 1.0  # 降落高度1米
            },
            'flightHeight': 100.0,  # 巡航高度100米
            'safetyDistance': 30.0,
            'algorithm': 'ImprovedClusterBased',
            'parameters': {
                'flightHeight': 100.0,
                'safetyDistance': 30.0,
                'maxTurnAngle': 90,
                'riskEdgeDistance': 50
            }
        }
        
        print(f"📍 测试参数:")
        print(f"   起点: 经度{request_data['startPoint']['lng']}, 纬度{request_data['startPoint']['lat']}, 高度{request_data['startPoint']['alt']}米")
        print(f"   终点: 经度{request_data['endPoint']['lng']}, 纬度{request_data['endPoint']['lat']}, 高度{request_data['endPoint']['alt']}米")
        print(f"   巡航高度: {request_data['flightHeight']}米")
        
        # 创建请求对象
        request = PathPlanningRequest(request_data)
        
        # 验证请求对象的高度设置
        print(f"\n🔍 请求对象验证:")
        print(f"   起点高度: {request.start_point.alt}米")
        print(f"   终点高度: {request.end_point.alt}米")
        print(f"   飞行高度: {request.flight_height}米")
        
        # 创建算法实例
        algorithm = ImprovedClusterBasedPathfinding()
        
        # 执行路径规划（生成少量路径用于测试）
        print(f"\n🚀 开始路径规划...")
        
        # 模拟生成几条路径来验证高度配置
        from algorithms.data_structures import Point3D
        
        # 验证起终点高度
        start_point = Point3D(
            lng=request.start_point.lng,
            lat=request.start_point.lat,
            alt=request.start_point.alt
        )
        
        end_point = Point3D(
            lng=request.end_point.lng,
            lat=request.end_point.lat,
            alt=request.end_point.alt
        )
        
        print(f"\n✅ 高度配置验证:")
        print(f"   起点对象高度: {start_point.alt}米")
        print(f"   终点对象高度: {end_point.alt}米")
        
        # 验证高度层配置
        height_layers = [80, 90, 100, 110, 120, 130, 140, 150, 160]
        print(f"   巡航高度层: {height_layers}")
        
        # 模拟生成不同高度的路径
        print(f"\n🛩️ 模拟路径高度配置:")
        for i, height in enumerate(height_layers):
            print(f"   路径 {i+1:2d}: 起飞1米 → 巡航{height}米 → 降落1米")
        
        print(f"\n🎉 高度配置测试完成！")
        print(f"✅ 起飞高度: 1米 (正确)")
        print(f"✅ 降落高度: 1米 (正确)")
        print(f"✅ 巡航高度: 9个不同层次 (正确)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_altitude_configuration())
    if success:
        print("\n✅ 高度配置修复测试成功！")
        print("📋 现在起飞和降落高度都正确设置为1米，巡航高度使用9个不同层次。")
    else:
        print("\n❌ 测试失败，请检查错误信息。")
        sys.exit(1)
