#!/usr/bin/env python3
"""
前后端变量转换映射器
精确转换前端输出变量和后端算法输入参数之间的关系
"""

import json
import math
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

from algorithm_input_parameters import (
    AlgorithmInputParameters, 
    ALGORITHM_INPUT_PARAMETER_NAMES
)


class MappingType(Enum):
    """映射类型"""
    DIRECT = "direct"              # 直接映射
    TRANSFORM = "transform"        # 需要转换
    COMPUTE = "compute"           # 需要计算
    COMBINE = "combine"           # 需要组合多个变量
    CONDITIONAL = "conditional"    # 条件映射


@dataclass
class VariableMapping:
    """变量映射定义"""
    frontend_variable: str         # 前端变量名
    backend_parameter: str         # 后端参数名
    mapping_type: MappingType      # 映射类型
    transform_function: str = ""   # 转换函数名
    default_value: Any = None      # 默认值
    required: bool = True          # 是否必需
    description: str = ""          # 映射描述


class VariableMapper:
    """变量映射器 - 精确转换前后端变量"""
    
    def __init__(self):
        self.mapping_rules = self._initialize_mapping_rules()
        self.coordinate_system = "WGS84"  # 默认坐标系
        self.origin_point = None          # 坐标原点
        
    def _initialize_mapping_rules(self) -> Dict[str, VariableMapping]:
        """初始化映射规则"""
        return {
            # ==================== 基础坐标映射 ====================
            "start_point_mapping": VariableMapping(
                frontend_variable="selectedStartPoint",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["START_POINT"],
                mapping_type=MappingType.TRANSFORM,
                transform_function="transform_point_coordinates",
                required=True,
                description="起点坐标转换：经纬度→笛卡尔坐标"
            ),
            
            "end_point_mapping": VariableMapping(
                frontend_variable="selectedEndPoint", 
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["END_POINT"],
                mapping_type=MappingType.TRANSFORM,
                transform_function="transform_point_coordinates",
                required=True,
                description="终点坐标转换：经纬度→笛卡尔坐标"
            ),
            
            # ==================== 直接参数映射 ====================
            "flight_height_mapping": VariableMapping(
                frontend_variable="userFlightHeight",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["FLIGHT_HEIGHT"],
                mapping_type=MappingType.DIRECT,
                required=True,
                description="飞行高度直接映射"
            ),
            
            "safety_distance_mapping": VariableMapping(
                frontend_variable="userSafetyDistance",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["SAFETY_DISTANCE"],
                mapping_type=MappingType.DIRECT,
                required=False,
                description="安全距离直接映射"
            ),
            
            "max_turn_angle_mapping": VariableMapping(
                frontend_variable="userMaxTurnAngle",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["MAX_TURN_ANGLE"],
                mapping_type=MappingType.DIRECT,
                required=False,
                description="最大转向角度直接映射"
            ),
            
            # ==================== 算法参数映射 ====================
            "k_value_mapping": VariableMapping(
                frontend_variable="userAlgorithmParams.kValue",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["K_VALUE"],
                mapping_type=MappingType.TRANSFORM,
                transform_function="extract_nested_value",
                default_value=5.0,
                required=False,
                description="k值参数提取"
            ),
            
            "risk_edge_distance_mapping": VariableMapping(
                frontend_variable="userAlgorithmParams.riskEdgeDistance",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["RISK_EDGE_DISTANCE"],
                mapping_type=MappingType.TRANSFORM,
                transform_function="extract_nested_value",
                default_value=50.0,
                required=False,
                description="风险边缘距离提取"
            ),
            
            "enable_path_switching_mapping": VariableMapping(
                frontend_variable="userAlgorithmParams.enablePathSwitching",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["ENABLE_PATH_SWITCHING"],
                mapping_type=MappingType.TRANSFORM,
                transform_function="extract_nested_value",
                default_value=True,
                required=False,
                description="换路开关提取"
            ),
            
            # ==================== 环境数据映射 ====================
            "buildings_mapping": VariableMapping(
                frontend_variable="buildingData",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["BUILDINGS"],
                mapping_type=MappingType.TRANSFORM,
                transform_function="transform_building_data",
                default_value=[],
                required=False,
                description="建筑物数据转换"
            ),
            
            "weather_mapping": VariableMapping(
                frontend_variable="userEnvironmentSettings.weather",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["WEATHER_CONDITION"],
                mapping_type=MappingType.TRANSFORM,
                transform_function="extract_nested_value",
                default_value="clear",
                required=False,
                description="天气条件提取"
            ),
            
            "time_of_day_mapping": VariableMapping(
                frontend_variable="userEnvironmentSettings.timeOfDay",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["TIME_OF_DAY"],
                mapping_type=MappingType.TRANSFORM,
                transform_function="extract_nested_value",
                default_value="day",
                required=False,
                description="时间条件提取"
            ),
            
            "visibility_mapping": VariableMapping(
                frontend_variable="userEnvironmentSettings.visibility",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["VISIBILITY"],
                mapping_type=MappingType.TRANSFORM,
                transform_function="extract_nested_value",
                default_value=1.0,
                required=False,
                description="能见度提取"
            ),
            
            # ==================== 计算参数映射 ====================
            "protection_zones_mapping": VariableMapping(
                frontend_variable="computedBounds",
                backend_parameter=ALGORITHM_INPUT_PARAMETER_NAMES["PROTECTION_ZONES"],
                mapping_type=MappingType.COMPUTE,
                transform_function="compute_protection_zones",
                default_value=[],
                required=False,
                description="保护区计算生成"
            )
        }
    
    def map_frontend_to_backend(self, frontend_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将前端数据映射为后端算法参数
        
        Args:
            frontend_data: 前端输出的数据字典
            
        Returns:
            后端算法输入参数字典
        """
        backend_params = {}
        mapping_errors = []
        
        # 设置坐标原点（如果有起点的话）
        if "selectedStartPoint" in frontend_data:
            self._set_coordinate_origin(frontend_data["selectedStartPoint"])
        
        # 遍历所有映射规则
        for rule_name, mapping in self.mapping_rules.items():
            try:
                # 获取前端变量值
                frontend_value = self._get_frontend_value(frontend_data, mapping.frontend_variable)
                
                # 如果没有值且是必需的，记录错误
                if frontend_value is None and mapping.required:
                    mapping_errors.append(f"缺少必需的前端变量: {mapping.frontend_variable}")
                    continue
                
                # 如果没有值但有默认值，使用默认值
                if frontend_value is None and mapping.default_value is not None:
                    frontend_value = mapping.default_value
                
                # 如果仍然没有值，跳过
                if frontend_value is None:
                    continue
                
                # 根据映射类型进行转换
                backend_value = self._apply_mapping(frontend_value, mapping)
                
                # 验证后端参数
                param_valid, param_message = AlgorithmInputParameters.validate_parameter(
                    mapping.backend_parameter, backend_value
                )
                
                if not param_valid:
                    mapping_errors.append(f"参数验证失败 {mapping.backend_parameter}: {param_message}")
                    continue
                
                # 添加到后端参数
                backend_params[mapping.backend_parameter] = backend_value
                
            except Exception as e:
                mapping_errors.append(f"映射规则 {rule_name} 执行失败: {str(e)}")
        
        # 添加默认值（如果没有映射到的话）
        default_values = AlgorithmInputParameters.get_default_values()
        for param_name, default_value in default_values.items():
            if param_name not in backend_params:
                backend_params[param_name] = default_value
        
        # 返回结果
        result = {
            "success": len(mapping_errors) == 0,
            "backend_parameters": backend_params,
            "mapping_errors": mapping_errors,
            "mapped_count": len(backend_params),
            "total_rules": len(self.mapping_rules)
        }
        
        return result
    
    def _get_frontend_value(self, frontend_data: Dict[str, Any], variable_path: str) -> Any:
        """获取前端变量值（支持嵌套路径）"""
        if "." in variable_path:
            # 处理嵌套路径，如 "userAlgorithmParams.kValue"
            parts = variable_path.split(".")
            value = frontend_data
            
            for part in parts:
                if isinstance(value, dict) and part in value:
                    value = value[part]
                else:
                    return None
            
            return value
        else:
            # 直接获取
            return frontend_data.get(variable_path)
    
    def _apply_mapping(self, frontend_value: Any, mapping: VariableMapping) -> Any:
        """应用映射转换"""
        if mapping.mapping_type == MappingType.DIRECT:
            return frontend_value
        
        elif mapping.mapping_type == MappingType.TRANSFORM:
            transform_func = getattr(self, mapping.transform_function, None)
            if transform_func:
                return transform_func(frontend_value, mapping)
            else:
                raise ValueError(f"未找到转换函数: {mapping.transform_function}")
        
        elif mapping.mapping_type == MappingType.COMPUTE:
            compute_func = getattr(self, mapping.transform_function, None)
            if compute_func:
                return compute_func(frontend_value, mapping)
            else:
                raise ValueError(f"未找到计算函数: {mapping.transform_function}")
        
        else:
            raise ValueError(f"不支持的映射类型: {mapping.mapping_type}")
    
    def _set_coordinate_origin(self, start_point: Dict[str, Any]):
        """设置坐标转换原点"""
        if "lng" in start_point and "lat" in start_point:
            self.origin_point = {
                "lng": start_point["lng"],
                "lat": start_point["lat"],
                "alt": start_point.get("alt", 0)
            }
    
    # ==================== 转换函数 ====================
    
    def transform_point_coordinates(self, point_data: Dict[str, Any], mapping: VariableMapping) -> Dict[str, Any]:
        """转换点坐标：经纬度→笛卡尔坐标"""
        if not isinstance(point_data, dict):
            raise ValueError("点坐标数据必须是字典格式")
        
        # 如果已经有笛卡尔坐标，直接返回
        if "x" in point_data and "y" in point_data and "z" in point_data:
            return {
                "lng": point_data.get("lng", 0),
                "lat": point_data.get("lat", 0),
                "alt": point_data.get("alt", point_data.get("z", 0)),
                "x": point_data["x"],
                "y": point_data["y"],
                "z": point_data["z"]
            }
        
        # 经纬度转笛卡尔坐标
        if "lng" in point_data and "lat" in point_data:
            lng = point_data["lng"]
            lat = point_data["lat"]
            alt = point_data.get("alt", 100)
            
            # 如果有原点，计算相对坐标
            if self.origin_point:
                x, y = self._lnglat_to_xy(lng, lat, self.origin_point["lng"], self.origin_point["lat"])
                z = alt
            else:
                # 简化转换（适用于小范围）
                x = lng * 111320  # 大约每度111.32km
                y = lat * 110540  # 大约每度110.54km
                z = alt
            
            return {
                "lng": lng,
                "lat": lat,
                "alt": alt,
                "x": x,
                "y": y,
                "z": z
            }
        
        raise ValueError("点坐标数据缺少必要的坐标信息")
    
    def extract_nested_value(self, value: Any, mapping: VariableMapping) -> Any:
        """提取嵌套值（已在_get_frontend_value中处理）"""
        return value
    
    def transform_building_data(self, building_list: List[Dict], mapping: VariableMapping) -> List[Dict]:
        """转换建筑物数据格式"""
        if not isinstance(building_list, list):
            return []
        
        transformed_buildings = []
        
        for building in building_list:
            if not isinstance(building, dict):
                continue
            
            # 转换建筑物坐标
            if "coordinates" in building and isinstance(building["coordinates"], list):
                # 假设coordinates是经纬度数组
                if len(building["coordinates"]) >= 2:
                    lng, lat = building["coordinates"][0], building["coordinates"][1]
                    
                    if self.origin_point:
                        x, y = self._lnglat_to_xy(lng, lat, self.origin_point["lng"], self.origin_point["lat"])
                    else:
                        x, y = lng * 111320, lat * 110540
                    
                    transformed_building = {
                        "x": x,
                        "y": y,
                        "height": building.get("height", 50),
                        "radius": building.get("radius", 20),
                        "type": building.get("type", "building")
                    }
                    
                    transformed_buildings.append(transformed_building)
        
        return transformed_buildings
    
    def compute_protection_zones(self, bounds_data: Dict, mapping: VariableMapping) -> List[Dict]:
        """计算保护区数据"""
        if not isinstance(bounds_data, dict):
            return []
        
        # 基于边界数据生成保护区
        protection_zones = []
        
        if all(key in bounds_data for key in ["minX", "maxX", "minY", "maxY"]):
            # 创建一个基础保护区
            zone = {
                "zone_id": "computed_zone_1",
                "zone_type": "general",
                "polygon_points": [
                    {"lng": 0, "lat": 0, "alt": 0, "x": bounds_data["minX"], "y": bounds_data["minY"], "z": 0},
                    {"lng": 0, "lat": 0, "alt": 0, "x": bounds_data["maxX"], "y": bounds_data["minY"], "z": 0},
                    {"lng": 0, "lat": 0, "alt": 0, "x": bounds_data["maxX"], "y": bounds_data["maxY"], "z": 0},
                    {"lng": 0, "lat": 0, "alt": 0, "x": bounds_data["minX"], "y": bounds_data["maxY"], "z": 0}
                ],
                "collision_cost_density": 10.0
            }
            protection_zones.append(zone)
        
        return protection_zones
    
    def _lnglat_to_xy(self, lng: float, lat: float, origin_lng: float, origin_lat: float) -> Tuple[float, float]:
        """经纬度转换为相对笛卡尔坐标"""
        # 简化的墨卡托投影转换
        earth_radius = 6378137.0  # 地球半径（米）
        
        # 转换为弧度
        lng_rad = math.radians(lng)
        lat_rad = math.radians(lat)
        origin_lng_rad = math.radians(origin_lng)
        origin_lat_rad = math.radians(origin_lat)
        
        # 计算相对坐标
        x = earth_radius * (lng_rad - origin_lng_rad) * math.cos(origin_lat_rad)
        y = earth_radius * (lat_rad - origin_lat_rad)
        
        return x, y
    
    def get_mapping_summary(self) -> Dict[str, Any]:
        """获取映射规则摘要"""
        summary = {
            "total_rules": len(self.mapping_rules),
            "mapping_types": {},
            "required_mappings": [],
            "optional_mappings": []
        }
        
        for rule_name, mapping in self.mapping_rules.items():
            # 统计映射类型
            mapping_type = mapping.mapping_type.value
            summary["mapping_types"][mapping_type] = summary["mapping_types"].get(mapping_type, 0) + 1
            
            # 分类必需和可选映射
            if mapping.required:
                summary["required_mappings"].append({
                    "rule": rule_name,
                    "frontend": mapping.frontend_variable,
                    "backend": mapping.backend_parameter,
                    "description": mapping.description
                })
            else:
                summary["optional_mappings"].append({
                    "rule": rule_name,
                    "frontend": mapping.frontend_variable,
                    "backend": mapping.backend_parameter,
                    "description": mapping.description
                })
        
        return summary
    
    def validate_mapping_completeness(self, frontend_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证映射完整性"""
        required_params = AlgorithmInputParameters.get_required_parameters()
        mapped_params = set()
        missing_mappings = []
        
        # 检查每个必需参数是否有对应的映射
        for rule_name, mapping in self.mapping_rules.items():
            if mapping.required and mapping.backend_parameter in required_params:
                frontend_value = self._get_frontend_value(frontend_data, mapping.frontend_variable)
                if frontend_value is not None:
                    mapped_params.add(mapping.backend_parameter)
                else:
                    missing_mappings.append({
                        "parameter": mapping.backend_parameter,
                        "frontend_variable": mapping.frontend_variable,
                        "description": mapping.description
                    })
        
        return {
            "total_required": len(required_params),
            "mapped_required": len(mapped_params),
            "completeness_rate": len(mapped_params) / len(required_params) if required_params else 1.0,
            "missing_mappings": missing_mappings
        }


# 全局映射器实例
variable_mapper = VariableMapper()


def map_frontend_to_backend(frontend_data: Dict[str, Any]) -> Dict[str, Any]:
    """便捷函数：前端数据映射为后端参数"""
    return variable_mapper.map_frontend_to_backend(frontend_data)


if __name__ == "__main__":
    # 测试映射功能
    test_frontend_data = {
        "selectedStartPoint": {"lng": 116.404, "lat": 39.915, "alt": 100},
        "selectedEndPoint": {"lng": 116.504, "lat": 39.815, "alt": 120},
        "userFlightHeight": 100,
        "userSafetyDistance": 30,
        "userAlgorithmParams": {
            "kValue": 5.0,
            "riskEdgeDistance": 50.0,
            "enablePathSwitching": True
        },
        "userEnvironmentSettings": {
            "weather": "clear",
            "timeOfDay": "day",
            "visibility": 0.9
        },
        "buildingData": [
            {"coordinates": [116.454, 39.865], "height": 80, "radius": 30}
        ]
    }
    
    print("=== 变量映射测试 ===")
    
    # 获取映射摘要
    mapper = VariableMapper()
    summary = mapper.get_mapping_summary()
    print(f"映射规则总数: {summary['total_rules']}")
    print(f"必需映射数: {len(summary['required_mappings'])}")
    print(f"可选映射数: {len(summary['optional_mappings'])}")
    
    # 执行映射
    result = mapper.map_frontend_to_backend(test_frontend_data)
    print(f"\n映射结果:")
    print(f"  成功: {result['success']}")
    print(f"  映射参数数: {result['mapped_count']}")
    print(f"  错误数: {len(result['mapping_errors'])}")
    
    if result['mapping_errors']:
        print("  错误列表:")
        for error in result['mapping_errors']:
            print(f"    - {error}")
    
    print(f"\n后端参数:")
    for param, value in result['backend_parameters'].items():
        print(f"  {param}: {value}")
    
    # 验证完整性
    completeness = mapper.validate_mapping_completeness(test_frontend_data)
    print(f"\n映射完整性:")
    print(f"  完整率: {completeness['completeness_rate']*100:.1f}%")
    print(f"  缺失映射数: {len(completeness['missing_mappings'])}")
