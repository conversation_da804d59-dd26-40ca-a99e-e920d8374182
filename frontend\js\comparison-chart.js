/**
 * 算法对比图表管理器
 * 用于显示改进算法和基准算法的性能对比
 * 严格按照论文要求实现四个指标的对比分析
 */
class ComparisonChartManager {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.chartData = null;
        this.isVisible = false;
        this.comparisonResults = null;

        // 图表配置
        this.chartConfig = {
            colors: {
                improved: '#00d4ff',      // 改进算法 - 蓝色
                baseline: '#ff6b35',      // 基准算法 - 橙色
                improvement: '#00ff88',   // 改进指标 - 绿色
                degradation: '#ff4444'    // 退化指标 - 红色
            },
            metrics: [
                { key: 'pathLength', label: '路径长度', unit: 'm', lowerIsBetter: true },
                { key: 'turningCost', label: '转向成本', unit: 'rad', lowerIsBetter: true },
                { key: 'riskValue', label: '风险值', unit: '', lowerIsBetter: true },
                { key: 'collisionCost', label: '碰撞代价', unit: '', lowerIsBetter: true }
            ]
        };

        this.init();
    }
    
    init() {
        this.canvas = document.getElementById('comparison-chart');
        if (this.canvas) {
            this.ctx = this.canvas.getContext('2d');
            this.setupCanvas();
        }
    }
    
    setupCanvas() {
        // 设置高DPI显示
        const dpr = window.devicePixelRatio || 1;
        const rect = this.canvas.getBoundingClientRect();
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        this.ctx.scale(dpr, dpr);
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
    }
    
    /**
     * 显示算法对比图表
     * @param {Object} improvedData - 改进算法数据
     * @param {Object} baselineData - 基准算法数据
     */
    showComparison(improvedData, baselineData) {
        console.log('📊 showComparison - 输入数据:', { improved: improvedData, baseline: baselineData });
        
        // 验证输入数据
        if (!improvedData || !baselineData) {
            console.error('❌ 无效的对比数据:', { improved: improvedData, baseline: baselineData });
            return;
        }
        
        // 确保所有指标都是有效数字
        const validateData = (data) => {
            const metrics = ['pathLength', 'turningCost', 'riskValue', 'collisionCost', 'finalCost'];
            const result = {};
            
            metrics.forEach(metric => {
                let value = data[metric];
                // 尝试从不同的字段名获取
                if (value === undefined || value === null) {
                    // 尝试下划线命名
                    const snakeCase = metric.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
                    value = data[snakeCase];
                    
                    // 尝试其他可能的命名
                    if (value === undefined || value === null) {
                        if (metric === 'pathLength') value = data.length || data.path_length || 0;
                        else if (metric === 'turningCost') value = data.turning || data.turning_cost || 0;
                        else if (metric === 'riskValue') value = data.risk || data.risk_value || 0;
                        else if (metric === 'collisionCost') value = data.collision || data.collision_cost || 0;
                        else if (metric === 'finalCost') value = data.final || data.final_cost || 0;
                    }
                }
                
                // 确保是有效数字
                result[metric] = (value === undefined || value === null || isNaN(value)) ? 0 : Number(value);
            });
            
            return result;
        };
        
        // 验证并标准化数据
        const validImproved = validateData(improvedData);
        const validBaseline = validateData(baselineData);
        
        console.log('📊 showComparison - 验证后数据:', { 
            improved: validImproved, 
            baseline: validBaseline 
        });

        this.chartData = {
            improved: validImproved,
            baseline: validBaseline
        };

        // 计算对比分析结果
        this.comparisonResults = this.calculateComparisonResults(validImproved, validBaseline);

        this.drawChart();
        this.updateComparisonTable();
        this.showPanel();
    }

    /**
     * 计算对比分析结果
     * @param {Object} improvedData - 改进算法数据
     * @param {Object} baselineData - 基准算法数据
     * @returns {Object} 对比分析结果
     */
    calculateComparisonResults(improvedData, baselineData) {
        const results = {
            metrics: [],
            overallImprovement: 0,
            improvementCount: 0,
            degradationCount: 0,
            summary: ''
        };

        // 安全地计算改进百分比
        const calculateImprovement = (baseValue, improvedValue, lowerIsBetter = true) => {
            // 确保值是数字
            baseValue = Number(baseValue) || 0;
            improvedValue = Number(improvedValue) || 0;
            
            // 避免除以零
            if (baseValue === 0) {
                if (improvedValue === 0) return 0; // 两者都为零，无变化
                // 如果基准值为0但改进值不为0，根据指标特性决定是改进还是退化
                return lowerIsBetter ? (improvedValue > 0 ? -100 : 100) : (improvedValue > 0 ? 100 : -100);
            }
            
            // 根据指标特性计算改进百分比
            if (lowerIsBetter) {
                // 对于越小越好的指标（如路径长度、风险值、转向成本、碰撞代价）
                return ((baseValue - improvedValue) / baseValue) * 100;
            } else {
                // 对于越大越好的指标
                return ((improvedValue - baseValue) / baseValue) * 100;
            }
        };

        let totalImprovement = 0;

        this.chartConfig.metrics.forEach(metric => {
            const improvedValue = improvedData[metric.key] || 0;
            const baselineValue = baselineData[metric.key] || 0;

            // 计算改进百分比 - 确保所有指标都使用"越小越好"的逻辑
            const lowerIsBetter = true; // 所有指标都是越小越好
            const improvementPercent = calculateImprovement(
                baselineValue, improvedValue, lowerIsBetter
            );

            const isImprovement = improvementPercent > 0;
            const isDegradation = improvementPercent < 0;

            if (isImprovement) results.improvementCount++;
            if (isDegradation) results.degradationCount++;

            totalImprovement += improvementPercent;

            results.metrics.push({
                key: metric.key,
                label: metric.label,
                unit: metric.unit,
                improvedValue: improvedValue,
                baselineValue: baselineValue,
                improvementPercent: improvementPercent,
                isImprovement: isImprovement,
                isDegradation: isDegradation,
                lowerIsBetter: true // 所有指标都是越小越好
            });
        });

        // 记录详细的指标改进情况
        console.log('📊 calculateComparisonResults - 指标改进详情:', results.metrics);

        // 🔧 修复：使用论文公式14的最终代价计算真正的改进率
        // 而不是简单地将各项指标改进百分比相加平均
        results.overallImprovement = this.calculateFinalCostImprovement(improvedData, baselineData);

        // 生成基于最终代价的总结
        if (results.overallImprovement > 10) {
            results.summary = `改进算法显著优于基准算法，最终代价降低${results.overallImprovement.toFixed(1)}%`;
        } else if (results.overallImprovement > 5) {
            results.summary = `改进算法明显优于基准算法，最终代价降低${results.overallImprovement.toFixed(1)}%`;
        } else if (results.overallImprovement > 0) {
            results.summary = `改进算法略优于基准算法，最终代价降低${results.overallImprovement.toFixed(1)}%`;
        } else if (results.overallImprovement > -5) {
            results.summary = `改进算法与基准算法性能相近，最终代价差异${Math.abs(results.overallImprovement).toFixed(1)}%`;
        } else {
            results.summary = `改进算法性能低于基准算法，最终代价增加${Math.abs(results.overallImprovement).toFixed(1)}%`;
        }

        return results;
    }

    /**
     * 计算基于最终代价的真正改进率
     * 使用论文公式14的最终代价进行对比
     * @param {Object} improvedData - 改进算法数据
     * @param {Object} baselineData - 基准算法数据
     * @returns {number} 最终代价改进百分比
     */
    calculateFinalCostImprovement(improvedData, baselineData) {
        // 获取最终代价数据
        const improvedFinalCost = Number(improvedData.finalCost) || 0;
        const baselineFinalCost = Number(baselineData.finalCost) || 0;

        console.log('🔍 计算最终代价改进率:', {
            improved: improvedFinalCost,
            baseline: baselineFinalCost
        });

        // 避免除以零
        if (baselineFinalCost === 0) {
            if (improvedFinalCost === 0) return 0; // 两者都为零，无变化
            return improvedFinalCost > 0 ? -100 : 100; // 基准为0但改进不为0
        }

        // 计算最终代价改进百分比（越小越好）
        const finalCostImprovement = ((baselineFinalCost - improvedFinalCost) / baselineFinalCost) * 100;

        console.log('🔍 ComparisonChart - 最终代价改进率:', finalCostImprovement.toFixed(2) + '%');
        console.log('🔍 ComparisonChart - 改进判断:', finalCostImprovement > 0 ? '改进' : '退化');

        return finalCostImprovement;
    }

    /**
     * 绘制对比图表
     */
    drawChart() {
        if (!this.ctx || !this.chartData || !this.comparisonResults) return;

        const canvas = this.canvas;
        const ctx = this.ctx;
        const width = canvas.width / (window.devicePixelRatio || 1);
        const height = canvas.height / (window.devicePixelRatio || 1);

        // 清除画布
        ctx.clearRect(0, 0, width, height);

        // 图表配置
        const margin = { top: 40, right: 20, bottom: 80, left: 60 };
        const chartWidth = width - margin.left - margin.right;
        const chartHeight = height - margin.top - margin.bottom;

        // 绘制背景网格
        this.drawGrid(ctx, margin, chartWidth, chartHeight);

        // 绘制柱状图
        this.drawBars(ctx, margin, chartWidth, chartHeight);

        // 绘制图例
        this.drawLegend(ctx, width, margin);

        // 绘制标题
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('改进算法 vs 基准算法性能对比', width / 2, 20);

        // 绘制总结信息
        ctx.font = '12px Arial';
        ctx.fillStyle = this.comparisonResults.overallImprovement > 0 ? '#00ff88' : '#ff4444';
        ctx.fillText(this.comparisonResults.summary, width / 2, height - 10);
    }

    /**
     * 绘制背景网格
     */
    drawGrid(ctx, margin, chartWidth, chartHeight) {
        ctx.strokeStyle = '#333333';
        ctx.lineWidth = 0.5;

        // 绘制水平网格线
        for (let i = 0; i <= 5; i++) {
            const y = margin.top + (chartHeight / 5) * i;
            ctx.beginPath();
            ctx.moveTo(margin.left, y);
            ctx.lineTo(margin.left + chartWidth, y);
            ctx.stroke();
        }

        // 绘制垂直网格线
        const barGroupWidth = chartWidth / this.comparisonResults.metrics.length;
        for (let i = 0; i <= this.comparisonResults.metrics.length; i++) {
            const x = margin.left + barGroupWidth * i;
            ctx.beginPath();
            ctx.moveTo(x, margin.top);
            ctx.lineTo(x, margin.top + chartHeight);
            ctx.stroke();
        }
    }

    /**
     * 绘制柱状图
     */
    drawBars(ctx, margin, chartWidth, chartHeight) {
        const barGroupWidth = chartWidth / this.comparisonResults.metrics.length;
        const barWidth = barGroupWidth * 0.35;
        const barSpacing = barGroupWidth * 0.1;

        // 计算最大值用于缩放
        const allValues = this.comparisonResults.metrics.flatMap(m => [m.improvedValue, m.baselineValue]);
        const maxValue = Math.max(...allValues);
        const scale = chartHeight / (maxValue * 1.1);

        this.comparisonResults.metrics.forEach((metric, i) => {
            const groupX = margin.left + barGroupWidth * i;
            const centerX = groupX + barGroupWidth / 2;

            // 改进算法柱子
            const improvedHeight = metric.improvedValue * scale;
            const improvedX = centerX - barWidth - barSpacing / 2;
            ctx.fillStyle = this.chartConfig.colors.improved;
            ctx.fillRect(improvedX, margin.top + chartHeight - improvedHeight, barWidth, improvedHeight);

            // 基准算法柱子
            const baselineHeight = metric.baselineValue * scale;
            const baselineX = centerX + barSpacing / 2;
            ctx.fillStyle = this.chartConfig.colors.baseline;
            ctx.fillRect(baselineX, margin.top + chartHeight - baselineHeight, barWidth, baselineHeight);

            // 绘制改进百分比标识
            if (Math.abs(metric.improvementPercent) > 1) {
                const improvementColor = metric.isImprovement ?
                    this.chartConfig.colors.improvement : this.chartConfig.colors.degradation;
                const improvementText = `${metric.improvementPercent > 0 ? '+' : ''}${metric.improvementPercent.toFixed(1)}%`;

                ctx.fillStyle = improvementColor;
                ctx.font = 'bold 10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(improvementText, centerX, margin.top - 5);
            }

            // 绘制数值标签
            ctx.fillStyle = '#ffffff';
            ctx.font = '9px Arial';
            ctx.textAlign = 'center';

            // 改进算法数值
            if (improvedHeight > 15) {
                ctx.fillText(metric.improvedValue.toFixed(1),
                    improvedX + barWidth / 2,
                    margin.top + chartHeight - improvedHeight / 2);
            }

            // 基准算法数值
            if (baselineHeight > 15) {
                ctx.fillText(metric.baselineValue.toFixed(1),
                    baselineX + barWidth / 2,
                    margin.top + chartHeight - baselineHeight / 2);
            }

            // 绘制指标标签
            ctx.fillStyle = '#ffffff';
            ctx.font = '11px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(metric.label, centerX, margin.top + chartHeight + 20);

            // 绘制单位
            if (metric.unit) {
                ctx.font = '9px Arial';
                ctx.fillStyle = '#cccccc';
                ctx.fillText(`(${metric.unit})`, centerX, margin.top + chartHeight + 35);
            }
        });
    }

    /**
     * 绘制图例
     */
    drawLegend(ctx, width, margin) {
        const legendY = margin.top + 5;
        const legendItemWidth = 100;
        const startX = width - margin.right - legendItemWidth * 2;

        // 改进算法图例
        ctx.fillStyle = this.chartConfig.colors.improved;
        ctx.fillRect(startX, legendY, 15, 10);
        ctx.fillStyle = '#ffffff';
        ctx.font = '11px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('改进算法', startX + 20, legendY + 8);

        // 基准算法图例
        ctx.fillStyle = this.chartConfig.colors.baseline;
        ctx.fillRect(startX + legendItemWidth, legendY, 15, 10);
        ctx.fillStyle = '#ffffff';
        ctx.fillText('基准算法', startX + legendItemWidth + 20, legendY + 8);
    }
    
    /**
     * 更新对比表格
     */
    updateComparisonTable() {
        const tableBody = document.getElementById('comparison-table-body');
        if (!tableBody || !this.comparisonResults) return;

        tableBody.innerHTML = '';

        // 安全格式化数字
        const formatNumber = (value, decimals = 2) => {
            if (value === undefined || value === null || isNaN(value)) return '0.00';
            return Number(value).toFixed(decimals);
        };

        this.comparisonResults.metrics.forEach(metric => {
            const row = document.createElement('tr');

            // 指标名称
            const nameCell = document.createElement('td');
            nameCell.textContent = metric.label;
            nameCell.className = 'metric-name';
            row.appendChild(nameCell);

            // 改进算法值
            const improvedCell = document.createElement('td');
            improvedCell.textContent = `${formatNumber(metric.improvedValue)} ${metric.unit || ''}`;
            improvedCell.className = 'improved-value';
            row.appendChild(improvedCell);

            // 基准算法值
            const baselineCell = document.createElement('td');
            baselineCell.textContent = `${formatNumber(metric.baselineValue)} ${metric.unit || ''}`;
            baselineCell.className = 'baseline-value';
            row.appendChild(baselineCell);

            // 改进百分比
            const improvementCell = document.createElement('td');
            const improvementText = `${metric.improvementPercent > 0 ? '+' : ''}${formatNumber(metric.improvementPercent, 1)}%`;
            improvementCell.textContent = improvementText;
            improvementCell.className = metric.isImprovement ? 'improvement positive' :
                                       metric.isDegradation ? 'improvement negative' : 'improvement neutral';
            row.appendChild(improvementCell);

            // 状态指示
            const statusCell = document.createElement('td');
            if (metric.isImprovement) {
                statusCell.innerHTML = '<span class="status-icon improvement">↑</span> 改进';
                statusCell.className = 'status improvement';
            } else if (metric.isDegradation) {
                statusCell.innerHTML = '<span class="status-icon degradation">↓</span> 退化';
                statusCell.className = 'status degradation';
            } else {
                statusCell.innerHTML = '<span class="status-icon neutral">→</span> 相近';
                statusCell.className = 'status neutral';
            }
            row.appendChild(statusCell);

            tableBody.appendChild(row);
        });

        // 添加总体评估行
        const totalRow = document.createElement('tr');
        totalRow.className = 'total-row';
        
        const totalNameCell = document.createElement('td');
        totalNameCell.textContent = '总体评估';
        totalNameCell.className = 'metric-name total';
        totalRow.appendChild(totalNameCell);
        
        const totalImprovedCell = document.createElement('td');
        totalImprovedCell.colSpan = 2;
        totalImprovedCell.textContent = this.getPerformanceRating();
        totalImprovedCell.className = 'total-rating';
        totalRow.appendChild(totalImprovedCell);
        
        const totalImprovementCell = document.createElement('td');
        const overallImprovement = this.comparisonResults.overallImprovement;
        totalImprovementCell.textContent = `${overallImprovement > 0 ? '+' : ''}${formatNumber(overallImprovement, 1)}%`;
        totalImprovementCell.className = overallImprovement > 0 ? 'improvement positive' : 
                                       overallImprovement < 0 ? 'improvement negative' : 'improvement neutral';
        totalRow.appendChild(totalImprovementCell);
        
        const totalStatusCell = document.createElement('td');
        if (overallImprovement > 5) {
            totalStatusCell.innerHTML = '<span class="status-icon improvement">↑</span> 显著改进';
            totalStatusCell.className = 'status improvement';
        } else if (overallImprovement > 0) {
            totalStatusCell.innerHTML = '<span class="status-icon improvement">↑</span> 轻微改进';
            totalStatusCell.className = 'status improvement';
        } else if (overallImprovement > -5) {
            totalStatusCell.innerHTML = '<span class="status-icon neutral">→</span> 基本持平';
            totalStatusCell.className = 'status neutral';
        } else {
            totalStatusCell.innerHTML = '<span class="status-icon degradation">↓</span> 性能下降';
            totalStatusCell.className = 'status degradation';
        }
        totalRow.appendChild(totalStatusCell);
        
        tableBody.appendChild(totalRow);

        // 更新总结信息
        const summaryElement = document.getElementById('comparison-summary');
        if (summaryElement) {
            summaryElement.textContent = this.comparisonResults.summary;
            summaryElement.className = this.comparisonResults.overallImprovement > 0 ?
                'summary positive' : 'summary negative';
        }

        // 更新统计信息
        const statsElement = document.getElementById('comparison-stats');
        if (statsElement) {
            statsElement.innerHTML = `
                <div class="stat-item">
                    <span class="stat-label">改进指标:</span>
                    <span class="stat-value positive">${this.comparisonResults.improvementCount}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">退化指标:</span>
                    <span class="stat-value negative">${this.comparisonResults.degradationCount}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均改进:</span>
                    <span class="stat-value ${this.comparisonResults.overallImprovement > 0 ? 'positive' : 'negative'}">
                        ${formatNumber(this.comparisonResults.overallImprovement, 1)}%
                    </span>
                </div>
            `;
        }
    }

    /**
     * 显示图表面板
     */
    showPanel() {
        const panel = document.getElementById('comparison-chart-panel');
        if (panel) {
            panel.style.display = 'block';
            this.isVisible = true;
        }
    }
    
    /**
     * 隐藏图表面板
     */
    hidePanel() {
        const panel = document.getElementById('comparison-chart-panel');
        if (panel) {
            panel.style.display = 'none';
            this.isVisible = false;
        }
    }
    
    /**
     * 更新图表数据
     */
    updateChart(improvedData, baselineData) {
        this.showComparison(improvedData, baselineData);
    }
    
    /**
     * 清除图表
     */
    clearChart() {
        if (this.ctx) {
            const width = this.canvas.width / (window.devicePixelRatio || 1);
            const height = this.canvas.height / (window.devicePixelRatio || 1);
            this.ctx.clearRect(0, 0, width, height);
        }
        this.hidePanel();
        this.chartData = null;
    }
    
    /**
     * 处理窗口大小变化
     */
    handleResize() {
        if (this.canvas && this.isVisible) {
            this.setupCanvas();
            if (this.chartData) {
                this.drawChart();
            }
        }
    }

    /**
     * 导出对比结果为JSON
     */
    exportComparisonData() {
        if (!this.comparisonResults) return null;

        const exportData = {
            timestamp: new Date().toISOString(),
            comparison: {
                improved: this.chartData.improved,
                baseline: this.chartData.baseline,
                results: this.comparisonResults
            },
            metadata: {
                algorithm: 'Improved Cluster-based Path Planning',
                baseline: 'Standard A* Algorithm',
                metrics: this.chartConfig.metrics.map(m => ({
                    key: m.key,
                    label: m.label,
                    unit: m.unit,
                    lowerIsBetter: m.lowerIsBetter
                }))
            }
        };

        return exportData;
    }

    /**
     * 下载对比结果
     */
    downloadComparisonReport() {
        const data = this.exportComparisonData();
        if (!data) return;

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `algorithm_comparison_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * 导出81条路径数据为简洁CSV格式
     * 直接使用JSON文件生成简洁的CSV
     */
    async exportAllPathsData() {
        try {
            console.log('🔄 开始导出81条路径数据（简洁格式）...');

            // 调用简洁格式导出API
            const response = await fetch('/api/export_calculated_paths', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    timestamp: new Date().toISOString(),
                    request_type: 'simple_csv_export'
                })
            });

            console.log('🔍 调试：HTTP响应状态:', response.status, response.statusText);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('🔍 调试：HTTP错误响应内容:', errorText);
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('🔍 调试：解析后的响应数据:', data);

            if (!data.success) {
                console.error('🔍 调试：API返回失败:', data.error);
                throw new Error(data.error || '导出失败');
            }

            console.log('✅ 简洁格式CSV导出成功:', data);
            console.log('🔍 调试：API返回的完整数据:', JSON.stringify(data, null, 2));
            console.log('🔍 调试：filename字段值:', data.filename);
            console.log('🔍 调试：source_json字段值:', data.source_json);

            const filename = data.filename || '路径数据文件';
            const sourceJson = data.source_json || '未知';

            console.log('🔍 调试：处理后的filename:', filename);
            console.log('🔍 调试：处理后的sourceJson:', sourceJson);

            alert(`✅ 路径数据导出成功！\n\n文件名: ${filename}\n路径数量: ${data.total_paths}\n源JSON: ${sourceJson}\n\n文件已保存到 csv/ 目录`);

        } catch (error) {
            console.error('❌ 导出81条路径数据失败:', error);
            console.error('🔍 调试：错误详情:', error.stack);
            alert(`❌ 路径数据导出失败！\n\n错误信息: ${error.message}\n\n请检查控制台获取详细信息`);
        }
    }

    /**
     * 动态判断是否为基准算法路径
     * @param {Object} path - 路径数据
     * @returns {boolean} 是否为基准算法
     */
    isBaselineAlgorithm(path) {
        // 方法1：检查算法名称字段
        if (path.algorithm_name) {
            const algoName = path.algorithm_name.toLowerCase();
            return algoName.includes('astar') || algoName.includes('a*') || algoName.includes('baseline');
        }

        // 方法2：检查算法类型字段
        if (path.algorithm_type) {
            const algoType = path.algorithm_type.toLowerCase();
            return algoType === 'baseline' || algoType.includes('astar') || algoType.includes('a*');
        }

        // 方法3：检查簇类型（保留原有逻辑作为备用）
        if (path.cluster_type) {
            const clusterType = path.cluster_type.toLowerCase();
            return clusterType.includes('基准') || clusterType.includes('baseline') || clusterType.includes('a*');
        }

        // 方法4：检查路径来源或生成器
        if (path.source || path.generator) {
            const source = (path.source || path.generator || '').toLowerCase();
            return source.includes('astar') || source.includes('baseline');
        }

        // 方法5：基于路径特征判断（A*算法通常航点较少，路径较直）
        if (path.waypoints_count !== undefined && path.flight_direction === undefined && path.height_layer === undefined) {
            // 如果没有飞行方向和高度层信息，但有航点数量，可能是基准算法
            return true;
        }

        // 方法6：检查是否有改进算法特有的字段
        const improvedAlgorithmFields = ['flight_direction', 'height_layer', 'cluster_id'];
        const hasImprovedFields = improvedAlgorithmFields.some(field =>
            path[field] !== undefined && path[field] !== null && path[field] !== ''
        );

        // 如果没有改进算法特有字段，可能是基准算法
        if (!hasImprovedFields) {
            return true;
        }

        // 默认情况：如果无法确定，假设是改进算法
        return false;
    }

    /**
     * 计算路径的实际飞行高度（巡航阶段平均高度）
     */
    calculateFlightHeight(path) {
        // 如果路径数据中已经有flight_height字段，直接使用
        if (path.flight_height !== undefined && path.flight_height !== null) {
            return parseFloat(path.flight_height);
        }

        // 如果有航点数据，计算巡航阶段的平均高度
        if (path.waypoints && Array.isArray(path.waypoints) && path.waypoints.length > 2) {
            // 取中间60%的航点作为巡航阶段
            const startIdx = Math.floor(path.waypoints.length * 0.2);
            const endIdx = Math.floor(path.waypoints.length * 0.8);
            const cruiseWaypoints = path.waypoints.slice(startIdx, endIdx);

            if (cruiseWaypoints.length > 0) {
                const totalAlt = cruiseWaypoints.reduce((sum, wp) => sum + (wp.alt || wp.z || 0), 0);
                return totalAlt / cruiseWaypoints.length;
            }
        }

        // 如果有高度层信息，估算飞行高度
        if (path.height_layer) {
            // 假设高度层1-9对应50-130米的飞行高度
            const baseHeight = 50;
            const heightIncrement = 10;
            return baseHeight + (parseInt(path.height_layer) - 1) * heightIncrement;
        }

        // 默认返回100米
        return 100.0;
    }

    /**
     * 将路径数据转换为CSV格式并下载
     */
    /**
     * 🔧 完全重新设计：纯数据导出，无硬编码，错了就显示空值或NaN
     */
    downloadPathsDataAsCSV(pathsData) {
        if (!pathsData || pathsData.length === 0) {
            alert('没有可导出的路径数据');
            return;
        }

        console.log('🔧 完全重新设计：纯后端数据导出，无任何前端处理或默认值');

        // 🔧 纯后端字段映射，不做任何假设或默认值
        const headers = [
            'path_id',
            'algorithm_type',
            'algorithm_name',
            'flight_direction',
            'height_layer',
            'cluster_id',
            'cluster_type',
            'waypoints_count',
            'start_lng',
            'start_lat',
            'start_alt',
            'end_lng',
            'end_lat',
            'end_alt',
            'flight_height',
            'straight_distance',
            'path_length',
            'path_efficiency',
            'turning_cost',
            'risk_value',
            'collision_cost',
            'final_cost',
            'risk_reference',
            'collision_reference',
            'length_reference',
            'turning_reference',
            'actual_collision_cost',
            'weight_alpha',
            'weight_beta',
            'weight_gamma',
            'weight_delta',
            'risk_term',
            'collision_term',
            'length_term',
            'orient_term',
            'risk_percent',
            'collision_percent',
            'length_percent',
            'turning_percent',
            'execution_time',
            'timestamp'
        ];

        // 构建CSV内容
        let csvContent = headers.join(',') + '\n';

        pathsData.forEach((path) => {
            // 🔧 完全重新设计：纯数据映射，不存在就是undefined，显示为空
            const row = [
                path.path_id,
                path.algorithm_type,
                path.algorithm_name,
                path.flight_direction,
                path.height_layer,
                path.cluster_id,
                path.cluster_type,
                path.waypoints_count,
                path.start_lng,
                path.start_lat,
                path.start_alt,
                path.end_lng,
                path.end_lat,
                path.end_alt,
                path.flight_height,
                path.straight_distance,
                path.path_length,
                path.path_efficiency,
                path.turning_cost,
                path.risk_value,
                path.collision_cost,
                path.final_cost,
                path.risk_reference,
                path.collision_reference,
                path.length_reference,
                path.turning_reference,
                path.actual_collision_cost,
                path.weight_alpha,
                path.weight_beta,
                path.weight_gamma,
                path.weight_delta,
                path.risk_term,
                path.collision_term,
                path.length_term,
                path.orient_term,
                path.risk_percent,
                path.collision_percent,
                path.length_percent,
                path.turning_percent,
                path.execution_time,
                path.timestamp
            ];

            // 简单CSV格式处理，不做任何数据转换
            const processedRow = row.map(field => {
                if (field === undefined || field === null) {
                    return '';  // 空值就是空值
                }
                const str = String(field);
                if (str.includes(',')) {
                    return `"${str}"`;
                }
                return str;
            });

            csvContent += processedRow.join(',') + '\n';
        });

        // 下载CSV文件
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `all_paths_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('✅ 纯数据导出完成，无任何前端处理');
        alert(`导出 ${pathsData.length} 条路径数据完成`);
    }

    /**
     * 🔧 新方法：下载已计算的路径数据为CSV（中文表头）
     */
    downloadCalculatedPathsAsCSV(exportData) {
        if (!exportData || !exportData.all_paths || exportData.all_paths.length === 0) {
            alert('没有可导出的路径数据');
            return;
        }

        console.log('🔧 导出已计算的路径数据，使用中文表头');

        // 🔧 完整的中文表头定义（包含所有验证数据）
        const headers = [
            '路径ID',
            '飞行方向',
            '高度层级',
            '航点数量',
            '是否选中',
            '3X3簇ID',
            '4X4簇ID',
            '所有簇ID',
            '簇类型',
            '经过保护区数量',
            '经过保护区列表',
            '保护区类型',
            '保护区碰撞代价',
            '保护区总代价',
            '保护区详细信息',
            '路径长度(米)',
            '转向成本',
            '风险值',
            '碰撞代价',
            '实际碰撞代价',
            '最终代价',
            '权重α(风险)',
            '权重β(碰撞)',
            '权重γ(长度)',
            '权重δ(转向)',
            '风险参考值',
            '碰撞参考值',
            '转向参考值',
            '长度参考值',
            '风险归一化值',
            '碰撞归一化值',
            '长度归一化值',
            '转向归一化值',
            '风险加权项',
            '碰撞加权项',
            '长度加权项',
            '转向加权项',
            '风险贡献百分比',
            '碰撞贡献百分比',
            '长度贡献百分比',
            '转向贡献百分比',
            '基准路径长度',
            '基准转向成本',
            '基准风险值',
            '基准碰撞代价',
            '基准最终代价',
            '长度改进百分比',
            '转向改进百分比',
            '风险改进百分比',
            '碰撞改进百分比',
            '最终代价改进百分比',
            '起点经度',
            '起点纬度',
            '起点高度',
            '终点经度',
            '终点纬度',
            '终点高度',
            '算法名称',
            '导出时间'
        ];

        // 获取元数据
        const metadata = exportData.metadata || {};
        const startPoint = metadata.start_point || {};
        const endPoint = metadata.end_point || {};
        const algorithm = metadata.algorithm || '改进分簇算法';
        const exportTime = metadata.export_time || new Date().toISOString();

        // 构建CSV内容
        let csvContent = headers.join(',') + '\n';

        exportData.all_paths.forEach((pathData) => {
            // 🔧 从新的详细数据结构中提取所有数据
            const baselineComparison = pathData.baseline_comparison || {};

            const row = [
                pathData.path_id || '',
                pathData.flight_direction || '',
                pathData.height_layer || '',
                pathData.waypoints_count || '',
                pathData.is_selected ? '是' : '否',
                pathData.cluster_3x3_ids || '',
                pathData.cluster_4x4_ids || '',
                pathData.cluster_id || '',
                pathData.cluster_type || '',
                pathData.protection_zones_count || '',
                pathData.protection_zones_list || '',
                pathData.protection_zones_types || '',
                pathData.protection_zones_costs || '',
                pathData.total_protection_zone_cost || '',
                pathData.protection_zone_details || '',
                pathData.path_length || '',
                pathData.turning_cost || '',
                pathData.risk_value || '',
                pathData.collision_cost || '',
                pathData.actual_collision_cost || '',
                pathData.final_cost || '',
                pathData.weight_alpha || '',
                pathData.weight_beta || '',
                pathData.weight_gamma || '',
                pathData.weight_delta || '',
                pathData.risk_reference || '',
                pathData.collision_reference || '',
                pathData.turning_reference || '',
                pathData.length_reference || '',
                pathData.risk_normalized || '',
                pathData.collision_normalized || '',
                pathData.length_normalized || '',
                pathData.turning_normalized || '',
                pathData.risk_term || '',
                pathData.collision_term || '',
                pathData.length_term || '',
                pathData.orient_term || '',
                pathData.risk_percent || '',
                pathData.collision_percent || '',
                pathData.length_percent || '',
                pathData.turning_percent || '',
                baselineComparison.baseline_path_length || '',
                baselineComparison.baseline_turning_cost || '',
                baselineComparison.baseline_risk_value || '',
                baselineComparison.baseline_collision_cost || '',
                baselineComparison.baseline_final_cost || '',
                baselineComparison.length_improvement_percent || '',
                baselineComparison.turning_improvement_percent || '',
                baselineComparison.risk_improvement_percent || '',
                baselineComparison.collision_improvement_percent || '',
                baselineComparison.final_cost_improvement_percent || '',
                startPoint.lng || '',
                startPoint.lat || '',
                startPoint.alt || '',
                endPoint.lng || '',
                endPoint.lat || '',
                endPoint.alt || '',
                algorithm,
                exportTime
            ];

            // CSV格式处理
            const processedRow = row.map(field => {
                if (field === undefined || field === null || field === '') {
                    return '';
                }
                const str = String(field);
                if (str.includes(',') || str.includes('"')) {
                    return `"${str.replace(/"/g, '""')}"`;
                }
                return str;
            });

            csvContent += processedRow.join(',') + '\n';
        });

        // 下载CSV文件
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `已计算路径数据_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('✅ 已计算路径数据导出完成（中文表头）');
        alert(`导出 ${exportData.all_paths.length} 条已计算路径数据完成`);
    }



    /**
     * 生成对比报告文本
     */
    generateComparisonReport() {
        if (!this.comparisonResults) return '';

        let report = '算法性能对比报告\n';
        report += '=' * 50 + '\n\n';

        report += `生成时间: ${new Date().toLocaleString()}\n`;
        report += `算法: 改进的分簇路径规划算法 vs 基准A*算法\n\n`;

        report += '详细对比结果:\n';
        report += '-' * 30 + '\n';

        this.comparisonResults.metrics.forEach(metric => {
            report += `${metric.label}:\n`;
            report += `  改进算法: ${metric.improvedValue.toFixed(2)} ${metric.unit}\n`;
            report += `  基准算法: ${metric.baselineValue.toFixed(2)} ${metric.unit}\n`;
            report += `  改进幅度: ${metric.improvementPercent.toFixed(1)}% `;
            report += `(${metric.isImprovement ? '改进' : metric.isDegradation ? '退化' : '相近'})\n\n`;
        });

        report += '总结分析:\n';
        report += '-' * 30 + '\n';
        report += `${this.comparisonResults.summary}\n`;
        report += `改进指标数量: ${this.comparisonResults.improvementCount}\n`;
        report += `退化指标数量: ${this.comparisonResults.degradationCount}\n`;
        report += `最终代价改进率: ${this.comparisonResults.overallImprovement.toFixed(1)}%\n`;
        report += `性能评级: ${this.getPerformanceRating()}\n`;

        return report;
    }

    /**
     * 获取性能评级（基于最终代价改进率）
     */
    getPerformanceRating() {
        if (!this.comparisonResults) return 'N/A';

        const finalCostImprovement = this.comparisonResults.overallImprovement;
        const improvementCount = this.comparisonResults.improvementCount;
        const totalMetrics = this.comparisonResults.metrics.length;

        // 基于最终代价改进率的评级标准
        if (finalCostImprovement > 20) {
            return 'A+ (卓越) - 最终代价显著降低';
        } else if (finalCostImprovement > 10) {
            return 'A (优秀) - 最终代价明显降低';
        } else if (finalCostImprovement > 5) {
            return 'B+ (良好) - 最终代价有效降低';
        } else if (finalCostImprovement > 0) {
            return 'B (一般) - 最终代价略有降低';
        } else if (finalCostImprovement > -5) {
            return 'C (需要改进) - 最终代价基本持平';
        } else if (finalCostImprovement > -10) {
            return 'D (较差) - 最终代价略有增加';
        } else {
            return 'F (不佳) - 最终代价显著增加';
        }
    }
}

// 全局图表管理器实例
let comparisonChart = null;

// 初始化图表管理器
function initComparisonChart() {
    comparisonChart = new ComparisonChartManager();
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        if (comparisonChart) {
            comparisonChart.handleResize();
        }
    });
}

// 显示算法对比
function showAlgorithmComparison(improvedData, baselineData) {
    if (comparisonChart) {
        comparisonChart.showComparison(improvedData, baselineData);
    }
}

// 清除对比图表
function clearAlgorithmComparison() {
    if (comparisonChart) {
        comparisonChart.clearChart();
    }
}

// 导出对比数据
function exportComparisonData() {
    if (comparisonChart) {
        return comparisonChart.exportComparisonData();
    }
    return null;
}

// 下载对比报告
function downloadComparisonReport() {
    if (comparisonChart) {
        comparisonChart.downloadComparisonReport();
    }
}

// 生成对比报告文本
function generateComparisonReport() {
    if (comparisonChart) {
        return comparisonChart.generateComparisonReport();
    }
    return '';
}

// 获取性能评级
function getPerformanceRating() {
    if (comparisonChart) {
        return comparisonChart.getPerformanceRating();
    }
    return 'N/A';
}

// 获取对比结果
function getComparisonResults() {
    if (comparisonChart && comparisonChart.comparisonResults) {
        return comparisonChart.comparisonResults;
    }
    return null;
}

// 导出81条路径数据
async function exportAllPathsData() {
    if (comparisonChart) {
        await comparisonChart.exportAllPathsData();
    } else {
        alert('请先进行算法对比');
    }
}

// 更新图表数据
function updateComparisonChart(improvedData, baselineData) {
    if (comparisonChart) {
        comparisonChart.updateChart(improvedData, baselineData);
    }
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ComparisonChartManager,
        initComparisonChart,
        showAlgorithmComparison,
        clearAlgorithmComparison,
        exportComparisonData,
        downloadComparisonReport,
        generateComparisonReport,
        getPerformanceRating,
        getComparisonResults,
        updateComparisonChart,
        exportAllPathsData
    };
} else {
    window.ComparisonChartManager = ComparisonChartManager;
    window.initComparisonChart = initComparisonChart;
    window.showAlgorithmComparison = showAlgorithmComparison;
    window.clearAlgorithmComparison = clearAlgorithmComparison;
    window.exportComparisonData = exportComparisonData;
    window.downloadComparisonReport = downloadComparisonReport;
    window.generateComparisonReport = generateComparisonReport;
    window.getPerformanceRating = getPerformanceRating;
    window.getComparisonResults = getComparisonResults;
    window.updateComparisonChart = updateComparisonChart;
    window.exportAllPathsData = exportAllPathsData;
}
