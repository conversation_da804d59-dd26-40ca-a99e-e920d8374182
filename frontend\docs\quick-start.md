# 🚀 快速开始指南

## 📋 5分钟快速部署

### 前提条件
- Python 3.8+ 或 Node.js 16+
- 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)
- 网络连接 (访问Mapbox API)

### 方式一：一键部署脚本

**Linux/macOS:**
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

**Windows:**
```cmd
# 双击运行或在命令行执行
deploy.bat
```

### 方式二：手动部署

**1. 进入前端目录**
```bash
cd frontend
```

**2. 启动HTTP服务器**

Python方式：
```bash
python -m http.server 5000
# 或
python3 -m http.server 5000
```

Node.js方式：
```bash
npx http-server -p 5000 -c-1 --cors
```

**3. 打开浏览器**
```
http://localhost:5000/modern-city.html
```

## ⚙️ 配置Mapbox Token

**1. 获取Token**
- 访问 [Mapbox官网](https://account.mapbox.com/)
- 注册并创建Access Token

**2. 配置Token**
编辑 `config/mapbox-config.js`:
```javascript
const MAPBOX_CONFIG = {
    accessToken: '你的Mapbox-Token',  // 替换这里
    // ... 其他配置
};
```

## 🧪 验证部署

**1. 运行自动测试**
```
http://localhost:5000/test-deployment.html
```
点击"🚀 运行全部测试"

**2. 手动测试**
1. 打开主页面
2. 等待系统初始化
3. 设置起点和终点
4. 规划路径
5. 开始飞行

## 🔧 常见问题

**Q: 地图无法加载？**
A: 检查Mapbox Token是否正确配置

**Q: 3D建筑不显示？**
A: 点击"强制显示3D建筑"按钮，或调整缩放级别

**Q: 端口被占用？**
A: 更换其他端口，如8000、8080等

**Q: 性能问题？**
A: 使用Chrome浏览器，降低地图缩放级别

## 📚 更多文档

- [详细部署指南](deployment-guide.md)
- [算法接口指南](algorithm-interface-guide.md)
- [故障排除指南](../README.md#故障排除)

## 🎯 下一步

部署成功后，您可以：

1. **体验系统功能**
   - 探索东京23区3D地图
   - 测试不同路径规划算法
   - 观看无人机飞行模拟

2. **开发自定义算法**
   - 参考算法接口指南
   - 实现自己的路径规划算法
   - 集成到系统中

3. **生产环境部署**
   - 配置Nginx/Apache
   - 启用HTTPS
   - 设置监控和日志

---

**🎉 恭喜！您已成功部署无人机路径规划系统！**
