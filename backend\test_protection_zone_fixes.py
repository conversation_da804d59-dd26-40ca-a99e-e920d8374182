#!/usr/bin/env python3
"""
测试保护区修复效果
验证：
1. 保护区不再包含风险值字段
2. 保护区检测范围修复（50米缓冲区）
3. 只有真正接近路径的保护区参与计算
"""

import sys
import os
import math
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

def test_protection_zone_definition():
    """测试保护区定义修复"""
    
    print("=" * 60)
    print("保护区定义修复测试")
    print("=" * 60)
    
    try:
        from protection_zones import ProtectionZone, ProtectionZoneType, ProtectionZoneManager
        
        # 测试保护区定义
        print("1. 测试保护区定义")
        print("-" * 40)
        
        # 创建一个测试保护区
        test_zone = ProtectionZone(
            id="test_zone",
            name="测试保护区",
            zone_type=ProtectionZoneType.COMMERCIAL,
            center=(139.7673, 35.6812),
            radius=400,
            average_crash_cost=0.026,
            description="测试用保护区"
        )
        
        print(f"✅ 保护区创建成功")
        print(f"   ID: {test_zone.id}")
        print(f"   名称: {test_zone.name}")
        print(f"   类型: {test_zone.zone_type.value}")
        print(f"   中心: {test_zone.center}")
        print(f"   半径: {test_zone.radius}米")
        print(f"   平均碰撞代价: {test_zone.average_crash_cost}/m²")
        
        # 检查是否还有risk_value字段
        if hasattr(test_zone, 'risk_value'):
            print(f"❌ 错误：保护区仍然包含risk_value字段")
            return False
        else:
            print(f"✅ 正确：保护区不再包含risk_value字段")
        
        # 测试向后兼容的collision_cost_factor
        collision_factor = test_zone.collision_cost_factor
        expected_factor = test_zone.average_crash_cost * 2827  # 30米圆面积
        print(f"   向后兼容collision_cost_factor: {collision_factor:.4f}")
        print(f"   预期值: {expected_factor:.4f}")
        
        if abs(collision_factor - expected_factor) < 0.001:
            print(f"✅ collision_cost_factor计算正确")
        else:
            print(f"❌ collision_cost_factor计算错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 保护区定义测试失败: {e}")
        return False

def test_protection_zone_detection():
    """测试保护区检测范围修复"""
    
    print("\n2. 测试保护区检测范围修复")
    print("-" * 40)
    
    try:
        from protection_zones import ProtectionZoneManager
        
        # 创建保护区管理器
        manager = ProtectionZoneManager()
        
        print(f"✅ 保护区管理器创建成功")
        print(f"   总保护区数量: {len(manager.zones)}")
        
        # 模拟一条只经过东京站的路径
        tokyo_station_center = (139.7673, 35.6812)
        
        # 创建一条接近东京站的路径
        path_points = [
            (139.7670, 35.6810),  # 接近东京站
            (139.7673, 35.6812),  # 东京站中心
            (139.7676, 35.6814),  # 离开东京站
        ]
        
        print(f"\n测试路径（接近东京站）:")
        for i, (lng, lat) in enumerate(path_points):
            print(f"   点{i+1}: ({lng}, {lat})")
        
        # 使用修复后的检测范围（50米缓冲区）
        relevant_zones_50m = manager.get_zones_for_path(path_points, buffer_distance=50)
        
        print(f"\n使用50米缓冲区检测到的保护区:")
        for zone in relevant_zones_50m:
            distance_to_path = min(
                zone.get_distance_to_point(lng, lat) for lng, lat in path_points
            )
            print(f"   - {zone.name}: 距离路径最近{distance_to_path:.0f}米")
        
        # 对比：使用旧的1000米缓冲区
        relevant_zones_1000m = manager.get_zones_for_path(path_points, buffer_distance=1000)
        
        print(f"\n对比：使用1000米缓冲区检测到的保护区:")
        for zone in relevant_zones_1000m:
            distance_to_path = min(
                zone.get_distance_to_point(lng, lat) for lng, lat in path_points
            )
            print(f"   - {zone.name}: 距离路径最近{distance_to_path:.0f}米")
        
        print(f"\n检测结果对比:")
        print(f"   50米缓冲区: {len(relevant_zones_50m)} 个保护区")
        print(f"   1000米缓冲区: {len(relevant_zones_1000m)} 个保护区")
        print(f"   减少了: {len(relevant_zones_1000m) - len(relevant_zones_50m)} 个不相关保护区")
        
        # 验证修复效果
        if len(relevant_zones_50m) < len(relevant_zones_1000m):
            print(f"✅ 保护区检测范围修复成功，减少了不相关保护区的参与")
            return True
        else:
            print(f"⚠️ 保护区检测范围可能需要进一步调整")
            return False
        
    except Exception as e:
        print(f"❌ 保护区检测测试失败: {e}")
        return False

def test_collision_cost_calculation():
    """测试碰撞代价计算（不涉及风险值）"""
    
    print("\n3. 测试碰撞代价计算")
    print("-" * 40)
    
    try:
        from protection_zones import ProtectionZoneManager
        
        manager = ProtectionZoneManager()
        
        # 找到东京站保护区
        tokyo_station = None
        for zone in manager.zones:
            if zone.id == "tokyo_station":
                tokyo_station = zone
                break
        
        if not tokyo_station:
            print(f"❌ 未找到东京站保护区")
            return False
        
        print(f"✅ 找到东京站保护区")
        print(f"   名称: {tokyo_station.name}")
        print(f"   中心: {tokyo_station.center}")
        print(f"   半径: {tokyo_station.radius}米")
        print(f"   平均碰撞代价: {tokyo_station.average_crash_cost}/m²")
        
        # 测试不同距离的碰撞代价计算
        test_points = [
            (139.7673, 35.6812, "中心点"),
            (139.7673, 35.6820, "北侧100米"),
            (139.7673, 35.6804, "南侧100米"),
            (139.7680, 35.6812, "东侧500米"),
            (139.7666, 35.6812, "西侧500米"),
        ]
        
        print(f"\n碰撞代价计算测试:")
        for lng, lat, desc in test_points:
            distance = tokyo_station.get_distance_to_point(lng, lat)
            collision_cost = tokyo_station.get_collision_cost(lng, lat)
            
            print(f"   {desc}: 距离{distance:.0f}米, 碰撞代价{collision_cost:.4f}")
        
        # 验证碰撞代价计算不涉及风险值
        # 检查get_collision_cost方法的实现
        import inspect
        source = inspect.getsource(tokyo_station.get_collision_cost)
        
        if 'risk_value' in source:
            print(f"❌ 错误：碰撞代价计算仍然涉及风险值")
            return False
        else:
            print(f"✅ 正确：碰撞代价计算不涉及风险值")
            return True
        
    except Exception as e:
        print(f"❌ 碰撞代价计算测试失败: {e}")
        return False

def main():
    """主测试函数"""
    
    print("🚀 开始保护区修复效果测试...")
    
    # 执行各项测试
    test1_passed = test_protection_zone_definition()
    test2_passed = test_protection_zone_detection()
    test3_passed = test_collision_cost_calculation()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"📋 保护区定义修复: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"🎯 检测范围修复: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"💰 碰撞代价计算: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    all_passed = test1_passed and test2_passed and test3_passed
    
    if all_passed:
        print("\n🎉 所有测试通过！保护区修复成功！")
        print("\n修复总结:")
        print("✅ 保护区不再包含风险值字段")
        print("✅ 保护区检测范围从1000米改为50米")
        print("✅ 碰撞代价计算不涉及风险值")
        print("✅ 只有真正接近路径的保护区参与计算")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    main()
