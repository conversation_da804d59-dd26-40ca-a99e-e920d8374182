[{"session_id": "20250728_000119", "step_number": 1, "timestamp": "2025-07-28T00:01:19.285809", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "322f8bda-6c91-4f1c-9e1a-068b478a99ed", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "322f8bda-6c91-4f1c-9e1a-068b478a99ed", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-974.2649940021834, 1451.5260163042549, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_000119", "step_number": 2, "timestamp": "2025-07-28T00:01:19.292428", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "322f8bda-6c91-4f1c-9e1a-068b478a99ed", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "322f8bda-6c91-4f1c-9e1a-068b478a99ed", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-974.2649940021834, 1451.5260163042549, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_000119", "step_number": 3, "timestamp": "2025-07-28T00:01:21.991119", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2694.7038173675537}, {"session_id": "20250728_000119", "step_number": 4, "timestamp": "2025-07-28T00:01:21.992259", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2695.843458175659}, {"session_id": "20250728_000119", "step_number": 5, "timestamp": "2025-07-28T00:01:22.006135", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.373374938964844}, {"session_id": "20250728_000119", "step_number": 6, "timestamp": "2025-07-28T00:01:22.008341", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.578985214233398}, {"session_id": "20250728_000119", "step_number": 7, "timestamp": "2025-07-28T00:06:28.069994", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ba91de83-4331-43c9-8dff-6fdd5dac3584", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ba91de83-4331-43c9-8dff-6fdd5dac3584", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-974.2649940021834, 1451.5260163042549, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_000119", "step_number": 8, "timestamp": "2025-07-28T00:06:28.078945", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ba91de83-4331-43c9-8dff-6fdd5dac3584", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ba91de83-4331-43c9-8dff-6fdd5dac3584", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-974.2649940021834, 1451.5260163042549, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_000119", "step_number": 9, "timestamp": "2025-07-28T00:06:30.420620", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2336.2066745758057}, {"session_id": "20250728_000119", "step_number": 10, "timestamp": "2025-07-28T00:06:30.422234", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2337.8207683563232}, {"session_id": "20250728_000119", "step_number": 11, "timestamp": "2025-07-28T00:06:30.439696", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.553951263427734}, {"session_id": "20250728_000119", "step_number": 12, "timestamp": "2025-07-28T00:06:30.441226", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.084836959838867}, {"session_id": "20250728_000119", "step_number": 13, "timestamp": "2025-07-28T00:14:50.832890", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3e573d40-bb89-47a8-ae40-62bc90b826ac", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3e573d40-bb89-47a8-ae40-62bc90b826ac", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-632.6575582952313, 1072.352755402721, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_000119", "step_number": 14, "timestamp": "2025-07-28T00:14:50.840275", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3e573d40-bb89-47a8-ae40-62bc90b826ac", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3e573d40-bb89-47a8-ae40-62bc90b826ac", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-632.6575582952313, 1072.352755402721, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_000119", "step_number": 15, "timestamp": "2025-07-28T00:14:52.398278", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1552.0763397216797}, {"session_id": "20250728_000119", "step_number": 16, "timestamp": "2025-07-28T00:14:52.400810", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1554.60786819458}, {"session_id": "20250728_000119", "step_number": 17, "timestamp": "2025-07-28T00:14:52.419100", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 14.312028884887695}, {"session_id": "20250728_000119", "step_number": 18, "timestamp": "2025-07-28T00:14:52.421745", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.95728302001953}, {"session_id": "20250728_002539", "step_number": 1, "timestamp": "2025-07-28T00:25:39.416648", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c68f1dd3-a59c-4868-9b81-1d24d6e28f1e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c68f1dd3-a59c-4868-9b81-1d24d6e28f1e", "parameters": {"start_point": "(0.0, 0.0, 50)", "end_point": "(-5940.754839000443, -2365.555999999985, 50)", "flight_height": 100, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_002539", "step_number": 2, "timestamp": "2025-07-28T00:25:39.421926", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "c68f1dd3-a59c-4868-9b81-1d24d6e28f1e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "c68f1dd3-a59c-4868-9b81-1d24d6e28f1e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 50)", "end_point": "(-5940.754839000443, -2365.555999999985, 50)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_002539", "step_number": 3, "timestamp": "2025-07-28T00:25:46.282194", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 6858.206510543823}, {"session_id": "20250728_002539", "step_number": 4, "timestamp": "2025-07-28T00:25:46.284212", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 6860.225200653076}, {"session_id": "20250728_002539", "step_number": 5, "timestamp": "2025-07-28T00:25:46.324534", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.31879997253418}, {"session_id": "20250728_002539", "step_number": 6, "timestamp": "2025-07-28T00:25:46.326527", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.312217712402344}, {"session_id": "20250728_002656", "step_number": 1, "timestamp": "2025-07-28T00:26:56.717638", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9443b359-6abd-484a-a5b3-85b62ad6a3e3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9443b359-6abd-484a-a5b3-85b62ad6a3e3", "parameters": {"start_point": "(0.0, 0.0, 50)", "end_point": "(-5940.754839000443, -2365.555999999985, 50)", "flight_height": 100, "safety_distance": 30, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_002656", "step_number": 2, "timestamp": "2025-07-28T00:26:56.722687", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9443b359-6abd-484a-a5b3-85b62ad6a3e3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9443b359-6abd-484a-a5b3-85b62ad6a3e3", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 50)", "end_point": "(-5940.754839000443, -2365.555999999985, 50)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_002656", "step_number": 3, "timestamp": "2025-07-28T00:27:03.853707", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 7127.938508987427}, {"session_id": "20250728_002656", "step_number": 4, "timestamp": "2025-07-28T00:27:03.854695", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 7128.926515579224}, {"session_id": "20250728_002656", "step_number": 5, "timestamp": "2025-07-28T00:27:03.891441", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.75069999694824}, {"session_id": "20250728_002656", "step_number": 6, "timestamp": "2025-07-28T00:27:03.893552", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.861419677734375}, {"session_id": "20250728_002656", "step_number": 7, "timestamp": "2025-07-28T00:29:44.526451", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "448cf1c2-e01a-46a2-b86f-da2878831636", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "448cf1c2-e01a-46a2-b86f-da2878831636", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-738.2426046105182, 1226.1845013723523, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_002656", "step_number": 8, "timestamp": "2025-07-28T00:29:44.531706", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "448cf1c2-e01a-46a2-b86f-da2878831636", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "448cf1c2-e01a-46a2-b86f-da2878831636", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-738.2426046105182, 1226.1845013723523, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_002656", "step_number": 9, "timestamp": "2025-07-28T00:29:46.104027", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1568.2222843170166}, {"session_id": "20250728_002656", "step_number": 10, "timestamp": "2025-07-28T00:29:46.106022", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1570.2168941497803}, {"session_id": "20250728_002656", "step_number": 11, "timestamp": "2025-07-28T00:29:46.124060", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.041683197021484}, {"session_id": "20250728_002656", "step_number": 12, "timestamp": "2025-07-28T00:29:46.127563", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.54452896118164}, {"session_id": "20250728_090008", "step_number": 1, "timestamp": "2025-07-28T09:00:08.621392", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9173aff4-b111-433d-9456-15f2d0b7f525", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9173aff4-b111-433d-9456-15f2d0b7f525", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_090008", "step_number": 2, "timestamp": "2025-07-28T09:00:08.630361", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9173aff4-b111-433d-9456-15f2d0b7f525", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9173aff4-b111-433d-9456-15f2d0b7f525", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_090008", "step_number": 3, "timestamp": "2025-07-28T09:00:10.388521", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1752.2685527801514}, {"session_id": "20250728_090008", "step_number": 4, "timestamp": "2025-07-28T09:00:10.391471", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1755.218505859375}, {"session_id": "20250728_090008", "step_number": 5, "timestamp": "2025-07-28T09:00:10.417429", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 24.749040603637695}, {"session_id": "20250728_090008", "step_number": 6, "timestamp": "2025-07-28T09:00:10.419467", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.78680419921875}, {"session_id": "20250728_094232", "step_number": 1, "timestamp": "2025-07-28T09:42:32.507018", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5556eb6d-177d-4653-9325-390ca4ac81fc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5556eb6d-177d-4653-9325-390ca4ac81fc", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 90}}, "duration_ms": null}, {"session_id": "20250728_094232", "step_number": 2, "timestamp": "2025-07-28T09:42:32.527590", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5556eb6d-177d-4653-9325-390ca4ac81fc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5556eb6d-177d-4653-9325-390ca4ac81fc", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_094232", "step_number": 3, "timestamp": "2025-07-28T09:42:34.339274", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1803.518533706665}, {"session_id": "20250728_094232", "step_number": 4, "timestamp": "2025-07-28T09:42:34.341359", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1805.6035041809082}, {"session_id": "20250728_094232", "step_number": 5, "timestamp": "2025-07-28T09:42:34.371248", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.87129783630371}, {"session_id": "20250728_094232", "step_number": 6, "timestamp": "2025-07-28T09:42:34.373243", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 30.865907669067383}, {"session_id": "20250728_100625", "step_number": 1, "timestamp": "2025-07-28T10:06:25.158641", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5f01826d-e992-46e1-9cc6-3a144ecd3cce", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5f01826d-e992-46e1-9cc6-3a144ecd3cce", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_100625", "step_number": 2, "timestamp": "2025-07-28T10:06:25.163480", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5f01826d-e992-46e1-9cc6-3a144ecd3cce", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5f01826d-e992-46e1-9cc6-3a144ecd3cce", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_100625", "step_number": 3, "timestamp": "2025-07-28T10:06:25.532408", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 364.1064167022705}, {"session_id": "20250728_100625", "step_number": 4, "timestamp": "2025-07-28T10:06:25.535240", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 366.93811416625977}, {"session_id": "20250728_100625", "step_number": 5, "timestamp": "2025-07-28T10:06:25.559365", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.130727767944336}, {"session_id": "20250728_100625", "step_number": 6, "timestamp": "2025-07-28T10:06:25.562492", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 25.257349014282227}, {"session_id": "20250728_100625", "step_number": 7, "timestamp": "2025-07-28T10:10:51.567866", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "803267b3-45e6-4f95-8ffd-f8df378cb0ff", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "803267b3-45e6-4f95-8ffd-f8df378cb0ff", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_100625", "step_number": 8, "timestamp": "2025-07-28T10:10:51.572940", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "803267b3-45e6-4f95-8ffd-f8df378cb0ff", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "803267b3-45e6-4f95-8ffd-f8df378cb0ff", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_100625", "step_number": 9, "timestamp": "2025-07-28T10:10:54.077670", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2498.810291290283}, {"session_id": "20250728_100625", "step_number": 10, "timestamp": "2025-07-28T10:10:54.079805", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2500.945568084717}, {"session_id": "20250728_100625", "step_number": 11, "timestamp": "2025-07-28T10:10:54.112541", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.159639358520508}, {"session_id": "20250728_100625", "step_number": 12, "timestamp": "2025-07-28T10:10:54.116725", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.34412384033203}, {"session_id": "20250728_100625", "step_number": 13, "timestamp": "2025-07-28T10:18:33.798451", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fc279598-db69-4e6f-99ff-6a8df65aa466", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fc279598-db69-4e6f-99ff-6a8df65aa466", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_100625", "step_number": 14, "timestamp": "2025-07-28T10:18:33.803274", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fc279598-db69-4e6f-99ff-6a8df65aa466", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fc279598-db69-4e6f-99ff-6a8df65aa466", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_100625", "step_number": 15, "timestamp": "2025-07-28T10:18:34.137321", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 329.97608184814453}, {"session_id": "20250728_100625", "step_number": 16, "timestamp": "2025-07-28T10:18:34.139405", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 332.059383392334}, {"session_id": "20250728_100625", "step_number": 17, "timestamp": "2025-07-28T10:18:34.165347", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 24.762868881225586}, {"session_id": "20250728_100625", "step_number": 18, "timestamp": "2025-07-28T10:18:34.168357", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 27.77266502380371}, {"session_id": "20250728_102633", "step_number": 1, "timestamp": "2025-07-28T10:26:33.323143", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9f66eccf-6813-4c91-b6ec-7d31370c11ac", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9f66eccf-6813-4c91-b6ec-7d31370c11ac", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_102633", "step_number": 2, "timestamp": "2025-07-28T10:26:33.327127", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "9f66eccf-6813-4c91-b6ec-7d31370c11ac", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "9f66eccf-6813-4c91-b6ec-7d31370c11ac", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_102633", "step_number": 3, "timestamp": "2025-07-28T10:26:33.699178", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 369.52972412109375}, {"session_id": "20250728_102633", "step_number": 4, "timestamp": "2025-07-28T10:26:33.702183", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 372.53522872924805}, {"session_id": "20250728_102633", "step_number": 5, "timestamp": "2025-07-28T10:26:33.721596", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.41957664489746}, {"session_id": "20250728_102633", "step_number": 6, "timestamp": "2025-07-28T10:26:33.723834", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.657135009765625}, {"session_id": "20250728_102633", "step_number": 7, "timestamp": "2025-07-28T10:30:57.049840", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b6af9b44-26b8-4a59-91a6-5a3fe8a381bc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b6af9b44-26b8-4a59-91a6-5a3fe8a381bc", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_102633", "step_number": 8, "timestamp": "2025-07-28T10:30:57.056121", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b6af9b44-26b8-4a59-91a6-5a3fe8a381bc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b6af9b44-26b8-4a59-91a6-5a3fe8a381bc", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_102633", "step_number": 9, "timestamp": "2025-07-28T10:30:58.892645", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1831.345796585083}, {"session_id": "20250728_102633", "step_number": 10, "timestamp": "2025-07-28T10:30:58.895638", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1834.338665008545}, {"session_id": "20250728_102633", "step_number": 11, "timestamp": "2025-07-28T10:30:58.923474", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.331663131713867}, {"session_id": "20250728_102633", "step_number": 12, "timestamp": "2025-07-28T10:30:58.925615", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.471946716308594}, {"session_id": "20250728_103328", "step_number": 1, "timestamp": "2025-07-28T10:33:28.116391", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a0fa8837-e27d-4a33-a3a4-ed9267df0756", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a0fa8837-e27d-4a33-a3a4-ed9267df0756", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_103328", "step_number": 2, "timestamp": "2025-07-28T10:33:28.119846", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "a0fa8837-e27d-4a33-a3a4-ed9267df0756", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "a0fa8837-e27d-4a33-a3a4-ed9267df0756", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_103328", "step_number": 3, "timestamp": "2025-07-28T10:33:28.435785", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 312.603235244751}, {"session_id": "20250728_103328", "step_number": 4, "timestamp": "2025-07-28T10:33:28.438824", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 315.6423568725586}, {"session_id": "20250728_103328", "step_number": 5, "timestamp": "2025-07-28T10:33:28.457456", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.638994216918945}, {"session_id": "20250728_103328", "step_number": 6, "timestamp": "2025-07-28T10:33:28.459649", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.831729888916016}, {"session_id": "20250728_103328", "step_number": 7, "timestamp": "2025-07-28T10:38:23.946896", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fd4aa7b9-1335-4090-a76e-8004042c489e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fd4aa7b9-1335-4090-a76e-8004042c489e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_103328", "step_number": 8, "timestamp": "2025-07-28T10:38:23.951851", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "fd4aa7b9-1335-4090-a76e-8004042c489e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "fd4aa7b9-1335-4090-a76e-8004042c489e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_103328", "step_number": 9, "timestamp": "2025-07-28T10:38:25.731702", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1773.4274864196777}, {"session_id": "20250728_103328", "step_number": 10, "timestamp": "2025-07-28T10:38:25.733695", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1775.4201889038086}, {"session_id": "20250728_103328", "step_number": 11, "timestamp": "2025-07-28T10:38:25.760070", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 23.378849029541016}, {"session_id": "20250728_103328", "step_number": 12, "timestamp": "2025-07-28T10:38:25.762730", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.039838790893555}, {"session_id": "20250728_104015", "step_number": 1, "timestamp": "2025-07-28T10:40:15.394312", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b11ec25e-64a6-4317-922a-d554ec8881b4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b11ec25e-64a6-4317-922a-d554ec8881b4", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 0}}, "duration_ms": null}, {"session_id": "20250728_104015", "step_number": 2, "timestamp": "2025-07-28T10:40:15.399019", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b11ec25e-64a6-4317-922a-d554ec8881b4", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b11ec25e-64a6-4317-922a-d554ec8881b4", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250728_104015", "step_number": 3, "timestamp": "2025-07-28T10:40:15.658239", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 256.2284469604492}, {"session_id": "20250728_104015", "step_number": 4, "timestamp": "2025-07-28T10:40:15.660368", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 258.35728645324707}, {"session_id": "20250728_104015", "step_number": 5, "timestamp": "2025-07-28T10:40:15.676685", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 14.311552047729492}, {"session_id": "20250728_104015", "step_number": 6, "timestamp": "2025-07-28T10:40:15.679214", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.840696334838867}, {"session_id": "20250728_112438", "step_number": 1, "timestamp": "2025-07-28T11:24:38.410273", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4d43f07a-51b6-4a74-a5f0-8b1aefdb46b9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4d43f07a-51b6-4a74-a5f0-8b1aefdb46b9", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1384.136737390421, 1961.6459345110338, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_112438", "step_number": 2, "timestamp": "2025-07-28T11:24:38.418859", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "4d43f07a-51b6-4a74-a5f0-8b1aefdb46b9", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "4d43f07a-51b6-4a74-a5f0-8b1aefdb46b9", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1384.136737390421, 1961.6459345110338, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_112438", "step_number": 3, "timestamp": "2025-07-28T11:24:40.921499", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2495.851993560791}, {"session_id": "20250728_112438", "step_number": 4, "timestamp": "2025-07-28T11:24:40.925293", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2500.1487731933594}, {"session_id": "20250728_112438", "step_number": 5, "timestamp": "2025-07-28T11:24:40.973184", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 43.834686279296875}, {"session_id": "20250728_112438", "step_number": 6, "timestamp": "2025-07-28T11:24:40.976886", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 47.0271110534668}, {"session_id": "20250728_113328", "step_number": 1, "timestamp": "2025-07-28T11:33:28.618466", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ac7e54e0-413c-42b9-a245-e35549342332", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ac7e54e0-413c-42b9-a245-e35549342332", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-915.2856472076814, 1268.2984685054876, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_113328", "step_number": 2, "timestamp": "2025-07-28T11:33:28.624489", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ac7e54e0-413c-42b9-a245-e35549342332", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ac7e54e0-413c-42b9-a245-e35549342332", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-915.2856472076814, 1268.2984685054876, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_113328", "step_number": 3, "timestamp": "2025-07-28T11:33:30.279581", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1648.9620208740234}, {"session_id": "20250728_113328", "step_number": 4, "timestamp": "2025-07-28T11:33:30.282878", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1652.2583961486816}, {"session_id": "20250728_113328", "step_number": 5, "timestamp": "2025-07-28T11:33:30.316072", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 30.03406524658203}, {"session_id": "20250728_113328", "step_number": 6, "timestamp": "2025-07-28T11:33:30.321767", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.72821617126465}, {"session_id": "20250728_120534", "step_number": 1, "timestamp": "2025-07-28T12:05:34.231094", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ed2f5047-fc5b-44f3-baed-18da112d316c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ed2f5047-fc5b-44f3-baed-18da112d316c", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-744.342162173595, 1390.362630723839, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_120534", "step_number": 2, "timestamp": "2025-07-28T12:05:34.237809", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ed2f5047-fc5b-44f3-baed-18da112d316c", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ed2f5047-fc5b-44f3-baed-18da112d316c", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-744.342162173595, 1390.362630723839, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_120534", "step_number": 3, "timestamp": "2025-07-28T12:05:35.752623", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1507.8332424163818}, {"session_id": "20250728_120534", "step_number": 4, "timestamp": "2025-07-28T12:05:35.756609", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1511.8191242218018}, {"session_id": "20250728_120534", "step_number": 5, "timestamp": "2025-07-28T12:05:35.792116", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.50575065612793}, {"session_id": "20250728_120534", "step_number": 6, "timestamp": "2025-07-28T12:05:35.797098", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.48798370361328}, {"session_id": "20250728_123446", "step_number": 1, "timestamp": "2025-07-28T12:34:46.580775", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0a69473e-fddf-4e61-ad8f-a1a154909218", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0a69473e-fddf-4e61-ad8f-a1a154909218", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1316.4065552467894, 1307.9943470557541, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_123446", "step_number": 2, "timestamp": "2025-07-28T12:34:46.588313", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0a69473e-fddf-4e61-ad8f-a1a154909218", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0a69473e-fddf-4e61-ad8f-a1a154909218", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1316.4065552467894, 1307.9943470557541, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_123446", "step_number": 3, "timestamp": "2025-07-28T12:34:48.403408", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1808.8316917419434}, {"session_id": "20250728_123446", "step_number": 4, "timestamp": "2025-07-28T12:34:48.407021", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1812.4449253082275}, {"session_id": "20250728_123446", "step_number": 5, "timestamp": "2025-07-28T12:34:48.435746", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 25.717735290527344}, {"session_id": "20250728_123446", "step_number": 6, "timestamp": "2025-07-28T12:34:48.439886", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.85835075378418}, {"session_id": "20250728_124623", "step_number": 1, "timestamp": "2025-07-28T12:46:23.456889", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1321038e-0fe8-4d01-a59b-27e63dbdfd80", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1321038e-0fe8-4d01-a59b-27e63dbdfd80", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-955.2530069031202, 1332.7120089764937, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_124623", "step_number": 2, "timestamp": "2025-07-28T12:46:23.463619", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1321038e-0fe8-4d01-a59b-27e63dbdfd80", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1321038e-0fe8-4d01-a59b-27e63dbdfd80", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-955.2530069031202, 1332.7120089764937, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_124623", "step_number": 3, "timestamp": "2025-07-28T12:46:25.065232", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1595.184564590454}, {"session_id": "20250728_124623", "step_number": 4, "timestamp": "2025-07-28T12:46:25.068777", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1598.7298488616943}, {"session_id": "20250728_124623", "step_number": 5, "timestamp": "2025-07-28T12:46:25.097925", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.165008544921875}, {"session_id": "20250728_124623", "step_number": 6, "timestamp": "2025-07-28T12:46:25.103216", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.45575523376465}, {"session_id": "20250728_125441", "step_number": 1, "timestamp": "2025-07-28T12:54:41.183929", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e7dc28db-b7d6-475f-914f-285ff2c72e0b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e7dc28db-b7d6-475f-914f-285ff2c72e0b", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-907.3809878520177, 1328.3789057932743, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_125441", "step_number": 2, "timestamp": "2025-07-28T12:54:41.190556", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e7dc28db-b7d6-475f-914f-285ff2c72e0b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e7dc28db-b7d6-475f-914f-285ff2c72e0b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-907.3809878520177, 1328.3789057932743, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_125441", "step_number": 3, "timestamp": "2025-07-28T12:54:42.821254", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1625.6115436553955}, {"session_id": "20250728_125441", "step_number": 4, "timestamp": "2025-07-28T12:54:42.824574", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1628.9310455322266}, {"session_id": "20250728_125441", "step_number": 5, "timestamp": "2025-07-28T12:54:42.857393", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 29.16860580444336}, {"session_id": "20250728_125441", "step_number": 6, "timestamp": "2025-07-28T12:54:42.860598", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.37318992614746}, {"session_id": "20250728_125910", "step_number": 1, "timestamp": "2025-07-28T12:59:10.156388", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1054cfd4-388e-418c-ab7d-7b04da4dd9ef", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1054cfd4-388e-418c-ab7d-7b04da4dd9ef", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-962.8378338111629, 1500.5338326055075, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_125910", "step_number": 2, "timestamp": "2025-07-28T12:59:10.162049", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1054cfd4-388e-418c-ab7d-7b04da4dd9ef", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1054cfd4-388e-418c-ab7d-7b04da4dd9ef", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-962.8378338111629, 1500.5338326055075, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_125910", "step_number": 3, "timestamp": "2025-07-28T12:59:11.894208", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1726.2043952941895}, {"session_id": "20250728_125910", "step_number": 4, "timestamp": "2025-07-28T12:59:11.897579", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1729.57444190979}, {"session_id": "20250728_125910", "step_number": 5, "timestamp": "2025-07-28T12:59:11.928966", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 27.94027328491211}, {"session_id": "20250728_125910", "step_number": 6, "timestamp": "2025-07-28T12:59:11.932182", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.15701675415039}, {"session_id": "20250728_130441", "step_number": 1, "timestamp": "2025-07-28T13:04:41.471457", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "142f61ec-d4b2-40d2-916a-96210bfc8bc2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "142f61ec-d4b2-40d2-916a-96210bfc8bc2", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-854.7429939467424, 1363.3011423316489, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_130441", "step_number": 2, "timestamp": "2025-07-28T13:04:41.477909", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "142f61ec-d4b2-40d2-916a-96210bfc8bc2", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "142f61ec-d4b2-40d2-916a-96210bfc8bc2", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-854.7429939467424, 1363.3011423316489, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_130441", "step_number": 3, "timestamp": "2025-07-28T13:04:43.071122", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1586.8268013000488}, {"session_id": "20250728_130441", "step_number": 4, "timestamp": "2025-07-28T13:04:43.074381", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1590.0862216949463}, {"session_id": "20250728_130441", "step_number": 5, "timestamp": "2025-07-28T13:04:43.102406", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 25.30694007873535}, {"session_id": "20250728_130441", "step_number": 6, "timestamp": "2025-07-28T13:04:43.105901", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.801918029785156}, {"session_id": "20250728_130938", "step_number": 1, "timestamp": "2025-07-28T13:09:38.014746", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "da7a9705-5098-4655-bfad-e5d6697131fa", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "da7a9705-5098-4655-bfad-e5d6697131fa", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1441.7268036899563, 1538.895712765527, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_130938", "step_number": 2, "timestamp": "2025-07-28T13:09:38.022229", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "da7a9705-5098-4655-bfad-e5d6697131fa", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "da7a9705-5098-4655-bfad-e5d6697131fa", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1441.7268036899563, 1538.895712765527, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_130938", "step_number": 3, "timestamp": "2025-07-28T13:09:40.231973", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2203.2299041748047}, {"session_id": "20250728_130938", "step_number": 4, "timestamp": "2025-07-28T13:09:40.234964", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2206.2206268310547}, {"session_id": "20250728_130938", "step_number": 5, "timestamp": "2025-07-28T13:09:40.275564", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.96091270446777}, {"session_id": "20250728_130938", "step_number": 6, "timestamp": "2025-07-28T13:09:40.279070", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.46733474731445}, {"session_id": "20250728_140729", "step_number": 1, "timestamp": "2025-07-28T14:07:29.602993", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "2004ed3d-f932-484c-94c1-32b2dab84228", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "2004ed3d-f932-484c-94c1-32b2dab84228", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1089.4853488312933, 1537.1824424304236, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_140729", "step_number": 2, "timestamp": "2025-07-28T14:07:29.620360", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "2004ed3d-f932-484c-94c1-32b2dab84228", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "2004ed3d-f932-484c-94c1-32b2dab84228", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1089.4853488312933, 1537.1824424304236, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_140729", "step_number": 3, "timestamp": "2025-07-28T14:07:31.428178", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1801.5923500061035}, {"session_id": "20250728_140729", "step_number": 4, "timestamp": "2025-07-28T14:07:31.431985", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1805.3991794586182}, {"session_id": "20250728_140729", "step_number": 5, "timestamp": "2025-07-28T14:07:31.460416", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 24.43385124206543}, {"session_id": "20250728_140729", "step_number": 6, "timestamp": "2025-07-28T14:07:31.464933", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.950929641723633}, {"session_id": "20250728_151014", "step_number": 1, "timestamp": "2025-07-28T15:10:14.874687", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "af3b7e4f-5037-4138-84fc-43fe5a115bdc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "af3b7e4f-5037-4138-84fc-43fe5a115bdc", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1493.5839885311102, 1760.3910445915549, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_151014", "step_number": 2, "timestamp": "2025-07-28T15:10:14.880667", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "af3b7e4f-5037-4138-84fc-43fe5a115bdc", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "af3b7e4f-5037-4138-84fc-43fe5a115bdc", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1493.5839885311102, 1760.3910445915549, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_151014", "step_number": 3, "timestamp": "2025-07-28T15:10:17.301686", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2414.489507675171}, {"session_id": "20250728_151014", "step_number": 4, "timestamp": "2025-07-28T15:10:17.305442", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2418.245315551758}, {"session_id": "20250728_151014", "step_number": 5, "timestamp": "2025-07-28T15:10:17.340713", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 32.05132484436035}, {"session_id": "20250728_151014", "step_number": 6, "timestamp": "2025-07-28T15:10:17.345204", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.54193878173828}, {"session_id": "20250728_152141", "step_number": 1, "timestamp": "2025-07-28T15:21:41.037669", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "442612c8-9583-4c26-b17a-7427dc4d384b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "442612c8-9583-4c26-b17a-7427dc4d384b", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1409.5507999534489, 2036.5955463375476, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_152141", "step_number": 2, "timestamp": "2025-07-28T15:21:41.045425", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "442612c8-9583-4c26-b17a-7427dc4d384b", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "442612c8-9583-4c26-b17a-7427dc4d384b", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1409.5507999534489, 2036.5955463375476, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_152141", "step_number": 3, "timestamp": "2025-07-28T15:21:43.695246", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2642.838716506958}, {"session_id": "20250728_152141", "step_number": 4, "timestamp": "2025-07-28T15:21:43.699077", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2646.6691493988037}, {"session_id": "20250728_152141", "step_number": 5, "timestamp": "2025-07-28T15:21:43.734162", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.099319458007812}, {"session_id": "20250728_152141", "step_number": 6, "timestamp": "2025-07-28T15:21:43.737664", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.60121154785156}, {"session_id": "20250728_171245", "step_number": 1, "timestamp": "2025-07-28T17:12:45.579909", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3f85a3c9-2558-4234-a4ed-2e9bd0175436", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3f85a3c9-2558-4234-a4ed-2e9bd0175436", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-745.8660110509384, 1401.4692886582502, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 92}}, "duration_ms": null}, {"session_id": "20250728_171245", "step_number": 2, "timestamp": "2025-07-28T17:12:45.593514", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "3f85a3c9-2558-4234-a4ed-2e9bd0175436", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "3f85a3c9-2558-4234-a4ed-2e9bd0175436", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-745.8660110509384, 1401.4692886582502, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_171245", "step_number": 3, "timestamp": "2025-07-28T17:12:47.335289", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1730.0407886505127}, {"session_id": "20250728_171245", "step_number": 4, "timestamp": "2025-07-28T17:12:47.340073", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 1734.8246574401855}, {"session_id": "20250728_171245", "step_number": 5, "timestamp": "2025-07-28T17:12:47.370665", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 26.134252548217773}, {"session_id": "20250728_171245", "step_number": 6, "timestamp": "2025-07-28T17:12:47.376295", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.763553619384766}, {"session_id": "20250728_212612", "step_number": 1, "timestamp": "2025-07-28T21:26:12.918787", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1ab08ae6-b3c3-40d3-ab9f-9a66ac9ade20", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1ab08ae6-b3c3-40d3-ab9f-9a66ac9ade20", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1376.8881619296249, 1781.7887721687155, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_212612", "step_number": 2, "timestamp": "2025-07-28T21:26:12.928320", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1ab08ae6-b3c3-40d3-ab9f-9a66ac9ade20", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1ab08ae6-b3c3-40d3-ab9f-9a66ac9ade20", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1376.8881619296249, 1781.7887721687155, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_212612", "step_number": 3, "timestamp": "2025-07-28T21:26:15.411147", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2474.6830463409424}, {"session_id": "20250728_212612", "step_number": 4, "timestamp": "2025-07-28T21:26:15.414654", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2478.189706802368}, {"session_id": "20250728_212612", "step_number": 5, "timestamp": "2025-07-28T21:26:15.454741", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 36.10038757324219}, {"session_id": "20250728_212612", "step_number": 6, "timestamp": "2025-07-28T21:26:15.458737", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 40.09675979614258}, {"session_id": "20250728_212612", "step_number": 7, "timestamp": "2025-07-28T21:31:32.326078", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "278bcbf9-eca1-46c2-8e20-49ef9f477ba0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "278bcbf9-eca1-46c2-8e20-49ef9f477ba0", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1358.096134368743, 1711.3907544314188, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_212612", "step_number": 8, "timestamp": "2025-07-28T21:31:32.332563", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "278bcbf9-eca1-46c2-8e20-49ef9f477ba0", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "278bcbf9-eca1-46c2-8e20-49ef9f477ba0", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1358.096134368743, 1711.3907544314188, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_212612", "step_number": 9, "timestamp": "2025-07-28T21:31:34.849320", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2508.3818435668945}, {"session_id": "20250728_212612", "step_number": 10, "timestamp": "2025-07-28T21:31:34.854901", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2513.963222503662}, {"session_id": "20250728_212612", "step_number": 11, "timestamp": "2025-07-28T21:31:34.898854", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.211273193359375}, {"session_id": "20250728_212612", "step_number": 12, "timestamp": "2025-07-28T21:31:34.905699", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 46.05603218078613}, {"session_id": "20250728_212612", "step_number": 13, "timestamp": "2025-07-28T21:34:33.244129", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "aaddfed0-9132-4cc3-b301-e02636165b64", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "aaddfed0-9132-4cc3-b301-e02636165b64", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1184.0892699754095, 1501.2342523022517, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_212612", "step_number": 14, "timestamp": "2025-07-28T21:34:33.251863", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "aaddfed0-9132-4cc3-b301-e02636165b64", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "aaddfed0-9132-4cc3-b301-e02636165b64", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1184.0892699754095, 1501.2342523022517, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_212612", "step_number": 15, "timestamp": "2025-07-28T21:34:35.562611", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2302.389621734619}, {"session_id": "20250728_212612", "step_number": 16, "timestamp": "2025-07-28T21:34:35.568355", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2308.134078979492}, {"session_id": "20250728_212612", "step_number": 17, "timestamp": "2025-07-28T21:34:35.607914", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 34.32154655456543}, {"session_id": "20250728_212612", "step_number": 18, "timestamp": "2025-07-28T21:34:35.612432", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 38.840293884277344}, {"session_id": "20250728_214529", "step_number": 1, "timestamp": "2025-07-28T21:45:29.699760", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "54c8de12-8ff3-4b5a-94c3-28e3ee5eb838", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "54c8de12-8ff3-4b5a-94c3-28e3ee5eb838", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1377.9310574137098, 1818.403164130471, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_214529", "step_number": 2, "timestamp": "2025-07-28T21:45:29.711905", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "54c8de12-8ff3-4b5a-94c3-28e3ee5eb838", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "54c8de12-8ff3-4b5a-94c3-28e3ee5eb838", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1377.9310574137098, 1818.403164130471, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_214529", "step_number": 3, "timestamp": "2025-07-28T21:45:32.385838", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2664.843797683716}, {"session_id": "20250728_214529", "step_number": 4, "timestamp": "2025-07-28T21:45:32.390347", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2669.3525314331055}, {"session_id": "20250728_214529", "step_number": 5, "timestamp": "2025-07-28T21:45:32.436690", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 41.992902755737305}, {"session_id": "20250728_214529", "step_number": 6, "timestamp": "2025-07-28T21:45:32.441551", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 46.85401916503906}, {"session_id": "20250728_214529", "step_number": 7, "timestamp": "2025-07-28T21:51:06.349802", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "12e57434-b358-40aa-8371-5c3b79160793", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "12e57434-b358-40aa-8371-5c3b79160793", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1170.2680498923269, 1635.2491830921715, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_214529", "step_number": 8, "timestamp": "2025-07-28T21:51:06.359987", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "12e57434-b358-40aa-8371-5c3b79160793", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "12e57434-b358-40aa-8371-5c3b79160793", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1170.2680498923269, 1635.2491830921715, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_214529", "step_number": 9, "timestamp": "2025-07-28T21:51:08.718690", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2349.9724864959717}, {"session_id": "20250728_214529", "step_number": 10, "timestamp": "2025-07-28T21:51:08.723660", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2354.942560195923}, {"session_id": "20250728_214529", "step_number": 11, "timestamp": "2025-07-28T21:51:08.762870", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 35.15481948852539}, {"session_id": "20250728_214529", "step_number": 12, "timestamp": "2025-07-28T21:51:08.768674", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 40.95864295959473}, {"session_id": "20250728_221119", "step_number": 1, "timestamp": "2025-07-28T22:11:19.707240", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ac4fa603-8293-471c-b809-d464b13da000", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ac4fa603-8293-471c-b809-d464b13da000", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1074.0822443610723, 1099.2728776871218, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_221119", "step_number": 2, "timestamp": "2025-07-28T22:11:19.717967", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ac4fa603-8293-471c-b809-d464b13da000", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ac4fa603-8293-471c-b809-d464b13da000", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1074.0822443610723, 1099.2728776871218, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_221119", "step_number": 3, "timestamp": "2025-07-28T22:11:21.946184", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2220.6568717956543}, {"session_id": "20250728_221119", "step_number": 4, "timestamp": "2025-07-28T22:11:21.951069", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2225.5425453186035}, {"session_id": "20250728_221119", "step_number": 5, "timestamp": "2025-07-28T22:11:21.990515", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 33.41054916381836}, {"session_id": "20250728_221119", "step_number": 6, "timestamp": "2025-07-28T22:11:21.994753", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 37.648677825927734}, {"session_id": "20250728_221119", "step_number": 7, "timestamp": "2025-07-28T22:12:13.493506", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5b8587d0-fc9e-43fb-806e-926402221fa3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5b8587d0-fc9e-43fb-806e-926402221fa3", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(-1074.0822443610723, 1099.2728776871218, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_221119", "step_number": 8, "timestamp": "2025-07-28T22:12:13.504120", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5b8587d0-fc9e-43fb-806e-926402221fa3", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5b8587d0-fc9e-43fb-806e-926402221fa3", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(-1074.0822443610723, 1099.2728776871218, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_221119", "step_number": 9, "timestamp": "2025-07-28T22:12:15.697019", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2180.068254470825}, {"session_id": "20250728_221119", "step_number": 10, "timestamp": "2025-07-28T22:12:15.702484", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2185.5337619781494}, {"session_id": "20250728_221119", "step_number": 11, "timestamp": "2025-07-28T22:12:15.746746", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.07012939453125}, {"session_id": "20250728_221119", "step_number": 12, "timestamp": "2025-07-28T22:12:15.753157", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.48168182373047}, {"session_id": "20250728_221119", "step_number": 13, "timestamp": "2025-07-28T22:13:03.690213", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "054662b9-aa14-49ae-966a-eb0fa2b327c1", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "054662b9-aa14-49ae-966a-eb0fa2b327c1", "parameters": {"start_point": "(0.0, 0.0, 100)", "end_point": "(301.5321274234191, 1705.007156885184, 100)", "flight_height": 100, "safety_distance": 30, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250728_221119", "step_number": 14, "timestamp": "2025-07-28T22:13:03.699246", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "054662b9-aa14-49ae-966a-eb0fa2b327c1", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "054662b9-aa14-49ae-966a-eb0fa2b327c1", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 100)", "end_point": "(301.5321274234191, 1705.007156885184, 100)", "flight_height": 100, "safety_distance": 30}}, "duration_ms": null}, {"session_id": "20250728_221119", "step_number": 15, "timestamp": "2025-07-28T22:13:06.149704", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2437.9498958587646}, {"session_id": "20250728_221119", "step_number": 16, "timestamp": "2025-07-28T22:13:06.155188", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100, "safety_distance": 30}}, "duration_ms": 2443.434000015259}, {"session_id": "20250728_221119", "step_number": 17, "timestamp": "2025-07-28T22:13:06.199678", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 39.49570655822754}, {"session_id": "20250728_221119", "step_number": 18, "timestamp": "2025-07-28T22:13:06.205561", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 45.378923416137695}]