[{"session_id": "20250731_112459", "step_number": 1, "timestamp": "2025-07-31T11:24:59.364039", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 2, "timestamp": "2025-07-31T11:24:59.369546", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 3, "timestamp": "2025-07-31T11:25:02.324115", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2950.566053390503}, {"session_id": "20250731_112459", "step_number": 4, "timestamp": "2025-07-31T11:25:02.325656", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2952.106475830078}, {"session_id": "20250731_112459", "step_number": 5, "timestamp": "2025-07-31T11:25:02.348140", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.483421325683594}, {"session_id": "20250731_112459", "step_number": 6, "timestamp": "2025-07-31T11:25:02.349170", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.513151168823242}, {"session_id": "20250731_112459", "step_number": 7, "timestamp": "2025-07-31T11:34:04.050713", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 8, "timestamp": "2025-07-31T11:34:04.058029", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 9, "timestamp": "2025-07-31T11:34:06.813915", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2750.1890659332275}, {"session_id": "20250731_112459", "step_number": 10, "timestamp": "2025-07-31T11:34:06.814406", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2750.6797313690186}, {"session_id": "20250731_112459", "step_number": 11, "timestamp": "2025-07-31T11:34:06.835555", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.643234252929688}, {"session_id": "20250731_112459", "step_number": 12, "timestamp": "2025-07-31T11:34:06.838570", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 23.658275604248047}, {"session_id": "20250731_112459", "step_number": 13, "timestamp": "2025-07-31T11:42:25.684551", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0d421cc9-2763-4dd3-ac41-f793bea2b3fb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0d421cc9-2763-4dd3-ac41-f793bea2b3fb", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 14, "timestamp": "2025-07-31T11:42:25.689011", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0d421cc9-2763-4dd3-ac41-f793bea2b3fb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0d421cc9-2763-4dd3-ac41-f793bea2b3fb", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 15, "timestamp": "2025-07-31T11:42:28.026038", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2332.8943252563477}, {"session_id": "20250731_112459", "step_number": 16, "timestamp": "2025-07-31T11:42:28.028038", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2334.8937034606934}, {"session_id": "20250731_112459", "step_number": 17, "timestamp": "2025-07-31T11:42:28.058994", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.95641326904297}, {"session_id": "20250731_112459", "step_number": 18, "timestamp": "2025-07-31T11:42:28.061669", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.630516052246094}, {"session_id": "20250731_112459", "step_number": 19, "timestamp": "2025-07-31T11:46:40.297035", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0e1192f3-3bf8-4d87-ba5f-142dbaf13273", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0e1192f3-3bf8-4d87-ba5f-142dbaf13273", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 20, "timestamp": "2025-07-31T11:46:40.300227", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0e1192f3-3bf8-4d87-ba5f-142dbaf13273", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0e1192f3-3bf8-4d87-ba5f-142dbaf13273", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 21, "timestamp": "2025-07-31T11:46:42.698686", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2393.458604812622}, {"session_id": "20250731_112459", "step_number": 22, "timestamp": "2025-07-31T11:46:42.699685", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2394.458055496216}, {"session_id": "20250731_112459", "step_number": 23, "timestamp": "2025-07-31T11:46:42.717401", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.704797744750977}, {"session_id": "20250731_112459", "step_number": 24, "timestamp": "2025-07-31T11:46:42.718472", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.7762508392334}, {"session_id": "20250731_112459", "step_number": 25, "timestamp": "2025-07-31T11:59:29.366837", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6a0694e8-83a3-4edb-b4f1-64dba68cde68", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6a0694e8-83a3-4edb-b4f1-64dba68cde68", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 26, "timestamp": "2025-07-31T11:59:29.375364", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6a0694e8-83a3-4edb-b4f1-64dba68cde68", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6a0694e8-83a3-4edb-b4f1-64dba68cde68", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 27, "timestamp": "2025-07-31T11:59:32.022029", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2641.923666000366}, {"session_id": "20250731_112459", "step_number": 28, "timestamp": "2025-07-31T11:59:32.023123", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2643.017530441284}, {"session_id": "20250731_112459", "step_number": 29, "timestamp": "2025-07-31T11:59:32.042535", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.835140228271484}, {"session_id": "20250731_112459", "step_number": 30, "timestamp": "2025-07-31T11:59:32.044548", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.847869873046875}, {"session_id": "20250731_112459", "step_number": 31, "timestamp": "2025-07-31T12:21:51.205197", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b112f23a-b368-4f47-85e7-94a9f0d78c5e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b112f23a-b368-4f47-85e7-94a9f0d78c5e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 32, "timestamp": "2025-07-31T12:21:51.213506", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b112f23a-b368-4f47-85e7-94a9f0d78c5e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b112f23a-b368-4f47-85e7-94a9f0d78c5e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 33, "timestamp": "2025-07-31T12:21:53.674143", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2454.348087310791}, {"session_id": "20250731_112459", "step_number": 34, "timestamp": "2025-07-31T12:21:53.675600", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2455.8050632476807}, {"session_id": "20250731_112459", "step_number": 35, "timestamp": "2025-07-31T12:21:53.694394", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.83807373046875}, {"session_id": "20250731_112459", "step_number": 36, "timestamp": "2025-07-31T12:21:53.696394", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.838882446289062}, {"session_id": "20250731_112459", "step_number": 37, "timestamp": "2025-07-31T12:27:39.398316", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "185ff7aa-a445-4f06-ae66-a0142d1b4a36", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "185ff7aa-a445-4f06-ae66-a0142d1b4a36", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 97}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 38, "timestamp": "2025-07-31T12:27:39.406838", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "185ff7aa-a445-4f06-ae66-a0142d1b4a36", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "185ff7aa-a445-4f06-ae66-a0142d1b4a36", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 39, "timestamp": "2025-07-31T12:27:41.381464", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1960.6742858886719}, {"session_id": "20250731_112459", "step_number": 40, "timestamp": "2025-07-31T12:27:41.383146", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1962.3565673828125}, {"session_id": "20250731_112459", "step_number": 41, "timestamp": "2025-07-31T12:27:41.400034", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.790462493896484}, {"session_id": "20250731_112459", "step_number": 42, "timestamp": "2025-07-31T12:27:41.401209", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.965627670288086}, {"session_id": "20250731_112459", "step_number": 43, "timestamp": "2025-07-31T12:34:03.498890", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "054218eb-8d41-4d21-8949-afe378d56c96", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "054218eb-8d41-4d21-8949-afe378d56c96", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 82}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 44, "timestamp": "2025-07-31T12:34:03.506893", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "054218eb-8d41-4d21-8949-afe378d56c96", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "054218eb-8d41-4d21-8949-afe378d56c96", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 45, "timestamp": "2025-07-31T12:34:05.701552", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2184.9365234375}, {"session_id": "20250731_112459", "step_number": 46, "timestamp": "2025-07-31T12:34:05.702551", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2185.936212539673}, {"session_id": "20250731_112459", "step_number": 47, "timestamp": "2025-07-31T12:34:05.720178", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.62643051147461}, {"session_id": "20250731_112459", "step_number": 48, "timestamp": "2025-07-31T12:34:05.721782", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.230510711669922}, {"session_id": "20250731_112459", "step_number": 49, "timestamp": "2025-07-31T12:38:47.573988", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ac669382-2832-4701-95d2-f0cc8f957680", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ac669382-2832-4701-95d2-f0cc8f957680", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 72}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 50, "timestamp": "2025-07-31T12:38:47.583257", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ac669382-2832-4701-95d2-f0cc8f957680", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ac669382-2832-4701-95d2-f0cc8f957680", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 51, "timestamp": "2025-07-31T12:38:49.630111", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2034.8296165466309}, {"session_id": "20250731_112459", "step_number": 52, "timestamp": "2025-07-31T12:38:49.632110", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2036.8287563323975}, {"session_id": "20250731_112459", "step_number": 53, "timestamp": "2025-07-31T12:38:49.647947", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.833045959472656}, {"session_id": "20250731_112459", "step_number": 54, "timestamp": "2025-07-31T12:38:49.649992", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.88241958618164}, {"session_id": "20250731_112459", "step_number": 55, "timestamp": "2025-07-31T12:45:07.114364", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e85f8d1a-dc63-4d7f-ab4d-9f5be424c0cf", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e85f8d1a-dc63-4d7f-ab4d-9f5be424c0cf", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 86}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 56, "timestamp": "2025-07-31T12:45:07.122943", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e85f8d1a-dc63-4d7f-ab4d-9f5be424c0cf", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e85f8d1a-dc63-4d7f-ab4d-9f5be424c0cf", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 57, "timestamp": "2025-07-31T12:45:09.377313", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2245.3556060791016}, {"session_id": "20250731_112459", "step_number": 58, "timestamp": "2025-07-31T12:45:09.378302", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2246.344566345215}, {"session_id": "20250731_112459", "step_number": 59, "timestamp": "2025-07-31T12:45:09.396133", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.326976776123047}, {"session_id": "20250731_112459", "step_number": 60, "timestamp": "2025-07-31T12:45:09.399146", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.340110778808594}, {"session_id": "20250731_112459", "step_number": 61, "timestamp": "2025-07-31T12:46:58.148154", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e9ebf2ab-f77e-4fe3-94e9-238b2607f7fb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e9ebf2ab-f77e-4fe3-94e9-238b2607f7fb", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 105}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 62, "timestamp": "2025-07-31T12:46:58.160574", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "e9ebf2ab-f77e-4fe3-94e9-238b2607f7fb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "e9ebf2ab-f77e-4fe3-94e9-238b2607f7fb", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 63, "timestamp": "2025-07-31T12:47:00.226185", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2055.8972358703613}, {"session_id": "20250731_112459", "step_number": 64, "timestamp": "2025-07-31T12:47:00.228280", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2057.9915046691895}, {"session_id": "20250731_112459", "step_number": 65, "timestamp": "2025-07-31T12:47:00.243636", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 13.516664505004883}, {"session_id": "20250731_112459", "step_number": 66, "timestamp": "2025-07-31T12:47:00.246642", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.521930694580078}, {"session_id": "20250731_112459", "step_number": 67, "timestamp": "2025-07-31T12:54:33.035744", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1828d21b-6b9b-4b3e-8e3c-74b997d00112", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1828d21b-6b9b-4b3e-8e3c-74b997d00112", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 98}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 68, "timestamp": "2025-07-31T12:54:33.047525", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "1828d21b-6b9b-4b3e-8e3c-74b997d00112", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "1828d21b-6b9b-4b3e-8e3c-74b997d00112", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 69, "timestamp": "2025-07-31T12:54:35.021698", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1959.9249362945557}, {"session_id": "20250731_112459", "step_number": 70, "timestamp": "2025-07-31T12:54:35.024706", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1962.9333019256592}, {"session_id": "20250731_112459", "step_number": 71, "timestamp": "2025-07-31T12:54:35.043213", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.513824462890625}, {"session_id": "20250731_112459", "step_number": 72, "timestamp": "2025-07-31T12:54:35.045407", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.70870590209961}, {"session_id": "20250731_112459", "step_number": 73, "timestamp": "2025-07-31T12:59:37.276721", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "023bce8d-3f93-4896-ad26-4741fb79e54f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "023bce8d-3f93-4896-ad26-4741fb79e54f", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 64}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 74, "timestamp": "2025-07-31T12:59:37.286275", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "023bce8d-3f93-4896-ad26-4741fb79e54f", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "023bce8d-3f93-4896-ad26-4741fb79e54f", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 75, "timestamp": "2025-07-31T12:59:39.252452", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 1958.4870338439941}, {"session_id": "20250731_112459", "step_number": 76, "timestamp": "2025-07-31T12:59:39.253453", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 1959.4876766204834}, {"session_id": "20250731_112459", "step_number": 77, "timestamp": "2025-07-31T12:59:39.271079", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.112638473510742}, {"session_id": "20250731_112459", "step_number": 78, "timestamp": "2025-07-31T12:59:39.273257", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.290353775024414}, {"session_id": "20250731_112459", "step_number": 79, "timestamp": "2025-07-31T13:05:04.340632", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ac654194-5ffd-4920-9250-59e145ec1312", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ac654194-5ffd-4920-9250-59e145ec1312", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 61}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 80, "timestamp": "2025-07-31T13:05:04.348511", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "ac654194-5ffd-4920-9250-59e145ec1312", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "ac654194-5ffd-4920-9250-59e145ec1312", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 81, "timestamp": "2025-07-31T13:05:06.795391", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2435.3387355804443}, {"session_id": "20250731_112459", "step_number": 82, "timestamp": "2025-07-31T13:05:06.797562", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2437.509775161743}, {"session_id": "20250731_112459", "step_number": 83, "timestamp": "2025-07-31T13:05:06.816456", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.14236831665039}, {"session_id": "20250731_112459", "step_number": 84, "timestamp": "2025-07-31T13:05:06.818455", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.141984939575195}, {"session_id": "20250731_143610", "step_number": 1, "timestamp": "2025-07-31T14:36:10.409566", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "799ab0aa-351b-4013-8d96-85f7c7963e37", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "799ab0aa-351b-4013-8d96-85f7c7963e37", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_143610", "step_number": 2, "timestamp": "2025-07-31T14:36:10.416479", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "799ab0aa-351b-4013-8d96-85f7c7963e37", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "799ab0aa-351b-4013-8d96-85f7c7963e37", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_143610", "step_number": 3, "timestamp": "2025-07-31T14:36:12.740927", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2319.0436363220215}, {"session_id": "20250731_143610", "step_number": 4, "timestamp": "2025-07-31T14:36:12.744228", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2322.3447799682617}, {"session_id": "20250731_143610", "step_number": 5, "timestamp": "2025-07-31T14:36:12.761285", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 15.05732536315918}, {"session_id": "20250731_143610", "step_number": 6, "timestamp": "2025-07-31T14:36:12.764296", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.06783676147461}, {"session_id": "20250731_143610", "step_number": 7, "timestamp": "2025-07-31T14:36:28.769430", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5609607a-6da2-427a-9763-c7e209993412", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5609607a-6da2-427a-9763-c7e209993412", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_143610", "step_number": 8, "timestamp": "2025-07-31T14:36:28.777312", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "5609607a-6da2-427a-9763-c7e209993412", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "5609607a-6da2-427a-9763-c7e209993412", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_143610", "step_number": 9, "timestamp": "2025-07-31T14:36:31.143107", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2359.6720695495605}, {"session_id": "20250731_143610", "step_number": 10, "timestamp": "2025-07-31T14:36:31.145439", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2362.0035648345947}, {"session_id": "20250731_143610", "step_number": 11, "timestamp": "2025-07-31T14:36:31.165401", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.962528228759766}, {"session_id": "20250731_143610", "step_number": 12, "timestamp": "2025-07-31T14:36:31.167401", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.962383270263672}, {"session_id": "20250731_143610", "step_number": 13, "timestamp": "2025-07-31T14:45:41.408906", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "673e40af-0eaa-4696-a954-8e3f6655a495", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "673e40af-0eaa-4696-a954-8e3f6655a495", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 85}}, "duration_ms": null}, {"session_id": "20250731_143610", "step_number": 14, "timestamp": "2025-07-31T14:45:41.418512", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "673e40af-0eaa-4696-a954-8e3f6655a495", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "673e40af-0eaa-4696-a954-8e3f6655a495", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_143610", "step_number": 15, "timestamp": "2025-07-31T14:45:43.727490", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2298.6044883728027}, {"session_id": "20250731_143610", "step_number": 16, "timestamp": "2025-07-31T14:45:43.729995", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2301.110029220581}, {"session_id": "20250731_143610", "step_number": 17, "timestamp": "2025-07-31T14:45:43.750903", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.878293991088867}, {"session_id": "20250731_143610", "step_number": 18, "timestamp": "2025-07-31T14:45:43.753732", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.70784568786621}]