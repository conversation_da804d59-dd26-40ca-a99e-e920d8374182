[{"session_id": "20250731_112459", "step_number": 1, "timestamp": "2025-07-31T11:24:59.364039", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 2, "timestamp": "2025-07-31T11:24:59.369546", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 3, "timestamp": "2025-07-31T11:25:02.324115", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2950.566053390503}, {"session_id": "20250731_112459", "step_number": 4, "timestamp": "2025-07-31T11:25:02.325656", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2952.106475830078}, {"session_id": "20250731_112459", "step_number": 5, "timestamp": "2025-07-31T11:25:02.348140", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.483421325683594}, {"session_id": "20250731_112459", "step_number": 6, "timestamp": "2025-07-31T11:25:02.349170", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.513151168823242}, {"session_id": "20250731_112459", "step_number": 7, "timestamp": "2025-07-31T11:34:04.050713", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 8, "timestamp": "2025-07-31T11:34:04.058029", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 9, "timestamp": "2025-07-31T11:34:06.813915", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2750.1890659332275}, {"session_id": "20250731_112459", "step_number": 10, "timestamp": "2025-07-31T11:34:06.814406", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2750.6797313690186}, {"session_id": "20250731_112459", "step_number": 11, "timestamp": "2025-07-31T11:34:06.835555", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.643234252929688}, {"session_id": "20250731_112459", "step_number": 12, "timestamp": "2025-07-31T11:34:06.838570", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 23.658275604248047}, {"session_id": "20250731_112459", "step_number": 13, "timestamp": "2025-07-31T11:42:25.684551", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0d421cc9-2763-4dd3-ac41-f793bea2b3fb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0d421cc9-2763-4dd3-ac41-f793bea2b3fb", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 14, "timestamp": "2025-07-31T11:42:25.689011", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0d421cc9-2763-4dd3-ac41-f793bea2b3fb", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0d421cc9-2763-4dd3-ac41-f793bea2b3fb", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 15, "timestamp": "2025-07-31T11:42:28.026038", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2332.8943252563477}, {"session_id": "20250731_112459", "step_number": 16, "timestamp": "2025-07-31T11:42:28.028038", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2334.8937034606934}, {"session_id": "20250731_112459", "step_number": 17, "timestamp": "2025-07-31T11:42:28.058994", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 28.95641326904297}, {"session_id": "20250731_112459", "step_number": 18, "timestamp": "2025-07-31T11:42:28.061669", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 31.630516052246094}, {"session_id": "20250731_112459", "step_number": 19, "timestamp": "2025-07-31T11:46:40.297035", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0e1192f3-3bf8-4d87-ba5f-142dbaf13273", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0e1192f3-3bf8-4d87-ba5f-142dbaf13273", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 20, "timestamp": "2025-07-31T11:46:40.300227", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "0e1192f3-3bf8-4d87-ba5f-142dbaf13273", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "0e1192f3-3bf8-4d87-ba5f-142dbaf13273", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 21, "timestamp": "2025-07-31T11:46:42.698686", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2393.458604812622}, {"session_id": "20250731_112459", "step_number": 22, "timestamp": "2025-07-31T11:46:42.699685", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2394.458055496216}, {"session_id": "20250731_112459", "step_number": 23, "timestamp": "2025-07-31T11:46:42.717401", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.704797744750977}, {"session_id": "20250731_112459", "step_number": 24, "timestamp": "2025-07-31T11:46:42.718472", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.7762508392334}, {"session_id": "20250731_112459", "step_number": 25, "timestamp": "2025-07-31T11:59:29.366837", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6a0694e8-83a3-4edb-b4f1-64dba68cde68", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6a0694e8-83a3-4edb-b4f1-64dba68cde68", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 26, "timestamp": "2025-07-31T11:59:29.375364", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "6a0694e8-83a3-4edb-b4f1-64dba68cde68", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "6a0694e8-83a3-4edb-b4f1-64dba68cde68", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 27, "timestamp": "2025-07-31T11:59:32.022029", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2641.923666000366}, {"session_id": "20250731_112459", "step_number": 28, "timestamp": "2025-07-31T11:59:32.023123", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2643.017530441284}, {"session_id": "20250731_112459", "step_number": 29, "timestamp": "2025-07-31T11:59:32.042535", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 17.835140228271484}, {"session_id": "20250731_112459", "step_number": 30, "timestamp": "2025-07-31T11:59:32.044548", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 19.847869873046875}, {"session_id": "20250731_112459", "step_number": 31, "timestamp": "2025-07-31T12:21:51.205197", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b112f23a-b368-4f47-85e7-94a9f0d78c5e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b112f23a-b368-4f47-85e7-94a9f0d78c5e", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 32, "timestamp": "2025-07-31T12:21:51.213506", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "b112f23a-b368-4f47-85e7-94a9f0d78c5e", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "b112f23a-b368-4f47-85e7-94a9f0d78c5e", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 33, "timestamp": "2025-07-31T12:21:53.674143", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2454.348087310791}, {"session_id": "20250731_112459", "step_number": 34, "timestamp": "2025-07-31T12:21:53.675600", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2455.8050632476807}, {"session_id": "20250731_112459", "step_number": 35, "timestamp": "2025-07-31T12:21:53.694394", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 16.83807373046875}, {"session_id": "20250731_112459", "step_number": 36, "timestamp": "2025-07-31T12:21:53.696394", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 18.838882446289062}]