[{"session_id": "20250731_112459", "step_number": 1, "timestamp": "2025-07-31T11:24:59.364039", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 2, "timestamp": "2025-07-31T11:24:59.369546", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "df383afb-27a8-4e8b-a106-5f15a476f863", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 3, "timestamp": "2025-07-31T11:25:02.324115", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2950.566053390503}, {"session_id": "20250731_112459", "step_number": 4, "timestamp": "2025-07-31T11:25:02.325656", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2952.106475830078}, {"session_id": "20250731_112459", "step_number": 5, "timestamp": "2025-07-31T11:25:02.348140", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 21.483421325683594}, {"session_id": "20250731_112459", "step_number": 6, "timestamp": "2025-07-31T11:25:02.349170", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 22.513151168823242}, {"session_id": "20250731_112459", "step_number": 7, "timestamp": "2025-07-31T11:34:04.050713", "step_type": "算法开始", "level": "INFO", "message": "开始执行算法: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "parameters": {"start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0, "buildings_count": 14}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 8, "timestamp": "2025-07-31T11:34:04.058029", "step_type": "算法结束", "level": "INFO", "message": "算法执行成功: ImprovedClusterBased", "algorithm": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "details": {"algorithm_name": "ImprovedClusterBased", "request_id": "7872dfb9-2398-4792-aa9e-b6a9e85acb51", "success": true, "result": {"algorithm_name": "ImprovedClusterBased", "start_point": "(0.0, 0.0, 0.0)", "end_point": "(0.0, 0.0, 0.0)", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": null}, {"session_id": "20250731_112459", "step_number": 9, "timestamp": "2025-07-31T11:34:06.813915", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "actual_count": 81, "generation_method": "优化版本", "cost_calculation_completed": true}}, "duration_ms": 2750.1890659332275}, {"session_id": "20250731_112459", "step_number": 10, "timestamp": "2025-07-31T11:34:06.814406", "step_type": "路径生成", "level": "INFO", "message": "生成路径 81 条", "algorithm": null, "request_id": null, "details": {"path_count": 81, "generation_details": {"target_count": 81, "generation_method": "改进分簇算法", "flight_height": 100.0, "safety_distance": 20.0}}, "duration_ms": 2750.6797313690186}, {"session_id": "20250731_112459", "step_number": 11, "timestamp": "2025-07-31T11:34:06.835555", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 20.643234252929688}, {"session_id": "20250731_112459", "step_number": 12, "timestamp": "2025-07-31T11:34:06.838570", "step_type": "分簇处理", "level": "INFO", "message": "完成分簇: 13 个簇，81 条路径", "algorithm": null, "request_id": null, "details": {"cluster_count": 13, "path_count": 81}, "duration_ms": 23.658275604248047}]