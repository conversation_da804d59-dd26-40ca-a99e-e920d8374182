#!/usr/bin/env python3
"""
调试保护区检测逻辑
详细检查为什么改进算法检测不到保护区
"""

def debug_protection_zone_detection():
    """调试保护区检测逻辑"""
    
    print("🔍 调试保护区检测逻辑")
    print("=" * 80)
    
    try:
        # 1. 测试保护区管理器
        print("📋 1. 测试保护区管理器")
        
        from protection_zones import ProtectionZoneManager
        manager = ProtectionZoneManager()
        
        print(f"   保护区总数: {len(manager.zones)}")
        
        # 显示前几个保护区的位置
        for i, zone in enumerate(manager.zones[:5]):
            print(f"   保护区{i+1}: {zone.name}")
            print(f"     中心: ({zone.center[0]:.6f}, {zone.center[1]:.6f})")
            print(f"     半径: {zone.radius}m")
            print(f"     人数: {zone.people_count}, 车辆: {zone.vehicle_count}")
            print(f"     总代价: {zone.total_crash_cost}")
        
        # 2. 测试路径点
        print(f"\n🛤️ 2. 测试路径点")
        
        # 使用调试脚本中相同的路径点
        test_path_points = [
            (139.7670, 35.6810),  # 东京站附近
            (139.7673, 35.6812),  # 东京站中心
            (139.7016, 35.6598),  # 涩谷十字路口
        ]
        
        print(f"   测试路径点数: {len(test_path_points)}")
        for i, (lng, lat) in enumerate(test_path_points):
            print(f"     点{i+1}: ({lng:.6f}, {lat:.6f})")
        
        # 3. 测试距离计算
        print(f"\n📏 3. 测试距离计算")
        
        # 检查每个路径点到每个保护区的距离
        for i, (lng, lat) in enumerate(test_path_points):
            print(f"   路径点{i+1} ({lng:.6f}, {lat:.6f}):")
            
            for j, zone in enumerate(manager.zones[:5]):  # 只检查前5个保护区
                distance = zone.get_distance_to_point(lng, lat)
                is_in_range = distance <= zone.radius + 500  # 500米缓冲区
                print(f"     到{zone.name}: {distance:.1f}m (半径{zone.radius}m) {'✅' if is_in_range else '❌'}")
        
        # 4. 测试get_zones_for_path方法
        print(f"\n🔍 4. 测试get_zones_for_path方法")
        
        relevant_zones = manager.get_zones_for_path(test_path_points, buffer_distance=500)
        print(f"   检测到相关保护区: {len(relevant_zones)} 个")
        
        for zone in relevant_zones:
            print(f"     - {zone.name}: 半径{zone.radius}m, 代价{zone.total_crash_cost}")
        
        # 5. 测试碰撞代价计算
        print(f"\n💰 5. 测试碰撞代价计算")
        
        if relevant_zones:
            for i, (lng, lat) in enumerate(test_path_points):
                print(f"   路径点{i+1} ({lng:.6f}, {lat:.6f}):")
                point_total_cost = 0.0
                
                for zone in relevant_zones:
                    cost = zone.get_collision_cost(lng, lat)
                    if cost > 0:
                        point_total_cost += cost
                        print(f"     {zone.name}: {cost:.4f}")
                
                print(f"     点总代价: {point_total_cost:.4f}")
        else:
            print("   ❌ 没有相关保护区，无法计算碰撞代价")
        
        # 6. 模拟改进算法的检测逻辑
        print(f"\n🤖 6. 模拟改进算法的检测逻辑")
        
        # 模拟改进算法中的路径点格式
        class MockWaypoint:
            def __init__(self, lng, lat, alt=120):
                self.lng = lng
                self.lat = lat
                self.alt = alt
        
        mock_final_path = [MockWaypoint(lng, lat) for lng, lat in test_path_points]
        
        print(f"   模拟最终路径点数: {len(mock_final_path)}")
        
        # 模拟改进算法的保护区检测
        path_points_for_detection = [(wp.lng, wp.lat) for wp in mock_final_path]
        relevant_zones_algo = manager.get_zones_for_path(path_points_for_detection, buffer_distance=500)
        
        print(f"   算法检测到相关保护区: {len(relevant_zones_algo)} 个")
        
        # 模拟碰撞代价计算
        collision_cost_breakdown = {}
        total_estimated_cost = 0.0
        
        for i, waypoint in enumerate(mock_final_path):
            waypoint_cost = 0.0
            
            for zone in relevant_zones_algo:
                distance = zone.get_distance_to_point(waypoint.lng, waypoint.lat)
                
                if distance <= zone.radius:
                    collision_cost = zone.get_collision_cost(waypoint.lng, waypoint.lat)
                    
                    if collision_cost > 0:
                        waypoint_cost += collision_cost
                        
                        if zone.id not in collision_cost_breakdown:
                            collision_cost_breakdown[zone.id] = {
                                'zone_name': zone.name,
                                'zone_type': zone.zone_type.value,
                                'total_cost': 0.0,
                                'average_crash_cost': zone.average_crash_cost
                            }
                        
                        collision_cost_breakdown[zone.id]['total_cost'] += collision_cost
            
            if waypoint_cost > 0:
                print(f"     航点{i+1}: {waypoint_cost:.4f}")
                total_estimated_cost += waypoint_cost
        
        print(f"   总估计代价: {total_estimated_cost:.4f}")
        print(f"   活跃保护区数: {len(collision_cost_breakdown)}")
        print(f"   活跃保护区ID: {list(collision_cost_breakdown.keys())}")
        
        # 7. 检查具体的保护区
        print(f"\n🎯 7. 检查具体的保护区")
        
        # 检查东京站保护区
        tokyo_station = None
        for zone in manager.zones:
            if zone.id == 'tokyo_station':
                tokyo_station = zone
                break
        
        if tokyo_station:
            print(f"   东京站保护区:")
            print(f"     中心: ({tokyo_station.center[0]:.6f}, {tokyo_station.center[1]:.6f})")
            print(f"     半径: {tokyo_station.radius}m")
            
            # 检查第一个路径点（应该在东京站附近）
            test_point = test_path_points[0]  # (139.7670, 35.6810)
            distance = tokyo_station.get_distance_to_point(test_point[0], test_point[1])
            cost = tokyo_station.get_collision_cost(test_point[0], test_point[1])
            
            print(f"     测试点到东京站距离: {distance:.1f}m")
            print(f"     是否在保护区内: {'是' if distance <= tokyo_station.radius else '否'}")
            print(f"     碰撞代价: {cost:.4f}")
        else:
            print(f"   ❌ 找不到东京站保护区")
        
        # 8. 生成调试报告
        print(f"\n📊 8. 调试报告")
        
        print(f"   保护区系统状态:")
        print(f"     - 总保护区数: {len(manager.zones)}")
        print(f"     - 路径相关保护区: {len(relevant_zones)}")
        print(f"     - 算法检测保护区: {len(relevant_zones_algo)}")
        print(f"     - 活跃保护区: {len(collision_cost_breakdown)}")
        
        if len(collision_cost_breakdown) == 0:
            print(f"\n❌ 问题诊断: 没有检测到活跃保护区")
            print(f"   可能原因:")
            print(f"   1. 路径点不在任何保护区范围内")
            print(f"   2. 保护区半径设置过小")
            print(f"   3. 碰撞代价计算逻辑有问题")
            print(f"   4. 距离计算有误")
            
            # 建议解决方案
            print(f"\n💡 建议解决方案:")
            print(f"   1. 检查保护区中心坐标是否正确")
            print(f"   2. 增加保护区半径")
            print(f"   3. 检查距离计算公式")
            print(f"   4. 验证路径点坐标格式")
        else:
            print(f"\n✅ 保护区检测正常")
            print(f"   活跃保护区: {', '.join([info['zone_name'] for info in collision_cost_breakdown.values()])}")
        
        return {
            'total_zones': len(manager.zones),
            'relevant_zones': len(relevant_zones),
            'active_zones': len(collision_cost_breakdown),
            'total_cost': total_estimated_cost,
            'active_zone_ids': list(collision_cost_breakdown.keys())
        }
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = debug_protection_zone_detection()
    if result:
        print(f"\n🎉 保护区检测调试完成")
        print(f"   总保护区: {result['total_zones']}")
        print(f"   相关保护区: {result['relevant_zones']}")
        print(f"   活跃保护区: {result['active_zones']}")
        print(f"   总代价: {result['total_cost']:.4f}")
        
        if result['active_zones'] > 0:
            print(f"   ✅ 保护区检测正常工作")
            print(f"   活跃保护区ID: {result['active_zone_ids']}")
        else:
            print(f"   ❌ 保护区检测有问题，需要进一步调试")
    else:
        print(f"\n❌ 保护区检测调试失败")
