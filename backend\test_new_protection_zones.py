#!/usr/bin/env python3
"""
测试新的保护区系统
验证基于人车数量的碰撞代价计算
"""

def test_new_protection_zones():
    """测试新的保护区系统"""
    
    print("🔍 测试新的保护区系统")
    print("=" * 60)
    
    try:
        from protection_zones import ProtectionZoneManager
        manager = ProtectionZoneManager()
        
        print(f"📋 1. 保护区基本信息")
        print(f"   总保护区数: {len(manager.zones)}")
        
        # 显示每个保护区的详细信息
        print(f"\n🛡️ 2. 保护区详细信息")
        for zone in manager.zones:
            print(f"   {zone.name} (ID: {zone.id})")
            print(f"     类型: {zone.zone_type.value}")
            print(f"     人数: {zone.people_count} 人")
            print(f"     车辆: {zone.vehicle_count} 辆")
            print(f"     总代价: {zone.total_crash_cost:.1f}")
            print(f"     平均代价: {zone.average_crash_cost:.6f}/m²")
            print(f"     半径: {zone.radius}m")
            print()
        
        # 测试代价计算常数
        print(f"💰 3. 代价计算常数")
        from protection_zones import ProtectionZone
        print(f"   人员碰撞代价: {ProtectionZone.PERSON_CRASH_COST}/人")
        print(f"   车辆碰撞代价: {ProtectionZone.VEHICLE_CRASH_COST}/车")
        
        # 验证代价计算
        print(f"\n🧮 4. 代价计算验证")
        test_zone = manager.zones[0]  # 东京站
        expected_cost = (test_zone.people_count * ProtectionZone.PERSON_CRASH_COST + 
                        test_zone.vehicle_count * ProtectionZone.VEHICLE_CRASH_COST)
        actual_cost = test_zone.total_crash_cost
        
        print(f"   测试保护区: {test_zone.name}")
        print(f"   人数: {test_zone.people_count}, 车辆: {test_zone.vehicle_count}")
        print(f"   预期总代价: {expected_cost}")
        print(f"   实际总代价: {actual_cost}")
        print(f"   计算正确: {'✅' if abs(expected_cost - actual_cost) < 0.01 else '❌'}")
        
        # 测试路径碰撞代价计算
        print(f"\n🛤️ 5. 路径碰撞代价计算测试")
        
        # 模拟经过东京站和涩谷的路径
        test_path_points = [
            (139.7670, 35.6810),  # 东京站附近
            (139.7673, 35.6812),  # 东京站中心
            (139.7016, 35.6598),  # 涩谷十字路口
        ]
        
        print(f"   测试路径点数: {len(test_path_points)}")
        
        # 获取相关保护区
        relevant_zones = manager.get_zones_for_path(test_path_points, buffer_distance=500)
        print(f"   检测到相关保护区: {len(relevant_zones)} 个")
        
        for zone in relevant_zones:
            print(f"     - {zone.name}: {zone.people_count}人 + {zone.vehicle_count}车 = {zone.total_crash_cost:.1f}代价")
        
        # 计算路径总碰撞代价
        total_cost = manager.calculate_path_collision_cost(test_path_points)
        print(f"   路径总碰撞代价: {total_cost:.4f}")
        
        # 分析高风险保护区
        print(f"\n⚠️ 6. 高风险保护区分析")
        
        high_risk_zones = [zone for zone in manager.zones if zone.total_crash_cost >= 5000]
        print(f"   高风险保护区数量: {len(high_risk_zones)}")
        
        for zone in high_risk_zones:
            print(f"     - {zone.name}: {zone.total_crash_cost:.1f} (人:{zone.people_count}, 车:{zone.vehicle_count})")
        
        # 按类型统计
        print(f"\n📊 7. 按类型统计")
        
        type_stats = {}
        for zone in manager.zones:
            zone_type = zone.zone_type.value
            if zone_type not in type_stats:
                type_stats[zone_type] = {
                    'count': 0,
                    'total_people': 0,
                    'total_vehicles': 0,
                    'total_cost': 0.0
                }
            
            type_stats[zone_type]['count'] += 1
            type_stats[zone_type]['total_people'] += zone.people_count
            type_stats[zone_type]['total_vehicles'] += zone.vehicle_count
            type_stats[zone_type]['total_cost'] += zone.total_crash_cost
        
        for zone_type, stats in type_stats.items():
            avg_cost = stats['total_cost'] / stats['count']
            print(f"   {zone_type}: {stats['count']}个, 平均代价: {avg_cost:.1f}")
            print(f"     总人数: {stats['total_people']}, 总车辆: {stats['total_vehicles']}")
        
        # 测试API响应格式
        print(f"\n📤 8. API响应格式测试")
        
        api_data = manager.to_dict()
        print(f"   API响应包含: {list(api_data.keys())}")
        print(f"   保护区数据字段: {list(api_data['zones'][0].keys())}")
        print(f"   统计信息字段: {list(api_data['statistics'].keys())}")
        
        # 验证新字段
        first_zone = api_data['zones'][0]
        required_fields = ['people_count', 'vehicle_count', 'total_crash_cost']
        missing_fields = [field for field in required_fields if field not in first_zone]
        
        if not missing_fields:
            print(f"   ✅ 所有新字段都存在")
        else:
            print(f"   ❌ 缺少字段: {missing_fields}")
        
        # 生成总结报告
        print(f"\n📋 9. 总结报告")
        
        total_people = sum(zone.people_count for zone in manager.zones)
        total_vehicles = sum(zone.vehicle_count for zone in manager.zones)
        total_cost = sum(zone.total_crash_cost for zone in manager.zones)
        
        print(f"   保护区总数: {len(manager.zones)}")
        print(f"   总人数: {total_people}")
        print(f"   总车辆数: {total_vehicles}")
        print(f"   总碰撞代价: {total_cost:.1f}")
        print(f"   平均每个保护区代价: {total_cost/len(manager.zones):.1f}")
        
        # 代价分布
        costs = [zone.total_crash_cost for zone in manager.zones]
        costs.sort()
        print(f"   代价分布:")
        print(f"     最低: {costs[0]:.1f}")
        print(f"     最高: {costs[-1]:.1f}")
        print(f"     中位数: {costs[len(costs)//2]:.1f}")
        
        return {
            'total_zones': len(manager.zones),
            'total_people': total_people,
            'total_vehicles': total_vehicles,
            'total_cost': total_cost,
            'high_risk_zones': len(high_risk_zones),
            'api_format_valid': not missing_fields
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_new_protection_zones()
    if result:
        print(f"\n🎉 新保护区系统测试完成")
        print(f"✅ 保护区数量: {result['total_zones']}")
        print(f"✅ 总人数: {result['total_people']}")
        print(f"✅ 总车辆: {result['total_vehicles']}")
        print(f"✅ 总代价: {result['total_cost']:.1f}")
        print(f"✅ 高风险区域: {result['high_risk_zones']}")
        print(f"✅ API格式: {'正确' if result['api_format_valid'] else '需要修复'}")
        
        print(f"\n💡 新保护区系统特点:")
        print(f"   - 基于人数和车辆数计算代价")
        print(f"   - 人员代价: 8.0/人")
        print(f"   - 车辆代价: 15.0/车")
        print(f"   - 总代价 = 人数×8.0 + 车辆数×15.0")
        print(f"   - 代价分摊到保护区面积得到平均代价")
        print(f"   - 前端可以清楚看到每个保护区的人车构成")
    else:
        print(f"\n❌ 新保护区系统测试失败")
