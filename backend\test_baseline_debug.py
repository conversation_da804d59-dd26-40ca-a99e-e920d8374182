#!/usr/bin/env python3
"""
基准算法调试测试脚本
"""

def test_imports():
    """测试导入"""
    print("🧪 测试模块导入...")
    
    try:
        from algorithm_comparison_api import get_latest_improved_result, get_latest_baseline_result
        print("✅ 算法对比API导入成功")
        
        improved = get_latest_improved_result()
        baseline = get_latest_baseline_result()
        
        print(f"📊 改进算法数据: {'✅ 可用' if improved else '❌ 不可用'}")
        print(f"📊 基准算法数据: {'✅ 可用' if baseline else '❌ 不可用'}")
        
        if baseline:
            print(f"   基准算法详情: {baseline}")
        
        return improved, baseline
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_baseline_algorithm():
    """测试基准算法"""
    print("\n🧪 测试基准算法...")
    
    try:
        from algorithms.astar import AStarAlgorithm
        from algorithms.data_structures import PathPlanningRequest
        
        # 创建基准算法实例
        baseline_algorithm = AStarAlgorithm()
        print("✅ 基准算法实例创建成功")
        
        # 创建简单的测试请求
        test_request_data = {
            'startPoint': {'longitude': 139.767296, 'latitude': 35.678924, 'height': 100},
            'endPoint': {'longitude': 139.759127, 'latitude': 35.689760, 'height': 100},
            'algorithm': 'AStar',
            'parameters': {'gridSize': 50, 'maxIterations': 1000}
        }
        
        request = PathPlanningRequest(test_request_data)
        print("✅ 测试请求创建成功")
        
        # 同步测试基准算法
        import asyncio
        
        async def run_test():
            print("🔄 开始执行基准算法...")
            response = await baseline_algorithm.calculate_path(request)
            print(f"✅ 基准算法执行完成")
            print(f"   - 成功: {response.success}")
            print(f"   - 路径点数: {len(response.path) if response.path else 0}")
            return response
        
        # 运行测试
        response = asyncio.run(run_test())
        return response.success
            
    except Exception as e:
        print(f"❌ 基准算法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始基准算法调试测试...")
    
    # 测试导入和数据可用性
    improved, baseline = test_imports()
    
    # 测试基准算法本身
    baseline_works = test_baseline_algorithm()
    
    print(f"\n📊 测试结果总结:")
    print(f"   改进算法数据: {'✅ 可用' if improved else '❌ 不可用'}")
    print(f"   基准算法数据: {'✅ 可用' if baseline else '❌ 不可用'}")
    print(f"   基准算法功能: {'✅ 正常' if baseline_works else '❌ 异常'}")
    
    if improved and not baseline and baseline_works:
        print("\n🔍 结论: 基准算法本身工作正常，但在算法对比过程中没有保存结果")
        print("   建议: 检查算法对比API中基准算法的执行和保存逻辑")
    elif improved and not baseline and not baseline_works:
        print("\n🔍 结论: 基准算法本身存在问题")
        print("   建议: 修复基准算法的实现")
    elif not improved:
        print("\n🔍 结论: 没有运行算法对比")
        print("   建议: 先在前端运行算法对比")
