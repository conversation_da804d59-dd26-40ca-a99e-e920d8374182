
路径数据导出报告
================

导出时间: 2025-07-28 19:36:26
时间戳: 20250728_193626

导出文件列表:
- 路径指标对比数据: path_metrics_comparison_20250728_193626.csv (21605 bytes)
- 路径坐标数据: path_coordinates_20250728_193626.csv (63674 bytes)
- 算法元数据: algorithm_metadata_20250728_193626.json (1283 bytes)
- 保护区分析数据: protection_zones_analysis_20250728_193626.csv (22044 bytes)
- 统计分析数据: statistical_analysis_20250728_193626.csv (817 bytes)


数据说明:
========

1. path_metrics_comparison_20250728_193626.csv
   - 包含基准路径和81条改进路径的完整指标对比
   - 四个核心指标: 风险值、碰撞代价、路径长度、转向成本
   - 对应的参考值和标准化指标
   - 权重信息和最终代价

2. path_coordinates_20250728_193626.csv
   - 所有路径的详细坐标数据
   - 每个航点的经纬度、高度信息
   - 航点级别的碰撞代价和风险值

3. algorithm_metadata_20250728_193626.json
   - 算法参数配置
   - 环境信息和性能指标
   - 起终点坐标和飞行参数

4. protection_zones_analysis_20250728_193626.csv
   - 保护区对各路径的影响分析
   - 保护区基本信息和碰撞代价贡献

5. statistical_analysis_20250728_193626.csv
   - 基准路径vs改进路径的统计对比
   - 各指标的均值、标准差、最值
   - 最优路径的详细信息

使用建议:
========
- 使用Excel或Python pandas读取CSV文件进行分析
- JSON文件包含完整的实验配置信息
- 可用于学术论文的数据验证和对比分析
