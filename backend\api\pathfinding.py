"""
路径规划API模块
提供统一的路径规划服务接口，支持多种算法
"""
from flask import Blueprint, request, jsonify
import asyncio
import logging
from typing import Dict, Any

from algorithms import (
    algorithm_manager,
    PathPlanningRequest,
    PathPlanningResponse
)

# 创建蓝图
pathfinding_bp = Blueprint('pathfinding', __name__)

# 配置日志
logger = logging.getLogger(__name__)

@pathfinding_bp.route('/calculate', methods=['POST'])
def calculate_path():
    """
    统一的路径规划接口
    支持多种算法：StraightLine, AStar, RRT
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据不能为空'
            }), 400

        # 创建路径规划请求
        try:
            planning_request = PathPlanningRequest(data)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'请求数据格式错误: {str(e)}'
            }), 400

        # 验证算法是否存在
        algorithm_name = planning_request.algorithm
        if algorithm_name not in [alg['name'] for alg in algorithm_manager.list_algorithms()]:
            return jsonify({
                'success': False,
                'error': f'不支持的算法: {algorithm_name}',
                'available_algorithms': [alg['name'] for alg in algorithm_manager.list_algorithms()]
            }), 400

        # 执行算法（同步包装异步调用）
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            response = loop.run_until_complete(
                algorithm_manager.execute_algorithm(algorithm_name, planning_request)
            )
        finally:
            loop.close()

        # 返回结果
        return jsonify(response.to_dict())

    except Exception as e:
        logger.error(f"路径规划API错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}'
        }), 500

@pathfinding_bp.route('/algorithms', methods=['GET'])
def list_algorithms():
    """
    获取可用算法列表
    """
    try:
        algorithms = algorithm_manager.list_algorithms()

        return jsonify({
            'success': True,
            'algorithms': algorithms,
            'total_count': len(algorithms)
        })

    except Exception as e:
        logger.error(f"获取算法列表错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取算法列表失败: {str(e)}'
        }), 500


@pathfinding_bp.route('/algorithms/<algorithm_name>', methods=['GET'])
def get_algorithm_info(algorithm_name: str):
    """
    获取特定算法的详细信息
    """
    try:
        algorithm_info = algorithm_manager.get_algorithm_info(algorithm_name)

        if algorithm_info is None:
            return jsonify({
                'success': False,
                'error': f'算法 {algorithm_name} 不存在'
            }), 404

        return jsonify({
            'success': True,
            'algorithm_info': algorithm_info
        })

    except Exception as e:
        logger.error(f"获取算法信息错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取算法信息失败: {str(e)}'
        }), 500

@pathfinding_bp.route('/stats', methods=['GET'])
def get_performance_stats():
    """
    获取算法性能统计信息
    """
    try:
        stats = algorithm_manager.get_performance_stats()

        return jsonify({
            'success': True,
            'performance_stats': stats
        })

    except Exception as e:
        logger.error(f"获取性能统计错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取性能统计失败: {str(e)}'
        }), 500


@pathfinding_bp.route('/cache', methods=['DELETE'])
def clear_cache():
    """
    清空算法缓存
    """
    try:
        algorithm_manager.clear_cache()

        return jsonify({
            'success': True,
            'message': '算法缓存已清空'
        })

    except Exception as e:
        logger.error(f"清空缓存错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'清空缓存失败: {str(e)}'
        }), 500


@pathfinding_bp.route('/health', methods=['GET'])
def health_check():
    """
    路径规划服务健康检查
    """
    try:
        algorithms = algorithm_manager.list_algorithms()

        return jsonify({
            'success': True,
            'status': 'healthy',
            'message': '路径规划服务运行正常',
            'available_algorithms': len(algorithms),
            'algorithms': [alg['name'] for alg in algorithms]
        })

    except Exception as e:
        logger.error(f"健康检查错误: {str(e)}")
        return jsonify({
            'success': False,
            'status': 'unhealthy',
            'error': str(e)
        }), 500

# 兼容性接口 - 保持与旧版本的兼容性

@pathfinding_bp.route('/astar', methods=['POST'])
def astar_pathfinding():
    """
    A*算法路径规划接口（兼容性接口）
    """
    try:
        data = request.get_json()

        # 转换旧格式到新格式
        if 'start_point' in data and 'end_point' in data:
            start_point = data['start_point']
            end_point = data['end_point']

            # 构建新格式的请求数据
            new_data = {
                'startPoint': {
                    'lng': start_point[0] if len(start_point) > 0 else 0,
                    'lat': start_point[1] if len(start_point) > 1 else 0,
                    'alt': start_point[2] if len(start_point) > 2 else 100
                },
                'endPoint': {
                    'lng': end_point[0] if len(end_point) > 0 else 0,
                    'lat': end_point[1] if len(end_point) > 1 else 0,
                    'alt': end_point[2] if len(end_point) > 2 else 100
                },
                'algorithm': 'AStar',
                'parameters': data.get('parameters', {})
            }

            # 调用新的统一接口
            planning_request = PathPlanningRequest(new_data)

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                response = loop.run_until_complete(
                    algorithm_manager.execute_algorithm('AStar', planning_request)
                )
            finally:
                loop.close()

            # 转换为旧格式响应
            if response.success:
                return jsonify({
                    'success': True,
                    'algorithm': 'A*',
                    'start_point': start_point,
                    'end_point': end_point,
                    'path': [[p.lng, p.lat, p.alt] for p in response.path],
                    'path_length': len(response.path),
                    'message': 'A*路径规划完成'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': response.error
                }), 500
        else:
            return jsonify({'error': '缺少起点或终点坐标'}), 400

    except Exception as e:
        return jsonify({'error': f'A*路径规划失败: {str(e)}'}), 500


@pathfinding_bp.route('/rrt_star', methods=['POST'])
def rrt_star_pathfinding():
    """
    RRT算法路径规划接口（兼容性接口）
    """
    try:
        data = request.get_json()

        # 转换旧格式到新格式
        if 'start_point' in data and 'end_point' in data:
            start_point = data['start_point']
            end_point = data['end_point']

            # 构建新格式的请求数据
            new_data = {
                'startPoint': {
                    'lng': start_point[0] if len(start_point) > 0 else 0,
                    'lat': start_point[1] if len(start_point) > 1 else 0,
                    'alt': start_point[2] if len(start_point) > 2 else 100
                },
                'endPoint': {
                    'lng': end_point[0] if len(end_point) > 0 else 0,
                    'lat': end_point[1] if len(end_point) > 1 else 0,
                    'alt': end_point[2] if len(end_point) > 2 else 100
                },
                'algorithm': 'RRT',
                'parameters': data.get('parameters', {})
            }

            # 调用新的统一接口
            planning_request = PathPlanningRequest(new_data)

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                response = loop.run_until_complete(
                    algorithm_manager.execute_algorithm('RRT', planning_request)
                )
            finally:
                loop.close()

            # 转换为旧格式响应
            if response.success:
                return jsonify({
                    'success': True,
                    'algorithm': 'RRT*',
                    'start_point': start_point,
                    'end_point': end_point,
                    'path': [[p.lng, p.lat, p.alt] for p in response.path],
                    'path_length': len(response.path),
                    'message': 'RRT*路径规划完成'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': response.error
                }), 500
        else:
            return jsonify({'error': '缺少起点或终点坐标'}), 400

    except Exception as e:
        return jsonify({'error': f'RRT*路径规划失败: {str(e)}'}), 500
