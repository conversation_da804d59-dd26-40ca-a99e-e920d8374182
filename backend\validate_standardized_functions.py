#!/usr/bin/env python3
"""
验证标准化函数的正确性
检查更新后的函数是否符合变量管理规范
"""

import sys
import os
import asyncio
import inspect
from typing import Dict, List, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_standardized_functions():
    """测试标准化后的函数"""
    print("=" * 60)
    print("🧪 验证标准化函数")
    print("=" * 60)
    
    test_results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': [],
        'function_signatures': {},
        'parameter_validations': {}
    }
    
    try:
        from algorithms.improved_cluster_pathfinding import (
            ImprovedClusterBasedPathPlanning,
            CostCalculator,
            InitialPathSetGenerator,
            ClusterManager,
            GradientFieldManager,
            PathSwitchingStrategy,
            ImprovedPathPoint
        )
        from algorithms.data_structures import Point3D
        from algorithm_input_parameters import ALGORITHM_INPUT_PARAMETER_NAMES
        
        print("✅ 成功导入所有模块")
        
        # 1. 测试CostCalculator.calculate_final_cost
        print(f"\n🧮 测试 CostCalculator.calculate_final_cost")
        test_results['total_tests'] += 1
        
        try:
            cost_calculator = CostCalculator({'maxTurnAngle': 90, 'kValue': 5})
            
            # 检查函数签名
            sig = inspect.signature(cost_calculator.calculate_final_cost)
            params = list(sig.parameters.keys())
            expected_params = ['self', 'path_length', 'turning_cost', 'risk_value', 'collision_cost', 
                             'risk_reference', 'collision_reference', 'turning_reference']
            
            test_results['function_signatures']['calculate_final_cost'] = {
                'actual_params': params,
                'expected_params': expected_params,
                'matches': set(params) >= set(expected_params)
            }
            
            # 测试函数调用
            result = cost_calculator.calculate_final_cost(
                path_length=1000.0,
                turning_cost=1.5,
                risk_value=50.0,
                collision_cost=20.0
            )
            
            if isinstance(result, float) and result > 0:
                print(f"   ✅ 函数调用成功，返回值: {result:.6f}")
                test_results['passed_tests'] += 1
            else:
                print(f"   ❌ 函数返回值异常: {result}")
                test_results['failed_tests'].append('calculate_final_cost: 返回值异常')
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            test_results['failed_tests'].append(f'calculate_final_cost: {e}')
        
        # 2. 测试ImprovedClusterBasedPathPlanning.calculate_path
        print(f"\n🛤️ 测试 ImprovedClusterBasedPathPlanning.calculate_path")
        test_results['total_tests'] += 1
        
        try:
            algorithm = ImprovedClusterBasedPathPlanning()
            
            # 检查函数签名
            sig = inspect.signature(algorithm.calculate_path)
            params = list(sig.parameters.keys())
            expected_params = ['self', 'start_point', 'end_point', 'flight_height', 'safety_distance',
                             'max_turn_angle', 'buildings', 'protection_zones', 'k_value', 'enable_path_switching']
            
            test_results['function_signatures']['calculate_path'] = {
                'actual_params': params,
                'expected_params': expected_params,
                'matches': set(params) >= set(expected_params)
            }
            
            # 测试参数验证
            try:
                await algorithm.calculate_path(
                    start_point={'x': 0, 'y': 0, 'z': 100},
                    end_point={'x': 1000, 'y': 800, 'z': 120},
                    flight_height=100
                )
                print(f"   ✅ 基本参数验证通过")
            except Exception as e:
                print(f"   ⚠️ 参数验证异常: {e}")
            
            # 测试无效参数
            try:
                await algorithm.calculate_path(
                    start_point={'x': 0, 'y': 0, 'z': 100},
                    end_point={'x': 1000, 'y': 800, 'z': 120},
                    flight_height=-100  # 无效值
                )
                print(f"   ❌ 应该拒绝无效参数")
                test_results['failed_tests'].append('calculate_path: 未正确验证无效参数')
            except ValueError:
                print(f"   ✅ 正确拒绝无效参数")
                test_results['passed_tests'] += 1
            except Exception as e:
                print(f"   ⚠️ 参数验证异常: {e}")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            test_results['failed_tests'].append(f'calculate_path: {e}')
        
        # 3. 测试InitialPathSetGenerator.generate_initial_path_set
        print(f"\n🔄 测试 InitialPathSetGenerator.generate_initial_path_set")
        test_results['total_tests'] += 1
        
        try:
            from algorithms.astar import AStarAlgorithm
            astar = AStarAlgorithm()
            generator = InitialPathSetGenerator(astar, {'flightHeight': 100})
            
            # 检查函数签名
            sig = inspect.signature(generator.generate_initial_path_set)
            params = list(sig.parameters.keys())
            expected_params = ['self', 'start_point', 'end_point', 'buildings', 'flight_height']
            
            test_results['function_signatures']['generate_initial_path_set'] = {
                'actual_params': params,
                'expected_params': expected_params,
                'matches': set(params) >= set(expected_params)
            }
            
            # 测试函数调用
            start_point = Point3D(lng=0, lat=0, alt=100, x=0, y=0, z=100)
            end_point = Point3D(lng=1000, lat=800, alt=120, x=1000, y=800, z=120)
            
            paths = await generator.generate_initial_path_set(start_point, end_point)
            
            if isinstance(paths, list):
                print(f"   ✅ 函数调用成功，生成路径数: {len(paths)}")
                test_results['passed_tests'] += 1
            else:
                print(f"   ❌ 函数返回值类型错误")
                test_results['failed_tests'].append('generate_initial_path_set: 返回值类型错误')
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            test_results['failed_tests'].append(f'generate_initial_path_set: {e}')
        
        # 4. 测试ClusterManager.assign_paths_to_clusters
        print(f"\n🗂️ 测试 ClusterManager.assign_paths_to_clusters")
        test_results['total_tests'] += 1
        
        try:
            cluster_manager = ClusterManager()
            
            # 检查函数签名
            sig = inspect.signature(cluster_manager.assign_paths_to_clusters)
            params = list(sig.parameters.keys())
            expected_params = ['self', 'initial_paths']
            
            test_results['function_signatures']['assign_paths_to_clusters'] = {
                'actual_params': params,
                'expected_params': expected_params,
                'matches': set(params) >= set(expected_params)
            }
            
            # 测试参数验证
            try:
                cluster_manager.assign_paths_to_clusters([])  # 空列表应该被拒绝
                print(f"   ❌ 应该拒绝空路径列表")
                test_results['failed_tests'].append('assign_paths_to_clusters: 未正确验证空列表')
            except ValueError:
                print(f"   ✅ 正确拒绝空路径列表")
                test_results['passed_tests'] += 1
            except Exception as e:
                print(f"   ⚠️ 参数验证异常: {e}")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            test_results['failed_tests'].append(f'assign_paths_to_clusters: {e}')
        
        # 5. 测试GradientFieldManager.calculate_gradient_field
        print(f"\n⚡ 测试 GradientFieldManager.calculate_gradient_field")
        test_results['total_tests'] += 1
        
        try:
            cost_calculator = CostCalculator({'maxTurnAngle': 90, 'kValue': 5})
            gradient_manager = GradientFieldManager(cost_calculator)
            
            # 检查函数签名
            sig = inspect.signature(gradient_manager.calculate_gradient_field)
            params = list(sig.parameters.keys())
            expected_params = ['self', 'waypoint', 'detected_objects']
            
            test_results['function_signatures']['calculate_gradient_field'] = {
                'actual_params': params,
                'expected_params': expected_params,
                'matches': set(params) >= set(expected_params)
            }
            
            # 测试函数调用
            waypoint = ImprovedPathPoint(x=100, y=100, z=50, waypoint_index=1)
            gradient_field = gradient_manager.calculate_gradient_field(waypoint)
            
            if hasattr(gradient_field, 'gradient_magnitude'):
                print(f"   ✅ 函数调用成功，梯度强度: {gradient_field.gradient_magnitude:.4f}")
                test_results['passed_tests'] += 1
            else:
                print(f"   ❌ 返回值缺少必要属性")
                test_results['failed_tests'].append('calculate_gradient_field: 返回值格式错误')
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            test_results['failed_tests'].append(f'calculate_gradient_field: {e}')
        
        # 6. 测试PathSwitchingStrategy.should_switch_path
        print(f"\n🔄 测试 PathSwitchingStrategy.should_switch_path")
        test_results['total_tests'] += 1
        
        try:
            from algorithms.astar import AStarAlgorithm
            astar = AStarAlgorithm()
            cluster_manager = ClusterManager()
            gradient_manager = GradientFieldManager(cost_calculator)
            switching_strategy = PathSwitchingStrategy(cluster_manager, gradient_manager, astar, cost_calculator)
            
            # 检查函数签名
            sig = inspect.signature(switching_strategy.should_switch_path)
            params = list(sig.parameters.keys())
            expected_params = ['self', 'current_path', 'current_waypoint_index', 'anomaly_threshold', 'consecutive_check_count']
            
            test_results['function_signatures']['should_switch_path'] = {
                'actual_params': params,
                'expected_params': expected_params,
                'matches': set(params) >= set(expected_params)
            }
            
            # 测试函数调用
            test_path = [
                ImprovedPathPoint(x=0, y=0, z=100, waypoint_index=0),
                ImprovedPathPoint(x=50, y=50, z=110, waypoint_index=1),
                ImprovedPathPoint(x=100, y=100, z=120, waypoint_index=2),
                ImprovedPathPoint(x=150, y=150, z=130, waypoint_index=3),
                ImprovedPathPoint(x=200, y=200, z=140, waypoint_index=4),
                ImprovedPathPoint(x=250, y=250, z=150, waypoint_index=5)
            ]
            
            decision = switching_strategy.should_switch_path(test_path, 5)
            
            if isinstance(decision, dict) and 'should_switch' in decision:
                print(f"   ✅ 函数调用成功，换路决策: {decision['should_switch']}")
                test_results['passed_tests'] += 1
            else:
                print(f"   ❌ 返回值格式错误")
                test_results['failed_tests'].append('should_switch_path: 返回值格式错误')
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            test_results['failed_tests'].append(f'should_switch_path: {e}')
        
        # 输出测试总结
        print(f"\n" + "=" * 60)
        print(f"📊 测试结果总结")
        print(f"=" * 60)
        
        success_rate = (test_results['passed_tests'] / test_results['total_tests'] * 100 
                       if test_results['total_tests'] > 0 else 0)
        
        print(f"总测试数: {test_results['total_tests']}")
        print(f"通过测试: {test_results['passed_tests']}")
        print(f"失败测试: {len(test_results['failed_tests'])}")
        print(f"成功率: {success_rate:.1f}%")
        
        if test_results['failed_tests']:
            print(f"\n❌ 失败的测试:")
            for failure in test_results['failed_tests']:
                print(f"   - {failure}")
        
        print(f"\n📋 函数签名检查:")
        for func_name, sig_info in test_results['function_signatures'].items():
            status = "✅" if sig_info['matches'] else "❌"
            print(f"   {status} {func_name}: 参数匹配 {sig_info['matches']}")
            if not sig_info['matches']:
                missing = set(sig_info['expected_params']) - set(sig_info['actual_params'])
                extra = set(sig_info['actual_params']) - set(sig_info['expected_params'])
                if missing:
                    print(f"      缺少参数: {missing}")
                if extra:
                    print(f"      额外参数: {extra}")
        
        return success_rate >= 80  # 80%以上通过率认为成功
        
    except Exception as e:
        print(f"❌ 验证过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始验证标准化函数...")
    
    success = asyncio.run(test_standardized_functions())
    
    if success:
        print(f"\n🎉 函数标准化验证通过!")
    else:
        print(f"\n⚠️ 函数标准化需要进一步完善")
    
    sys.exit(0 if success else 1)
