"""
路径数据导出API
提供81条路径详细数据的导出功能，用于调参分析
"""

from flask import Blueprint, request, jsonify
import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import math

# 创建蓝图
path_export_bp = Blueprint('path_export', __name__)

# 全局变量存储最新的路径数据
latest_paths_data = []

def _find_all_clusters(flight_direction: int, height_layer: int) -> dict:
    """
    根据飞行方向和高度层坐标，找到所有相关的簇

    Args:
        flight_direction: 飞行方向编号 (1-9)
        height_layer: 高度层编号 (1-9)

    Returns:
        包含所有相关簇ID和类型的字典
    """
    x, y = flight_direction, height_layer

    # 9个3×3簇的位置范围定义（与算法中保持一致）
    cluster_3x3_configs = [
        ((1, 3), (1, 3), "3x3_cluster_1"),    # 簇1: (1,1)-(3,3)
        ((4, 6), (1, 3), "3x3_cluster_2"),    # 簇2: (4,1)-(6,3)
        ((7, 9), (1, 3), "3x3_cluster_3"),    # 簇3: (7,1)-(9,3)
        ((1, 3), (4, 6), "3x3_cluster_4"),    # 簇4: (1,4)-(3,6)
        ((4, 6), (4, 6), "3x3_cluster_5"),    # 簇5: (4,4)-(6,6)
        ((7, 9), (4, 6), "3x3_cluster_6"),    # 簇6: (7,4)-(9,6)
        ((1, 3), (7, 9), "3x3_cluster_7"),    # 簇7: (1,7)-(3,9)
        ((4, 6), (7, 9), "3x3_cluster_8"),    # 簇8: (4,7)-(6,9)
        ((7, 9), (7, 9), "3x3_cluster_9")     # 簇9: (7,7)-(9,9)
    ]

    # 4个4×4簇的位置范围定义
    cluster_4x4_configs = [
        ((2, 5), (2, 5), "4x4_cluster_1"),    # 簇10: (2,2)-(5,5)
        ((5, 8), (2, 5), "4x4_cluster_2"),    # 簇11: (5,2)-(8,5)
        ((2, 5), (5, 8), "4x4_cluster_3"),    # 簇12: (2,5)-(5,8)
        ((5, 8), (5, 8), "4x4_cluster_4")     # 簇13: (5,5)-(8,8)
    ]

    # 🔧 修复：找到所有包含该位置的簇
    cluster_3x3_ids = []
    cluster_4x4_ids = []

    # 检查所有3×3簇
    for (x_min, x_max), (y_min, y_max), cluster_id in cluster_3x3_configs:
        if x_min <= x <= x_max and y_min <= y <= y_max:
            cluster_3x3_ids.append(cluster_id)

    # 检查所有4×4簇
    for (x_min, x_max), (y_min, y_max), cluster_id in cluster_4x4_configs:
        if x_min <= x <= x_max and y_min <= y <= y_max:
            cluster_4x4_ids.append(cluster_id)

    # 🔧 返回所有相关的簇ID
    all_cluster_ids = cluster_3x3_ids + cluster_4x4_ids
    all_cluster_types = ['3x3'] * len(cluster_3x3_ids) + ['4x4'] * len(cluster_4x4_ids)

    return {
        'cluster_3x3_ids': cluster_3x3_ids,
        'cluster_4x4_ids': cluster_4x4_ids,
        'all_ids': all_cluster_ids,
        'all_types': all_cluster_types,
        'id': '; '.join(all_cluster_ids) if all_cluster_ids else 'unknown_cluster',
        'type': '; '.join(all_cluster_types) if all_cluster_types else '3x3'
    }

def get_algorithm_manager():
    """动态获取算法管理器实例"""
    try:
        # 尝试从Flask应用上下文获取算法管理器
        from flask import current_app
        if hasattr(current_app, 'algorithm_manager'):
            return current_app.algorithm_manager

        # 尝试从全局变量获取
        import sys
        for module_name, module in sys.modules.items():
            if hasattr(module, 'algorithm_manager'):
                return module.algorithm_manager

        # 最后尝试创建新实例
        from algorithms.manager import AlgorithmManager
        return AlgorithmManager()
    except Exception as e:
        print(f"⚠️ 获取算法管理器失败: {e}")
        return None

def _calculate_protection_zone_info(path_data, algorithm_manager):
    """计算路径经过的保护区信息"""
    zones_info = {
        'zones_passed_count': 0,
        'zones_passed_names': '',
        'total_zone_distance': 0.0,
        'max_zone_cost': 0.0
    }

    try:
        # 从算法管理器获取保护区管理器
        protection_zone_manager = None
        if algorithm_manager:
            for name, algo in algorithm_manager.algorithms.items():
                if hasattr(algo, 'protection_zone_manager') and algo.protection_zone_manager:
                    protection_zone_manager = algo.protection_zone_manager
                    break

        if not protection_zone_manager:
            return zones_info

        # 构建路径点列表
        path_points = []
        if 'waypoints' in path_data and path_data['waypoints']:
            for wp in path_data['waypoints']:
                if hasattr(wp, 'lng') and hasattr(wp, 'lat'):
                    path_points.append((wp.lng, wp.lat))

        # 如果没有航点，使用起止点
        if not path_points:
            start_lng = path_data.get('start_lng', 0.0)
            start_lat = path_data.get('start_lat', 0.0)
            end_lng = path_data.get('end_lng', 0.0)
            end_lat = path_data.get('end_lat', 0.0)

            if start_lng != 0.0 and start_lat != 0.0:
                path_points.append((start_lng, start_lat))
            if end_lng != 0.0 and end_lat != 0.0:
                path_points.append((end_lng, end_lat))

        if path_points:
            # 获取相关保护区
            relevant_zones = protection_zone_manager.get_zones_for_path(path_points)

            if relevant_zones:
                zones_info['zones_passed_count'] = len(relevant_zones)
                zones_info['zones_passed_names'] = ', '.join([zone.name for zone in relevant_zones])
                zones_info['max_zone_cost'] = max([zone.collision_cost_factor for zone in relevant_zones])

                # 估算在保护区内的距离（简化计算）
                total_distance = 0.0
                for zone in relevant_zones:
                    zone_distance = 0.0
                    for lng, lat in path_points:
                        if zone.contains_point(lng, lat):
                            zone_distance += 50.0  # 假设每个点间距50米
                    total_distance += zone_distance

                zones_info['total_zone_distance'] = total_distance

    except Exception as e:
        print(f"⚠️ 计算保护区信息失败: {e}")

    return zones_info

def get_baseline_path_data(algorithm_manager):
    """获取基准算法路径数据"""
    try:
        print("🔍 开始获取基准算法路径数据...")

        # 查找A*基准算法
        baseline_algorithm = None
        for name, algo in algorithm_manager.algorithms.items():
            if 'astar' in name.lower() or 'a*' in name.lower():
                baseline_algorithm = algo
                break

        if not baseline_algorithm:
            print("⚠️ 未找到A*基准算法")
            return None

        # 🔧 修复：确保基准路径数据来自真正的算法计算结果
        try:
            # 首先尝试从算法对比API获取最新的真实基准算法结果
            from algorithm_comparison_api import get_latest_baseline_result
            baseline_result = get_latest_baseline_result()

            if baseline_result:
                print(f"✅ 从算法对比API获取到真实基准算法结果")
                print(f"   路径长度: {baseline_result.get('path_length', 0)}")
                print(f"   最终代价: {baseline_result.get('final_cost', 0)}")
            else:
                print("❌ 没有可用的基准算法结果！")
                print("❌ 请先运行算法对比以生成基准路径数据")
                return None

            # 提取基准路径数据
            baseline_path_info = {
                'path_id': 0,           # 基准路径使用ID=0（区别于1-81的改进算法路径）
                'path_index': 0,        # 基准路径使用索引=0
                'flight_direction': 'optimal',  # 基准路径为最优方向
                'height_layer': 'single',       # 基准路径为单一高度
                'cluster_id': 'BASELINE',
                'cluster_type': 'A*基准算法',

                # 坐标信息 - 🔧 修复：使用算法管理器中的实际起止坐标
                'waypoints_count': len(baseline_result.get('path', [])),
                'start_lng': 0.0,  # 将在下面从算法管理器获取
                'start_lat': 0.0,
                'start_alt': 0.0,
                'end_lng': 0.0,
                'end_lat': 0.0,
                'end_alt': 0.0,
                'straight_distance': baseline_result.get('path_length', 1428.33),  # 🔧 修复：使用实际路径长度

                # 核心指标
                'path_length': baseline_result.get('path_length', 0.0),
                'path_efficiency': baseline_result.get('path_length', 1500.0) / 6394.15,
                'turning_cost': baseline_result.get('turning_cost', 0.0),
                'risk_value': baseline_result.get('risk_value', 0.0),
                'collision_cost': baseline_result.get('collision_cost', 0.0),
                'final_cost': baseline_result.get('final_cost', 0.0),

                # 保护区信息（将在后面计算）
                'zones_passed_count': 0,
                'zones_passed_names': '',
                'total_zone_distance': 0.0,
                'max_zone_cost': 0.0,

                # 🔧 修复：权重信息（基准算法使用固定权重）
                'weight_alpha': 0.0,    # 基准算法不考虑风险
                'weight_beta': 0.0,     # 基准算法不考虑碰撞
                'weight_gamma': 1.0,    # 基准算法主要考虑路径长度
                'weight_delta': 0.0,    # 基准算法不考虑转向

                # 计算参数
                'manhattan_distance': 3.0 * (abs(139.7016 - 139.7673) + abs(35.6598 - 35.6812) + 0),
                'risk_density': 0.0,
                'dynamic_weights': {
                    'alpha': 0.0,
                    'beta': 0.0,
                    'gamma': 0.0,
                    'delta': 0.0
                },
                'reference_values': {
                    'risk': 100.0,
                    'collision': 50.0,
                    'turning': 30.0
                },

                # 排序信息
                'ranking': 'BASELINE',
                'cluster_ranking': 'BASELINE',

                # 代价分解
                'risk_term': 0.0,
                'collision_term': 0.0,
                'length_term': baseline_result.get('path_length', 1500.0) / 1000.0,  # 转换为km
                'orient_term': 0.0,

                # 代价占比
                'risk_percent': 0.0,
                'collision_percent': 0.0,
                'length_percent': 100.0,  # A*主要是长度代价
                'turning_percent': 0.0,

                # 排名信息
                'is_selected': 0,  # 基准路径不是选中路径

                # 时间信息
                'execution_time': baseline_result.get('execution_time', 0.0),
                'generation_time': datetime.now().isoformat()
            }

            # 🔧 修复：从算法管理器获取实际的起止坐标，确保与改进算法一致
            actual_start_lng, actual_start_lat, actual_start_alt = 0.0, 0.0, 0.0
            actual_end_lng, actual_end_lat, actual_end_alt = 0.0, 0.0, 0.0

            if algorithm_manager:
                for name, algo in algorithm_manager.algorithms.items():
                    if hasattr(algo, 'start_point') and algo.start_point:
                        actual_start_lng = algo.start_point.lng
                        actual_start_lat = algo.start_point.lat
                        actual_start_alt = algo.start_point.alt
                        break

                for name, algo in algorithm_manager.algorithms.items():
                    if hasattr(algo, 'end_point') and algo.end_point:
                        actual_end_lng = algo.end_point.lng
                        actual_end_lat = algo.end_point.lat
                        actual_end_alt = algo.end_point.alt
                        break

            # 更新基准路径的坐标信息
            baseline_path_info['start_lng'] = actual_start_lng if actual_start_lng != 0.0 else 139.7673
            baseline_path_info['start_lat'] = actual_start_lat if actual_start_lat != 0.0 else 35.6812
            baseline_path_info['start_alt'] = actual_start_alt if actual_start_alt != 0.0 else 100.0
            baseline_path_info['end_lng'] = actual_end_lng if actual_end_lng != 0.0 else 139.7016
            baseline_path_info['end_lat'] = actual_end_lat if actual_end_lat != 0.0 else 35.6598
            baseline_path_info['end_alt'] = actual_end_alt if actual_end_alt != 0.0 else 100.0

            # 提取起点和终点信息（用于兼容性）
            path = baseline_result.get('path', [])
            if path:
                start_point = path[0]
                end_point = path[-1]

                baseline_path_info['start_point'] = {
                    'lng': baseline_path_info['start_lng'],
                    'lat': baseline_path_info['start_lat'],
                    'alt': baseline_path_info['start_alt']
                }

                baseline_path_info['end_point'] = {
                    'lng': baseline_path_info['end_lng'],
                    'lat': baseline_path_info['end_lat'],
                    'alt': baseline_path_info['end_alt']
                }

                # 计算曼哈顿距离（公式2）
                if baseline_path_info['start_point'] and baseline_path_info['end_point']:
                    dx = abs(baseline_path_info['end_point']['lng'] - baseline_path_info['start_point']['lng'])
                    dy = abs(baseline_path_info['end_point']['lat'] - baseline_path_info['start_point']['lat'])
                    dz = abs(baseline_path_info['end_point']['alt'] - baseline_path_info['start_point']['alt'])
                    baseline_path_info['manhattan_distance'] = 3.0 * (dx + dy + dz)

            # 计算风险密度（公式8）
            if baseline_path_info['path_length'] > 0:
                baseline_path_info['risk_density'] = baseline_path_info['risk_value'] / baseline_path_info['path_length']

            # 计算动态权重（公式15）
            if baseline_path_info['risk_density'] > 0:
                k = 5  # 控制指数变化速率的参数
                exp_neg_k_rd = math.exp(-k * baseline_path_info['risk_density'])

                baseline_path_info['dynamic_weights'] = {
                    'alpha': 0.6 * (1 - exp_neg_k_rd),  # 风险权重
                    'beta': 0.3 * (1 - exp_neg_k_rd),   # 碰撞权重
                    'gamma': 0.7 * exp_neg_k_rd + 0.1,  # 长度权重
                    'delta': 0.2 * exp_neg_k_rd         # 转向权重
                }

            # 🔧 修复：计算基准路径的保护区信息
            baseline_zones_info = _calculate_protection_zone_info(baseline_path_info, algorithm_manager)
            baseline_path_info.update(baseline_zones_info)

            print("✅ 成功获取基准算法路径数据")
            print(f"🛡️ 基准路径保护区信息: {baseline_zones_info['zones_passed_count']}个保护区")
            return baseline_path_info

        except ImportError:
            print("⚠️ 无法导入算法对比API，使用占位符数据")
            return create_placeholder_baseline_data()

    except Exception as e:
        print(f"❌ 获取基准算法路径数据失败: {e}")
        return create_placeholder_baseline_data()

def create_placeholder_baseline_data():
    """创建基准算法占位符数据"""
    return {
        'path_id': 0,
        'flight_direction': 'BASELINE',
        'height_layer': 'BASELINE',
        'cluster_id': 'BASELINE',
        'cluster_type': 'A*基准算法(占位符)',
        'path_length': 0.0,
        'turning_cost': 0.0,
        'risk_value': 0.0,
        'collision_cost': 0.0,
        'final_cost': 0.0,
        'waypoint_count': 0,
        'start_point': {'lng': 0.0, 'lat': 0.0, 'alt': 120.0},
        'end_point': {'lng': 0.0, 'lat': 0.0, 'alt': 120.0},
        'manhattan_distance': 0.0,
        'risk_density': 0.0,
        'dynamic_weights': {'alpha': 0.0, 'beta': 0.0, 'gamma': 0.0, 'delta': 0.0},
        'reference_values': {'risk': 100.0, 'collision': 50.0, 'turning': 30.0},
        'ranking': 'BASELINE',
        'cluster_ranking': 'BASELINE',
        'execution_time': 0.0,
        'generation_time': datetime.now().isoformat()
    }

@path_export_bp.route('/export_all_paths_data', methods=['POST'])
def export_all_paths_data():
    """
    导出81条路径的详细数据
    返回包含所有路径详细信息的JSON数据
    """
    try:
        print("🔄 开始导出81条路径数据...")
        
        # 获取请求数据
        request_data = request.get_json() or {}
        
        # 获取算法管理器
        algorithm_manager = get_algorithm_manager()
        if not algorithm_manager:
            print("❌ 算法管理器获取失败")
            return jsonify({
                'success': False,
                'error': '算法管理器未初始化，请先运行算法对比'
            }), 400
        
        # 获取改进算法实例
        improved_algorithm = None
        print(f"🔍 可用算法: {list(algorithm_manager.algorithms.keys())}")

        for name, algo in algorithm_manager.algorithms.items():
            print(f"🔍 检查算法: {name}")
            if 'improved' in name.lower() or 'cluster' in name.lower():
                improved_algorithm = algo
                print(f"✅ 找到改进算法: {name}")
                break

        if not improved_algorithm:
            print("❌ 未找到改进分簇算法实例")
            return jsonify({
                'success': False,
                'error': '未找到改进分簇算法实例，请先运行算法对比'
            }), 400
        
        # 检查是否有初始路径集数据
        if not hasattr(improved_algorithm, 'initial_path_set'):
            print("❌ 改进算法没有initial_path_set属性")
            return jsonify({
                'success': False,
                'error': '改进算法没有初始路径集数据，请先运行算法对比'
            }), 400

        if not improved_algorithm.initial_path_set:
            print("❌ 改进算法的initial_path_set为空，尝试从算法对比API获取数据")

            # 尝试从算法对比API获取最新的改进算法结果
            try:
                from algorithm_comparison_api import get_latest_improved_result
                improved_result = get_latest_improved_result()

                if improved_result and improved_result.get('initial_path_set'):
                    print("✅ 从算法对比API获取到初始路径集数据")
                    # 使用算法对比API中保存的数据
                    paths_data = []

                    # 添加基准路径数据
                    baseline_path_data = get_baseline_path_data(algorithm_manager)
                    if baseline_path_data:
                        paths_data.append(baseline_path_data)

                    # 添加改进算法的路径数据
                    # 🔧 修复：先按最终代价排序，计算正确的排名
                    initial_path_set = improved_result['initial_path_set']
                    sorted_by_cost = sorted(initial_path_set, key=lambda p: p.get('final_cost', float('inf')))

                    # 🔧 添加调试信息：验证路径数据差异性
                    print(f"📊 从算法对比API获取到 {len(initial_path_set)} 条路径数据")
                    if len(initial_path_set) >= 3:
                        print("🔍 验证前3条路径的关键属性:")
                        for i in range(3):
                            path_data = initial_path_set[i]
                            flight_dir = path_data.get('flight_direction', 'N/A')
                            height_layer = path_data.get('height_layer', 'N/A')
                            path_id = path_data.get('path_id', 'N/A')
                            final_cost = path_data.get('final_cost', 'N/A')
                            print(f"   路径{i}: ID={path_id}, 方向={flight_dir}, 高度={height_layer}, 代价={final_cost}")

                    # 检查是否所有路径的flight_direction都相同
                    flight_directions = [path_data.get('flight_direction') for path_data in initial_path_set[:10]]
                    height_layers = [path_data.get('height_layer') for path_data in initial_path_set[:10]]
                    print(f"🔍 前10条路径的方向分布: {flight_directions}")
                    print(f"🔍 前10条路径的高度分布: {height_layers}")

                    if len(set(flight_directions)) == 1:
                        print("⚠️ 警告：前10条路径的flight_direction都相同！这可能是问题所在")
                    if len(set(height_layers)) == 1:
                        print("⚠️ 警告：前10条路径的height_layer都相同！这可能是问题所在")

                    # 🔧 修复：获取选中路径的信息，优先使用最终代价最低的路径
                    selected_path_id = None
                    min_final_cost = float('inf')

                    # 找到最终代价最低的路径作为选中路径
                    for i, path_data in enumerate(initial_path_set):
                        final_cost = path_data.get('final_cost', float('inf'))
                        if final_cost < min_final_cost:
                            min_final_cost = final_cost
                            selected_path_id = i + 1  # 路径ID从1开始（按生成顺序）

                    print(f"🎯 选中路径ID: {selected_path_id}, 最终代价: {min_final_cost:.6f}")

                    for i, path_data in enumerate(initial_path_set):
                        # 补充计算缺失的字段
                        enhanced_path_data = dict(path_data)  # 复制原数据

                        # 🔧 关键修复：路径索引编号 vs 排序序号
                        path_index = i + 1  # 路径索引编号：按生成顺序（1-81）

                        # 🔧 计算排序序号：按最终代价排序后的排名
                        rank_by_cost = 1
                        for j, sorted_path in enumerate(sorted_by_cost):
                            if sorted_path == path_data:
                                rank_by_cost = j + 1
                                break

                        # 🔧 修复：确保路径ID等于路径索引（按论文原文要求从1开始）
                        enhanced_path_data['path_id'] = path_index  # 路径索引编号：1-81
                        enhanced_path_data['path_index'] = path_index  # 路径索引编号：1-81
                        enhanced_path_data['rank'] = rank_by_cost  # 🔧 排序序号：按最终代价排序（1-81）
                        enhanced_path_data['original_path_id'] = path_data.get('original_path_id', path_data.get('path_id', i))  # 保留原始路径ID

                        # 🔧 确保包含簇信息（用于参数调优分析）
                        if 'cluster_id' not in enhanced_path_data:
                            # 🔧 修复：根据飞行方向和高度层计算正确的簇ID
                            flight_direction = enhanced_path_data.get('flight_direction', (i % 9) + 1)
                            height_layer = enhanced_path_data.get('height_layer', (i // 9) + 1)

                            # 🔧 修复：根据位置坐标确定所有相关的簇
                            all_clusters = _find_all_clusters(flight_direction, height_layer)
                            enhanced_path_data['cluster_id'] = all_clusters['id']  # 所有簇ID，用分号分隔
                            enhanced_path_data['cluster_type'] = all_clusters['type']  # 所有簇类型，用分号分隔
                            enhanced_path_data['cluster_3x3_ids'] = '; '.join(all_clusters['cluster_3x3_ids'])  # 3x3簇ID
                            enhanced_path_data['cluster_4x4_ids'] = '; '.join(all_clusters['cluster_4x4_ids'])  # 4x4簇ID

                        if 'cluster_type' not in enhanced_path_data:
                            enhanced_path_data['cluster_type'] = '3x3' if enhanced_path_data.get('cluster_id', '').startswith('3x3') else '4x4'

                        # 🔧 确保包含飞行方向和高度层（81条路径的核心参数）
                        # 按论文原文：9个飞行方向 × 9个高度层 = 81条路径
                        if 'flight_direction' not in enhanced_path_data or enhanced_path_data['flight_direction'] == 0:
                            enhanced_path_data['flight_direction'] = (i % 9) + 1  # 1-9（飞行方向编号）
                        if 'height_layer' not in enhanced_path_data or enhanced_path_data['height_layer'] == 0:
                            enhanced_path_data['height_layer'] = (i // 9) + 1     # 1-9（高度层编号）

                        # 🔧 修复起点终点坐标缺失问题
                        if enhanced_path_data.get('start_lng', 0.0) == 0.0 or enhanced_path_data.get('start_lat', 0.0) == 0.0:
                            # 从算法管理器获取实际的起点终点
                            if algorithm_manager and hasattr(algorithm_manager, 'algorithms'):
                                for name, algo in algorithm_manager.algorithms.items():
                                    if 'improved' in name.lower() and hasattr(algo, 'start_point') and algo.start_point:
                                        enhanced_path_data['start_lng'] = algo.start_point.lng
                                        enhanced_path_data['start_lat'] = algo.start_point.lat
                                        enhanced_path_data['start_alt'] = algo.start_point.alt
                                        break

                            # 如果还是没有，使用默认值（东京站坐标）
                            if enhanced_path_data.get('start_lng', 0.0) == 0.0:
                                enhanced_path_data['start_lng'] = 139.7673
                                enhanced_path_data['start_lat'] = 35.6812
                                enhanced_path_data['start_alt'] = 100.0

                        if enhanced_path_data.get('end_lng', 0.0) == 0.0 or enhanced_path_data.get('end_lat', 0.0) == 0.0:
                            # 从算法管理器获取实际的终点
                            if algorithm_manager and hasattr(algorithm_manager, 'algorithms'):
                                for name, algo in algorithm_manager.algorithms.items():
                                    if 'improved' in name.lower() and hasattr(algo, 'end_point') and algo.end_point:
                                        enhanced_path_data['end_lng'] = algo.end_point.lng
                                        enhanced_path_data['end_lat'] = algo.end_point.lat
                                        enhanced_path_data['end_alt'] = algo.end_point.alt
                                        break

                            # 如果还是没有，使用默认值（涩谷站坐标）
                            if enhanced_path_data.get('end_lng', 0.0) == 0.0:
                                enhanced_path_data['end_lng'] = 139.7016
                                enhanced_path_data['end_lat'] = 35.6598
                                enhanced_path_data['end_alt'] = 100.0

                        # 计算曼哈顿距离（公式2）
                        if enhanced_path_data.get('start_lng') and enhanced_path_data.get('end_lng'):
                            dx = abs(enhanced_path_data['end_lng'] - enhanced_path_data['start_lng'])
                            dy = abs(enhanced_path_data['end_lat'] - enhanced_path_data['start_lat'])
                            dz = abs(enhanced_path_data['end_alt'] - enhanced_path_data['start_alt'])
                            enhanced_path_data['manhattan_distance'] = 3.0 * (dx + dy + dz)
                        else:
                            enhanced_path_data['manhattan_distance'] = 0.0

                        # 计算风险密度（公式8）
                        if enhanced_path_data.get('path_length', 0) > 0:
                            enhanced_path_data['risk_density'] = enhanced_path_data.get('risk_value', 0) / enhanced_path_data['path_length']
                        else:
                            enhanced_path_data['risk_density'] = 0.0

                        # 计算动态权重（公式15）
                        if enhanced_path_data['risk_density'] > 0:
                            k = 5  # 控制指数变化速率的参数
                            exp_neg_k_rd = math.exp(-k * enhanced_path_data['risk_density'])

                            enhanced_path_data['dynamic_weights'] = {
                                'alpha': 0.6 * (1 - exp_neg_k_rd),  # 风险权重
                                'beta': 0.3 * (1 - exp_neg_k_rd),   # 碰撞权重
                                'gamma': 0.7 * exp_neg_k_rd + 0.1,  # 长度权重
                                'delta': 0.2 * exp_neg_k_rd         # 转向权重
                            }
                        else:
                            enhanced_path_data['dynamic_weights'] = {
                                'alpha': 0.0,
                                'beta': 0.0,
                                'gamma': 0.1,  # 最小值
                                'delta': 0.0
                            }

                        # 🔧 修复：计算保护区信息（从算法管理器获取）
                        zones_info = _calculate_protection_zone_info(enhanced_path_data, algorithm_manager)
                        enhanced_path_data.update(zones_info)

                        # 🔧 确定是否为选中路径
                        is_selected_path = 0
                        if selected_path_id is not None:
                            # 检查路径ID是否匹配选中路径
                            if path_index == selected_path_id:
                                is_selected_path = 1
                        elif rank_by_cost == 1:
                            # 如果没有明确的选中路径，最优路径（排名第1）为选中路径
                            is_selected_path = 1

                        # 🔧 确保包含所有必要的字段（匹配前端CSV导出格式）
                        # 如果缺少字段，使用默认值
                        field_defaults = {
                            'risk_term': 0.0,
                            'collision_term': 0.0,
                            'length_term': 0.0,
                            'orient_term': 0.0,
                            'risk_percent': 0.0,
                            'collision_percent': 0.0,
                            'length_percent': 0.0,
                            'turning_percent': 0.0,
                            'is_selected': is_selected_path  # 🔧 修复：正确设置选中路径标记
                        }

                        for field, default_value in field_defaults.items():
                            if field not in enhanced_path_data:
                                enhanced_path_data[field] = default_value

                        paths_data.append(enhanced_path_data)

                    return jsonify({
                        'success': True,
                        'paths_data': paths_data,
                        'total_paths': len(paths_data),
                        'export_time': datetime.now().isoformat(),
                        'data_source': 'algorithm_comparison_api'
                    })
                else:
                    print("❌ 算法对比API中也没有路径数据")
            except ImportError:
                print("⚠️ 无法导入算法对比API")

            return jsonify({
                'success': False,
                'error': '初始路径集为空，请先运行算法对比'
            }), 400
        
        print(f"📊 找到 {len(improved_algorithm.initial_path_set)} 条路径数据")

        # 🔧 添加调试信息：验证路径数据差异性
        if len(improved_algorithm.initial_path_set) >= 3:
            print("🔍 验证前3条路径的关键属性:")
            for i in range(3):
                path = improved_algorithm.initial_path_set[i]
                flight_dir = getattr(path, 'flight_direction', 'N/A')
                height_layer = getattr(path, 'height_layer', 'N/A')
                path_id = getattr(path, 'path_id', 'N/A')
                final_cost = getattr(path, 'final_cost', 'N/A')
                waypoint_count = len(path.waypoints) if hasattr(path, 'waypoints') and path.waypoints else 0
                print(f"   路径{i}: ID={path_id}, 方向={flight_dir}, 高度={height_layer}, 代价={final_cost}, 航点数={waypoint_count}")

        # 检查是否所有路径的flight_direction都相同
        flight_directions = [getattr(path, 'flight_direction', None) for path in improved_algorithm.initial_path_set[:10]]
        height_layers = [getattr(path, 'height_layer', None) for path in improved_algorithm.initial_path_set[:10]]
        print(f"🔍 前10条路径的方向分布: {flight_directions}")
        print(f"🔍 前10条路径的高度分布: {height_layers}")

        if len(set(flight_directions)) == 1:
            print("⚠️ 警告：前10条路径的flight_direction都相同！这可能是问题所在")
        if len(set(height_layers)) == 1:
            print("⚠️ 警告：前10条路径的height_layer都相同！这可能是问题所在")

        # 提取路径详细数据
        paths_data = []

        # 🔧 新增：首先添加基准路径数据
        baseline_path_data = get_baseline_path_data(algorithm_manager)
        if baseline_path_data:
            paths_data.append(baseline_path_data)
            print("✅ 已添加基准路径数据到第一行")
        
        # 🔧 修复：先按最终代价排序，计算正确的排名
        sorted_paths = sorted(improved_algorithm.initial_path_set, key=lambda p: getattr(p, 'final_cost', float('inf')))

        # 🔧 修复：获取选中路径的信息，使用最终代价最低的路径
        selected_path_id = None
        min_final_cost = float('inf')

        # 找到最终代价最低的路径作为选中路径
        for i, path in enumerate(improved_algorithm.initial_path_set):
            final_cost = getattr(path, 'final_cost', float('inf'))
            if final_cost < min_final_cost:
                min_final_cost = final_cost
                selected_path_id = i + 1  # 路径ID从1开始（按生成顺序）

        print(f"🎯 选中路径ID: {selected_path_id}, 最终代价: {min_final_cost:.6f}")

        # 如果还是没有找到，使用排序后的第一个路径
        if selected_path_id is None and sorted_paths:
            for i, path in enumerate(improved_algorithm.initial_path_set):
                if path == sorted_paths[0]:
                    selected_path_id = i + 1  # 路径ID从1开始
                    break

        for i, path in enumerate(improved_algorithm.initial_path_set):
            try:
                # 🔧 关键修复：路径索引编号 vs 排序序号
                path_index = i + 1  # 路径索引编号：按生成顺序（1-81）

                # 🔧 计算排序序号：按最终代价排序后的排名
                rank_by_cost = 1
                for j, sorted_path in enumerate(sorted_paths):
                    if sorted_path == path:
                        rank_by_cost = j + 1
                        break

                # 🔧 确定是否为选中路径
                is_selected_path = 1 if path_index == selected_path_id else 0

                # 基础路径信息
                path_info = {
                    'path_id': path_index,  # 🔧 修复：路径ID按论文要求从1开始（1-81）
                    'path_index': path_index,  # 🔧 路径索引编号（1-81）
                    'rank': rank_by_cost,  # 🔧 排序序号：按最终代价排序（1-81）
                    'original_path_id': getattr(path, 'path_id', i),  # 保留原始路径ID
                    'flight_direction': getattr(path, 'flight_direction', ''),
                    'height_layer': getattr(path, 'height_layer', ''),
                    'cluster_id': getattr(path, 'cluster_id', ''),
                    'cluster_type': getattr(path, 'cluster_type', ''),
                    'is_selected': is_selected_path,  # 🔧 修复：正确设置选中路径标记
                    
                    # 核心指标
                    'path_length': getattr(path, 'path_length', 0.0),
                    'turning_cost': getattr(path, 'turning_cost', 0.0),
                    'risk_value': getattr(path, 'risk_value', 0.0),
                    'collision_cost': getattr(path, 'collision_cost', 0.0),
                    'final_cost': getattr(path, 'final_cost', 0.0),
                    
                    # 路径详情
                    'waypoint_count': len(getattr(path, 'waypoints', [])),
                    'start_point': None,
                    'end_point': None,
                    
                    # 计算参数
                    'manhattan_distance': 0.0,
                    'risk_density': 0.0,
                    'dynamic_weights': {
                        'alpha': 0.0,
                        'beta': 0.0,
                        'gamma': 0.0,
                        'delta': 0.0
                    },
                    'reference_values': {
                        'risk': 0.0,
                        'collision': 0.0,
                        'turning': 0.0
                    },
                    
                    # 排序信息
                    'ranking': getattr(path, 'ranking', ''),
                    'cluster_ranking': getattr(path, 'cluster_ranking', ''),
                    
                    # 时间信息
                    'execution_time': getattr(path, 'execution_time', 0.0),
                    'generation_time': datetime.now().isoformat()
                }
                
                # 提取起点和终点信息
                waypoints = getattr(path, 'waypoints', [])
                if waypoints:
                    start_wp = waypoints[0]
                    end_wp = waypoints[-1]
                    
                    path_info['start_point'] = {
                        'lng': getattr(start_wp, 'x', getattr(start_wp, 'lng', 0.0)),
                        'lat': getattr(start_wp, 'y', getattr(start_wp, 'lat', 0.0)),
                        'alt': getattr(start_wp, 'z', getattr(start_wp, 'alt', 120.0))
                    }
                    
                    path_info['end_point'] = {
                        'lng': getattr(end_wp, 'x', getattr(end_wp, 'lng', 0.0)),
                        'lat': getattr(end_wp, 'y', getattr(end_wp, 'lat', 0.0)),
                        'alt': getattr(end_wp, 'z', getattr(end_wp, 'alt', 120.0))
                    }
                    
                    # 计算曼哈顿距离（公式2）
                    if path_info['start_point'] and path_info['end_point']:
                        dx = abs(path_info['end_point']['lng'] - path_info['start_point']['lng'])
                        dy = abs(path_info['end_point']['lat'] - path_info['start_point']['lat'])
                        dz = abs(path_info['end_point']['alt'] - path_info['start_point']['alt'])
                        path_info['manhattan_distance'] = 3.0 * (dx + dy + dz)
                
                # 计算风险密度（公式8）
                if path_info['path_length'] > 0:
                    path_info['risk_density'] = path_info['risk_value'] / path_info['path_length']
                
                # 计算动态权重（公式15）
                if path_info['risk_density'] > 0:
                    k = 5  # 控制指数变化速率的参数
                    exp_neg_k_rd = math.exp(-k * path_info['risk_density'])
                    
                    path_info['dynamic_weights'] = {
                        'alpha': 0.6 * (1 - exp_neg_k_rd),  # 风险权重
                        'beta': 0.3 * (1 - exp_neg_k_rd),   # 碰撞权重
                        'gamma': 0.7 * exp_neg_k_rd + 0.1,  # 长度权重
                        'delta': 0.2 * exp_neg_k_rd         # 转向权重
                    }
                
                # 设置参考值
                path_info['reference_values'] = {
                    'risk': getattr(improved_algorithm, 'risk_reference_value', 100.0),
                    'collision': getattr(improved_algorithm, 'collision_reference_value', 50.0),
                    'turning': getattr(improved_algorithm, 'turning_reference_value', 30.0)
                }
                
                paths_data.append(path_info)
                
            except Exception as e:
                print(f"⚠️ 处理路径 {i+1} 时出错: {e}")
                # 添加基础信息，即使出错也要包含
                paths_data.append({
                    'path_id': path_index,  # 🔧 修复：路径ID按论文要求从1开始（1-81）
                    'path_index': path_index,  # 🔧 路径索引编号（1-81）
                    'rank': rank_by_cost,  # 🔧 排序序号：按最终代价排序（1-81）
                    'error': str(e),
                    'path_length': getattr(path, 'path_length', 0.0),
                    'turning_cost': getattr(path, 'turning_cost', 0.0),
                    'risk_value': getattr(path, 'risk_value', 0.0),
                    'collision_cost': getattr(path, 'collision_cost', 0.0),
                    'final_cost': getattr(path, 'final_cost', 0.0)
                })
        
        print(f"✅ 成功处理 {len(paths_data)} 条路径数据")
        
        # 返回数据
        response_data = {
            'success': True,
            'paths_data': paths_data,
            'total_paths': len(paths_data),
            'export_time': datetime.now().isoformat(),
            'algorithm_info': {
                'name': 'Improved Cluster-based Path Planning',
                'version': '1.0',
                'total_initial_paths': 81,
                'actual_paths': len(paths_data)
            },
            'metadata': {
                'export_format': 'detailed_analysis',
                'purpose': 'parameter_tuning',
                'includes_dynamic_weights': True,
                'includes_reference_values': True
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"❌ 导出81条路径数据失败: {e}")
        return jsonify({
            'success': False,
            'error': f'导出失败: {str(e)}'
        }), 500

@path_export_bp.route('/paths_data_status', methods=['GET'])
def get_paths_data_status():
    """
    获取路径数据状态
    """
    try:
        algorithm_manager = get_algorithm_manager()

        status = {
            'algorithm_manager_available': algorithm_manager is not None,
            'improved_algorithm_available': False,
            'paths_count': 0,
            'last_update': None
        }

        if algorithm_manager:
            # 查找改进算法
            for name, algo in algorithm_manager.algorithms.items():
                if 'improved' in name.lower() or 'cluster' in name.lower():
                    status['improved_algorithm_available'] = True
                    if hasattr(algo, 'initial_path_set') and algo.initial_path_set:
                        status['paths_count'] = len(algo.initial_path_set)
                        status['last_update'] = datetime.now().isoformat()
                    break
        
        return jsonify({
            'success': True,
            'status': status
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
